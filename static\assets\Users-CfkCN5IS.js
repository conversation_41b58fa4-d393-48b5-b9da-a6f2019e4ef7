import{r as a,j as e,c as H,B as c,d as J}from"./index-dCUkEeO4.js";import{C as I}from"./Card-CLlE15Sf.js";import{adminApi as V}from"./adminApi-BFZ8qr13.js";import{F as O,a as Y}from"./MagnifyingGlassIcon-C56FJ4Gh.js";import{F as w}from"./UserIcon-DIEnBeEF.js";function Z(){const[D,v]=a.useState(!0),[y,k]=a.useState(null),[x,$]=a.useState(""),[h,B]=a.useState("all"),[u,P]=a.useState("all"),[l,o]=a.useState(1),[p]=a.useState(10),[S,z]=a.useState([]),[j,T]=a.useState(0),[t,C]=a.useState(null),[R,U]=a.useState(!1),[g,E]=a.useState(!1),m=async()=>{var s,r;try{console.log("🔄 Fetching users..."),v(!0),k(null);const i={page:l,per_page:p};x.trim()&&(i.search=x.trim()),h!=="all"&&(i.blockchain=h),u!=="all"&&(i.status=u),console.log("📤 API call params:",i);const n=await V.getUsers(i);console.log("📥 API response:",n),z(n.users),T(n.total)}catch(i){console.error("❌ Failed to fetch users:",i),k(((r=(s=i.response)==null?void 0:s.data)==null?void 0:r.error)||"Failed to load users")}finally{v(!1)}};a.useEffect(()=>{g||(m(),E(!0))},[]),a.useEffect(()=>{g&&m()},[l,h,u]),a.useEffect(()=>{if(!g)return;const s=setTimeout(()=>{l===1?m():o(1)},500);return()=>clearTimeout(s)},[x]);const d=Math.ceil(j/p),L=s=>{C(s),U(!0)},b=()=>{C(null),U(!1)},M=s=>new Date(s*1e3).toLocaleDateString(),_=s=>{const r=Date.now(),i=s*1e3,n=Math.floor((r-i)/(1e3*60));if(n<1)return"Just now";if(n<60)return`${n}m ago`;const f=Math.floor(n/60);if(f<24)return`${f}h ago`;const N=Math.floor(f/24);return N<30?`${N}d ago`:`${Math.floor(N/30)}mo ago`},A=s=>{const r=Date.now(),i=s.last_seen*1e3;return(r-i)/(1e3*60*60)<24?e.jsxs("span",{className:"inline-flex items-center rounded-full bg-green-900/30 border border-green-500/30 backdrop-blur-sm px-2.5 py-0.5 text-xs font-medium text-green-300",children:[e.jsx("span",{className:"h-1.5 w-1.5 rounded-full bg-green-400 mr-1.5 animate-pulse"}),"Active"]}):e.jsxs("span",{className:"inline-flex items-center rounded-full bg-gray-900/30 border border-gray-500/30 backdrop-blur-sm px-2.5 py-0.5 text-xs font-medium text-gray-300",children:[e.jsx("span",{className:"h-1.5 w-1.5 rounded-full bg-gray-400 mr-1.5"}),"Inactive"]})},F=s=>{const i={sol:"bg-purple-900/30 border-purple-500/30 text-purple-300",eth:"bg-blue-900/30 border-blue-500/30 text-blue-300",bsc:"bg-yellow-900/30 border-yellow-500/30 text-yellow-300",base:"bg-indigo-900/30 border-indigo-500/30 text-indigo-300"}[s]||"bg-gray-900/30 border-gray-500/30 text-gray-300";return e.jsx("span",{className:`inline-flex items-center rounded-full ${i} backdrop-blur-sm px-2.5 py-0.5 text-xs font-medium`,children:s.toUpperCase()})};return D?e.jsx("div",{className:"flex items-center justify-center h-64",children:e.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"})}):y?e.jsxs("div",{className:"space-y-8",children:[e.jsx("div",{className:"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4",children:e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold text-white",children:"User Management"}),e.jsx("p",{className:"mt-1 text-sm text-gray-400",children:"Manage user accounts and permissions"})]})}),e.jsx(I,{className:"p-6",children:e.jsx("div",{className:"flex items-center justify-center h-64",children:e.jsxs("div",{className:"text-center",children:[e.jsx(H,{className:"h-12 w-12 text-red-400 mx-auto mb-4"}),e.jsx("h3",{className:"text-lg font-medium text-white mb-2",children:"Failed to Load Users"}),e.jsx("p",{className:"text-gray-400 mb-4",children:y}),e.jsx(c,{onClick:m,variant:"glass",children:"Try Again"})]})})})]}):e.jsxs("div",{className:"space-y-8",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold text-white",children:"User Management"}),e.jsx("p",{className:"mt-1 text-sm text-gray-400",children:"Manage user accounts and permissions"})]}),e.jsx(c,{variant:"glass",glow:!0,onClick:m,children:"Refresh Users"})]}),e.jsxs(I,{className:"p-6",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row justify-between gap-4 mb-6",children:[e.jsxs("div",{className:"relative flex-grow max-w-md",children:[e.jsx("div",{className:"pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3",children:e.jsx(O,{className:"h-5 w-5 text-gray-400","aria-hidden":"true"})}),e.jsx("input",{type:"text",className:`block w-full rounded-xl border-0 py-2 pl-10 pr-4 text-white ring-1 ring-inset ring-white/10
                placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-red-500
                sm:text-sm sm:leading-6 bg-white/5 backdrop-blur-sm transition-all duration-300`,placeholder:"Search users by name or email",value:x,onChange:s=>$(s.target.value)})]}),e.jsxs("div",{className:"flex flex-col sm:flex-row gap-2 sm:gap-4",children:[e.jsxs("select",{className:`rounded-xl border-0 py-2 pl-3 pr-10 text-white ring-1 ring-inset ring-white/10
                focus:ring-2 focus:ring-inset focus:ring-red-500
                sm:text-sm sm:leading-6 bg-white/5 backdrop-blur-sm transition-all duration-300`,value:h,onChange:s=>B(s.target.value),children:[e.jsx("option",{value:"all",children:"All Blockchains"}),e.jsx("option",{value:"sol",children:"Solana"}),e.jsx("option",{value:"eth",children:"Ethereum"}),e.jsx("option",{value:"bsc",children:"BSC"}),e.jsx("option",{value:"base",children:"Base"})]}),e.jsxs("select",{className:`rounded-xl border-0 py-2 pl-3 pr-10 text-white ring-1 ring-inset ring-white/10
                focus:ring-2 focus:ring-inset focus:ring-red-500
                sm:text-sm sm:leading-6 bg-white/5 backdrop-blur-sm transition-all duration-300`,value:u,onChange:s=>P(s.target.value),children:[e.jsx("option",{value:"all",children:"All Status"}),e.jsx("option",{value:"active",children:"Active"}),e.jsx("option",{value:"inactive",children:"Inactive"})]})]})]}),e.jsx("div",{className:"overflow-hidden rounded-xl border border-white/10 bg-white/5 backdrop-blur-sm",children:e.jsxs("table",{className:"min-w-full divide-y divide-white/10",children:[e.jsx("thead",{children:e.jsxs("tr",{children:[e.jsx("th",{scope:"col",className:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-white sm:pl-6",children:"User"}),e.jsx("th",{scope:"col",className:"px-3 py-3.5 text-left text-sm font-semibold text-white",children:"Username"}),e.jsx("th",{scope:"col",className:"px-3 py-3.5 text-left text-sm font-semibold text-white",children:"Blockchain"}),e.jsx("th",{scope:"col",className:"px-3 py-3.5 text-left text-sm font-semibold text-white",children:"Status"}),e.jsx("th",{scope:"col",className:"px-3 py-3.5 text-left text-sm font-semibold text-white",children:"Created"}),e.jsx("th",{scope:"col",className:"px-3 py-3.5 text-left text-sm font-semibold text-white",children:"Last Seen"}),e.jsx("th",{scope:"col",className:"relative py-3.5 pl-3 pr-4 sm:pr-6",children:e.jsx("span",{className:"sr-only",children:"Actions"})})]})}),e.jsx("tbody",{className:"divide-y divide-white/10",children:S.length===0?e.jsx("tr",{children:e.jsx("td",{colSpan:7,className:"px-6 py-12 text-center",children:e.jsxs("div",{className:"text-gray-400",children:[e.jsx(w,{className:"h-12 w-12 mx-auto mb-4 text-gray-500"}),e.jsx("h3",{className:"text-lg font-medium text-white mb-2",children:"No users found"}),e.jsx("p",{children:"No users match your current filters."})]})})}):S.map(s=>e.jsxs("tr",{className:"hover:bg-white/5 transition-colors duration-200",children:[e.jsx("td",{className:"whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-white sm:pl-6",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"h-8 w-8 flex-shrink-0 rounded-full bg-indigo-800/30 flex items-center justify-center mr-3",children:e.jsx(w,{className:"h-4 w-4 text-indigo-400"})}),e.jsxs("div",{children:[e.jsxs("div",{className:"font-medium text-white",children:[s.first_name," ",s.last_name||""]}),e.jsxs("div",{className:"text-xs text-gray-400",children:["ID: ",s.chat_id]})]})]})}),e.jsx("td",{className:"whitespace-nowrap px-3 py-4 text-sm text-gray-300",children:s.username?`@${s.username}`:"N/A"}),e.jsx("td",{className:"whitespace-nowrap px-3 py-4 text-sm",children:F(s.current_blockchain)}),e.jsx("td",{className:"whitespace-nowrap px-3 py-4 text-sm",children:A(s)}),e.jsx("td",{className:"whitespace-nowrap px-3 py-4 text-sm text-gray-300",children:M(s.created_at)}),e.jsx("td",{className:"whitespace-nowrap px-3 py-4 text-sm text-gray-300",children:_(s.last_seen)}),e.jsx("td",{className:"relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-6",children:e.jsx(c,{variant:"outline",size:"sm",className:"p-1",title:"View User Details",onClick:()=>L(s),children:e.jsx(Y,{className:"h-4 w-4"})})})]},s.id))})]})}),d>1&&e.jsxs("div",{className:"flex items-center justify-between border-t border-white/10 bg-white/5 px-4 py-3 sm:px-6 mt-4 rounded-xl",children:[e.jsxs("div",{className:"flex flex-1 justify-between sm:hidden",children:[e.jsx(c,{variant:"outline",size:"sm",onClick:()=>o(Math.max(1,l-1)),disabled:l===1,children:"Previous"}),e.jsx(c,{variant:"outline",size:"sm",onClick:()=>o(Math.min(d,l+1)),disabled:l===d,children:"Next"})]}),e.jsxs("div",{className:"hidden sm:flex sm:flex-1 sm:items-center sm:justify-between",children:[e.jsx("div",{children:e.jsxs("p",{className:"text-sm text-gray-400",children:["Showing ",e.jsx("span",{className:"font-medium text-white",children:(l-1)*p+1})," to"," ",e.jsx("span",{className:"font-medium text-white",children:Math.min(l*p,j)})," ","of ",e.jsx("span",{className:"font-medium text-white",children:j})," results"]})}),e.jsx("div",{children:e.jsxs("nav",{className:"isolate inline-flex -space-x-px rounded-md shadow-sm","aria-label":"Pagination",children:[e.jsx(c,{variant:"outline",size:"sm",className:"rounded-l-md",onClick:()=>o(Math.max(1,l-1)),disabled:l===1,children:"Previous"}),Array.from({length:d}).map((s,r)=>e.jsx(c,{variant:l===r+1?"glass":"outline",size:"sm",className:"px-4",onClick:()=>o(r+1),children:r+1},r)),e.jsx(c,{variant:"outline",size:"sm",className:"rounded-r-md",onClick:()=>o(Math.min(d,l+1)),disabled:l===d,children:"Next"})]})})]})]})]}),R&&t&&e.jsx("div",{className:"fixed inset-0 z-50 overflow-y-auto",children:e.jsxs("div",{className:"flex min-h-screen items-center justify-center px-4 pt-4 pb-20 text-center sm:block sm:p-0",children:[e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-75 transition-opacity",onClick:b}),e.jsxs("div",{className:"inline-block transform overflow-hidden rounded-xl bg-gray-900 border border-white/10 px-4 pt-5 pb-4 text-left align-bottom shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-2xl sm:p-6 sm:align-middle",children:[e.jsx("div",{className:"absolute top-0 right-0 pt-4 pr-4",children:e.jsxs("button",{type:"button",className:"rounded-md bg-gray-900 text-gray-400 hover:text-white focus:outline-none focus:ring-2 focus:ring-indigo-500",onClick:b,children:[e.jsx("span",{className:"sr-only",children:"Close"}),e.jsx(J,{className:"h-6 w-6","aria-hidden":"true"})]})}),e.jsxs("div",{className:"sm:flex sm:items-start",children:[e.jsx("div",{className:"mx-auto flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-full bg-indigo-900/30 sm:mx-0 sm:h-10 sm:w-10",children:e.jsx(w,{className:"h-6 w-6 text-indigo-400","aria-hidden":"true"})}),e.jsxs("div",{className:"mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left flex-1",children:[e.jsx("h3",{className:"text-lg font-medium leading-6 text-white",children:"User Details"}),e.jsx("div",{className:"mt-4",children:e.jsxs("div",{className:"bg-gray-800/50 rounded-lg p-4 space-y-4",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300",children:"User ID"}),e.jsx("p",{className:"mt-1 text-sm text-white font-mono",children:t.id})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300",children:"Chat ID"}),e.jsx("p",{className:"mt-1 text-sm text-white",children:t.chat_id})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300",children:"First Name"}),e.jsx("p",{className:"mt-1 text-sm text-white",children:t.first_name})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300",children:"Last Name"}),e.jsx("p",{className:"mt-1 text-sm text-white",children:t.last_name||"N/A"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300",children:"Username"}),e.jsx("p",{className:"mt-1 text-sm text-white",children:t.username?`@${t.username}`:"N/A"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300",children:"Current Blockchain"}),e.jsx("p",{className:"mt-1 text-sm",children:F(t.current_blockchain)})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300",children:"Status"}),e.jsx("p",{className:"mt-1 text-sm",children:A(t)})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300",children:"Active"}),e.jsx("p",{className:"mt-1 text-sm text-white",children:t.is_active?"Yes":"No"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300",children:"Total Transactions"}),e.jsx("p",{className:"mt-1 text-sm text-white",children:t.total_transactions})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300",children:"Total Volume"}),e.jsxs("p",{className:"mt-1 text-sm text-white",children:["$",t.total_volume.toFixed(2)]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300",children:"Created At"}),e.jsx("p",{className:"mt-1 text-sm text-white",children:M(t.created_at)})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300",children:"Last Seen"}),e.jsx("p",{className:"mt-1 text-sm text-white",children:_(t.last_seen)})]})]}),e.jsxs("div",{className:"mt-6",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Raw JSON Data"}),e.jsx("div",{className:"bg-gray-900 rounded-md p-3 overflow-x-auto",children:e.jsx("pre",{className:"text-xs text-gray-300 whitespace-pre-wrap",children:JSON.stringify(t,null,2)})})]})]})})]})]}),e.jsx("div",{className:"mt-5 sm:mt-4 sm:flex sm:flex-row-reverse",children:e.jsx(c,{variant:"glass",onClick:b,className:"w-full sm:w-auto",children:"Close"})})]})]})})]})}export{Z as default};
