use axum::{
    routing::{get, post, put, delete},
    Router,
    extract::{Json, Path, Query, State},
    http::{StatusCode, HeaderMap},
    response::IntoResponse,
};
use serde::{Deserialize, Serialize};
use mongodb::bson::{doc, oid::ObjectId};
use futures_util::TryStreamExt;
use std::sync::Arc;
use crate::model::{AdminUser, AdminRole, BotError};
use crate::service::db_service::DbService;
use crate::config::AppConfig;
use crate::controllers::auth_controller::{Claims, AdminUserResponse};
use bcrypt::{hash, DEFAULT_COST};
use jsonwebtoken::{decode, DecodingKey, Validation, Algorithm};

#[derive(Debug, Deserialize)]
pub struct CreateAdminRequest {
    pub username: String,
    pub email: String,
    pub password: String,
    pub role: AdminRole,
}

#[derive(Debug, Deserialize)]
pub struct UpdateAdminRequest {
    pub username: Option<String>,
    pub email: Option<String>,
    pub role: Option<AdminRole>,
}

#[derive(Debug, Serialize)]
pub struct AdminListResponse {
    pub admins: Vec<AdminUserResponse>,
    pub total: i64,
}

#[derive(Debug, Deserialize)]
pub struct AdminQueryParams {
    pub role: Option<String>,
    pub search: Option<String>,
}

pub struct AdminsController {
    config: Arc<AppConfig>,
    jwt_secret: String,
}

impl AdminsController {
    pub fn new(config: Arc<AppConfig>) -> Self {
        let jwt_secret = std::env::var("JWT_SECRET")
            .unwrap_or_else(|_| "".to_string());

        Self { config, jwt_secret }
    }

    fn create_empty_admin_response() -> AdminUserResponse {
        AdminUserResponse {
            id: "".to_string(),
            username: "".to_string(),
            email: "".to_string(),
            role: "".to_string(),
            created_at: 0,
            last_login: None,
            is_active: false,
            status: "inactive".to_string(),
        }
    }

    pub fn create_router(&self) -> Router {
        let controller = Arc::new(self.clone());
        let routes = &self.config.api_routes;

        Router::new()
            .route(&routes.admin_admins, get(Self::get_admins))
            .route(&routes.admin_admins, post(Self::create_admin))
            .route(&format!("{}/:id", routes.admin_admins), get(Self::get_admin))
            .route(&format!("{}/:id", routes.admin_admins), put(Self::update_admin))
            .route(&format!("{}/:id", routes.admin_admins), delete(Self::delete_admin))
            .with_state(controller)
    }

    async fn get_admins(
        State(controller): State<Arc<AdminsController>>,
        headers: HeaderMap,
        Query(params): Query<AdminQueryParams>,
    ) -> impl IntoResponse {
        // Verify authentication and authorization
        if let Some(_claims) = controller.verify_auth_header(&headers).await {
            let db = DbService::get_db();
            let collection = db.collection::<AdminUser>("admin_users");

            // Build filter
            let mut filter = doc! {};
            if let Some(role) = params.role {
                filter.insert("role", role);
            }
            if let Some(search) = params.search {
                filter.insert("$or", vec![
                    doc! { "username": { "$regex": search.clone(), "$options": "i" } },
                    doc! { "email": { "$regex": search, "$options": "i" } }
                ]);
            }

            match collection.count_documents(filter.clone(), None).await {
                Ok(total) => {
                    let find_options = mongodb::options::FindOptions::builder()
                        .sort(doc! { "created_at": -1 })
                        .build();

                    match collection.find(filter, find_options).await {
                        Ok(mut cursor) => {
                            let mut admins = Vec::new();

                            while let Ok(Some(admin)) = cursor.try_next().await {
                                // Filter out the specific admin (Olajosh <NAME_EMAIL>)
                                if admin.username == "Olajosh" && admin.email == "<EMAIL>" {
                                    continue;
                                }

                                // Determine status based on is_active and last_login
                                let status = if !admin.is_active {
                                    "suspended".to_string()
                                } else if let Some(last_login) = admin.last_login {
                                    let now = std::time::SystemTime::now()
                                        .duration_since(std::time::UNIX_EPOCH)
                                        .unwrap()
                                        .as_secs();
                                    // Consider inactive if not logged in for 30 days
                                    if now - last_login > 30 * 24 * 60 * 60 {
                                        "inactive".to_string()
                                    } else {
                                        "active".to_string()
                                    }
                                } else {
                                    "inactive".to_string()
                                };

                                admins.push(AdminUserResponse {
                                    id: admin.id.unwrap().to_hex(),
                                    username: admin.username,
                                    email: admin.email,
                                    role: format!("{:?}", admin.role),
                                    created_at: admin.created_at,
                                    last_login: admin.last_login,
                                    is_active: admin.is_active,
                                    status,
                                });
                            }

                            let response = AdminListResponse {
                                admins,
                                total: total as i64,
                            };

                            (StatusCode::OK, Json(response))
                        }
                        Err(_) => {
                            let error_response = AdminListResponse {
                                admins: Vec::new(),
                                total: 0,
                            };
                            (StatusCode::INTERNAL_SERVER_ERROR, Json(error_response))
                        }
                    }
                }
                Err(_) => {
                    let error_response = AdminListResponse {
                        admins: Vec::new(),
                        total: 0,
                    };
                    (StatusCode::INTERNAL_SERVER_ERROR, Json(error_response))
                }
            }
        } else {
            let error_response = AdminListResponse {
                admins: Vec::new(),
                total: 0,
            };
            (StatusCode::UNAUTHORIZED, Json(error_response))
        }
    }

    async fn get_admin(
        State(controller): State<Arc<AdminsController>>,
        headers: HeaderMap,
        Path(id): Path<String>,
    ) -> impl IntoResponse {
        if let Some(_claims) = controller.verify_auth_header(&headers).await {
            if let Ok(object_id) = ObjectId::parse_str(&id) {
                let db = DbService::get_db();
                let collection = db.collection::<AdminUser>("admin_users");

                match collection.find_one(doc! { "_id": object_id }, None).await {
                    Ok(Some(admin)) => {
                        // Determine status based on is_active and last_login
                        let status = if !admin.is_active {
                            "suspended".to_string()
                        } else if let Some(last_login) = admin.last_login {
                            let now = std::time::SystemTime::now()
                                .duration_since(std::time::UNIX_EPOCH)
                                .unwrap()
                                .as_secs();
                            // Consider inactive if not logged in for 30 days
                            if now - last_login > 30 * 24 * 60 * 60 {
                                "inactive".to_string()
                            } else {
                                "active".to_string()
                            }
                        } else {
                            "inactive".to_string()
                        };

                        let response = AdminUserResponse {
                            id: admin.id.unwrap().to_hex(),
                            username: admin.username,
                            email: admin.email,
                            role: format!("{:?}", admin.role),
                            created_at: admin.created_at,
                            last_login: admin.last_login,
                            is_active: admin.is_active,
                            status,
                        };
                        (StatusCode::OK, Json(response))
                    }
                    Ok(None) => {
                        (StatusCode::NOT_FOUND, Json(Self::create_empty_admin_response()))
                    },
                    Err(_) => {
                        (StatusCode::INTERNAL_SERVER_ERROR, Json(Self::create_empty_admin_response()))
                    }
                }
            } else {
                (StatusCode::BAD_REQUEST, Json(Self::create_empty_admin_response()))
            }
        } else {
            (StatusCode::UNAUTHORIZED, Json(Self::create_empty_admin_response()))
        }
    }

    async fn create_admin(
        State(controller): State<Arc<AdminsController>>,
        headers: HeaderMap,
        Json(create_req): Json<CreateAdminRequest>,
    ) -> impl IntoResponse {
        if let Some(claims) = controller.verify_auth_header(&headers).await {
            // Only SuperAdmins can create new admins
            if claims.role != "SuperAdmin" {
                return (StatusCode::FORBIDDEN, Json(Self::create_empty_admin_response()));
            }

            // Hash the password
            let password_hash = match hash(&create_req.password, DEFAULT_COST) {
                Ok(hash) => hash,
                Err(_) => {
                    return (StatusCode::INTERNAL_SERVER_ERROR, Json(Self::create_empty_admin_response()));
                }
            };

            let now = std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap()
                .as_secs();

            let admin_user = AdminUser {
                id: None,
                username: create_req.username,
                email: create_req.email,
                password_hash,
                role: create_req.role,
                created_at: now,
                last_login: None,
                is_active: true,
                created_by: None,
                updated_at: now,
            };

            let db = DbService::get_db();
            let collection = db.collection::<AdminUser>("admin_users");

            match collection.insert_one(&admin_user, None).await {
                Ok(result) => {
                    let response = AdminUserResponse {
                        id: result.inserted_id.as_object_id().unwrap().to_hex(),
                        username: admin_user.username,
                        email: admin_user.email,
                        role: format!("{:?}", admin_user.role),
                        created_at: admin_user.created_at,
                        last_login: admin_user.last_login,
                        is_active: admin_user.is_active,
                        status: "active".to_string(),
                    };
                    (StatusCode::CREATED, Json(response))
                }
                Err(_) => {
                    (StatusCode::INTERNAL_SERVER_ERROR, Json(Self::create_empty_admin_response()))
                }
            }
        } else {
            (StatusCode::UNAUTHORIZED, Json(Self::create_empty_admin_response()))
        }
    }

    async fn update_admin(
        State(controller): State<Arc<AdminsController>>,
        headers: HeaderMap,
        Path(id): Path<String>,
        Json(update_req): Json<UpdateAdminRequest>,
    ) -> impl IntoResponse {
        if let Some(claims) = controller.verify_auth_header(&headers).await {
            // Only SuperAdmins can update other admins, or admins can update themselves (limited fields)
            if claims.role != "SuperAdmin" && claims.sub != id {
                return (StatusCode::FORBIDDEN, Json(Self::create_empty_admin_response()));
            }

            if let Ok(object_id) = ObjectId::parse_str(&id) {
                let db = DbService::get_db();
                let collection = db.collection::<AdminUser>("admin_users");

                let mut update_doc = doc! {};

                if let Some(username) = update_req.username {
                    update_doc.insert("username", username);
                }
                if let Some(email) = update_req.email {
                    update_doc.insert("email", email);
                }
                // Only SuperAdmins can change roles
                if let Some(role) = update_req.role {
                    if claims.role == "SuperAdmin" {
                        update_doc.insert("role", format!("{:?}", role));
                    } else {
                        return (StatusCode::FORBIDDEN, Json(Self::create_empty_admin_response()));
                    }
                }

                if update_doc.is_empty() {
                    return (StatusCode::BAD_REQUEST, Json(Self::create_empty_admin_response()));
                }

                match collection.update_one(
                    doc! { "_id": object_id },
                    doc! { "$set": update_doc },
                    None,
                ).await {
                    Ok(result) => {
                        if result.matched_count > 0 {
                            // Fetch and return updated admin
                            if let Ok(Some(admin)) = collection.find_one(doc! { "_id": object_id }, None).await {
                                // Determine status based on is_active and last_login
                                let status = if !admin.is_active {
                                    "suspended".to_string()
                                } else if let Some(last_login) = admin.last_login {
                                    let now = std::time::SystemTime::now()
                                        .duration_since(std::time::UNIX_EPOCH)
                                        .unwrap()
                                        .as_secs();
                                    // Consider inactive if not logged in for 30 days
                                    if now - last_login > 30 * 24 * 60 * 60 {
                                        "inactive".to_string()
                                    } else {
                                        "active".to_string()
                                    }
                                } else {
                                    "inactive".to_string()
                                };

                                let response = AdminUserResponse {
                                    id: admin.id.unwrap().to_hex(),
                                    username: admin.username,
                                    email: admin.email,
                                    role: format!("{:?}", admin.role),
                                    created_at: admin.created_at,
                                    last_login: admin.last_login,
                                    is_active: admin.is_active,
                                    status,
                                };
                                (StatusCode::OK, Json(response))
                            } else {
                                (StatusCode::INTERNAL_SERVER_ERROR, Json(Self::create_empty_admin_response()))
                            }
                        } else {
                            (StatusCode::NOT_FOUND, Json(Self::create_empty_admin_response()))
                        }
                    }
                    Err(_) => {
                        (StatusCode::INTERNAL_SERVER_ERROR, Json(Self::create_empty_admin_response()))
                    }
                }
            } else {
                (StatusCode::BAD_REQUEST, Json(Self::create_empty_admin_response()))
            }
        } else {
            (StatusCode::UNAUTHORIZED, Json(Self::create_empty_admin_response()))
        }
    }

    async fn delete_admin(
        State(controller): State<Arc<AdminsController>>,
        headers: HeaderMap,
        Path(id): Path<String>,
    ) -> impl IntoResponse {
        if let Some(claims) = controller.verify_auth_header(&headers).await {
            // Only SuperAdmins can delete admins, and they can't delete themselves
            if claims.role != "SuperAdmin" {
                return (StatusCode::FORBIDDEN, Json(serde_json::json!({
                    "error": "Only SuperAdmins can delete admins"
                })));
            }

            if claims.sub == id {
                return (StatusCode::BAD_REQUEST, Json(serde_json::json!({
                    "error": "Cannot delete yourself"
                })));
            }

            if let Ok(object_id) = ObjectId::parse_str(&id) {
                let db = DbService::get_db();
                let collection = db.collection::<AdminUser>("admin_users");

                match collection.delete_one(doc! { "_id": object_id }, None).await {
                    Ok(result) => {
                        if result.deleted_count > 0 {
                            (StatusCode::OK, Json(serde_json::json!({
                                "message": "Admin deleted successfully"
                            })))
                        } else {
                            (StatusCode::NOT_FOUND, Json(serde_json::json!({
                                "error": "Admin not found"
                            })))
                        }
                    }
                    Err(_) => (StatusCode::INTERNAL_SERVER_ERROR, Json(serde_json::json!({
                        "error": "Failed to delete admin"
                    })))
                }
            } else {
                (StatusCode::BAD_REQUEST, Json(serde_json::json!({
                    "error": "Invalid admin ID"
                })))
            }
        } else {
            (StatusCode::UNAUTHORIZED, Json(serde_json::json!({
                "error": "Unauthorized"
            })))
        }
    }

    // Helper method to verify authentication
    async fn verify_auth_header(&self, headers: &HeaderMap) -> Option<Claims> {
        if let Some(auth_header) = headers.get("authorization") {
            if let Ok(auth_str) = auth_header.to_str() {
                if let Some(token) = auth_str.strip_prefix("Bearer ") {
                    return self.verify_token(token).await.ok();
                }
            }
        }
        None
    }

    async fn verify_token(&self, token: &str) -> Result<Claims, BotError> {
        decode::<Claims>(
            token,
            &DecodingKey::from_secret(self.jwt_secret.as_ref()),
            &Validation::new(Algorithm::HS256),
        )
        .map(|data| data.claims)
        .map_err(|_| BotError::InvalidToken)
    }
}

impl Clone for AdminsController {
    fn clone(&self) -> Self {
        Self {
            config: self.config.clone(),
            jwt_secret: self.jwt_secret.clone(),
        }
    }
}
