use teloxide::types::{InlineKeyboardButton, InlineKeyboardMarkup};

use crate::model::{UserData, BotError, Blockchain};
use crate::model::wallet_config::WalletConfig;
use crate::service::BotService;
use bson::Decimal128;
use std::str::FromStr;

/// Shows the snipe configuration screen for a specific blockchain
pub async fn show_snipe_config_screen(
    bot_service: &BotService,
    user_data: &UserData,
    message_id: i32,
    blockchain: Blockchain,
    token_address: &str,
) -> Result<(), BotError> {
    println!("Showing snipe config screen for {:?} token: {}", blockchain, token_address);

    // Get the wallet config for the blockchain (for default values)
    let config = match user_data.get_config(&blockchain) {
        Some(config) => config.clone(),
        None => {
            // Create a default config if none exists
            match blockchain {
                Blockchain::BSC => WalletConfig::bsc_default(),
                Blockchain::SOL => WalletConfig::sol_default(),
                Blockchain::ETH => WalletConfig::eth_default(),
                Blockchain::BASE => WalletConfig::base_default(),
            }
        }
    };

    // Fetch snipes from the database
    let snipes = crate::service::DbService::get_user_snipes_by_blockchain(user_data.chat_id(), &blockchain).await?;

    // Find the snipe with the matching token address (case-insensitive)
    let mut snipe_opt = None;
    for s in snipes {
        if s.contract_address.to_lowercase() == token_address.to_lowercase() {
            snipe_opt = Some(s);
            break;
        }
    }

    // Check if the token address matches the current contract address in the user's config
    let is_current_token = match blockchain {
        Blockchain::BSC => user_data.config.bsc_current_contract_address.as_deref() == Some(token_address),
        Blockchain::SOL => user_data.config.sol_current_contract_address.as_deref() == Some(token_address),
        Blockchain::ETH => user_data.config.eth_current_contract_address.as_deref() == Some(token_address),
        Blockchain::BASE => user_data.config.base_current_contract_address.as_deref() == Some(token_address),
    };

    // Format the snipe config screen text - prioritize database values, then current token config, then defaults
    let config_text = if let Some(snipe) = snipe_opt {
        // Use database values
        format!(
            "🎯 <b>{} Snipe Configuration</b>\n\n\
            👤 <b>User:</b> {}\n\
            🔖 <b>Token:</b> {}\n\n\
            <b>Snipe Settings:</b>\n\
            • <b>Amount:</b> {} {}\n\
            • <b>Slippage:</b> {}%\n\
            • <b>Anti-Rug Protection:</b> {}\n\
            • <b>Anti-MEV Protection:</b> {}\n\n\
            <i>Tap a setting to change it. Click 'Execute Snipe' to save.</i>",
            blockchain.to_string().to_uppercase(),
            user_data.display_name(),
            token_address,
            snipe.amount.to_string(),
            blockchain.get_native_symbol(),
            snipe.slippage.to_string(),
            "✅ Enabled", // Always enabled
            "✅ Enabled"  // Always enabled
        )
    } else if is_current_token {
        // Use the current blockchain config
        format!(
            "🎯 <b>{} Snipe Configuration</b>\n\n\
            👤 <b>User:</b> {}\n\
            🔖 <b>Token:</b> {}\n\n\
            <b>Snipe Settings:</b>\n\
            • <b>Amount:</b> {} {}\n\
            • <b>Slippage:</b> {}%\n\
            • <b>Anti-Rug Protection:</b> {}\n\
            • <b>Anti-MEV Protection:</b> {}\n\n\
            <i>Tap a setting to change it. Click 'Execute Snipe' to save.</i>",
            blockchain.to_string().to_uppercase(),
            user_data.display_name(),
            token_address,
            "0.1", // Default amount
            blockchain.get_native_symbol(),
            config.slippage.to_string(),
            "✅ Enabled", // Always enabled
            "✅ Enabled"  // Always enabled
        )
    } else {
        // Use defaults
        format!(
            "🎯 <b>{} Snipe Configuration</b>\n\n\
            👤 <b>User:</b> {}\n\
            🔖 <b>Token:</b> {}\n\n\
            <b>Snipe Settings:</b>\n\
            • <b>Amount:</b> {} {}\n\
            • <b>Slippage:</b> {}%\n\
            • <b>Anti-Rug Protection:</b> {}\n\
            • <b>Anti-MEV Protection:</b> {}\n\n\
            <i>Tap a setting to change it. Click 'Execute Snipe' to save.</i>",
            blockchain.to_string().to_uppercase(),
            user_data.display_name(),
            token_address,
            "0.1", // Default amount
            blockchain.get_native_symbol(),
            config.slippage.to_string(),
            "✅ Enabled", // Always enabled
            "✅ Enabled"  // Always enabled
        )
    };

    // Create the snipe config keyboard
    let keyboard = create_snipe_config_keyboard(blockchain, token_address);

    // Edit the message to show the snipe config screen
    bot_service.edit_message_with_keyboard(
        user_data.chat_id(),
        message_id,
        &config_text,
        keyboard,
    ).await?;

    Ok(())
}

/// Create the snipe configuration keyboard
pub fn create_snipe_config_keyboard(blockchain: Blockchain, token_address: &str) -> InlineKeyboardMarkup {
    // For Solana tokens, we need to shorten the address to fit within Telegram's 64-byte limit
    // Store the full token address in the user's config and use a shortened version for buttons
    let token_short = if token_address.len() > 16 {
        // Use first 8 and last 8 characters for a shortened representation
        format!("{}...{}", &token_address[0..8], &token_address[token_address.len()-8..])
    } else {
        token_address.to_string()
    };

    // The token address is already stored in the user's config when they enter it
    // We're just using a shortened version for the callback data

    let keyboard = vec![
        // Amount setting
        vec![
            InlineKeyboardButton::callback(
                "💰 Set Amount".to_string(),
                format!("set_snipe_amount:{}:{}", blockchain.as_str(), token_short),
            )
        ],
        // Slippage setting
        vec![
            InlineKeyboardButton::callback(
                "🔧 Set Slippage".to_string(),
                format!("set_snipe_slippage:{}:{}", blockchain.as_str(), token_short),
            )
        ],
        // Protection settings info (not toggleable)
        vec![
            InlineKeyboardButton::callback(
                "🛡️ Security: Anti-Rug & Anti-MEV Enabled".to_string(),
                format!("security_info:{}:{}", blockchain.as_str(), token_short),
            )
        ],
        // Execute snipe button
        vec![
            InlineKeyboardButton::callback(
                "🎯 Execute Snipe".to_string(),
                format!("execute_snipe:{}:{}", blockchain.as_str(), token_short),
            )
        ],
        // View active snipes button
        vec![
            InlineKeyboardButton::callback(
                "👁️ View Active Snipes".to_string(),
                format!("view_active_snipes_{}", blockchain.as_str()),
            )
        ],
        // Back button
        vec![
            InlineKeyboardButton::callback(
                "◀️ Back to Dashboard".to_string(),
                format!("view_{}", blockchain.as_str()),
            )
        ]
    ];

    InlineKeyboardMarkup::new(keyboard)
}

/// Toggle a boolean setting in the snipe configuration
pub async fn toggle_snipe_setting(
    bot_service: &BotService,
    user_data: &mut UserData,
    message_id: i32,
    blockchain: Blockchain,
    token_address: &str,
    setting: &str,
) -> Result<(), BotError> {
    println!("Toggling snipe {} setting for {:?}", setting, blockchain);

    // Fetch snipes from the database
    let snipes = crate::service::DbService::get_user_snipes_by_blockchain(user_data.chat_id(), &blockchain).await?;

    // Find the snipe with the matching token address (case-insensitive)
    let mut snipe_opt = None;
    for s in snipes {
        if s.contract_address.to_lowercase() == token_address.to_lowercase() {
            snipe_opt = Some(s);
            break;
        }
    }

    // If no snipe exists, create a new one
    let mut snipe = if let Some(s) = snipe_opt {
        s
    } else {
        // Get the user's config for this blockchain
        let config = user_data.get_config(&blockchain).cloned().unwrap_or_else(|| {
            match blockchain {
                Blockchain::BSC => WalletConfig::bsc_default(),
                Blockchain::SOL => WalletConfig::sol_default(),
                Blockchain::ETH => WalletConfig::eth_default(),
                Blockchain::BASE => WalletConfig::base_default(),
            }
        });

        println!("Creating new snipe for token: {}", token_address);

        // Create a new snipe with default values
        let amount = mongodb::bson::Decimal128::from_str("0.1").unwrap();
        crate::model::Snipe::new(
            user_data.chat_id(),
            blockchain.clone(),
            token_address.to_string(),
            amount,
            config.slippage,
            config.antirug,
            config.anti_mev
        )
    };

    // Toggle the setting
    match setting {
        "antirug" => snipe.antirug = !snipe.antirug,
        "antimev" => snipe.anti_mev = !snipe.anti_mev,
        _ => return Err(BotError::user_error(format!("Unknown setting: {}", setting))),
    }

    println!("Toggled {} to {}", setting, match setting {
        "antirug" => snipe.antirug,
        "antimev" => snipe.anti_mev,
        _ => false,
    });

    // Instead of saving to database, update the user's config (cache)
    println!("CRITICAL: Updating user config for token: {}", token_address);

    // Update the user's config based on blockchain
    match blockchain {
        Blockchain::BSC => {
            // Store the token address in the current contract address
            user_data.config.bsc_current_contract_address = Some(token_address.to_string());

            // Get or create the wallet config
            let mut wallet_config = user_data.config.bsc_config.clone().unwrap_or_else(WalletConfig::bsc_default);

            // Update the setting
            match setting {
                "antirug" => wallet_config.antirug = !wallet_config.antirug,
                "antimev" => wallet_config.anti_mev = !wallet_config.anti_mev,
                _ => {}
            }

            // Save the updated wallet config
            user_data.config.bsc_config = Some(wallet_config);
        },
        Blockchain::SOL => {
            // Store the token address in the current contract address
            user_data.config.sol_current_contract_address = Some(token_address.to_string());

            // Get or create the wallet config
            let mut wallet_config = user_data.config.sol_config.clone().unwrap_or_else(WalletConfig::sol_default);

            // Update the setting
            match setting {
                "antirug" => wallet_config.antirug = !wallet_config.antirug,
                "antimev" => wallet_config.anti_mev = !wallet_config.anti_mev,
                _ => {}
            }

            // Save the updated wallet config
            user_data.config.sol_config = Some(wallet_config);
        },
        Blockchain::ETH => {
            // Store the token address in the current contract address
            user_data.config.eth_current_contract_address = Some(token_address.to_string());

            // Get or create the wallet config
            let mut wallet_config = user_data.config.eth_config.clone().unwrap_or_else(WalletConfig::eth_default);

            // Update the setting
            match setting {
                "antirug" => wallet_config.antirug = !wallet_config.antirug,
                "antimev" => wallet_config.anti_mev = !wallet_config.anti_mev,
                _ => {}
            }

            // Save the updated wallet config
            user_data.config.eth_config = Some(wallet_config);
        },
        Blockchain::BASE => {
            // Store the token address in the current contract address
            user_data.config.base_current_contract_address = Some(token_address.to_string());

            // Get or create the wallet config
            let mut wallet_config = user_data.config.base_config.clone().unwrap_or_else(WalletConfig::base_default);

            // Update the setting
            match setting {
                "antirug" => wallet_config.antirug = !wallet_config.antirug,
                "antimev" => wallet_config.anti_mev = !wallet_config.anti_mev,
                _ => {}
            }

            // Save the updated wallet config
            user_data.config.base_config = Some(wallet_config);
        },
    }

    // Save the updated config
    crate::service::DbService::save_user_config(&user_data.config).await?;

    // If this is an existing snipe in the database, update it
    if snipe.id.is_some() {
        println!("CRITICAL: Updating existing snipe with ID: {:?}", snipe.id);
        match crate::service::DbService::save_snipe(&mut snipe).await {
            Ok(id) => println!("CRITICAL: Successfully updated snipe with ID: {}", id),
            Err(e) => println!("CRITICAL ERROR: Failed to update snipe: {}", e)
        }
    }

    // Update the snipe config screen
    show_snipe_config_screen(bot_service, user_data, message_id, blockchain, token_address).await?;

    Ok(())
}

/// Update a numeric setting in the snipe configuration
pub async fn update_snipe_numeric_setting(
    bot_service: &BotService,
    user_data: &mut UserData,
    message_id: i32,
    blockchain: Blockchain,
    token_address: &str,
    setting: &str,
    value: &str,
) -> Result<(), BotError> {
    println!("Updating snipe {} setting for {:?} to {}", setting, blockchain, value);

    // Fetch snipes from the database
    let snipes = crate::service::DbService::get_user_snipes_by_blockchain(user_data.chat_id(), &blockchain).await?;

    // Find the snipe with the matching token address (case-insensitive)
    let mut snipe_opt = None;
    for s in snipes {
        if s.contract_address.to_lowercase() == token_address.to_lowercase() {
            snipe_opt = Some(s);
            break;
        }
    }

    // If no snipe exists, create a new one
    let mut snipe = if let Some(s) = snipe_opt {
        s
    } else {
        // Get the user's config for this blockchain
        let config = user_data.get_config(&blockchain).cloned().unwrap_or_else(|| {
            match blockchain {
                Blockchain::BSC => WalletConfig::bsc_default(),
                Blockchain::SOL => WalletConfig::sol_default(),
                Blockchain::ETH => WalletConfig::eth_default(),
                Blockchain::BASE => WalletConfig::base_default(),
            }
        });

        println!("Creating new snipe for token: {}", token_address);

        // Create a new snipe with default values
        let amount = mongodb::bson::Decimal128::from_str("0.1").unwrap();
        crate::model::Snipe::new(
            user_data.chat_id(),
            blockchain.clone(),
            token_address.to_string(),
            amount,
            config.slippage,
            config.antirug,
            config.anti_mev
        )
    };

    // Parse the value and update the setting
    let result = match setting {
        "amount" => {
            // Use from instead of from_str
            match value.parse::<f64>() {
                Ok(_) => {
                    snipe.amount = Decimal128::from_str(value).unwrap();
                    Ok(())
                },
                Err(_) => Err(BotError::user_error("Invalid amount value. Please enter a number."))
            }
        },
        "slippage" => {
            // Use from instead of from_str
            match value.parse::<f64>() {
                Ok(_) => {
                    snipe.slippage = Decimal128::from_str(value).unwrap();
                    Ok(())
                },
                Err(_) => Err(BotError::user_error("Invalid slippage value. Please enter a number."))
            }
        },
        _ => Err(BotError::user_error(format!("Unknown setting: {}", setting))),
    };

    // If there was an error, return it
    if let Err(e) = result {
        return Err(e);
    }

    // Instead of saving to database, update the user's config (cache)
    println!("CRITICAL: Updating user config for token: {}", token_address);

    // Update the user's config based on blockchain
    match blockchain {
        Blockchain::BSC => {
            // Store the token address in the current contract address
            user_data.config.bsc_current_contract_address = Some(token_address.to_string());

            // Get or create the wallet config
            let mut wallet_config = user_data.config.bsc_config.clone().unwrap_or_else(WalletConfig::bsc_default);

            // Update the setting
            match setting {
                "amount" => {
                    // We don't have an amount field in WalletConfig, so we'll just update the snipe
                    // when the user clicks "Execute Snipe"
                    println!("CRITICAL: Amount will be saved when user executes the snipe");
                },
                "slippage" => wallet_config.slippage = snipe.slippage,
                _ => {}
            }

            // Save the updated wallet config
            user_data.config.bsc_config = Some(wallet_config);
        },
        Blockchain::SOL => {
            // Store the token address in the current contract address
            user_data.config.sol_current_contract_address = Some(token_address.to_string());

            // Get or create the wallet config
            let mut wallet_config = user_data.config.sol_config.clone().unwrap_or_else(WalletConfig::sol_default);

            // Update the setting
            match setting {
                "amount" => {
                    // We don't have an amount field in WalletConfig, so we'll just update the snipe
                    // when the user clicks "Execute Snipe"
                    println!("CRITICAL: Amount will be saved when user executes the snipe");
                },
                "slippage" => wallet_config.slippage = snipe.slippage,
                _ => {}
            }

            // Save the updated wallet config
            user_data.config.sol_config = Some(wallet_config);
        },
        Blockchain::ETH => {
            // Store the token address in the current contract address
            user_data.config.eth_current_contract_address = Some(token_address.to_string());

            // Get or create the wallet config
            let mut wallet_config = user_data.config.eth_config.clone().unwrap_or_else(WalletConfig::eth_default);

            // Update the setting
            match setting {
                "amount" => {
                    // We don't have an amount field in WalletConfig, so we'll just update the snipe
                    // when the user clicks "Execute Snipe"
                    println!("CRITICAL: Amount will be saved when user executes the snipe");
                },
                "slippage" => wallet_config.slippage = snipe.slippage,
                _ => {}
            }

            // Save the updated wallet config
            user_data.config.eth_config = Some(wallet_config);
        },
        Blockchain::BASE => {
            // Store the token address in the current contract address
            user_data.config.base_current_contract_address = Some(token_address.to_string());

            // Get or create the wallet config
            let mut wallet_config = user_data.config.base_config.clone().unwrap_or_else(WalletConfig::base_default);

            // Update the setting
            match setting {
                "amount" => {
                    // We don't have an amount field in WalletConfig, so we'll just update the snipe
                    // when the user clicks "Execute Snipe"
                    println!("CRITICAL: Amount will be saved when user executes the snipe");
                },
                "slippage" => wallet_config.slippage = snipe.slippage,
                _ => {}
            }

            // Save the updated wallet config
            user_data.config.base_config = Some(wallet_config);
        },
    }

    // Save the updated config
    crate::service::DbService::save_user_config(&user_data.config).await?;

    // If this is an existing snipe in the database, update it
    if snipe.id.is_some() {
        println!("CRITICAL: Updating existing snipe with ID: {:?}", snipe.id);
        match crate::service::DbService::save_snipe(&mut snipe).await {
            Ok(id) => println!("CRITICAL: Successfully updated snipe with ID: {}", id),
            Err(e) => println!("CRITICAL ERROR: Failed to update snipe: {}", e)
        }
    }

    // Update the snipe config screen
    show_snipe_config_screen(bot_service, user_data, message_id, blockchain, token_address).await?;

    Ok(())
}
