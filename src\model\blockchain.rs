use serde::{Deserialize, Serialize};
use std::fmt;

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ialEq, Eq, Hash, Serialize, Deserialize)]
pub enum Blockchain {
    #[serde(rename = "bsc")]
    BSC,
    #[serde(rename = "sol")]
    SOL,
    #[serde(rename = "eth")]
    ETH,
    #[serde(rename = "base")]
    BASE,
}

impl Blockchain {
    pub fn as_str(&self) -> &'static str {
        match self {
            Blockchain::BSC => "bsc",
            Blockchain::SOL => "sol",
            Blockchain::ETH => "eth",
            Blockchain::BASE => "base",
        }
    }

    pub fn get_native_symbol(&self) -> &'static str {
        match self {
            Blockchain::BSC => "BNB",
            Blockchain::SOL => "SOL",
            Blockchain::ETH => "ETH",
            Blockchain::BASE => "ETH",
        }
    }
}

impl Default for Blockchain {
    fn default() -> Self {
        Blockchain::BSC
    }
}

impl fmt::Display for Blockchain {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "{}", self.as_str())
    }
}
