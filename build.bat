@echo off
set OPENSSL_NO_VENDOR=1
set OPENSSL_STATIC=0
set OPENSSL_DIR=C:\Program Files\OpenSSL
set OPENSSL_LIB_DIR=C:\Program Files\OpenSSL\bin
set OPENSSL_INCLUDE_DIR=C:\Program Files\OpenSSL\bin
set RUSTLS_NATIVE_CERTS=1

REM Set blockchain RPC endpoints (these will be shared with TypeScript)
set ETH_RPC_URL=https://mainnet.infura.io/v3/********************************
set BSC_RPC_URL=https://bsc-dataseed.binance.org
set SOL_RPC_URL=https://api.mainnet-beta.solana.com
set BASE_RPC_URL=https://base-mainnet.g.alchemy.com/v2/6O-PggabiAzJuhB3Qxw7vrO5eTpEH5rx/


echo Building and running Rust application...
cargo run
