use axum::{
    extract::{State, Query},
    http::{HeaderMap, StatusCode},
    response::IntoResponse,
    routing::{get, post, put},
    Json, Router,
};
use serde::{Deserialize, Serialize};
use std::sync::Arc;
use std::collections::HashMap;
use log::{error, info, debug};
use crate::config::AppConfig;
use crate::service::DbService;
use crate::model::BotError;
use mongodb::bson::{doc, Document};
use futures::TryStreamExt;

#[derive(Clone)]
pub struct AdminSettingsController {
    config: AppConfig,
    jwt_secret: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct AdminSettings {
    pub id: Option<String>,
    pub admin_fee_percentage: f64,
    pub admin_fee_percentage_eth: Option<f64>,
    pub admin_fee_percentage_bsc: Option<f64>,
    pub admin_fee_percentage_base: Option<f64>,
    pub admin_fee_percentage_sol: Option<f64>,
    pub admin_wallet_eth: Option<String>,
    pub admin_wallet_bsc: Option<String>,
    pub admin_wallet_base: Option<String>,
    pub admin_wallet_sol: Option<String>,
    pub rpc_url_eth: Option<String>,
    pub rpc_url_bsc: Option<String>,
    pub rpc_url_base: Option<String>,
    pub rpc_url_sol: Option<String>,
    pub max_users_per_admin: i32,
    pub maintenance_mode: bool,
    pub auto_fee_collection: bool,
    pub fee_collection_threshold: f64,
    pub updated_at: u64,
    pub updated_by: String,
    pub custom_settings: HashMap<String, serde_json::Value>,
}

#[derive(Debug, Deserialize)]
pub struct UpdateSettingsRequest {
    pub admin_fee_percentage: Option<f64>,
    pub admin_fee_percentage_eth: Option<f64>,
    pub admin_fee_percentage_bsc: Option<f64>,
    pub admin_fee_percentage_base: Option<f64>,
    pub admin_fee_percentage_sol: Option<f64>,
    pub admin_wallet_eth: Option<String>,
    pub admin_wallet_bsc: Option<String>,
    pub admin_wallet_base: Option<String>,
    pub admin_wallet_sol: Option<String>,
    pub rpc_url_eth: Option<String>,
    pub rpc_url_bsc: Option<String>,
    pub rpc_url_base: Option<String>,
    pub rpc_url_sol: Option<String>,
    pub max_users_per_admin: Option<i32>,
    pub maintenance_mode: Option<bool>,
    pub auto_fee_collection: Option<bool>,
    pub fee_collection_threshold: Option<f64>,
    pub custom_settings: Option<HashMap<String, serde_json::Value>>,
}

impl AdminSettingsController {
    pub fn new(config: AppConfig, jwt_secret: String) -> Self {
        Self { config, jwt_secret }
    }

    pub fn create_router(&self) -> Router {
        let controller = Arc::new(self.clone());
        
        Router::new()
            .route("/admin/settings", get(Self::get_settings))
            .route("/admin/settings", put(Self::update_settings))
            .route("/admin/settings/reset", post(Self::reset_settings))
            .route("/admin/settings/backup", post(Self::backup_settings))
            .route("/admin/settings/restore", post(Self::restore_settings))
            .with_state(controller)
    }

    async fn get_settings(
        State(controller): State<Arc<AdminSettingsController>>,
        headers: HeaderMap,
    ) -> impl IntoResponse {
        if let Some(_claims) = controller.verify_auth_header(&headers).await {
            match controller.fetch_settings().await {
                Ok(settings) => (StatusCode::OK, Json(settings)).into_response(),
                Err(e) => {
                    error!("Failed to fetch settings: {:?}", e);
                    (StatusCode::INTERNAL_SERVER_ERROR, Json(serde_json::json!({
                        "error": "Failed to fetch settings",
                        "code": "SETTINGS_FETCH_ERROR"
                    }))).into_response()
                }
            }
        } else {
            (StatusCode::UNAUTHORIZED, Json(serde_json::json!({
                "error": "Unauthorized"
            }))).into_response()
        }
    }

    async fn update_settings(
        State(controller): State<Arc<AdminSettingsController>>,
        headers: HeaderMap,
        Json(request): Json<UpdateSettingsRequest>,
    ) -> impl IntoResponse {
        if let Some(claims) = controller.verify_auth_header(&headers).await {
            match controller.update_settings_impl(request, &claims.sub).await {
                Ok(settings) => (StatusCode::OK, Json(settings)).into_response(),
                Err(e) => {
                    error!("Failed to update settings: {:?}", e);
                    (StatusCode::INTERNAL_SERVER_ERROR, Json(serde_json::json!({
                        "error": "Failed to update settings",
                        "code": "SETTINGS_UPDATE_ERROR"
                    }))).into_response()
                }
            }
        } else {
            (StatusCode::UNAUTHORIZED, Json(serde_json::json!({
                "error": "Unauthorized"
            }))).into_response()
        }
    }

    async fn reset_settings(
        State(controller): State<Arc<AdminSettingsController>>,
        headers: HeaderMap,
    ) -> impl IntoResponse {
        if let Some(claims) = controller.verify_auth_header(&headers).await {
            match controller.reset_to_defaults(&claims.sub).await {
                Ok(settings) => (StatusCode::OK, Json(settings)).into_response(),
                Err(e) => {
                    error!("Failed to reset settings: {:?}", e);
                    (StatusCode::INTERNAL_SERVER_ERROR, Json(serde_json::json!({
                        "error": "Failed to reset settings",
                        "code": "SETTINGS_RESET_ERROR"
                    }))).into_response()
                }
            }
        } else {
            (StatusCode::UNAUTHORIZED, Json(serde_json::json!({
                "error": "Unauthorized"
            }))).into_response()
        }
    }

    async fn backup_settings(
        State(controller): State<Arc<AdminSettingsController>>,
        headers: HeaderMap,
    ) -> impl IntoResponse {
        if let Some(_claims) = controller.verify_auth_header(&headers).await {
            match controller.create_settings_backup().await {
                Ok(backup_id) => (StatusCode::OK, Json(serde_json::json!({
                    "backup_id": backup_id,
                    "message": "Settings backup created successfully"
                }))).into_response(),
                Err(e) => {
                    error!("Failed to backup settings: {:?}", e);
                    (StatusCode::INTERNAL_SERVER_ERROR, Json(serde_json::json!({
                        "error": "Failed to backup settings",
                        "code": "SETTINGS_BACKUP_ERROR"
                    }))).into_response()
                }
            }
        } else {
            (StatusCode::UNAUTHORIZED, Json(serde_json::json!({
                "error": "Unauthorized"
            }))).into_response()
        }
    }

    async fn restore_settings(
        State(controller): State<Arc<AdminSettingsController>>,
        headers: HeaderMap,
        Json(request): Json<serde_json::Value>,
    ) -> impl IntoResponse {
        if let Some(claims) = controller.verify_auth_header(&headers).await {
            let backup_id = request.get("backup_id").and_then(|v| v.as_str()).unwrap_or("");
            match controller.restore_settings_from_backup(backup_id, &claims.sub).await {
                Ok(settings) => (StatusCode::OK, Json(settings)).into_response(),
                Err(e) => {
                    error!("Failed to restore settings: {:?}", e);
                    (StatusCode::INTERNAL_SERVER_ERROR, Json(serde_json::json!({
                        "error": "Failed to restore settings",
                        "code": "SETTINGS_RESTORE_ERROR"
                    }))).into_response()
                }
            }
        } else {
            (StatusCode::UNAUTHORIZED, Json(serde_json::json!({
                "error": "Unauthorized"
            }))).into_response()
        }
    }

    async fn fetch_settings(&self) -> Result<AdminSettings, BotError> {
        let db = DbService::get_db();
        let collection = db.collection::<Document>("admin_settings");

        // Try to find existing settings
        if let Some(doc) = collection.find_one(doc! {}, None).await.map_err(|e| BotError::database_error(e.to_string()))? {
            let settings = AdminSettings {
                id: doc.get_object_id("_id").ok().map(|id| id.to_hex()),
                admin_fee_percentage: doc.get_f64("admin_fee_percentage").unwrap_or(1.0),
                admin_fee_percentage_eth: doc.get_f64("admin_fee_percentage_eth").ok(),
                admin_fee_percentage_bsc: doc.get_f64("admin_fee_percentage_bsc").ok(),
                admin_fee_percentage_base: doc.get_f64("admin_fee_percentage_base").ok(),
                admin_fee_percentage_sol: doc.get_f64("admin_fee_percentage_sol").ok(),
                admin_wallet_eth: doc.get_str("admin_wallet_eth").ok().map(|s| s.to_string()),
                admin_wallet_bsc: doc.get_str("admin_wallet_bsc").ok().map(|s| s.to_string()),
                admin_wallet_base: doc.get_str("admin_wallet_base").ok().map(|s| s.to_string()),
                admin_wallet_sol: doc.get_str("admin_wallet_sol").ok().map(|s| s.to_string()),
                rpc_url_eth: doc.get_str("rpc_url_eth").ok().map(|s| s.to_string()),
                rpc_url_bsc: doc.get_str("rpc_url_bsc").ok().map(|s| s.to_string()),
                rpc_url_base: doc.get_str("rpc_url_base").ok().map(|s| s.to_string()),
                rpc_url_sol: doc.get_str("rpc_url_sol").ok().map(|s| s.to_string()),
                max_users_per_admin: doc.get_i32("max_users_per_admin").unwrap_or(1000),
                maintenance_mode: doc.get_bool("maintenance_mode").unwrap_or(false),
                auto_fee_collection: doc.get_bool("auto_fee_collection").unwrap_or(true),
                fee_collection_threshold: doc.get_f64("fee_collection_threshold").unwrap_or(10.0),
                updated_at: doc.get_i64("updated_at").unwrap_or(0) as u64,
                updated_by: doc.get_str("updated_by").unwrap_or("system").to_string(),
                custom_settings: doc.get_document("custom_settings")
                    .ok()
                    .and_then(|d| serde_json::from_slice(&mongodb::bson::to_vec(d).ok()?).ok())
                    .unwrap_or_default(),
            };
            Ok(settings)
        } else {
            // Create default settings
            self.create_default_settings().await
        }
    }

    async fn create_default_settings(&self) -> Result<AdminSettings, BotError> {
        let db = DbService::get_db();
        let collection = db.collection::<Document>("admin_settings");

        let now = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap()
            .as_secs();

        let default_settings = doc! {
            "admin_fee_percentage": 1.0,
            "max_users_per_admin": 1000,
            "maintenance_mode": false,
            "auto_fee_collection": true,
            "fee_collection_threshold": 10.0,
            "updated_at": now as i64,
            "updated_by": "system",
            "custom_settings": {}
        };

        collection.insert_one(default_settings.clone(), None).await
            .map_err(|e| BotError::database_error(e.to_string()))?;

        Ok(AdminSettings {
            id: None,
            admin_fee_percentage: 1.0,
            admin_fee_percentage_eth: None,
            admin_fee_percentage_bsc: None,
            admin_fee_percentage_base: None,
            admin_fee_percentage_sol: None,
            admin_wallet_eth: None,
            admin_wallet_bsc: None,
            admin_wallet_base: None,
            admin_wallet_sol: None,
            rpc_url_eth: None,
            rpc_url_bsc: None,
            rpc_url_base: None,
            rpc_url_sol: None,
            max_users_per_admin: 1000,
            maintenance_mode: false,
            auto_fee_collection: true,
            fee_collection_threshold: 10.0,
            updated_at: now,
            updated_by: "system".to_string(),
            custom_settings: HashMap::new(),
        })
    }

    async fn update_settings_impl(&self, request: UpdateSettingsRequest, updated_by: &str) -> Result<AdminSettings, BotError> {
        let db = DbService::get_db();
        let collection = db.collection::<Document>("admin_settings");

        let now = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap()
            .as_secs();

        let mut update_doc = doc! {
            "updated_at": now as i64,
            "updated_by": updated_by
        };

        // Update only provided fields
        if let Some(fee) = request.admin_fee_percentage {
            update_doc.insert("admin_fee_percentage", fee);
        }
        if let Some(fee) = request.admin_fee_percentage_eth {
            update_doc.insert("admin_fee_percentage_eth", fee);
        }
        if let Some(fee) = request.admin_fee_percentage_bsc {
            update_doc.insert("admin_fee_percentage_bsc", fee);
        }
        if let Some(fee) = request.admin_fee_percentage_base {
            update_doc.insert("admin_fee_percentage_base", fee);
        }
        if let Some(fee) = request.admin_fee_percentage_sol {
            update_doc.insert("admin_fee_percentage_sol", fee);
        }
        if let Some(wallet) = request.admin_wallet_eth {
            update_doc.insert("admin_wallet_eth", wallet);
        }
        if let Some(wallet) = request.admin_wallet_bsc {
            update_doc.insert("admin_wallet_bsc", wallet);
        }
        if let Some(wallet) = request.admin_wallet_base {
            update_doc.insert("admin_wallet_base", wallet);
        }
        if let Some(wallet) = request.admin_wallet_sol {
            update_doc.insert("admin_wallet_sol", wallet);
        }
        if let Some(rpc_url) = request.rpc_url_eth {
            update_doc.insert("rpc_url_eth", rpc_url);
        }
        if let Some(rpc_url) = request.rpc_url_bsc {
            update_doc.insert("rpc_url_bsc", rpc_url);
        }
        if let Some(rpc_url) = request.rpc_url_base {
            update_doc.insert("rpc_url_base", rpc_url);
        }
        if let Some(rpc_url) = request.rpc_url_sol {
            update_doc.insert("rpc_url_sol", rpc_url);
        }
        if let Some(max_users) = request.max_users_per_admin {
            update_doc.insert("max_users_per_admin", max_users);
        }
        if let Some(maintenance) = request.maintenance_mode {
            update_doc.insert("maintenance_mode", maintenance);
        }
        if let Some(auto_collection) = request.auto_fee_collection {
            update_doc.insert("auto_fee_collection", auto_collection);
        }
        if let Some(threshold) = request.fee_collection_threshold {
            update_doc.insert("fee_collection_threshold", threshold);
        }
        if let Some(custom) = request.custom_settings {
            if let Ok(custom_doc) = mongodb::bson::to_document(&custom) {
                update_doc.insert("custom_settings", custom_doc);
            }
        }

        collection.update_one(
            doc! {},
            doc! { "$set": update_doc },
            None
        ).await.map_err(|e| BotError::database_error(e.to_string()))?;

        // Return updated settings
        self.fetch_settings().await
    }

    async fn reset_to_defaults(&self, updated_by: &str) -> Result<AdminSettings, BotError> {
        let db = DbService::get_db();
        let collection = db.collection::<Document>("admin_settings");

        // Delete existing settings
        collection.delete_many(doc! {}, None).await
            .map_err(|e| BotError::database_error(e.to_string()))?;

        // Create new default settings
        self.create_default_settings().await
    }

    async fn create_settings_backup(&self) -> Result<String, BotError> {
        let settings = self.fetch_settings().await?;
        let backup_id = format!("backup_{}", std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap()
            .as_secs());

        let db = DbService::get_db();
        let collection = db.collection::<Document>("admin_settings_backups");

        let backup_doc = doc! {
            "backup_id": &backup_id,
            "settings": mongodb::bson::to_document(&settings)
                .map_err(|e| BotError::general_error(e.to_string()))?,
            "created_at": std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap()
                .as_secs() as i64
        };

        collection.insert_one(backup_doc, None).await
            .map_err(|e| BotError::database_error(e.to_string()))?;

        Ok(backup_id)
    }

    async fn restore_settings_from_backup(&self, backup_id: &str, updated_by: &str) -> Result<AdminSettings, BotError> {
        let db = DbService::get_db();
        let backups_collection = db.collection::<Document>("admin_settings_backups");

        // Find the backup
        let backup = backups_collection.find_one(doc! { "backup_id": backup_id }, None).await
            .map_err(|e| BotError::database_error(e.to_string()))?
            .ok_or_else(|| BotError::general_error("Backup not found".to_string()))?;

        let settings_doc = backup.get_document("settings")
            .map_err(|_| BotError::general_error("Invalid backup format".to_string()))?;

        // Update current settings with backup data
        let settings_collection = db.collection::<Document>("admin_settings");
        
        let mut restore_doc = settings_doc.clone();
        restore_doc.insert("updated_at", std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap()
            .as_secs() as i64);
        restore_doc.insert("updated_by", format!("{} (restored from {})", updated_by, backup_id));

        settings_collection.replace_one(
            doc! {},
            restore_doc,
            None
        ).await.map_err(|e| BotError::database_error(e.to_string()))?;

        self.fetch_settings().await
    }

    async fn verify_auth_header(&self, headers: &HeaderMap) -> Option<crate::model::Claims> {
        // This should be implemented to verify JWT tokens
        // For now, return a dummy claims object
        Some(crate::model::Claims {
            sub: "admin".to_string(),
            exp: 0,
            role: "SuperAdmin".to_string(),
        })
    }
}
