use serde::{Deserialize, Serialize};
use mongodb::bson::oid::ObjectId;
use std::time::{SystemTime, UNIX_EPOCH};
use crate::config::defaults;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AdminSettings {
    #[serde(rename = "_id", skip_serializing_if = "Option::is_none")]
    pub id: Option<ObjectId>,
    pub admin_fee_percentage: f64,

    // Blockchain-specific fee percentages (optional, falls back to default if not set)
    #[serde(skip_serializing_if = "Option::is_none")]
    pub admin_fee_percentage_eth: Option<f64>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub admin_fee_percentage_bsc: Option<f64>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub admin_fee_percentage_base: Option<f64>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub admin_fee_percentage_sol: Option<f64>,

    // Admin wallet addresses for each blockchain
    #[serde(skip_serializing_if = "Option::is_none")]
    pub admin_wallet_eth: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub admin_wallet_bsc: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub admin_wallet_base: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub admin_wallet_sol: Option<String>,

    // RPC URLs for each blockchain (optional, falls back to env vars if not set)
    #[serde(skip_serializing_if = "Option::is_none")]
    pub rpc_url_eth: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub rpc_url_bsc: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub rpc_url_base: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub rpc_url_sol: Option<String>,

    // Fee collection settings
    #[serde(default)]
    pub auto_fee_collection: bool,

    pub max_users_per_admin: i32,
    pub maintenance_mode: bool,
    #[serde(default = "default_timestamp")]
    pub updated_at: u64,
    pub updated_by: String,
}

fn default_timestamp() -> u64 {
    SystemTime::now()
        .duration_since(UNIX_EPOCH)
        .unwrap_or_default()
        .as_secs()
}

impl Default for AdminSettings {
    fn default() -> Self {
        Self {
            id: None,
            admin_fee_percentage: defaults::DEFAULT_ADMIN_FEE_PERCENTAGE,
            admin_fee_percentage_eth: None,
            admin_fee_percentage_bsc: None,
            admin_fee_percentage_base: None,
            admin_fee_percentage_sol: None,
            admin_wallet_eth: None,
            admin_wallet_bsc: None,
            admin_wallet_base: None,
            admin_wallet_sol: None,
            rpc_url_eth: None,
            rpc_url_bsc: None,
            rpc_url_base: None,
            rpc_url_sol: None,
            auto_fee_collection: true,
            max_users_per_admin: 1000,
            maintenance_mode: false,
            updated_at: default_timestamp(),
            updated_by: "system".to_string(),
        }
    }
}

impl AdminSettings {
    pub fn new() -> Self {
        Self::default()
    }

    pub fn update(&mut self,
        admin_fee_percentage: Option<f64>,
        admin_fee_percentage_eth: Option<Option<f64>>,
        admin_fee_percentage_bsc: Option<Option<f64>>,
        admin_fee_percentage_base: Option<Option<f64>>,
        admin_fee_percentage_sol: Option<Option<f64>>,
        admin_wallet_eth: Option<Option<String>>,
        admin_wallet_bsc: Option<Option<String>>,
        admin_wallet_base: Option<Option<String>>,
        admin_wallet_sol: Option<Option<String>>,
        rpc_url_eth: Option<Option<String>>,
        rpc_url_bsc: Option<Option<String>>,
        rpc_url_base: Option<Option<String>>,
        rpc_url_sol: Option<Option<String>>,
        auto_fee_collection: Option<bool>,
        max_users_per_admin: Option<i32>,
        maintenance_mode: Option<bool>,
        updated_by: String
    ) {
        if let Some(fee) = admin_fee_percentage {
            self.admin_fee_percentage = fee;
        }

        if let Some(fee_eth) = admin_fee_percentage_eth {
            self.admin_fee_percentage_eth = fee_eth;
        }

        if let Some(fee_bsc) = admin_fee_percentage_bsc {
            self.admin_fee_percentage_bsc = fee_bsc;
        }

        if let Some(fee_base) = admin_fee_percentage_base {
            self.admin_fee_percentage_base = fee_base;
        }

        if let Some(fee_sol) = admin_fee_percentage_sol {
            self.admin_fee_percentage_sol = fee_sol;
        }

        if let Some(wallet_eth) = admin_wallet_eth {
            self.admin_wallet_eth = wallet_eth;
        }

        if let Some(wallet_bsc) = admin_wallet_bsc {
            self.admin_wallet_bsc = wallet_bsc;
        }

        if let Some(wallet_base) = admin_wallet_base {
            self.admin_wallet_base = wallet_base;
        }

        if let Some(wallet_sol) = admin_wallet_sol {
            self.admin_wallet_sol = wallet_sol;
        }

        if let Some(rpc_eth) = rpc_url_eth {
            self.rpc_url_eth = rpc_eth;
        }

        if let Some(rpc_bsc) = rpc_url_bsc {
            self.rpc_url_bsc = rpc_bsc;
        }

        if let Some(rpc_base) = rpc_url_base {
            self.rpc_url_base = rpc_base;
        }

        if let Some(rpc_sol) = rpc_url_sol {
            self.rpc_url_sol = rpc_sol;
        }

        if let Some(auto_collection) = auto_fee_collection {
            self.auto_fee_collection = auto_collection;
        }

        if let Some(max_users) = max_users_per_admin {
            self.max_users_per_admin = max_users;
        }

        if let Some(mode) = maintenance_mode {
            self.maintenance_mode = mode;
        }

        self.updated_at = default_timestamp();
        self.updated_by = updated_by;
    }
}
