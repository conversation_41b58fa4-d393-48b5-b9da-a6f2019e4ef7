// Admin types

export interface AdminSettings {
  id?: string;
  admin_fee_percentage: number;
  max_users_per_admin: number;
  maintenance_mode: boolean;
  max_bots_per_user?: number;
  max_transactions_per_day?: number;
  min_transaction_amount?: number;
  max_transaction_amount?: number;
  bot_execution_interval?: number;
  updated_at: number;
  updated_by: string;
}

export interface UpdateSettingsRequest {
  admin_fee_percentage?: number;
  max_users_per_admin?: number;
  maintenance_mode?: boolean;
  max_bots_per_user?: number;
  max_transactions_per_day?: number;
  min_transaction_amount?: number;
  max_transaction_amount?: number;
  bot_execution_interval?: number;
}
