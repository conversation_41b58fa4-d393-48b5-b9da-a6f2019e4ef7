use crate::model::blockchain::Blockchain;
use crate::service::{BalanceValidationService, BalanceValidationResult};
use crate::model::error::BotError;
use std::sync::Arc;

/// Helper functions for balance checking in bot handlers
pub struct BalanceChecker {
    validation_service: Arc<BalanceValidationService>,
}

impl BalanceChecker {
    pub fn new(validation_service: Arc<BalanceValidationService>) -> Self {
        Self {
            validation_service,
        }
    }

    /// Check balance before executing a buy order
    /// Returns Ok(()) if balance is sufficient, Err with user-friendly message if not
    pub async fn check_buy_balance(
        &self,
        wallet_address: &str,
        amount_wei: &str,
        blockchain: &Blockchain,
    ) -> Result<(), String> {
        match self.validation_service.validate_buy_balance(
            wallet_address,
            amount_wei,
            blockchain,
        ).await {
            Ok(result) => {
                if result.has_sufficient_balance {
                    Ok(())
                } else {
                    let message = self.validation_service.generate_insufficient_balance_message(
                        &result,
                        "buy",
                    );
                    Err(message)
                }
            }
            Err(e) => {
                Err(format!("❌ Failed to check balance: {}", e))
            }
        }
    }

    /// Check balance before executing a sell order
    /// Returns Ok(()) if balance is sufficient, Err with user-friendly message if not
    pub async fn check_sell_balance(
        &self,
        wallet_address: &str,
        token_address: &str,
        amount: &str,
        blockchain: &Blockchain,
    ) -> Result<(), String> {
        match self.validation_service.validate_sell_balance(
            wallet_address,
            token_address,
            amount,
            blockchain,
        ).await {
            Ok(result) => {
                if result.has_sufficient_balance {
                    Ok(())
                } else {
                    let message = self.validation_service.generate_insufficient_balance_message(
                        &result,
                        "sell",
                    );
                    Err(message)
                }
            }
            Err(e) => {
                Err(format!("❌ Failed to check token balance: {}", e))
            }
        }
    }

    /// Check Solana balance before buy (requires keypair)
    pub async fn check_solana_buy_balance(
        &self,
        wallet: &solana_sdk::signature::Keypair,
        amount_lamports: u64,
    ) -> Result<(), String> {
        match self.validation_service.validate_solana_buy_balance(
            wallet,
            amount_lamports,
        ).await {
            Ok(result) => {
                if result.has_sufficient_balance {
                    Ok(())
                } else {
                    let message = self.validation_service.generate_insufficient_balance_message(
                        &result,
                        "buy",
                    );
                    Err(message)
                }
            }
            Err(e) => {
                Err(format!("❌ Failed to check SOL balance: {}", e))
            }
        }
    }

    /// Check Solana token balance before sell (requires keypair)
    pub async fn check_solana_sell_balance(
        &self,
        wallet: &solana_sdk::signature::Keypair,
        token_mint: &solana_sdk::pubkey::Pubkey,
        sell_amount: u64,
        decimals: u8,
    ) -> Result<(), String> {
        match self.validation_service.validate_solana_sell_balance(
            wallet,
            token_mint,
            sell_amount,
            decimals,
        ).await {
            Ok(result) => {
                if result.has_sufficient_balance {
                    Ok(())
                } else {
                    let message = self.validation_service.generate_insufficient_balance_message(
                        &result,
                        "sell",
                    );
                    Err(message)
                }
            }
            Err(e) => {
                Err(format!("❌ Failed to check token balance: {}", e))
            }
        }
    }

    /// Generate a comprehensive balance check message for debugging
    pub async fn get_balance_info(
        &self,
        wallet_address: &str,
        blockchain: &Blockchain,
    ) -> String {
        let currency = match blockchain {
            Blockchain::ETH => "ETH",
            Blockchain::BSC => "BNB",
            Blockchain::BASE => "ETH",
            Blockchain::SOL => "SOL",
        };

        format!(
            "🔍 **Balance Check Info**\n\
             • Wallet: `{}`\n\
             • Network: {:?}\n\
             • Currency: {}\n\n\
             💡 **Requirements for Trading:**\n\
             • Sufficient {} for trade amount\n\
             • Admin fee (0.5% default)\n\
             • Gas fees for transactions\n\
             • Network buffer for slippage\n\n\
             ⚠️ **Note:** Always keep some {} for gas fees!",
            &wallet_address[..8],
            blockchain,
            currency,
            currency,
            currency
        )
    }
}
