console.log('Checking environment variables...');
console.log('ETH_RPC_URL:', process.env.ETH_RPC_URL || 'Not set');
console.log('BSC_RPC_URL:', process.env.BSC_RPC_URL || 'Not set');
console.log('SOL_RPC_URL:', process.env.SOL_RPC_URL || 'Not set');
console.log('BASE_RPC_URL:', process.env.BASE_RPC_URL || 'Not set');
console.log('NODE_PATH:', process.env.NODE_PATH || 'Not set');

try {
  require('ethers');
  console.log('ethers module is installed');
} catch (e) {
  console.error('ethers module is not installed:', e.message);
}

try {
  require('@solana/web3.js');
  console.log('@solana/web3.js module is installed');
} catch (e) {
  console.error('@solana/web3.js module is not installed:', e.message);
}

try {
  require('@solana/spl-token');
  console.log('@solana/spl-token module is installed');
} catch (e) {
  console.error('@solana/spl-token module is not installed:', e.message);
}

console.log('Environment check completed');
