use serde::{Deserialize, Serialize};
use mongodb::bson::oid::ObjectId;
use crate::model::blockchain::Blockchain;
use std::time::{SystemTime, UNIX_EPOCH};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct User {
    #[serde(rename = "_id", skip_serializing_if = "Option::is_none")]
    pub id: Option<ObjectId>,
    pub chat_id: i64,
    pub first_name: String,
    pub last_name: Option<String>,
    pub username: Option<String>,
    #[serde(default)]
    pub current_blockchain: Blockchain,
    #[serde(default = "default_timestamp")]
    pub created_at: u64,
    #[serde(default = "default_timestamp")]
    pub last_seen: u64,
}

fn default_timestamp() -> u64 {
    SystemTime::now()
        .duration_since(UNIX_EPOCH)
        .unwrap_or_default()
        .as_secs()
}

impl User {
    pub fn from_telegram_user(tg_user: &teloxide::types::User) -> Self {
        let user_id = tg_user.id.0 as i64;
        let now = default_timestamp();

        Self {
            id: None,
            chat_id: user_id,
            username: tg_user.username.clone(),
            first_name: tg_user.first_name.clone(),
            last_name: tg_user.last_name.clone(),
            current_blockchain: Blockchain::BSC,
            created_at: now,
            last_seen: now,
        }
    }

    pub fn display_name(&self) -> String {
        if let Some(username) = &self.username {
            format!("@{}", username)
        } else {
            let mut name = self.first_name.clone();
            if let Some(last_name) = &self.last_name {
                name.push_str(&format!(" {}", last_name));
            }
            name
        }
    }

    pub fn update_last_seen(&mut self) {
        self.last_seen = default_timestamp();
    }
}
