import { Link } from 'react-router-dom';
import { ServerIcon } from '@heroicons/react/24/outline';

const navigation = {
  solutions: [
    { name: 'Web Hosting', href: '#' },
    { name: 'Domains', href: '#' },
    { name: 'Containers', href: '#' },
    { name: 'SSL Certificates', href: '#' },
    { name: 'Analytics', href: '#' },
  ],
  support: [
    { name: 'Documentation', href: '#' },
    { name: 'Guides', href: '#' },
    { name: 'API Status', href: '#' },
    { name: 'Pricing', href: '#' },
  ],
  company: [
    { name: 'About', href: '#' },
    { name: 'Blog', href: '#' },
    { name: 'Jobs', href: '#' },
    { name: 'Press', href: '#' },
    { name: 'Partners', href: '#' },
  ],
  legal: [
    { name: 'Privacy', href: '#' },
    { name: 'Terms', href: '#' },
    { name: '<PERSON>ie Policy', href: '#' },
  ],
  social: [
    {
      name: 'Twitter',
      href: '#',
      icon: (props: React.SVGProps<SVGSVGElement>) => (
        <svg fill="currentColor" viewBox="0 0 24 24" {...props}>
          <path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84" />
        </svg>
      ),
    },
    {
      name: 'GitHub',
      href: '#',
      icon: (props: React.SVGProps<SVGSVGElement>) => (
        <svg fill="currentColor" viewBox="0 0 24 24" {...props}>
          <path
            fillRule="evenodd"
            d="M12 2C6.477 2 2 6.484 2 12.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0112 6.844c.85.004 1.705.115 2.504.337 1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.202 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.943.359.309.678.92.678 1.855 0 1.338-.012 2.419-.012 2.747 0 .268.18.58.688.482A10.019 10.019 0 0022 12.017C22 6.484 17.522 2 12 2z"
            clipRule="evenodd"
          />
        </svg>
      ),
    },
    {
      name: 'LinkedIn',
      href: '#',
      icon: (props: React.SVGProps<SVGSVGElement>) => (
        <svg fill="currentColor" viewBox="0 0 24 24" {...props}>
          <path
            fillRule="evenodd"
            d="M4.98 3.5c0 1.381-1.11 2.5-2.48 2.5s-2.48-1.119-2.48-2.5c0-1.38 1.11-2.5 2.48-2.5s2.48 1.12 2.48 2.5zm.02 4.5h-5v16h5v-16zm7.982 0h-4.968v16h4.969v-8.399c0-4.67 6.029-5.052 6.029 0v8.399h4.988v-10.131c0-7.88-8.922-7.593-11.018-3.714v-2.155z"
            clipRule="evenodd"
          />
        </svg>
      ),
    },
  ],
};

export default function Footer() {
  return (
    <footer className="relative mt-24 overflow-hidden" aria-labelledby="footer-heading">
      {/* Decorative elements */}
      <div className="absolute inset-0 -z-10">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-secondary-600/5 rounded-full blur-3xl"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-primary-600/5 rounded-full blur-3xl"></div>
      </div>

      {/* Glass card effect for footer */}
      <div className="relative mx-auto max-w-7xl px-6 pb-8 pt-16 sm:pt-24 lg:px-8 lg:pt-32">
        <div className="glass-card rounded-t-3xl p-8 backdrop-blur-lg">
          <h2 id="footer-heading" className="sr-only">
            Footer
          </h2>

          <div className="xl:grid xl:grid-cols-3 xl:gap-8">
            <div className="space-y-8 animate-fade-in" style={{ animationDelay: '100ms' }}>
              <Link to="/" className="flex items-center transition-transform duration-300 hover:scale-105">
                <ServerIcon className="h-8 w-auto text-secondary-500" />
                <span className="ml-2 text-xl font-bold text-white">PoolotHost</span>
              </Link>
              <p className="text-sm leading-6 text-gray-300">
                Making cloud hosting simple, fast, and affordable for developers, agencies, and businesses.
              </p>
              <div className="flex space-x-6">
                {navigation.social.map((item, index) => (
                  <a
                    key={item.name}
                    href={item.href}
                    className="text-gray-400 hover:text-secondary-400 transition-colors duration-300"
                    style={{ animationDelay: `${index * 100}ms` }}
                  >
                    <span className="sr-only">{item.name}</span>
                    <item.icon className="h-6 w-6 animate-fade-in" aria-hidden="true" />
                  </a>
                ))}
              </div>
            </div>
            <div className="mt-16 grid grid-cols-2 gap-8 xl:col-span-2 xl:mt-0">
              <div className="md:grid md:grid-cols-2 md:gap-8">
                <div className="animate-fade-in animate-slide-up" style={{ animationDelay: '200ms' }}>
                  <h3 className="text-sm font-semibold leading-6 text-white">Solutions</h3>
                  <ul role="list" className="mt-6 space-y-4">
                    {navigation.solutions.map((item, index) => (
                      <li key={item.name} style={{ animationDelay: `${300 + index * 50}ms` }} className="animate-fade-in">
                        <a
                          href={item.href}
                          className="text-sm leading-6 text-gray-300 hover:text-white transition-colors duration-200"
                        >
                          {item.name}
                        </a>
                      </li>
                    ))}
                  </ul>
                </div>
                <div className="mt-10 md:mt-0 animate-fade-in animate-slide-up" style={{ animationDelay: '300ms' }}>
                  <h3 className="text-sm font-semibold leading-6 text-white">Support</h3>
                  <ul role="list" className="mt-6 space-y-4">
                    {navigation.support.map((item, index) => (
                      <li key={item.name} style={{ animationDelay: `${400 + index * 50}ms` }} className="animate-fade-in">
                        <a
                          href={item.href}
                          className="text-sm leading-6 text-gray-300 hover:text-white transition-colors duration-200"
                        >
                          {item.name}
                        </a>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
              <div className="md:grid md:grid-cols-2 md:gap-8">
                <div className="animate-fade-in animate-slide-up" style={{ animationDelay: '400ms' }}>
                  <h3 className="text-sm font-semibold leading-6 text-white">Company</h3>
                  <ul role="list" className="mt-6 space-y-4">
                    {navigation.company.map((item, index) => (
                      <li key={item.name} style={{ animationDelay: `${500 + index * 50}ms` }} className="animate-fade-in">
                        <a
                          href={item.href}
                          className="text-sm leading-6 text-gray-300 hover:text-white transition-colors duration-200"
                        >
                          {item.name}
                        </a>
                      </li>
                    ))}
                  </ul>
                </div>
                <div className="mt-10 md:mt-0 animate-fade-in animate-slide-up" style={{ animationDelay: '500ms' }}>
                  <h3 className="text-sm font-semibold leading-6 text-white">Legal</h3>
                  <ul role="list" className="mt-6 space-y-4">
                    {navigation.legal.map((item, index) => (
                      <li key={item.name} style={{ animationDelay: `${600 + index * 50}ms` }} className="animate-fade-in">
                        <a
                          href={item.href}
                          className="text-sm leading-6 text-gray-300 hover:text-white transition-colors duration-200"
                        >
                          {item.name}
                        </a>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>
          </div>
          <div className="mt-16 border-t border-white/10 pt-8 sm:mt-20 lg:mt-24">
            <p className="text-xs leading-5 text-gray-400 text-center">
              &copy; {new Date().getFullYear()} PoolotHost, Inc. All rights reserved.
            </p>
          </div>
        </div>
      </div>
    </footer>
  );
}
