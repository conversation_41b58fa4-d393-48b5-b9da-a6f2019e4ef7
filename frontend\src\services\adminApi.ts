import axios from 'axios';
import { requestDeduplicator } from '../utils/requestDeduplication';

// Create axios instance with base configuration
const api = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('admin_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle auth errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    // Only redirect on 401 if it's not a login request
    if (error.response?.status === 401 && !error.config?.url?.includes('/auth/login')) {
      localStorage.removeItem('admin_token');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// Types for API responses
export interface AdminUser {
  id: string;
  username: string;
  email: string;
  role: 'SuperAdmin' | 'Admin';
  created_at: number;
  last_login?: number;
  is_active: boolean;
  status: 'active' | 'inactive' | 'suspended';
}

export interface LoginResponse {
  token: string;
  user: AdminUser;
  expires_in: number;
}

export interface Bot {
  id: string;
  name: string;
  description: string;
  bot_type: 'BSC' | 'Ethereum' | 'Solana' | 'Base';
  status: 'Active' | 'Inactive' | 'Paused' | 'Error' | 'Maintenance';
  config: BotConfig;
  total_users: number;
  active_users: number;
  total_transactions: number;
  total_volume: number;
  success_rate: number;
  average_response_time: number;
  uptime_percentage: number;
  last_error?: string;
  version: string;
  created_at: number;
  updated_at: number;
  last_restart?: number;
}

export interface BotConfig {
  max_slippage: number;
  gas_price?: number;
  priority_fee?: number;
  auto_approve: boolean;
  max_transaction_amount: number;
  min_liquidity: number;
  honeypot_check: boolean;
  rugpull_check: boolean;
  custom_settings: Record<string, string>;
}

export interface BotListResponse {
  bots: Bot[];
  total: number;
  page: number;
  per_page: number;
}

export interface Transaction {
  id: string;
  user_id: string;
  username?: string;
  first_name?: string;
  last_name?: string;
  bot_type: string;
  transaction_type: string;
  direction: string; // "buy" or "sell"
  amount: number;
  native_token_amount?: number;
  token_amount?: number;
  native_token_symbol?: string;
  token_symbol: string;
  token_address: string;
  status: string;
  timestamp: number;
  blockchain: string;
  gas_fee?: number;
  success: boolean;
  hash?: string;
  block_number?: number;
  error_message?: string;
  admin_fee_amount?: number;
  admin_fee_percentage?: number;
  admin_fee_transaction_id?: string;
  admin_fee_status?: 'pending' | 'completed' | 'failed' | 'retrying';
  admin_fee_collection_method?: 'native_token' | 'received_token' | 'smart_collection';
  admin_fee_token_symbol?: string;
  admin_fee_token_address?: string;
}

export interface BlockchainVolume {
  blockchain: string;
  total_volume: number;
  total_volume_usd?: number;
  native_symbol: string;
  transaction_count: number;
}

export interface TransactionListResponse {
  transactions: Transaction[];
  total: number;
  page: number;
  per_page: number;
  success_rate: number;
  blockchain_volumes: BlockchainVolume[];
}

export interface DashboardAnalytics {
  total_users: number;
  active_users_24h: number;
  total_transactions: number;
  transactions_24h: number;
  total_volume: number;
  volume_24h: number;
  total_fees_collected: number;
  fees_collected_24h: number;
  success_rate: number;
  average_response_time: number;
  blockchain_distribution: Record<string, number>;
  hourly_stats: HourlyStats[];
}

export interface HourlyStats {
  hour: number;
  transactions: number;
  volume: number;
  users: number;
  success_rate: number;
}

export interface DashboardResponse {
  analytics: DashboardAnalytics;
  system_health: SystemHealth;
  alerts: SystemAlert[];
  bots_summary: BotSummaryItem[];
  recent_transactions: TransactionSummaryItem[];
}

export interface BotSummaryItem {
  id: string;
  name: string;
  bot_type: string;
  status: string;
  active_users: number;
  transactions_24h: number;
  volume_24h: number;
  success_rate: number;
  uptime_percentage: number;
}

export interface TransactionSummaryItem {
  id: string;
  user_id: string;
  bot_type: string;
  amount: number;
  token_symbol: string;
  status: string;
  timestamp: number;
  blockchain: string;
}

export interface SystemHealth {
  uptime_seconds: number;
  cpu_usage: number;
  memory_usage: number;
  disk_usage: number;
  active_connections: number;
  api_requests_per_minute: number;
  database_connections: number;
  blockchain_connections: Record<string, boolean>;
  last_backup?: number;
}

export interface SystemAlert {
  id: string;
  severity: 'Info' | 'Warning' | 'Error' | 'Critical';
  title: string;
  message: string;
  timestamp: number;
  resolved: boolean;
  component: string;
}

export interface User {
  id: string;
  chat_id: number;
  first_name: string;
  last_name?: string;
  username?: string;
  current_blockchain: 'bsc' | 'sol' | 'eth' | 'base';
  created_at: number;
  last_seen: number;
}

export interface UserListResponse {
  users: User[];
  total: number;
  page: number;
  per_page: number;
}

export interface AdminSettings {
  id: string;
  admin_fee_percentage: number;
  max_users_per_admin: number;
  maintenance_mode: boolean;
  updated_at: number;
  updated_by: string;
  custom_settings: Record<string, any>;
  // Blockchain-specific fee settings
  eth_fee_percentage?: number;
  bsc_fee_percentage?: number;
  base_fee_percentage?: number;
  sol_fee_percentage?: number;
  // Admin wallet addresses
  eth_admin_wallet?: string;
  bsc_admin_wallet?: string;
  base_admin_wallet?: string;
  sol_admin_wallet?: string;
}

export interface BlockchainAnalytics {
  blockchain: string;
  total_transactions: number;
  total_volume: number;
  total_fees: number;
  unique_users: number;
  success_rate: number;
  avg_response_time: number;
  transactions_24h: number;
  volume_24h: number;
  fees_24h: number;
  unique_users_24h: number;
  avg_transaction_amount: number;
  user_growth_24h: number;
}

export interface BlockchainAnalyticsResponse {
  [blockchain: string]: BlockchainAnalytics;
}

// API functions
export const adminApi = {
  // Authentication
  login: async (username: string, password: string): Promise<LoginResponse> => {
    const response = await api.post<LoginResponse>('/auth/login', { username, password });
    return response.data;
  },

  logout: async (): Promise<void> => {
    await api.post('/auth/logout');
  },

  refreshToken: async (): Promise<{ token: string; expires_in: number }> => {
    const response = await api.post('/auth/refresh');
    return response.data;
  },

  getCurrentUser: async (): Promise<AdminUser> => {
    const response = await api.get<AdminUser>('/auth/me');
    return response.data;
  },

  changePassword: async (currentPassword: string, newPassword: string): Promise<void> => {
    await api.post('/auth/change-password', {
      current_password: currentPassword,
      new_password: newPassword,
    });
  },

  // Dashboard Analytics
  getDashboardAnalytics: async (): Promise<DashboardResponse> => {
    const key = requestDeduplicator.generateKey('/admin/dashboard');
    return requestDeduplicator.execute(key, async () => {
      const response = await api.get<DashboardResponse>('/admin/dashboard');
      return response.data;
    });
  },

  getSystemHealth: async (): Promise<SystemHealth> => {
    const key = requestDeduplicator.generateKey('/admin/system-health');
    return requestDeduplicator.execute(key, async () => {
      const response = await api.get<SystemHealth>('/admin/system-health');
      return response.data;
    });
  },

  getSystemAlerts: async (): Promise<SystemAlert[]> => {
    const key = requestDeduplicator.generateKey('/admin/alerts');
    return requestDeduplicator.execute(key, async () => {
      const response = await api.get<SystemAlert[]>('/admin/alerts');
      return response.data;
    });
  },

  resolveAlert: async (alertId: string): Promise<void> => {
    await api.post(`/admin/alerts/${alertId}/resolve`);
  },

  getBlockchainAnalytics: async (): Promise<BlockchainAnalyticsResponse> => {
    const response = await api.get<BlockchainAnalyticsResponse>('/admin/blockchain-analytics');
    return response.data;
  },

  getTransactionMetrics: async (): Promise<any> => {
    const response = await api.get('/admin/transaction-metrics');
    return response.data;
  },

  getUserMetrics: async (): Promise<any> => {
    const response = await api.get('/admin/user-metrics');
    return response.data;
  },



  resetSettings: async (): Promise<any> => {
    const response = await api.post('/admin/settings/reset');
    return response.data;
  },

  backupSettings: async (): Promise<any> => {
    const response = await api.post('/admin/settings/backup');
    return response.data;
  },

  restoreSettings: async (backupId: string): Promise<any> => {
    const response = await api.post('/admin/settings/restore', { backup_id: backupId });
    return response.data;
  },





  updateAdminFeeSettings: async (settings: {
    eth_fee_percentage?: number;
    bsc_fee_percentage?: number;
    base_fee_percentage?: number;
    sol_fee_percentage?: number;
    default_fee_percentage?: number;
    eth_admin_wallet?: string;
    bsc_admin_wallet?: string;
    base_admin_wallet?: string;
    sol_admin_wallet?: string;
  }): Promise<void> => {
    await api.put('/admin/fee-settings', settings);
  },

  // Bots Management
  getBots: async (params?: {
    page?: number;
    per_page?: number;
    bot_type?: string;
    status?: string;
    search?: string;
  }): Promise<BotListResponse> => {
    const key = requestDeduplicator.generateKey('/bots', params);
    return requestDeduplicator.execute(key, async () => {
      const response = await api.get<BotListResponse>('/bots', { params });
      return response.data;
    });
  },

  getBot: async (id: string): Promise<Bot> => {
    const response = await api.get<Bot>(`/bots/${id}`);
    return response.data;
  },

  createBot: async (bot: {
    name: string;
    description: string;
    bot_type: string;
    config?: Partial<BotConfig>;
  }): Promise<Bot> => {
    const response = await api.post<Bot>('/bots', bot);
    return response.data;
  },

  updateBot: async (id: string, updates: {
    name?: string;
    description?: string;
    status?: string;
    config?: Partial<BotConfig>;
  }): Promise<Bot> => {
    const response = await api.put<Bot>(`/bots/${id}`, updates);
    return response.data;
  },

  deleteBot: async (id: string): Promise<void> => {
    await api.delete(`/bots/${id}`);
  },

  startBot: async (id: string): Promise<void> => {
    await api.post(`/bots/${id}/start`);
  },

  stopBot: async (id: string): Promise<void> => {
    await api.post(`/bots/${id}/stop`);
  },

  restartBot: async (id: string): Promise<void> => {
    await api.post(`/bots/${id}/restart`);
  },

  updateBotStats: async (id: string, stats: {
    transactions: number;
    volume: number;
    success_rate: number;
    response_time: number;
  }): Promise<void> => {
    await api.put(`/bots/${id}/stats`, stats);
  },

  getBotsAnalytics: async (): Promise<any> => {
    const response = await api.get('/bots/analytics');
    return response.data;
  },

  // Transactions Management
  getTransactions: async (params?: {
    page?: number;
    per_page?: number;
    bot_type?: string;
    status?: string;
    blockchain?: string;
    user_id?: string;
    start_date?: string;
    end_date?: string;
  }): Promise<TransactionListResponse> => {
    const key = requestDeduplicator.generateKey('/admin/transactions', params);
    return requestDeduplicator.execute(key, async () => {
      const response = await api.get<TransactionListResponse>('/admin/transactions', { params });
      return response.data;
    });
  },

  getTransaction: async (id: string): Promise<Transaction> => {
    const response = await api.get<Transaction>(`/admin/transactions/${id}`);
    return response.data;
  },

  collectAdminFee: async (transactionId: string): Promise<{ success: boolean; message?: string }> => {
    const response = await api.post(`/admin/transactions/${transactionId}/collect-fee`);
    return response.data;
  },

  getTransactionsByUser: async (userId: string, params?: {
    page?: number;
    per_page?: number;
  }): Promise<TransactionListResponse> => {
    const response = await api.get<TransactionListResponse>(`/admin/users/${userId}/transactions`, { params });
    return response.data;
  },

  // Users Management
  getUsers: async (params?: {
    page?: number;
    per_page?: number;
    search?: string;
    blockchain?: string;
    status?: string;
  }): Promise<UserListResponse> => {
    const key = requestDeduplicator.generateKey('/admin/users', params);
    return requestDeduplicator.execute(key, async () => {
      const response = await api.get<UserListResponse>('/admin/users', { params });
      return response.data;
    });
  },

  getUser: async (id: string): Promise<User> => {
    const response = await api.get<User>(`/admin/users/${id}`);
    return response.data;
  },

  updateUser: async (id: string, updates: Partial<User>): Promise<User> => {
    const response = await api.put<User>(`/admin/users/${id}`, updates);
    return response.data;
  },

  deleteUser: async (id: string): Promise<void> => {
    await api.delete(`/admin/users/${id}`);
  },

  getUserAnalytics: async (id: string): Promise<any> => {
    const response = await api.get(`/admin/users/${id}/analytics`);
    return response.data;
  },

  // Admins Management
  getAdmins: async (): Promise<AdminUser[]> => {
    const response = await api.get<AdminUser[]>('/admin/admins');
    return response.data;
  },

  createAdmin: async (admin: {
    username: string;
    email: string;
    password: string;
    role: string;
  }): Promise<AdminUser> => {
    const response = await api.post<AdminUser>('/auth/create-admin', admin);
    return response.data;
  },

  updateAdmin: async (id: string, updates: {
    username?: string;
    email?: string;
    role?: string;
  }): Promise<AdminUser> => {
    const response = await api.put<AdminUser>(`/admin/admins/${id}`, updates);
    return response.data;
  },

  deleteAdmin: async (id: string): Promise<void> => {
    await api.delete(`/admin/admins/${id}`);
  },

  // Settings Management
  getSettings: async (): Promise<AdminSettings> => {
    const response = await api.get<AdminSettings>('/admin/settings');
    return response.data;
  },

  updateSettings: async (settings: Partial<AdminSettings>): Promise<AdminSettings> => {
    const response = await api.put<AdminSettings>('/admin/settings', settings);
    return response.data;
  },

  // Analytics and Reporting
  getAnalyticsReport: async (params: {
    start_date: string;
    end_date: string;
    granularity: 'hour' | 'day' | 'week' | 'month';
  }): Promise<any> => {
    const response = await api.get('/admin/analytics/report', { params });
    return response.data;
  },

  exportData: async (type: 'users' | 'transactions' | 'bots', format: 'csv' | 'json'): Promise<Blob> => {
    const response = await api.get(`/admin/export/${type}`, {
      params: { format },
      responseType: 'blob',
    });
    return response.data;
  },
};

export default adminApi;
