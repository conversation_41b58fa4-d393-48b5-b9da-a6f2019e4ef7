use axum::{
    routing::{get, post, put, delete},
    Router,
    extract::{Json, Path, Query, State},
    http::{StatusCode, HeaderMap},
    response::IntoResponse,
};
use serde::{Deserialize, Serialize};
use solana_sdk::pubkey::Pubkey;
use std::str::FromStr;
use mongodb::{
    bson::{doc, oid::ObjectId},
    options::{FindOptions, ReplaceOptions},
    error::Error as MongoError,
};
use futures_util::TryStreamExt;
use std::{
    sync::Arc,
    collections::HashMap,
    time::{SystemTime, UNIX_EPOCH, Duration},
};
use tokio::time::timeout;
use tracing::{error, warn, info, debug};
use anyhow::{Result, Context, anyhow};
use crate::model::{AdminSettings, BotError};
use crate::service::db_service::DbService;
use crate::config::AppConfig;
use crate::controllers::auth_controller::Claims;
use jsonwebtoken::{decode, DecodingKey, Validation, Algorithm};

// Production constants
const DB_OPERATION_TIMEOUT: Duration = Duration::from_secs(10);
const MAX_SETTING_KEY_LENGTH: usize = 100;
const MAX_SETTING_VALUE_SIZE: usize = 10000; // 10KB max for setting values
const MIN_ADMIN_FEE: f64 = 0.001;
const MAX_ADMIN_FEE: f64 = 100.0;
const MIN_MAX_USERS: i32 = 1;
const MAX_MAX_USERS: i32 = 100000;

#[derive(Debug, thiserror::Error)]
pub enum SettingsError {
    #[error("Database operation failed: {0}")]
    DatabaseError(#[from] MongoError),
    #[error("Setting not found: {0}")]
    SettingNotFound(String),
    #[error("Invalid input: {0}")]
    InvalidInput(String),
    #[error("Authorization failed: {0}")]
    AuthorizationFailed(String),
    #[error("Operation timeout")]
    Timeout,
    #[error("Validation failed: {0}")]
    ValidationError(String),
}

#[derive(Debug, Serialize)]
pub struct SettingsResponse {
    pub id: String,
    pub admin_fee_percentage: f64,

    // Blockchain-specific fee percentages
    #[serde(skip_serializing_if = "Option::is_none")]
    pub admin_fee_percentage_eth: Option<f64>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub admin_fee_percentage_bsc: Option<f64>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub admin_fee_percentage_base: Option<f64>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub admin_fee_percentage_sol: Option<f64>,

    // Admin wallet addresses
    #[serde(skip_serializing_if = "Option::is_none")]
    pub admin_wallet_eth: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub admin_wallet_bsc: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub admin_wallet_base: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub admin_wallet_sol: Option<String>,

    // Fee collection settings
    pub auto_fee_collection: bool,

    pub max_users_per_admin: i32,
    pub maintenance_mode: bool,
    pub updated_at: u64,
    pub updated_by: String,
    pub custom_settings: HashMap<String, serde_json::Value>,
}

#[derive(Debug, Deserialize)]
pub struct ConvertSolanaAddressRequest {
    pub address: String,
    pub from_format: String, // "base58" or "hex"
}

#[derive(Debug, Serialize)]
pub struct ConvertSolanaAddressResponse {
    pub original_address: String,
    pub converted_address: String,
    pub from_format: String,
    pub to_format: String,
}

#[derive(Debug, Deserialize)]
pub struct UpdateSettingsRequest {
    pub admin_fee_percentage: Option<f64>,

    // Blockchain-specific fee percentages
    pub admin_fee_percentage_eth: Option<f64>,
    pub admin_fee_percentage_bsc: Option<f64>,
    pub admin_fee_percentage_base: Option<f64>,
    pub admin_fee_percentage_sol: Option<f64>,

    // Admin wallet addresses
    pub admin_wallet_eth: Option<String>,
    pub admin_wallet_bsc: Option<String>,
    pub admin_wallet_base: Option<String>,
    pub admin_wallet_sol: Option<String>,

    // Fee collection settings
    pub auto_fee_collection: Option<bool>,

    pub max_users_per_admin: Option<i32>,
    pub maintenance_mode: Option<bool>,
    pub custom_settings: Option<HashMap<String, serde_json::Value>>,
}

#[derive(Debug, Deserialize)]
pub struct CreateSettingRequest {
    pub key: String,
    pub value: serde_json::Value,
    pub description: Option<String>,
}

#[derive(Debug, Serialize)]
pub struct SettingItem {
    pub key: String,
    pub value: serde_json::Value,
    pub description: Option<String>,
    pub updated_at: u64,
    pub updated_by: String,
}

pub struct SettingsController {
    config: Arc<AppConfig>,
    jwt_secret: String,
}

impl SettingsController {
    pub fn new(config: Arc<AppConfig>) -> Self {
        let jwt_secret = std::env::var("JWT_SECRET")
            .context("JWT_SECRET environment variable is required")
            .expect("Critical configuration missing");

        Self { config, jwt_secret }
    }

    /// Verify authentication header and extract claims
    async fn verify_auth_header(&self, headers: &HeaderMap) -> Option<Claims> {
        if let Some(auth_header) = headers.get("authorization") {
            if let Ok(auth_str) = auth_header.to_str() {
                if let Some(token) = auth_str.strip_prefix("Bearer ") {
                    let validation = Validation::new(Algorithm::HS256);
                    if let Ok(token_data) = decode::<Claims>(
                        token,
                        &DecodingKey::from_secret(self.jwt_secret.as_ref()),
                        &validation,
                    ) {
                        return Some(token_data.claims);
                    }
                }
            }
        }
        None
    }

    /// Validate settings update request
    fn validate_settings_update(&self, update_req: &UpdateSettingsRequest) -> Result<(), SettingsError> {
        // Validate default admin fee percentage
        if let Some(fee) = update_req.admin_fee_percentage {
            if fee < MIN_ADMIN_FEE || fee > MAX_ADMIN_FEE {
                return Err(SettingsError::InvalidInput(
                    format!("Admin fee must be between {}% and {}%", MIN_ADMIN_FEE, MAX_ADMIN_FEE)
                ));
            }
        }

        // Validate blockchain-specific fee percentages
        for (blockchain, fee_opt) in [
            ("Ethereum", update_req.admin_fee_percentage_eth),
            ("BSC", update_req.admin_fee_percentage_bsc),
            ("Base", update_req.admin_fee_percentage_base),
            ("Solana", update_req.admin_fee_percentage_sol),
        ] {
            if let Some(fee) = fee_opt {
                if fee < MIN_ADMIN_FEE || fee > MAX_ADMIN_FEE {
                    return Err(SettingsError::InvalidInput(
                        format!("{} admin fee must be between {}% and {}%", blockchain, MIN_ADMIN_FEE, MAX_ADMIN_FEE)
                    ));
                }
            }
        }

        // Validate wallet addresses format
        for (blockchain, wallet_opt) in [
            ("Ethereum", &update_req.admin_wallet_eth),
            ("BSC", &update_req.admin_wallet_bsc),
            ("Base", &update_req.admin_wallet_base),
        ] {
            if let Some(wallet) = wallet_opt {
                if !wallet.trim().is_empty() && !wallet.starts_with("0x") {
                    return Err(SettingsError::InvalidInput(
                        format!("{} wallet address must start with 0x", blockchain)
                    ));
                }
                if !wallet.trim().is_empty() && wallet.len() != 42 {
                    return Err(SettingsError::InvalidInput(
                        format!("{} wallet address must be 42 characters long", blockchain)
                    ));
                }
            }
        }

        // Validate Solana wallet address (base58 format)
        if let Some(sol_wallet) = &update_req.admin_wallet_sol {
            if !sol_wallet.trim().is_empty() {
                use solana_sdk::pubkey::Pubkey;
                use std::str::FromStr;

                if Pubkey::from_str(sol_wallet.trim()).is_err() {
                    return Err(SettingsError::InvalidInput(
                        "Invalid Solana wallet address format. Must be a valid base58 encoded address.".to_string()
                    ));
                }
            }
        }

        if let Some(max_users) = update_req.max_users_per_admin {
            if max_users < MIN_MAX_USERS || max_users > MAX_MAX_USERS {
                return Err(SettingsError::InvalidInput(
                    format!("Max users per admin must be between {} and {}", MIN_MAX_USERS, MAX_MAX_USERS)
                ));
            }
        }

        // Validate custom settings if provided
        if let Some(custom_settings) = &update_req.custom_settings {
            for (key, value) in custom_settings {
                if key.len() > MAX_SETTING_KEY_LENGTH {
                    return Err(SettingsError::InvalidInput(
                        format!("Setting key '{}' too long (max {} characters)", key, MAX_SETTING_KEY_LENGTH)
                    ));
                }

                let value_size = serde_json::to_string(value)
                    .map_err(|_| SettingsError::ValidationError("Invalid JSON value".to_string()))?
                    .len();

                if value_size > MAX_SETTING_VALUE_SIZE {
                    return Err(SettingsError::InvalidInput(
                        format!("Setting value for '{}' too large (max {} bytes)", key, MAX_SETTING_VALUE_SIZE)
                    ));
                }

                // Check for potentially dangerous keys
                if key.contains(['<', '>', '"', '\'', '&', '\0']) {
                    return Err(SettingsError::InvalidInput(
                        format!("Setting key '{}' contains invalid characters", key)
                    ));
                }
            }
        }

        Ok(())
    }

    /// Validate individual setting key
    fn validate_setting_key(&self, key: &str) -> Result<(), SettingsError> {
        if key.trim().is_empty() {
            return Err(SettingsError::InvalidInput("Setting key cannot be empty".to_string()));
        }

        if key.len() > MAX_SETTING_KEY_LENGTH {
            return Err(SettingsError::InvalidInput(
                format!("Setting key too long (max {} characters)", MAX_SETTING_KEY_LENGTH)
            ));
        }

        if key.contains(['<', '>', '"', '\'', '&', '\0', '/']) {
            return Err(SettingsError::InvalidInput("Setting key contains invalid characters".to_string()));
        }

        Ok(())
    }

    pub fn create_router(&self) -> Router {
        let controller = Arc::new(self.clone());
        let routes = &self.config.api_routes;

        Router::new()
            .route(&routes.admin_settings, get(Self::get_settings))
            .route(&routes.admin_settings, put(Self::update_settings))
            .route(&format!("{}/reset", routes.admin_settings), post(Self::reset_settings))
            .route(&format!("{}/:key", routes.admin_settings), get(Self::get_setting))
            .route(&format!("{}/:key", routes.admin_settings), put(Self::update_setting))
            .route(&format!("{}/convert-solana-address", routes.admin_settings), post(Self::convert_solana_address))
            .route(&format!("{}/:key", routes.admin_settings), delete(Self::delete_setting))
            .with_state(controller)
    }

    async fn get_settings(
        State(controller): State<Arc<SettingsController>>,
        headers: HeaderMap,
    ) -> impl IntoResponse {
        let start_time = SystemTime::now();

        // Verify authentication
        let claims = match controller.verify_auth_header(&headers).await {
            Some(claims) => claims,
            None => {
                warn!("Unauthorized settings access attempt");
                return (StatusCode::UNAUTHORIZED, Json(serde_json::json!({
                    "error": "Unauthorized access",
                    "code": "AUTH_REQUIRED"
                }))).into_response();
            }
        };

        info!("Settings requested by admin: {}", claims.username);

        // Execute database operations with timeout
        let db_result = timeout(
            DB_OPERATION_TIMEOUT,
            controller.fetch_current_settings()
        ).await;

        match db_result {
            Ok(Ok(response)) => {
                let elapsed = start_time.elapsed().unwrap_or_default();
                info!("Settings fetched for {} in {:?}", claims.username, elapsed);
                (StatusCode::OK, Json(response)).into_response()
            }
            Ok(Err(e)) => {
                error!("Database error while fetching settings: {:?}", e);
                (StatusCode::INTERNAL_SERVER_ERROR, Json(serde_json::json!({
                    "error": "Failed to fetch settings",
                    "code": "DATABASE_ERROR"
                }))).into_response()
            }
            Err(_) => {
                error!("Timeout while fetching settings");
                (StatusCode::REQUEST_TIMEOUT, Json(serde_json::json!({
                    "error": "Request timeout",
                    "code": "TIMEOUT"
                }))).into_response()
            }
        }
    }

    async fn fetch_current_settings(&self) -> Result<SettingsResponse, SettingsError> {
        let db = DbService::get_db();
        let settings_collection = db.collection::<AdminSettings>("admin_settings");
        let custom_settings_collection = db.collection::<mongodb::bson::Document>("custom_settings");

        // Get the latest settings document
        let find_options = FindOptions::builder()
            .sort(doc! { "updated_at": -1 })
            .limit(1)
            .build();

        let find_one_options = mongodb::options::FindOneOptions::builder()
            .sort(doc! { "created_at": -1 })
            .build();
        let settings_future = settings_collection.find_one(doc! {}, find_one_options);
        let custom_settings_future = self.fetch_custom_settings(&custom_settings_collection);

        let (settings_result, custom_settings_result) = tokio::join!(settings_future, custom_settings_future);
        let settings_result = settings_result.map_err(SettingsError::DatabaseError)?;
        let custom_settings = custom_settings_result?;

        if let Some(settings) = settings_result {
            Ok(SettingsResponse {
                id: settings.id.unwrap_or_else(|| ObjectId::new()).to_hex(),
                admin_fee_percentage: settings.admin_fee_percentage,
                admin_fee_percentage_eth: settings.admin_fee_percentage_eth,
                admin_fee_percentage_bsc: settings.admin_fee_percentage_bsc,
                admin_fee_percentage_base: settings.admin_fee_percentage_base,
                admin_fee_percentage_sol: settings.admin_fee_percentage_sol,
                admin_wallet_eth: settings.admin_wallet_eth,
                admin_wallet_bsc: settings.admin_wallet_bsc,
                admin_wallet_base: settings.admin_wallet_base,
                admin_wallet_sol: settings.admin_wallet_sol,
                auto_fee_collection: settings.auto_fee_collection,
                max_users_per_admin: settings.max_users_per_admin,
                maintenance_mode: settings.maintenance_mode,
                updated_at: settings.updated_at,
                updated_by: settings.updated_by,
                custom_settings,
            })
        } else {
            // Create and return default settings if none exist
            self.create_default_settings().await
        }
    }

    async fn fetch_custom_settings(&self, collection: &mongodb::Collection<mongodb::bson::Document>) -> Result<HashMap<String, serde_json::Value>, SettingsError> {
        let mut custom_settings = HashMap::new();
        let mut cursor = collection.find(doc! {}, None).await?;

        while let Some(doc) = cursor.try_next().await? {
            if let (Ok(key), Some(value)) = (doc.get_str("key"), doc.get("value")) {
                if let Ok(json_value) = serde_json::to_value(value) {
                    custom_settings.insert(key.to_string(), json_value);
                }
            }
        }

        Ok(custom_settings)
    }

    async fn create_default_settings(&self) -> Result<SettingsResponse, SettingsError> {
        let db = DbService::get_db();
        let collection = db.collection::<AdminSettings>("admin_settings");

        let now = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .map_err(|_| SettingsError::ValidationError("System time error".to_string()))?
            .as_secs();

        let default_settings = AdminSettings::default();

        let result = collection.insert_one(&default_settings, None).await?;
        let settings_id = result.inserted_id.as_object_id()
            .ok_or_else(|| SettingsError::DatabaseError(MongoError::custom("Failed to get inserted ID".to_string())))?;

        info!("Created default settings with ID: {}", settings_id);

        Ok(SettingsResponse {
            id: settings_id.to_hex(),
            admin_fee_percentage: default_settings.admin_fee_percentage,
            admin_fee_percentage_eth: default_settings.admin_fee_percentage_eth,
            admin_fee_percentage_bsc: default_settings.admin_fee_percentage_bsc,
            admin_fee_percentage_base: default_settings.admin_fee_percentage_base,
            admin_fee_percentage_sol: default_settings.admin_fee_percentage_sol,
            admin_wallet_eth: default_settings.admin_wallet_eth,
            admin_wallet_bsc: default_settings.admin_wallet_bsc,
            admin_wallet_base: default_settings.admin_wallet_base,
            admin_wallet_sol: default_settings.admin_wallet_sol,
            auto_fee_collection: default_settings.auto_fee_collection,
            max_users_per_admin: default_settings.max_users_per_admin,
            maintenance_mode: default_settings.maintenance_mode,
            updated_at: default_settings.updated_at,
            updated_by: default_settings.updated_by,
            custom_settings: HashMap::new(),
        })
    }

    async fn get_individual_setting(&self, key: &str) -> Result<SettingItem, SettingsError> {
        let db = DbService::get_db();
        let collection = db.collection::<AdminSettings>("admin_settings");

        // Get the main settings document
        let settings = collection
            .find_one(doc! {}, None)
            .await?
            .ok_or_else(|| SettingsError::SettingNotFound(key.to_string()))?;

        // Map the key to the appropriate field
        let (value, description) = match key {
            "admin_fee_percentage" => (
                serde_json::json!(settings.admin_fee_percentage),
                Some("Percentage fee collected by admin on transactions".to_string())
            ),
            "max_users_per_admin" => (
                serde_json::json!(settings.max_users_per_admin),
                Some("Maximum number of users per admin".to_string())
            ),
            "maintenance_mode" => (
                serde_json::json!(settings.maintenance_mode),
                Some("Whether the system is in maintenance mode".to_string())
            ),
            _ => return Err(SettingsError::SettingNotFound(key.to_string()))
        };

        Ok(SettingItem {
            key: key.to_string(),
            value,
            description,
            updated_at: settings.updated_at,
            updated_by: settings.updated_by,
        })
    }

    async fn update_settings(
        State(controller): State<Arc<SettingsController>>,
        headers: HeaderMap,
        Json(update_req): Json<UpdateSettingsRequest>,
    ) -> impl IntoResponse {
        println!("Received settings update request: {:?}", update_req);

        if let Some(claims) = controller.verify_auth_header(&headers).await {
            // Only SuperAdmins can update settings
            if claims.role != "SuperAdmin" {
                return (StatusCode::FORBIDDEN, Json(serde_json::json!({
                    "error": "Only SuperAdmins can update settings"
                }))).into_response();
            }

            // Validate input parameters
            if let Err(validation_error) = controller.validate_settings_update(&update_req) {
                return (StatusCode::BAD_REQUEST, Json(serde_json::json!({
                    "error": validation_error.to_string(),
                    "code": "VALIDATION_ERROR"
                }))).into_response();
            }

            let db = DbService::get_db();
            let collection = db.collection::<AdminSettings>("admin_settings");

            // Get current settings or create default
            let current_settings = collection
                .find_one(doc! {}, None)
                .await
                .unwrap_or(None);

            let now = std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap()
                .as_secs();

            let updated_settings = if let Some(mut settings) = current_settings {
                // Update existing settings using the model's update method
                settings.update(
                    update_req.admin_fee_percentage,
                    Some(update_req.admin_fee_percentage_eth),
                    Some(update_req.admin_fee_percentage_bsc),
                    Some(update_req.admin_fee_percentage_base),
                    Some(update_req.admin_fee_percentage_sol),
                    Some(update_req.admin_wallet_eth),
                    Some(update_req.admin_wallet_bsc),
                    Some(update_req.admin_wallet_base),
                    Some(update_req.admin_wallet_sol),
                    None, // rpc_url_eth
                    None, // rpc_url_bsc
                    None, // rpc_url_base
                    None, // rpc_url_sol
                    update_req.auto_fee_collection,
                    update_req.max_users_per_admin,
                    update_req.maintenance_mode,
                    claims.username.clone(),
                );

                // Update the document
                println!("Updating existing settings in database: {:?}", settings);
                match collection.replace_one(
                    doc! { "_id": settings.id.unwrap() },
                    &settings,
                    None,
                ).await {
                    Ok(_) => settings,
                    Err(_) => {
                        return (StatusCode::INTERNAL_SERVER_ERROR, Json(serde_json::json!({
                            "error": "Failed to update settings"
                        }))).into_response();
                    }
                }
            } else {
                // Create new settings document with all fields
                let mut new_settings = AdminSettings::default();
                new_settings.update(
                    update_req.admin_fee_percentage,
                    Some(update_req.admin_fee_percentage_eth),
                    Some(update_req.admin_fee_percentage_bsc),
                    Some(update_req.admin_fee_percentage_base),
                    Some(update_req.admin_fee_percentage_sol),
                    Some(update_req.admin_wallet_eth),
                    Some(update_req.admin_wallet_bsc),
                    Some(update_req.admin_wallet_base),
                    Some(update_req.admin_wallet_sol),
                    None, // rpc_url_eth
                    None, // rpc_url_bsc
                    None, // rpc_url_base
                    None, // rpc_url_sol
                    update_req.auto_fee_collection,
                    update_req.max_users_per_admin,
                    update_req.maintenance_mode,
                    claims.username.clone(),
                );

                println!("Creating new settings in database: {:?}", new_settings);
                match collection.insert_one(&new_settings, None).await {
                    Ok(result) => {
                        let mut settings = new_settings;
                        settings.id = Some(result.inserted_id.as_object_id().unwrap());
                        settings
                    }
                    Err(_) => {
                        return (StatusCode::INTERNAL_SERVER_ERROR, Json(serde_json::json!({
                            "error": "Failed to create settings"
                        }))).into_response();
                    }
                }
            };

            let response = SettingsResponse {
                id: updated_settings.id.unwrap().to_hex(),
                admin_fee_percentage: updated_settings.admin_fee_percentage,
                admin_fee_percentage_eth: updated_settings.admin_fee_percentage_eth,
                admin_fee_percentage_bsc: updated_settings.admin_fee_percentage_bsc,
                admin_fee_percentage_base: updated_settings.admin_fee_percentage_base,
                admin_fee_percentage_sol: updated_settings.admin_fee_percentage_sol,
                admin_wallet_eth: updated_settings.admin_wallet_eth,
                admin_wallet_bsc: updated_settings.admin_wallet_bsc,
                admin_wallet_base: updated_settings.admin_wallet_base,
                admin_wallet_sol: updated_settings.admin_wallet_sol,
                auto_fee_collection: updated_settings.auto_fee_collection,
                max_users_per_admin: updated_settings.max_users_per_admin,
                maintenance_mode: updated_settings.maintenance_mode,
                updated_at: updated_settings.updated_at,
                updated_by: updated_settings.updated_by,
                custom_settings: HashMap::new(),
            };

            (StatusCode::OK, Json(response)).into_response()
        } else {
            (StatusCode::UNAUTHORIZED, Json(serde_json::json!({
                "error": "Unauthorized"
            }))).into_response()
        }
    }

    async fn reset_settings(
        State(controller): State<Arc<SettingsController>>,
        headers: HeaderMap,
    ) -> impl IntoResponse {
        if let Some(claims) = controller.verify_auth_header(&headers).await {
            // Only SuperAdmins can reset settings
            if claims.role != "SuperAdmin" {
                return (StatusCode::FORBIDDEN, Json(serde_json::json!({
                    "error": "Only SuperAdmins can reset settings"
                }))).into_response();
            }

            let db = DbService::get_db();
            let collection = db.collection::<AdminSettings>("admin_settings");

            let now = std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap()
                .as_secs();

            let mut default_settings = AdminSettings::default();
            default_settings.updated_at = now;
            default_settings.updated_by = claims.username.clone();

            // Delete all existing settings and create new default
            let _ = collection.delete_many(doc! {}, None).await;

            match collection.insert_one(&default_settings, None).await {
                Ok(result) => {
                    let response = SettingsResponse {
                        id: result.inserted_id.as_object_id().unwrap().to_hex(),
                        admin_fee_percentage: default_settings.admin_fee_percentage,
                        admin_fee_percentage_eth: default_settings.admin_fee_percentage_eth,
                        admin_fee_percentage_bsc: default_settings.admin_fee_percentage_bsc,
                        admin_fee_percentage_base: default_settings.admin_fee_percentage_base,
                        admin_fee_percentage_sol: default_settings.admin_fee_percentage_sol,
                        admin_wallet_eth: default_settings.admin_wallet_eth,
                        admin_wallet_bsc: default_settings.admin_wallet_bsc,
                        admin_wallet_base: default_settings.admin_wallet_base,
                        admin_wallet_sol: default_settings.admin_wallet_sol,
                        auto_fee_collection: default_settings.auto_fee_collection,
                        max_users_per_admin: default_settings.max_users_per_admin,
                        maintenance_mode: default_settings.maintenance_mode,
                        updated_at: default_settings.updated_at,
                        updated_by: default_settings.updated_by,
                        custom_settings: HashMap::new(),
                    };
                    (StatusCode::OK, Json(response)).into_response()
                }
                Err(_) => (StatusCode::INTERNAL_SERVER_ERROR, Json(serde_json::json!({
                    "error": "Failed to reset settings"
                }))).into_response()
            }
        } else {
            (StatusCode::UNAUTHORIZED, Json(serde_json::json!({
                "error": "Unauthorized"
            }))).into_response()
        }
    }

    async fn get_setting(
        State(controller): State<Arc<SettingsController>>,
        headers: HeaderMap,
        Path(key): Path<String>,
    ) -> impl IntoResponse {
        if let Some(_claims) = controller.verify_auth_header(&headers).await {
            match controller.get_individual_setting(&key).await {
                Ok(setting) => (StatusCode::OK, Json(setting)).into_response(),
                Err(SettingsError::SettingNotFound(_)) => {
                    (StatusCode::NOT_FOUND, Json(serde_json::json!({
                        "error": format!("Setting '{}' not found", key),
                        "code": "SETTING_NOT_FOUND"
                    }))).into_response()
                }
                Err(e) => {
                    error!("Failed to get setting '{}': {:?}", key, e);
                    (StatusCode::INTERNAL_SERVER_ERROR, Json(serde_json::json!({
                        "error": "Failed to retrieve setting",
                        "code": "SETTINGS_ERROR"
                    }))).into_response()
                }
            }
        } else {
            (StatusCode::UNAUTHORIZED, Json(serde_json::json!({
                "error": "Unauthorized"
            }))).into_response()
        }
    }

    async fn update_setting(
        State(controller): State<Arc<SettingsController>>,
        headers: HeaderMap,
        Path(key): Path<String>,
        Json(value): Json<serde_json::Value>,
    ) -> impl IntoResponse {
        if let Some(claims) = controller.verify_auth_header(&headers).await {
            // Only SuperAdmins can update individual settings
            if claims.role != "SuperAdmin" {
                return (StatusCode::FORBIDDEN, Json(serde_json::json!({
                    "error": "Only SuperAdmins can update settings"
                })));
            }

            // This would implement individual setting updates
            // For now, return a placeholder
            (StatusCode::OK, Json(serde_json::json!({
                "key": key,
                "value": value,
                "message": "Setting updated successfully"
            })))
        } else {
            (StatusCode::UNAUTHORIZED, Json(serde_json::json!({
                "error": "Unauthorized"
            })))
        }
    }

    async fn delete_setting(
        State(controller): State<Arc<SettingsController>>,
        headers: HeaderMap,
        Path(key): Path<String>,
    ) -> impl IntoResponse {
        if let Some(claims) = controller.verify_auth_header(&headers).await {
            // Only SuperAdmins can delete settings
            if claims.role != "SuperAdmin" {
                return (StatusCode::FORBIDDEN, Json(serde_json::json!({
                    "error": "Only SuperAdmins can delete settings"
                })));
            }

            // This would implement individual setting deletion
            // For now, return a placeholder
            (StatusCode::OK, Json(serde_json::json!({
                "message": format!("Setting '{}' deleted successfully", key)
            })))
        } else {
            (StatusCode::UNAUTHORIZED, Json(serde_json::json!({
                "error": "Unauthorized"
            })))
        }
    }


}

impl Clone for SettingsController {
    fn clone(&self) -> Self {
        Self {
            config: self.config.clone(),
            jwt_secret: self.jwt_secret.clone(),
        }
    }
}

impl SettingsController {
    async fn convert_solana_address(
        State(controller): State<Arc<SettingsController>>,
        headers: HeaderMap,
        Json(convert_req): Json<ConvertSolanaAddressRequest>,
    ) -> impl IntoResponse {
        if let Some(_claims) = controller.verify_auth_header(&headers).await {
            match convert_req.from_format.as_str() {
                "base58" => {
                    // Convert Base58 to hex
                    match Pubkey::from_str(&convert_req.address) {
                        Ok(pubkey) => {
                            let hex_address = format!("0x{}", hex::encode(pubkey.to_bytes()));
                            (StatusCode::OK, Json(ConvertSolanaAddressResponse {
                                original_address: convert_req.address,
                                converted_address: hex_address,
                                from_format: "base58".to_string(),
                                to_format: "hex".to_string(),
                            })).into_response()
                        }
                        Err(e) => {
                            (StatusCode::BAD_REQUEST, Json(serde_json::json!({
                                "error": format!("Invalid Base58 address: {}", e)
                            }))).into_response()
                        }
                    }
                }
                "hex" => {
                    // Convert hex to Base58
                    let hex_str = if convert_req.address.starts_with("0x") {
                        &convert_req.address[2..]
                    } else {
                        &convert_req.address
                    };

                    match hex::decode(hex_str) {
                        Ok(bytes) => {
                            if bytes.len() != 32 {
                                return (StatusCode::BAD_REQUEST, Json(serde_json::json!({
                                    "error": "Solana address must be 32 bytes"
                                }))).into_response();
                            }

                            let mut pubkey_bytes = [0u8; 32];
                            pubkey_bytes.copy_from_slice(&bytes);
                            let pubkey = Pubkey::new_from_array(pubkey_bytes);

                            (StatusCode::OK, Json(ConvertSolanaAddressResponse {
                                original_address: convert_req.address,
                                converted_address: pubkey.to_string(),
                                from_format: "hex".to_string(),
                                to_format: "base58".to_string(),
                            })).into_response()
                        }
                        Err(e) => {
                            (StatusCode::BAD_REQUEST, Json(serde_json::json!({
                                "error": format!("Invalid hex address: {}", e)
                            }))).into_response()
                        }
                    }
                }
                _ => {
                    (StatusCode::BAD_REQUEST, Json(serde_json::json!({
                        "error": "Invalid format. Must be 'base58' or 'hex'"
                    }))).into_response()
                }
            }
        } else {
            (StatusCode::UNAUTHORIZED, Json(serde_json::json!({
                "error": "Unauthorized"
            }))).into_response()
        }
    }
}
