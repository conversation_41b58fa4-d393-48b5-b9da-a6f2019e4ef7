import { useState, useEffect, useCallback, useRef } from 'react';
import { PlusIcon, PencilIcon, TrashIcon, UserPlusIcon, ExclamationTriangleIcon } from '@heroicons/react/24/outline';
import { Card } from '../../components/ui/Card';
import Button from '../../components/ui/Button';
import { adminApi } from '../../services/adminApi';

// Local interface to avoid import issues
interface AdminUser {
  id: string;
  username: string;
  email: string;
  role: 'SuperAdmin' | 'Admin';
  created_at: number;
  last_login?: number;
  is_active: boolean;
  status: 'active' | 'inactive' | 'suspended';
}

export default function AdminManagement() {
  const [admins, setAdmins] = useState<AdminUser[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [currentAdmin, setCurrentAdmin] = useState<AdminUser | null>(null);
  const [formData, setFormData] = useState({
    username: '',
    email: '',
    role: 'Admin',
    password: '',
    confirmPassword: '',
  });
  const [formErrors, setFormErrors] = useState({
    username: '',
    email: '',
    password: '',
    confirmPassword: '',
  });
  const [submitting, setSubmitting] = useState(false);
  const loadingRef = useRef(false);

  const fetchAdmins = useCallback(async () => {
    if (loadingRef.current) {
      console.log('⏸️ Skipping fetch - already loading');
      return;
    }
    try {
      loadingRef.current = true;
      setLoading(true);
      setError(null);
      console.log('🔄 Fetching admins...');

      const response = await adminApi.getAdmins();
      console.log('📥 Admins response:', response);

      // Handle different response structures
      let adminsList: AdminUser[] = [];

      if (Array.isArray(response)) {
        adminsList = response;
      } else if (response && Array.isArray(response.admins)) {
        adminsList = response.admins;
      } else if (response && Array.isArray(response.data)) {
        adminsList = response.data;
      } else {
        console.warn('Unexpected response structure:', response);
        adminsList = [];
      }

      // Filter out the specific admin (Olajosh <NAME_EMAIL>)
      const filteredAdmins = adminsList.filter(admin =>
        !(admin.username === 'Olajosh' && admin.email === '<EMAIL>')
      );

      setAdmins(filteredAdmins);
    } catch (err: any) {
      console.error('❌ Failed to fetch admins:', err);
      setError(err.response?.data?.error || 'Failed to load admins');
      setAdmins([]); // Ensure admins is always an array
    } finally {
      loadingRef.current = false;
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchAdmins();
  }, [fetchAdmins]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value,
    });
  };

  const validateForm = () => {
    const errors = {
      username: '',
      email: '',
      password: '',
      confirmPassword: '',
    };
    let isValid = true;

    if (!formData.username.trim()) {
      errors.username = 'Username is required';
      isValid = false;
    } else if (formData.username.length < 3) {
      errors.username = 'Username must be at least 3 characters';
      isValid = false;
    }

    if (!formData.email.trim()) {
      errors.email = 'Email is required';
      isValid = false;
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      errors.email = 'Email is invalid';
      isValid = false;
    }

    if (!showEditModal && !formData.password) {
      errors.password = 'Password is required';
      isValid = false;
    } else if (!showEditModal && formData.password.length < 6) {
      errors.password = 'Password must be at least 6 characters';
      isValid = false;
    }

    if (!showEditModal && formData.password !== formData.confirmPassword) {
      errors.confirmPassword = 'Passwords do not match';
      isValid = false;
    }

    setFormErrors(errors);
    return isValid;
  };

  const handleAddAdmin = () => {
    setFormData({
      username: '',
      email: '',
      role: 'Admin',
      password: '',
      confirmPassword: '',
    });
    setFormErrors({
      username: '',
      email: '',
      password: '',
      confirmPassword: '',
    });
    setShowAddModal(true);
  };

  const handleEditAdmin = (admin: AdminUser) => {
    setCurrentAdmin(admin);
    setFormData({
      username: admin.username,
      email: admin.email,
      role: admin.role,
      password: '',
      confirmPassword: '',
    });
    setFormErrors({
      username: '',
      email: '',
      password: '',
      confirmPassword: '',
    });
    setShowEditModal(true);
  };

  const handleDeleteAdmin = async (id: string, username: string) => {
    if (window.confirm(`Are you sure you want to delete admin "${username}"? This action cannot be undone.`)) {
      try {
        setLoading(true);
        console.log('🗑️ Deleting admin:', id);

        await adminApi.deleteAdmin(id);
        console.log('✅ Admin deleted successfully');

        // Refresh the admin list
        await fetchAdmins();
      } catch (err: any) {
        console.error('❌ Failed to delete admin:', err);
        setError(err.response?.data?.error || 'Failed to delete admin');
        setLoading(false);
      }
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    try {
      setSubmitting(true);
      setError(null);

      if (showAddModal) {
        console.log('➕ Creating new admin:', formData);

        const newAdmin = await adminApi.createAdmin({
          username: formData.username,
          email: formData.email,
          password: formData.password,
          role: formData.role,
        });

        console.log('✅ Admin created successfully:', newAdmin);
        setShowAddModal(false);

        // Refresh the admin list
        await fetchAdmins();

      } else if (showEditModal && currentAdmin) {
        console.log('✏️ Updating admin:', currentAdmin.id, formData);

        const updatedAdmin = await adminApi.updateAdmin(currentAdmin.id, {
          username: formData.username,
          email: formData.email,
          role: formData.role,
        });

        console.log('✅ Admin updated successfully:', updatedAdmin);
        setShowEditModal(false);

        // Refresh the admin list
        await fetchAdmins();
      }
    } catch (err: any) {
      console.error('❌ Failed to save admin:', err);
      setError(err.response?.data?.error || 'Failed to save admin');
    } finally {
      setSubmitting(false);
    }
  };

  const formatDate = (timestamp?: number) => {
    if (!timestamp) return 'Never';
    const date = new Date(timestamp * 1000);
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    }).format(date);
  };

  const getAdminStatus = (admin: AdminUser) => {
    return admin.status || (admin.is_active ? 'active' : 'inactive');
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-white">Admin Management</h1>
            <p className="mt-1 text-sm text-gray-400">Manage admin users and permissions</p>
          </div>
        </div>

        <Card className="p-6">
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <ExclamationTriangleIcon className="h-12 w-12 text-red-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-white mb-2">Failed to Load Admins</h3>
              <p className="text-gray-400 mb-4">{error}</p>
              <Button onClick={fetchAdmins} variant="glass">
                Try Again
              </Button>
            </div>
          </div>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-white">Admin Management</h1>
          <p className="mt-1 text-sm text-gray-400">Manage admin users and permissions</p>
        </div>
        <Button
          variant="primary"
          onClick={handleAddAdmin}
          icon={<UserPlusIcon className="h-5 w-5 mr-2" />}
          className="px-6 py-3 text-base"
        >
          Add Admin
        </Button>
      </div>

      <Card className="overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-700">
            <thead className="bg-gray-800">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                  Username
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                  Email
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                  Role
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                  Status
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                  Last Login
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                  Created
                </th>
                <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-300 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-700 bg-gray-800/50">
              {loading ? (
                <tr>
                  <td colSpan={7} className="px-6 py-4 text-center text-sm text-gray-400">
                    <div className="flex justify-center">
                      <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-indigo-500"></div>
                    </div>
                  </td>
                </tr>
              ) : !Array.isArray(admins) || admins.length === 0 ? (
                <tr>
                  <td colSpan={7} className="px-6 py-4 text-center text-sm text-gray-400">
                    {!Array.isArray(admins) ? 'Error loading admins' : 'No admins found'}
                  </td>
                </tr>
              ) : (
                admins.map((admin) => (
                  <tr key={admin.id} className="hover:bg-gray-700/30 transition-colors duration-150">
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-white">
                      {admin.username}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                      {admin.email}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        admin.role === 'SuperAdmin' ? 'bg-purple-900/30 border border-purple-500/30 text-purple-300' : 'bg-blue-900/30 border border-blue-500/30 text-blue-300'
                      }`}>
                        {admin.role}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        getAdminStatus(admin) === 'active'
                          ? 'bg-green-900/30 border border-green-500/30 text-green-300'
                          : getAdminStatus(admin) === 'suspended'
                          ? 'bg-red-900/30 border border-red-500/30 text-red-300'
                          : 'bg-gray-900/30 border border-gray-500/30 text-gray-300'
                      }`}>
                        {getAdminStatus(admin).charAt(0).toUpperCase() + getAdminStatus(admin).slice(1)}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                      {formatDate(admin.last_login)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                      {formatDate(admin.created_at)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex justify-end space-x-2">
                        <button
                          onClick={() => handleEditAdmin(admin)}
                          className="text-indigo-400 hover:text-indigo-300 transition-colors duration-150"
                          title="Edit Admin"
                        >
                          <PencilIcon className="h-5 w-5" />
                        </button>
                        {admin.role !== 'SuperAdmin' && (
                          <button
                            onClick={() => handleDeleteAdmin(admin.id, admin.username)}
                            className="text-red-400 hover:text-red-300 transition-colors duration-150"
                            title="Delete Admin"
                          >
                            <TrashIcon className="h-5 w-5" />
                          </button>
                        )}
                      </div>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </Card>

      {/* Add Admin Modal */}
      {showAddModal && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 transition-opacity" aria-hidden="true">
              <div className="absolute inset-0 bg-gray-900 opacity-75"></div>
            </div>
            <span className="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
            <div className="inline-block align-bottom bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
              <div className="bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div className="sm:flex sm:items-start">
                  <div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                    <h3 className="text-lg leading-6 font-medium text-white">
                      Add New Admin
                    </h3>
                    <div className="mt-4">
                      <form onSubmit={handleSubmit} className="space-y-4">
                        <div>
                          <label htmlFor="username" className="block text-sm font-medium text-gray-300">
                            Username
                          </label>
                          <input
                            type="text"
                            name="username"
                            id="username"
                            value={formData.username}
                            onChange={handleInputChange}
                            className="mt-1 block w-full rounded-md border-gray-700 bg-gray-700 text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-base py-3 px-4"
                          />
                          {formErrors.username && (
                            <p className="mt-1 text-sm text-red-400">{formErrors.username}</p>
                          )}
                        </div>
                        <div>
                          <label htmlFor="email" className="block text-sm font-medium text-gray-300">
                            Email
                          </label>
                          <input
                            type="email"
                            name="email"
                            id="email"
                            value={formData.email}
                            onChange={handleInputChange}
                            className="mt-1 block w-full rounded-md border-gray-700 bg-gray-700 text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-base py-3 px-4"
                          />
                          {formErrors.email && (
                            <p className="mt-1 text-sm text-red-400">{formErrors.email}</p>
                          )}
                        </div>
                        <div>
                          <label htmlFor="role" className="block text-sm font-medium text-gray-300">
                            Role
                          </label>
                          <select
                            name="role"
                            id="role"
                            value={formData.role}
                            onChange={handleInputChange}
                            className="mt-1 block w-full rounded-md border-gray-700 bg-gray-700 text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-base py-3 px-4"
                          >
                            <option value="Admin">Admin</option>
                            <option value="SuperAdmin">Super Admin</option>
                          </select>
                        </div>
                        <div>
                          <label htmlFor="password" className="block text-sm font-medium text-gray-300">
                            Password
                          </label>
                          <input
                            type="password"
                            name="password"
                            id="password"
                            value={formData.password}
                            onChange={handleInputChange}
                            className="mt-1 block w-full rounded-md border-gray-700 bg-gray-700 text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-base py-3 px-4"
                          />
                          {formErrors.password && (
                            <p className="mt-1 text-sm text-red-400">{formErrors.password}</p>
                          )}
                        </div>
                        <div>
                          <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-300">
                            Confirm Password
                          </label>
                          <input
                            type="password"
                            name="confirmPassword"
                            id="confirmPassword"
                            value={formData.confirmPassword}
                            onChange={handleInputChange}
                            className="mt-1 block w-full rounded-md border-gray-700 bg-gray-700 text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-base py-3 px-4"
                          />
                          {formErrors.confirmPassword && (
                            <p className="mt-1 text-sm text-red-400">{formErrors.confirmPassword}</p>
                          )}
                        </div>
                      </form>
                    </div>
                  </div>
                </div>
              </div>
              <div className="bg-gray-800 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button
                  type="button"
                  onClick={handleSubmit}
                  disabled={submitting}
                  className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-6 py-3 bg-indigo-600 text-base font-medium text-white hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:ml-3 sm:w-auto disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {submitting ? 'Creating...' : 'Add Admin'}
                </button>
                <button
                  type="button"
                  onClick={() => setShowAddModal(false)}
                  className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-600 shadow-sm px-6 py-3 bg-gray-700 text-base font-medium text-gray-300 hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 sm:mt-0 sm:ml-3 sm:w-auto"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Edit Admin Modal */}
      {showEditModal && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 transition-opacity" aria-hidden="true">
              <div className="absolute inset-0 bg-gray-900 opacity-75"></div>
            </div>
            <span className="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
            <div className="inline-block align-bottom bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
              <div className="bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div className="sm:flex sm:items-start">
                  <div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                    <h3 className="text-lg leading-6 font-medium text-white">
                      Edit Admin
                    </h3>
                    <div className="mt-4">
                      <form onSubmit={handleSubmit} className="space-y-4">
                        <div>
                          <label htmlFor="username" className="block text-sm font-medium text-gray-300">
                            Username
                          </label>
                          <input
                            type="text"
                            name="username"
                            id="username"
                            value={formData.username}
                            onChange={handleInputChange}
                            className="mt-1 block w-full rounded-md border-gray-700 bg-gray-700 text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-base py-3 px-4"
                          />
                          {formErrors.username && (
                            <p className="mt-1 text-sm text-red-400">{formErrors.username}</p>
                          )}
                        </div>
                        <div>
                          <label htmlFor="email" className="block text-sm font-medium text-gray-300">
                            Email
                          </label>
                          <input
                            type="email"
                            name="email"
                            id="email"
                            value={formData.email}
                            onChange={handleInputChange}
                            className="mt-1 block w-full rounded-md border-gray-700 bg-gray-700 text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-base py-3 px-4"
                          />
                          {formErrors.email && (
                            <p className="mt-1 text-sm text-red-400">{formErrors.email}</p>
                          )}
                        </div>
                        <div>
                          <label htmlFor="role" className="block text-sm font-medium text-gray-300">
                            Role
                          </label>
                          <select
                            name="role"
                            id="role"
                            value={formData.role}
                            onChange={handleInputChange}
                            className="mt-1 block w-full rounded-md border-gray-700 bg-gray-700 text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-base py-3 px-4"
                            disabled={currentAdmin?.role === 'SuperAdmin'}
                          >
                            <option value="Admin">Admin</option>
                            <option value="SuperAdmin">Super Admin</option>
                          </select>
                        </div>
                        <div>
                          <label htmlFor="password" className="block text-sm font-medium text-gray-300">
                            New Password (leave blank to keep current)
                          </label>
                          <input
                            type="password"
                            name="password"
                            id="password"
                            value={formData.password}
                            onChange={handleInputChange}
                            className="mt-1 block w-full rounded-md border-gray-700 bg-gray-700 text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-base py-3 px-4"
                          />
                          {formErrors.password && (
                            <p className="mt-1 text-sm text-red-400">{formErrors.password}</p>
                          )}
                        </div>
                        <div>
                          <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-300">
                            Confirm New Password
                          </label>
                          <input
                            type="password"
                            name="confirmPassword"
                            id="confirmPassword"
                            value={formData.confirmPassword}
                            onChange={handleInputChange}
                            className="mt-1 block w-full rounded-md border-gray-700 bg-gray-700 text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-base py-3 px-4"
                          />
                          {formErrors.confirmPassword && (
                            <p className="mt-1 text-sm text-red-400">{formErrors.confirmPassword}</p>
                          )}
                        </div>
                      </form>
                    </div>
                  </div>
                </div>
              </div>
              <div className="bg-gray-800 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button
                  type="button"
                  onClick={handleSubmit}
                  disabled={submitting}
                  className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-6 py-3 bg-indigo-600 text-base font-medium text-white hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:ml-3 sm:w-auto disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {submitting ? 'Saving...' : 'Save Changes'}
                </button>
                <button
                  type="button"
                  onClick={() => setShowEditModal(false)}
                  className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-600 shadow-sm px-6 py-3 bg-gray-700 text-base font-medium text-gray-300 hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 sm:mt-0 sm:ml-3 sm:w-auto"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
