import { CheckIcon } from '@heroicons/react/20/solid';
import Button from './Button';

interface PricingTier {
  name: string;
  id: string;
  price: string;
  description: string;
  features: string[];
  mostPopular?: boolean;
}

interface PricingTableProps {
  tiers: PricingTier[];
}

function classNames(...classes: string[]) {
  return classes.filter(Boolean).join(' ');
}

export default function PricingTable({ tiers }: PricingTableProps) {
  return (
    <div className="mx-auto max-w-7xl px-6 lg:px-8">
      <div className="isolate mx-auto mt-10 grid max-w-md grid-cols-1 gap-8 lg:mx-0 lg:max-w-none lg:grid-cols-3">
        {tiers.map((tier) => (
          <div
            key={tier.id}
            className={classNames(
              tier.mostPopular ? 'ring-2 ring-secondary-600' : 'ring-1 ring-gray-200 dark:ring-gray-700',
              'rounded-3xl p-8 xl:p-10'
            )}
          >
            <div className="flex items-center justify-between gap-x-4">
              <h3 id={tier.id} className="text-lg font-semibold leading-8 text-gray-900 dark:text-white">
                {tier.name}
              </h3>
              {tier.mostPopular ? (
                <p className="rounded-full bg-secondary-600/10 px-2.5 py-1 text-xs font-semibold leading-5 text-secondary-600">
                  Most popular
                </p>
              ) : null}
            </div>
            <p className="mt-4 text-sm leading-6 text-gray-600 dark:text-gray-400">{tier.description}</p>
            <p className="mt-6 flex items-baseline gap-x-1">
              <span className="text-4xl font-bold tracking-tight text-gray-900 dark:text-white">{tier.price}</span>
              <span className="text-sm font-semibold leading-6 text-gray-600 dark:text-gray-400">/month</span>
            </p>
            <a
              href="#"
              aria-describedby={tier.id}
              className={classNames(
                tier.mostPopular
                  ? 'bg-secondary-600 text-white shadow-sm hover:bg-secondary-500'
                  : 'text-secondary-600 ring-1 ring-inset ring-secondary-200 hover:ring-secondary-300',
                'mt-6 block rounded-md py-2 px-3 text-center text-sm font-semibold leading-6 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-secondary-600'
              )}
            >
              Get started
            </a>
            <ul role="list" className="mt-8 space-y-3 text-sm leading-6 text-gray-600 dark:text-gray-400">
              {tier.features.map((feature) => (
                <li key={feature} className="flex gap-x-3">
                  <CheckIcon className="h-6 w-5 flex-none text-secondary-600" aria-hidden="true" />
                  {feature}
                </li>
              ))}
            </ul>
          </div>
        ))}
      </div>
    </div>
  );
}
