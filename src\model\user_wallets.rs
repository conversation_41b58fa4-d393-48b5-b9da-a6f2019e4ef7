use serde::{Deserialize, Serialize};
use mongodb::bson::oid::ObjectId;
use crate::model::wallet::Wallet;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UserWallets {
    #[serde(rename = "_id", skip_serializing_if = "Option::is_none")]
    pub id: Option<ObjectId>,
    pub user_id: ObjectId,
    pub sol_wallet: Wallet,
    pub bsc_wallet: Wallet,
    pub base_wallet: Wallet,
    pub eth_wallet: Wallet,
    #[serde(default)]
    pub wallets_generated: bool,
}

impl UserWallets {
    pub fn new(user_id: ObjectId) -> Self {
        Self {
            id: None,
            user_id,
            sol_wallet: Wallet::empty(),
            bsc_wallet: Wallet::empty(),
            eth_wallet: Wallet::empty(),
            base_wallet: Wallet::empty(),
            wallets_generated: false,
        }
    }
}
