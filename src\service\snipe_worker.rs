use crate::model::{Blockchain, Snipe, SnipeStatus};
use crate::service::DbService;
use crate::config::AppConfig;
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::Mutex;
use tokio::time::{Duration, interval};
use reqwest::Client;
use serde::{Deserialize, Serialize};
use std::time::Instant;
use std::collections::HashSet;
use std::env;
use mongodb::bson::{doc, oid::ObjectId};

const ENV_DEXSCREENER_API_URL: &str = "API_DEXSCREENER_BASE";
const ENV_SNIPE_POLLING_INTERVAL: &str = "SNIPE_POLLING_INTERVAL";
const ENV_SNIPE_MAX_TOKENS_PER_REQUEST: &str = "SNIPE_MAX_TOKENS_PER_REQUEST";

const DEFAULT_MAX_TOKENS_PER_REQUEST: usize = 30;
const DEFAULT_POLLING_INTERVAL_SECONDS: u64 = 60;
const DEFAULT_DEXSCREENER_API_URL: &str = "https://api.dexscreener.com/tokens/v1";

#[derive(Debug, Clone)]
pub struct SnipeWorker {
    client: Client,
    active_snipes: Arc<Mutex<HashMap<Blockchain, Vec<Snipe>>>>,
    last_poll: Arc<Mutex<Instant>>,
}

// The /tokens/v1 endpoint returns a direct array of pairs
type DexscreenerResponse = Vec<DexscreenerPair>;

#[derive(Debug, Deserialize, Serialize)]
struct DexscreenerPair {
    #[serde(default)]
    chainId: String,
    #[serde(default)]
    dexId: String,
    #[serde(default)]
    url: String,
    #[serde(default)]
    pairAddress: String,
    #[serde(default)]
    baseToken: DexscreenerToken,
    #[serde(default)]
    quoteToken: DexscreenerToken,
    #[serde(default)]
    priceNative: Option<String>,
    #[serde(default)]
    priceUsd: Option<String>,
    #[serde(default)]
    txns: Option<DexscreenerTxns>,
    #[serde(default)]
    volume: Option<DexscreenerVolume>,
    #[serde(default)]
    priceChange: Option<DexscreenerPriceChange>,
    #[serde(default)]
    liquidity: Option<DexscreenerLiquidity>,
    #[serde(default)]
    fdv: Option<f64>,
    #[serde(default)]
    marketCap: Option<f64>,
    #[serde(default)]
    pairCreatedAt: Option<u64>,
    #[serde(default)]
    info: Option<DexscreenerInfo>,
}

#[derive(Debug, Deserialize, Serialize, Default)]
struct DexscreenerToken {
    #[serde(default)]
    address: String,
    #[serde(default)]
    name: String,
    #[serde(default)]
    symbol: String,
}

#[derive(Debug, Deserialize, Serialize, Default)]
struct DexscreenerTxns {
    #[serde(default)]
    m5: Option<DexscreenerTxnPeriod>,
    #[serde(default)]
    h1: Option<DexscreenerTxnPeriod>,
    #[serde(default)]
    h6: Option<DexscreenerTxnPeriod>,
    #[serde(default)]
    h24: Option<DexscreenerTxnPeriod>,
}

#[derive(Debug, Deserialize, Serialize, Default)]
struct DexscreenerTxnPeriod {
    #[serde(default)]
    buys: Option<u32>,
    #[serde(default)]
    sells: Option<u32>,
}

#[derive(Debug, Deserialize, Serialize, Default)]
struct DexscreenerVolume {
    #[serde(default)]
    h24: Option<f64>,
    #[serde(default)]
    h6: Option<f64>,
    #[serde(default)]
    h1: Option<f64>,
    #[serde(default)]
    m5: Option<f64>,
}

#[derive(Debug, Deserialize, Serialize, Default)]
struct DexscreenerPriceChange {
    #[serde(default)]
    m5: Option<f64>,
    #[serde(default)]
    h1: Option<f64>,
    #[serde(default)]
    h6: Option<f64>,
    #[serde(default)]
    h24: Option<f64>,
}

#[derive(Debug, Deserialize, Serialize, Default)]
struct DexscreenerLiquidity {
    #[serde(default)]
    usd: Option<f64>,
    #[serde(default)]
    base: Option<f64>,
    #[serde(default)]
    quote: Option<f64>,
}

#[derive(Debug, Deserialize, Serialize, Default)]
struct DexscreenerInfo {
    #[serde(default)]
    imageUrl: Option<String>,
    #[serde(default)]
    header: Option<String>,
    #[serde(default)]
    openGraph: Option<String>,
    #[serde(default)]
    websites: Option<Vec<DexscreenerWebsite>>,
    #[serde(default)]
    socials: Option<Vec<DexscreenerSocial>>,
}

#[derive(Debug, Deserialize, Serialize, Default)]
struct DexscreenerWebsite {
    #[serde(default)]
    label: Option<String>,
    #[serde(default)]
    url: Option<String>,
}

#[derive(Debug, Deserialize, Serialize, Default)]
struct DexscreenerSocial {
    #[serde(default)]
    r#type: Option<String>,
    #[serde(default)]
    url: Option<String>,
}

impl SnipeWorker {
    pub fn new() -> Self {
        Self {
            client: Client::new(),
            active_snipes: Arc::new(Mutex::new(HashMap::new())),
            last_poll: Arc::new(Mutex::new(Instant::now())),
        }
    }

    pub async fn start(&self) {
        println!("Starting snipe worker...");

        // Get polling interval from environment variable or use default
        let polling_interval = env::var(ENV_SNIPE_POLLING_INTERVAL)
            .ok()
            .and_then(|s| s.parse::<u64>().ok())
            .unwrap_or(DEFAULT_POLLING_INTERVAL_SECONDS);

        println!("Snipe worker polling interval: {} seconds", polling_interval);

        let client = self.client.clone();
        let active_snipes = self.active_snipes.clone();
        let last_poll = self.last_poll.clone();

        tokio::spawn(async move {
            let mut interval = interval(Duration::from_secs(polling_interval));

            loop {
                interval.tick().await;

                // Load all active snipes from the database
                match Self::load_all_active_snipes().await {
                    Ok(snipes) => {
                        // Use a scope to ensure the mutex is released as soon as possible
                        {
                            let mut snipes_map = active_snipes.lock().await;
                            *snipes_map = snipes;
                        }

                        // Get a read-only reference to the snipes map
                        let snipes_map = {
                            let guard = active_snipes.lock().await;
                            guard.clone()
                        };

                        // Check for liquidity for all active snipes
                        Self::check_liquidity_for_all_snipes(&client, &snipes_map).await;

                        // Update last poll time
                        {
                            let mut last_poll_time = last_poll.lock().await;
                            *last_poll_time = Instant::now();
                        }
                    },
                    Err(e) => {
                        println!("Error loading active snipes: {}", e);
                    }
                }
            }
        });
    }

    async fn load_all_active_snipes() -> Result<HashMap<Blockchain, Vec<Snipe>>, crate::model::BotError> {
        println!("Loading all active snipes from database...");

        let mut result = HashMap::new();

        // Load snipes for each blockchain
        for blockchain in [Blockchain::BSC, Blockchain::SOL, Blockchain::ETH, Blockchain::BASE].iter() {
            let snipes = DbService::get_all_pending_snipes(blockchain).await?;
            println!("Loaded {} pending snipes for {}", snipes.len(), blockchain.as_str());
            result.insert(blockchain.clone(), snipes);
        }

        Ok(result)
    }

    async fn check_liquidity_for_all_snipes(client: &Client, snipes_map: &HashMap<Blockchain, Vec<Snipe>>) {
        println!("Checking liquidity for all active snipes...");

        // Get max tokens per request from environment variable or use default
        let max_tokens_per_request = env::var(ENV_SNIPE_MAX_TOKENS_PER_REQUEST)
            .ok()
            .and_then(|s| s.parse::<usize>().ok())
            .unwrap_or(DEFAULT_MAX_TOKENS_PER_REQUEST);

        // Get Dexscreener API URL from environment variable or use default
        let dexscreener_api_url = env::var(ENV_DEXSCREENER_API_URL)
            .unwrap_or_else(|_| DEFAULT_DEXSCREENER_API_URL.to_string());

        for (blockchain, snipes) in snipes_map.iter() {
            if snipes.is_empty() {
                continue;
            }

            println!("Checking {} snipes for {}", snipes.len(), blockchain.as_str());

            // Group snipes by user to avoid duplicate checks and reduce API calls
            let mut user_token_map: HashMap<ObjectId, HashSet<String>> = HashMap::new();

            for snipe in snipes {
                user_token_map
                    .entry(snipe.user_id)
                    .or_insert_with(HashSet::new)
                    .insert(snipe.contract_address.clone());
            }

            // Process tokens in batches to stay within API limits
            for (user_id, tokens) in user_token_map.iter() {
                let tokens_vec: Vec<&String> = tokens.iter().collect();

                // Use chunks to process tokens in batches
                for chunk in tokens_vec.chunks(max_tokens_per_request) {
                    let chain_id = match blockchain {
                        Blockchain::BSC => "bsc",
                        Blockchain::SOL => "solana",
                        Blockchain::ETH => "ethereum",
                        Blockchain::BASE => "base",
                    };

                    // Create comma-separated list of token addresses
                    let token_list = chunk.iter().map(|s| s.as_str()).collect::<Vec<&str>>().join(",");

                    // Build the URL
                    let url = format!("{}/{}/{}", dexscreener_api_url, chain_id, token_list);

                    // Check for liquidity without retries since our check_dexscreener function now handles errors gracefully
                    match Self::check_dexscreener(client, &url).await {
                        Ok(tokens_with_liquidity) => {
                            // Process tokens with liquidity
                            for token_address in tokens_with_liquidity {
                                println!("Found liquidity for token: {} on {}", token_address, blockchain.as_str());

                                // Execute the snipe for this token
                                // Convert ObjectId to i64 by finding the user
                                if let Ok(chat_id) = Self::get_chat_id_from_object_id(user_id).await {
                                    if let Err(e) = Self::execute_snipe(chat_id, blockchain, &token_address).await {
                                        println!("Error executing snipe: {}", e);
                                    }
                                } else {
                                    println!("Error: Could not find user with ObjectId: {}", user_id);
                                }
                            }
                        },
                        Err(e) => {
                            // This should rarely happen now since check_dexscreener handles most errors internally
                            println!("Unexpected error checking Dexscreener for {}: {}", blockchain.as_str(), e);
                        }
                    }

                    // Add a small delay between batches to avoid rate limiting
                    tokio::time::sleep(Duration::from_millis(100)).await;
                }
            }
        }
    }

    async fn check_dexscreener(client: &Client, url: &str) -> Result<Vec<String>, reqwest::Error> {
        println!("Checking Dexscreener API: {}", url);

        // Make the API request
        let response = match client.get(url).send().await {
            Ok(resp) => resp,
            Err(e) => {
                // Only log the error, don't show retry messages for network errors
                println!("Network error checking Dexscreener: {}", e);
                return Ok(Vec::new()); // Return empty vector instead of error
            }
        };

        // Get response text first for better error handling
        let response_text = match response.text().await {
            Ok(text) => text,
            Err(e) => {
                println!("Error getting response text from Dexscreener: {}", e);
                return Ok(Vec::new());
            }
        };

        // Log response for debugging
        println!("Dexscreener response (first 200 chars): {}",
            if response_text.len() > 200 {
                &response_text[..200]
            } else {
                &response_text
            });

        // Parse the JSON response
        let pairs: DexscreenerResponse = match serde_json::from_str(&response_text) {
            Ok(data) => data,
            Err(e) => {
                println!("Error parsing Dexscreener response: {}", e);
                println!("Full response text: {}", response_text);
                return Ok(Vec::new()); // Return empty vector instead of error
            }
        };

        let mut tokens_with_liquidity = Vec::new();

        for pair in pairs {
            // Log detailed information about each pair
            println!("Processing pair: {} on {} ({})",
                pair.baseToken.symbol,
                pair.chainId,
                pair.dexId
            );

            // Log price information if available
            if let Some(price_usd) = &pair.priceUsd {
                println!("  Price USD: ${}", price_usd);
            }
            if let Some(price_native) = &pair.priceNative {
                println!("  Price Native: {}", price_native);
            }

            // Log market cap and FDV if available
            if let Some(market_cap) = pair.marketCap {
                println!("  Market Cap: ${:.2}", market_cap);
            }
            if let Some(fdv) = pair.fdv {
                println!("  FDV: ${:.2}", fdv);
            }

            // Log volume information if available
            if let Some(volume) = &pair.volume {
                if let Some(vol_24h) = volume.h24 {
                    println!("  Volume 24h: ${:.2}", vol_24h);
                }
                if let Some(vol_1h) = volume.h1 {
                    println!("  Volume 1h: ${:.2}", vol_1h);
                }
            }

            // Log transaction counts if available
            if let Some(txns) = &pair.txns {
                if let Some(txn_24h) = &txns.h24 {
                    if let (Some(buys), Some(sells)) = (txn_24h.buys, txn_24h.sells) {
                        println!("  Transactions 24h: {} buys, {} sells", buys, sells);
                    }
                }
            }

            // Log price changes if available
            if let Some(price_change) = &pair.priceChange {
                if let Some(change_24h) = price_change.h24 {
                    println!("  Price Change 24h: {:.2}%", change_24h);
                }
                if let Some(change_1h) = price_change.h1 {
                    println!("  Price Change 1h: {:.2}%", change_1h);
                }
            }

            // Check liquidity and add to results if sufficient
            if let Some(liquidity) = &pair.liquidity {
                if let Some(usd) = liquidity.usd {
                    println!("  Liquidity USD: ${:.2}", usd);
                    if usd > 0.0 {
                        // Token has liquidity
                        println!("  ✓ Token has liquidity - adding to snipe candidates");
                        tokens_with_liquidity.push(pair.baseToken.address);
                    } else {
                        println!("  ✗ No liquidity detected");
                    }
                } else {
                    println!("  ✗ No USD liquidity data available");
                }
            } else {
                println!("  ✗ No liquidity data available");
            }
        }

        // If no tokens with liquidity were found, that's normal for tokens that haven't launched yet
        if tokens_with_liquidity.is_empty() {
            println!("No tokens with liquidity found - this is normal for tokens that haven't launched yet");
        } else {
            println!("Found {} tokens with liquidity ready for sniping", tokens_with_liquidity.len());
        }

        Ok(tokens_with_liquidity)
    }

    async fn get_chat_id_from_object_id(user_id: &ObjectId) -> Result<i64, crate::model::BotError> {
        // Use proper database lookup to find user by ObjectId
        match DbService::find_user_by_id(*user_id).await {
            Ok(Some(user)) => {
                println!("✅ Found user with chat_id: {} for ObjectId: {}", user.chat_id, user_id);
                Ok(user.chat_id)
            }
            Ok(None) => {
                println!("❌ No user found for ObjectId: {}", user_id);
                Err(crate::model::BotError::GeneralError(format!("User not found with ObjectId: {}", user_id)))
            }
            Err(e) => {
                println!("❌ Database error finding user by ObjectId {}: {}", user_id, e);
                Err(crate::model::BotError::database_error(format!("Failed to find user: {}", e)))
            }
        }
    }

    async fn execute_snipe(user_id: i64, blockchain: &Blockchain, token_address: &str) -> Result<(), crate::model::BotError> {
        println!("Executing snipe for user {} on token {} ({})", user_id, token_address, blockchain.as_str());

        // Get all snipes for this user, blockchain, and token
        let snipes = DbService::find_snipes_by_user_blockchain_and_token(user_id, blockchain.clone(), token_address).await?;

        for mut snipe in snipes {
            // Update snipe status to executed
            snipe.status = SnipeStatus::Executed;
            snipe.updated_at = chrono::Utc::now();

            // Save the updated snipe
            DbService::save_snipe(&mut snipe).await?;

            // Execute actual token purchase based on blockchain
            match Self::execute_token_purchase(&snipe, blockchain, token_address).await {
                Ok(transaction_hash) => {
                    println!("Snipe executed successfully for token: {} ({}), tx: {}",
                             token_address, blockchain.as_str(), transaction_hash);

                    // Update snipe with transaction hash
                    snipe.transaction_hash = Some(transaction_hash);
                    snipe.status = SnipeStatus::Completed;
                    DbService::save_snipe(&mut snipe).await?;
                }
                Err(e) => {
                    println!("Failed to execute snipe for token: {} ({}): {}",
                             token_address, blockchain.as_str(), e);

                    // Mark snipe as failed
                    snipe.status = SnipeStatus::Failed;
                    DbService::save_snipe(&mut snipe).await?;
                }
            }
        }

        Ok(())
    }

    /// Execute actual token purchase for a snipe
    async fn execute_token_purchase(
        snipe: &Snipe,
        blockchain: &Blockchain,
        token_address: &str,
    ) -> Result<String, crate::model::BotError> {
        use crate::service::bot_service::BotService;
        use solana_sdk::{pubkey::Pubkey, signature::Keypair};
        use std::str::FromStr;

        println!("Executing token purchase for snipe: amount={}, slippage={}, blockchain={}",
                 snipe.amount, snipe.slippage, blockchain.as_str());

        // Get bot service instance
        let bot_service = BotService::new()?;

        match blockchain {
            Blockchain::SOL => {
                // Get user's Solana wallet
                let user_wallets = crate::service::db_service::DbService::get_user_wallets(snipe.user_id).await?
                    .ok_or_else(|| crate::model::BotError::wallet_error("No wallets found for user".to_string()))?;
                let solana_wallet = &user_wallets.sol_wallet;

                // Parse private key and create keypair
                let keypair = Keypair::from_base58_string(&solana_wallet.private_key);

                // Parse token mint address
                let token_mint = Pubkey::from_str(token_address)
                    .map_err(|e| crate::model::BotError::validation_error(format!("Invalid token address: {}", e)))?;

                // Convert amount from Decimal128 to u64 (lamports)
                let amount_f64 = snipe.amount.to_string().parse::<f64>()
                    .map_err(|e| crate::model::BotError::validation_error(format!("Invalid amount: {}", e)))?;
                let sol_amount = (amount_f64 * 1_000_000_000.0) as u64; // Convert SOL to lamports

                // Convert slippage from Decimal128 to u32 (basis points)
                let slippage_f64 = snipe.slippage.to_string().parse::<f64>()
                    .map_err(|e| crate::model::BotError::validation_error(format!("Invalid slippage: {}", e)))?;
                let slippage_bps = (slippage_f64 * 100.0) as u32; // Convert percentage to basis points

                // Execute the buy order
                let swap_result = bot_service.solana_trader_service()
                    .buy_token_with_sol(&keypair, &token_mint, sol_amount, slippage_bps, Some(snipe.user_id.to_string().parse::<i64>().unwrap_or(0)))
                    .await
                    .map_err(|e| crate::model::BotError::trading_error(format!("Solana buy failed: {}", e)))?;

                Ok(swap_result.signature.to_string())
            }
            Blockchain::ETH | Blockchain::BSC | Blockchain::BASE => {
                // Get user's EVM wallet for the specific blockchain
                let user_wallets = crate::service::db_service::DbService::get_user_wallets(snipe.user_id).await?
                    .ok_or_else(|| crate::model::BotError::wallet_error("No wallets found for user".to_string()))?;

                let evm_wallet = match blockchain {
                    Blockchain::ETH => &user_wallets.eth_wallet,
                    Blockchain::BSC => &user_wallets.bsc_wallet,
                    Blockchain::BASE => &user_wallets.base_wallet,
                    _ => return Err(crate::model::BotError::wallet_error(
                        format!("No {} wallet found for user", blockchain.as_str())
                    )),
                };

                // Convert amount from Decimal128 to string (wei)
                let amount_f64 = snipe.amount.to_string().parse::<f64>()
                    .map_err(|e| crate::model::BotError::validation_error(format!("Invalid amount: {}", e)))?;
                let wei_amount = (amount_f64 * 1e18) as u64; // Convert ETH to wei
                let amount_wei = wei_amount.to_string();

                // Convert slippage from Decimal128 to f64
                let slippage = snipe.slippage.to_string().parse::<f64>()
                    .map_err(|e| crate::model::BotError::validation_error(format!("Invalid slippage: {}", e)))?;

                // Get native token address for the blockchain
                let native_token = match blockchain {
                    Blockchain::ETH => "******************************************", // ETH
                    Blockchain::BSC => "******************************************", // BNB
                    Blockchain::BASE => "******************************************", // BASE
                    _ => return Err(crate::model::BotError::trading_error("Unsupported blockchain for EVM".to_string())),
                };

                // Get swap quote
                let quote = bot_service.evm_trader_service()
                    .get_swap_quote(native_token, token_address, &amount_wei, &evm_wallet.address, blockchain)
                    .await
                    .map_err(|e| crate::model::BotError::trading_error(format!("Failed to get swap quote: {}", e)))?;

                // 🚀 AUTO-TRIGGER: Execute the swap with high-performance trader
                let transaction_hash = bot_service.execute_high_performance_evm_buy(
                    &evm_wallet,
                    quote,
                    blockchain,
                    Some(snipe.user_id.to_string().parse::<i64>().unwrap_or(0))
                ).await
                .map_err(|e| crate::model::BotError::trading_error(format!("EVM buy failed: {}", e)))?;

                Ok(transaction_hash)
            }
        }
    }
}







