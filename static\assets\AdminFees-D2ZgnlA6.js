import{r as a,j as e,B as o,a as k,c as S}from"./index-dCUkEeO4.js";import{C as c}from"./Card-CLlE15Sf.js";import{adminApi as m}from"./adminApi-BFZ8qr13.js";import{F as h}from"./ArrowPathIcon-CSc3-Uxt.js";import{F as y}from"./ClockIcon-BpyFwEBR.js";import{F as g}from"./CheckCircleIcon-CWCxsrwa.js";import{F as _}from"./XCircleIcon-CX-NVp4V.js";const n=t=>t===0?"$0.00":t<.01?`$${t.toFixed(6)}`:t<1?`$${t.toFixed(4)}`:t<1e3?`$${t.toFixed(2)}`:t<1e6?`$${(t/1e3).toFixed(1)}K`:t<1e9?`$${(t/1e6).toFixed(1)}M`:`$${(t/1e9).toFixed(1)}B`,R=t=>new Date(t).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"});function L(){const[t,p]=a.useState([]),[i,w]=a.useState(null),[N,f]=a.useState(!0),[d,b]=a.useState("all"),[u,j]=a.useState(new Set);a.useEffect(()=>{x()},[d]);const x=async()=>{try{f(!0);const[s,r]=await Promise.all([m.getAdminFeeTransactions({status:d==="all"?void 0:d}),m.getAdminFeeStatistics()]);p(s.data||[]),w(r)}catch(s){console.error("Failed to load admin fee data:",s)}finally{f(!1)}},v=async s=>{try{j(r=>new Set(r).add(s)),await m.retryFailedFeeTransaction(s),await x()}catch(r){console.error("Failed to retry transaction:",r)}finally{j(r=>{const l=new Set(r);return l.delete(s),l})}},F=s=>{switch(s){case"completed":return e.jsx(g,{className:"h-4 w-4 text-green-400"});case"pending":return e.jsx(y,{className:"h-4 w-4 text-yellow-400"});case"retrying":return e.jsx(h,{className:"h-4 w-4 text-blue-400"});case"failed":return e.jsx(_,{className:"h-4 w-4 text-red-400"});default:return null}},$=s=>{switch(s){case"completed":return"bg-green-900/30 text-green-400 border border-green-500/30";case"pending":return"bg-yellow-900/30 text-yellow-400 border border-yellow-500/30";case"retrying":return"bg-blue-900/30 text-blue-400 border border-blue-500/30";case"failed":return"bg-red-900/30 text-red-400 border border-red-500/30";default:return"bg-gray-900/30 text-gray-400 border border-gray-500/30"}};return N?e.jsx("div",{className:"flex items-center justify-center h-64",children:e.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-500"})}):e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("h1",{className:"text-2xl font-bold text-white",children:"Admin Fee Management"}),e.jsxs(o,{onClick:x,variant:"outline",children:[e.jsx(h,{className:"h-4 w-4 mr-2"}),"Refresh"]})]}),i&&e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[e.jsx(c,{className:"p-6",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"flex-shrink-0",children:e.jsx(k,{className:"h-8 w-8 text-green-400"})}),e.jsxs("div",{className:"ml-4",children:[e.jsx("p",{className:"text-sm font-medium text-gray-300",children:"Total Collected"}),e.jsx("p",{className:"text-2xl font-bold text-white",children:n(i.total_fees_collected)})]})]})}),e.jsx(c,{className:"p-6",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"flex-shrink-0",children:e.jsx(y,{className:"h-8 w-8 text-yellow-400"})}),e.jsxs("div",{className:"ml-4",children:[e.jsx("p",{className:"text-sm font-medium text-gray-300",children:"Pending"}),e.jsx("p",{className:"text-2xl font-bold text-white",children:n(i.pending_fees)})]})]})}),e.jsx(c,{className:"p-6",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"flex-shrink-0",children:e.jsx(S,{className:"h-8 w-8 text-red-400"})}),e.jsxs("div",{className:"ml-4",children:[e.jsx("p",{className:"text-sm font-medium text-gray-300",children:"Failed"}),e.jsx("p",{className:"text-2xl font-bold text-white",children:n(i.failed_fees)})]})]})}),e.jsx(c,{className:"p-6",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"flex-shrink-0",children:e.jsx(g,{className:"h-8 w-8 text-green-400"})}),e.jsxs("div",{className:"ml-4",children:[e.jsx("p",{className:"text-sm font-medium text-gray-300",children:"Success Rate"}),e.jsxs("p",{className:"text-2xl font-bold text-white",children:[i.success_rate.toFixed(1),"%"]})]})]})})]}),e.jsx("div",{className:"flex space-x-1 bg-gray-800/50 p-1 rounded-lg",children:["all","pending","completed","failed","retrying"].map(s=>e.jsx("button",{onClick:()=>b(s),className:`px-4 py-2 text-sm font-medium rounded-md transition-colors ${d===s?"bg-indigo-600 text-white":"text-gray-300 hover:text-white hover:bg-gray-700"}`,children:s.charAt(0).toUpperCase()+s.slice(1)},s))}),e.jsx(c,{className:"p-6",children:e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:"min-w-full divide-y divide-white/10",children:[e.jsx("thead",{children:e.jsxs("tr",{children:[e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider",children:"Transaction"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider",children:"Type"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider",children:"Amount"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider",children:"Blockchain"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider",children:"Status"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider",children:"Created"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider",children:"Actions"})]})}),e.jsx("tbody",{className:"divide-y divide-white/10",children:t.map(s=>e.jsxs("tr",{className:"hover:bg-white/5",children:[e.jsxs("td",{className:"px-6 py-4 whitespace-nowrap",children:[e.jsx("div",{className:"text-sm text-white font-mono",children:s.id}),e.jsxs("div",{className:"text-xs text-gray-400",children:["User: ",s.user_id]})]}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsx("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${s.transaction_type==="BuyFee"?"bg-green-900/30 text-green-400":"bg-red-900/30 text-red-400"}`,children:s.transaction_type==="BuyFee"?"Buy Fee":"Sell Fee"})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsxs("div",{className:"text-sm text-white",children:[n(s.fee_amount)," ",s.fee_token_symbol]})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsx("span",{className:"text-sm text-gray-300",children:s.blockchain.toUpperCase()})}),e.jsxs("td",{className:"px-6 py-4 whitespace-nowrap",children:[e.jsxs("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${$(s.status)}`,children:[F(s.status),e.jsx("span",{className:"ml-1",children:s.status.charAt(0).toUpperCase()+s.status.slice(1)})]}),s.retry_count>0&&e.jsxs("div",{className:"text-xs text-gray-400 mt-1",children:["Retries: ",s.retry_count]})]}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-300",children:R(s.created_at*1e3)}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:e.jsxs("div",{className:"flex space-x-2",children:[s.status==="failed"&&e.jsx(o,{size:"sm",variant:"outline",onClick:()=>v(s.id),disabled:u.has(s.id),children:u.has(s.id)?e.jsx(h,{className:"h-4 w-4 animate-spin"}):"Retry"}),s.transaction_hash&&e.jsx(o,{size:"sm",variant:"outline",onClick:()=>{const l={eth:"https://etherscan.io/tx/",bsc:"https://bscscan.com/tx/",base:"https://basescan.org/tx/",sol:"https://solscan.io/tx/"}[s.blockchain.toLowerCase()];l&&window.open(`${l}${s.transaction_hash}`,"_blank")},children:"View"})]})})]},s.id))})]})})})]})}export{L as default};
