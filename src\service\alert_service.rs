use teloxide::types::{ParseM<PERSON>, ChatId};
use teloxide::prelude::Requester;
use teloxide::payloads::SendMessageSetters;
use crate::model::BotError;
use crate::service::BotService;
use std::sync::Arc;
use tokio::sync::RwLock;
use std::collections::HashMap;
use std::time::{Duration, Instant};

// Alert types
#[derive(Debug, Clone, PartialEq)]
pub enum AlertType {
    PriceAlert,
    SecurityAlert,
    TransactionAlert,
    SystemAlert,
}

// Alert data structure
#[derive(Debug, Clone)]
pub struct Alert {
    pub message: String,
    pub alert_type: AlertType,
    pub timestamp: Instant,
    pub is_read: bool,
}

// Alert service to manage user alerts
pub struct AlertService {
    alerts: Arc<RwLock<HashMap<i64, Vec<Alert>>>>,
    cooldowns: Arc<RwLock<HashMap<i64, Instant>>>,
    alert_bot: teloxide::Bot,
}

impl AlertService {
    pub fn new() -> Self {
        let config = crate::config::AppConfig::get();
        let alert_bot = teloxide::Bot::new(&config.alert_bot_token);

        Self {
            alerts: Arc::new(RwLock::new(HashMap::new())),
            cooldowns: Arc::new(RwLock::new(HashMap::new())),
            alert_bot,
        }
    }

    // Add an alert for a user
    pub async fn add_alert(&self, user_id: i64, message: String, alert_type: AlertType) {
        let alert = Alert {
            message,
            alert_type,
            timestamp: Instant::now(),
            is_read: false,
        };

        let mut alerts = self.alerts.write().await;
        alerts.entry(user_id).or_insert_with(Vec::new).push(alert);
    }

    // Get all unread alerts for a user
    pub async fn get_unread_alerts(&self, user_id: i64) -> Vec<Alert> {
        let alerts = self.alerts.read().await;
        if let Some(user_alerts) = alerts.get(&user_id) {
            user_alerts.iter()
                .filter(|alert| !alert.is_read)
                .cloned()
                .collect()
        } else {
            Vec::new()
        }
    }

    // Mark alerts as read
    pub async fn mark_as_read(&self, user_id: i64) {
        let mut alerts = self.alerts.write().await;
        if let Some(user_alerts) = alerts.get_mut(&user_id) {
            for alert in user_alerts.iter_mut() {
                alert.is_read = true;
            }
        }
    }

    // Send a silent alert to a user
    pub async fn send_silent_alert(
        &self,
        _bot_service: &BotService,
        user_id: i64,
        message: &str,
        alert_type: AlertType,
    ) -> Result<(), BotError> {
        // Check cooldown to prevent spam
        let can_send = {
            let cooldowns = self.cooldowns.read().await;
            if let Some(last_sent) = cooldowns.get(&user_id) {
                // Allow one alert per minute per user
                last_sent.elapsed() > Duration::from_secs(60)
            } else {
                true
            }
        };

        if !can_send {
            return Ok(());
        }

        // Update cooldown
        {
            let mut cooldowns = self.cooldowns.write().await;
            cooldowns.insert(user_id, Instant::now());
        }

        // Add to alerts collection
        self.add_alert(user_id, message.to_string(), alert_type.clone()).await;

        // Create alert icon based on type
        let icon = match alert_type {
            AlertType::PriceAlert => "💰",
            AlertType::SecurityAlert => "🔒",
            AlertType::TransactionAlert => "💸",
            AlertType::SystemAlert => "⚙️",
        };

        // Format the alert message
        let alert_message = format!(
            "{} <b>Alert</b>\n\n{}",
            icon, message
        );

        // Send as a silent notification using the alert bot
        self.alert_bot
            .send_message(ChatId(user_id), alert_message)
            .parse_mode(ParseMode::Html)
            .disable_notification(true) 
            .await?;

        Ok(())
    }

    // Broadcast an alert to multiple users
    pub async fn broadcast_alert(
        &self,
        bot_service: &BotService,
        user_ids: &[i64],
        message: &str,
        alert_type: AlertType,
    ) -> Result<(), BotError> {
        for &user_id in user_ids {
            let _ = self.send_silent_alert(bot_service, user_id, message, alert_type.clone()).await;
        }
        Ok(())
    }

    pub async fn cleanup_old_alerts(&self) {
        let mut alerts = self.alerts.write().await;
        for user_alerts in alerts.values_mut() {
            user_alerts.retain(|alert| {
                alert.timestamp.elapsed() < Duration::from_secs(7 * 24 * 60 * 60)
            });
        }
    }
}
