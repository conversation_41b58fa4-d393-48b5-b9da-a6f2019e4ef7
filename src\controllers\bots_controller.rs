use axum::{
    routing::{get, post, put, delete},
    Router,
    extract::{Json, Path, Query, State},
    http::{StatusCode, HeaderMap},
    response::IntoResponse,
};
use serde::{Deserialize, Serialize};
use mongodb::{
    bson::{doc, oid::ObjectId},
    options::{FindOptions, AggregateOptions},
    error::Error as MongoError,
};
use futures_util::TryStreamExt;
use std::{
    sync::Arc,
    collections::HashMap,
    time::{SystemTime, UNIX_EPOCH, Duration},
};
use tokio::time::timeout;
use tracing::{error, warn, info, debug};
use anyhow::{Result, Context, anyhow};
use crate::model::{Bot, BotType, BotStatus, BotConfig, BotError};
use crate::service::db_service::DbService;
use crate::config::AppConfig;
use crate::controllers::auth_controller::Claims;
use jsonwebtoken::{decode, DecodingKey, Validation, Algorithm};

// Production constants
const DB_OPERATION_TIMEOUT: Duration = Duration::from_secs(30);
const MAX_BOTS_PER_PAGE: i64 = 50;
const DEFAULT_BOTS_PER_PAGE: i64 = 10;
const MAX_BOT_NAME_LENGTH: usize = 100;
const MAX_BOT_DESCRIPTION_LENGTH: usize = 500;

#[derive(Debug, thiserror::Error)]
pub enum BotsError {
    #[error("Database operation failed: {0}")]
    DatabaseError(#[from] MongoError),
    #[error("Bot not found: {0}")]
    BotNotFound(String),
    #[error("Invalid input: {0}")]
    InvalidInput(String),
    #[error("Authorization failed: {0}")]
    AuthorizationFailed(String),
    #[error("Operation timeout")]
    Timeout,
    #[error("Bot operation failed: {0}")]
    BotOperationError(String),
    #[error("Validation failed: {0}")]
    ValidationError(String),
}

// Helper type for unified responses
type ApiResult<T> = Result<(StatusCode, Json<T>), (StatusCode, Json<serde_json::Value>)>;

#[derive(Debug, Deserialize)]
pub struct CreateBotRequest {
    pub name: String,
    pub description: String,
    pub bot_type: BotType,
    pub config: Option<BotConfig>,
}

#[derive(Debug, Deserialize)]
pub struct UpdateBotRequest {
    pub name: Option<String>,
    pub description: Option<String>,
    pub status: Option<BotStatus>,
    pub config: Option<BotConfig>,
}

#[derive(Debug, Deserialize)]
pub struct BotStatsRequest {
    pub transactions: i64,
    pub volume: f64,
    pub success_rate: f64,
    pub response_time: f64,
}

#[derive(Debug, Serialize)]
pub struct BotResponse {
    pub id: String,
    pub name: String,
    pub description: String,
    pub bot_type: String,
    pub status: String,
    pub config: BotConfig,
    pub total_users: i64,
    pub active_users: i64,
    pub total_transactions: i64,
    pub total_volume: f64,
    pub success_rate: f64,
    pub average_response_time: f64,
    pub uptime_percentage: f64,
    pub last_error: Option<String>,
    pub version: String,
    pub created_at: u64,
    pub updated_at: u64,
    pub last_restart: Option<u64>,
}

#[derive(Debug, Serialize)]
pub struct BotListResponse {
    pub bots: Vec<BotResponse>,
    pub total: i64,
    pub page: i64,
    pub per_page: i64,
}

#[derive(Debug, Deserialize)]
pub struct BotQueryParams {
    pub page: Option<i64>,
    pub per_page: Option<i64>,
    pub bot_type: Option<String>,
    pub status: Option<String>,
    pub search: Option<String>,
}

pub struct BotsController {
    config: Arc<AppConfig>,
    jwt_secret: String,
}

impl BotsController {
    pub fn new(config: Arc<AppConfig>) -> Self {
        let jwt_secret = std::env::var("JWT_SECRET")
            .context("JWT_SECRET environment variable is required")
            .expect("Critical configuration missing");

        Self { config, jwt_secret }
    }

    /// Verify authentication header and extract claims
    async fn verify_auth_header(&self, headers: &HeaderMap) -> Option<Claims> {
        if let Some(auth_header) = headers.get("authorization") {
            if let Ok(auth_str) = auth_header.to_str() {
                if let Some(token) = auth_str.strip_prefix("Bearer ") {
                    let validation = Validation::new(Algorithm::HS256);
                    if let Ok(token_data) = decode::<Claims>(
                        token,
                        &DecodingKey::from_secret(self.jwt_secret.as_ref()),
                        &validation,
                    ) {
                        return Some(token_data.claims);
                    }
                }
            }
        }
        None
    }

    /// Validate bot creation/update input
    fn validate_bot_input(&self, name: &str, description: &str) -> Result<(), BotsError> {
        if name.trim().is_empty() {
            return Err(BotsError::InvalidInput("Bot name cannot be empty".to_string()));
        }

        if name.len() > MAX_BOT_NAME_LENGTH {
            return Err(BotsError::InvalidInput(
                format!("Bot name too long (max {} characters)", MAX_BOT_NAME_LENGTH)
            ));
        }

        if description.len() > MAX_BOT_DESCRIPTION_LENGTH {
            return Err(BotsError::InvalidInput(
                format!("Bot description too long (max {} characters)", MAX_BOT_DESCRIPTION_LENGTH)
            ));
        }

        // Check for potentially dangerous characters
        if name.contains(['<', '>', '"', '\'', '&']) {
            return Err(BotsError::InvalidInput("Bot name contains invalid characters".to_string()));
        }

        Ok(())
    }

    pub fn create_router(&self) -> Router {
        let controller = Arc::new(self.clone());
        let routes = &self.config.api_routes;

        Router::new()
            .route(&routes.bots_base, get(Self::get_bots))
            .route(&routes.bots_base, post(Self::create_bot))
            .route(&format!("{}/:id", routes.bots_base), get(Self::get_bot))
            .route(&format!("{}/:id", routes.bots_base), put(Self::update_bot))
            .route(&format!("{}/:id", routes.bots_base), delete(Self::delete_bot))
            .route(&format!("{}/:id{}", routes.bots_base, routes.bot_start), post(Self::start_bot))
            .route(&format!("{}/:id{}", routes.bots_base, routes.bot_stop), post(Self::stop_bot))
            .route(&format!("{}/:id{}", routes.bots_base, routes.bot_restart), post(Self::restart_bot))
            .route(&format!("{}/:id{}", routes.bots_base, routes.bot_stats), put(Self::update_bot_stats))
            .route(&routes.bots_analytics, get(Self::get_bots_analytics))
            .with_state(controller)
    }

    async fn get_bots(
        State(controller): State<Arc<BotsController>>,
        headers: HeaderMap,
        Query(params): Query<BotQueryParams>,
    ) -> impl IntoResponse {
        let start_time = SystemTime::now();

        // Verify authentication
        let claims = match controller.verify_auth_header(&headers).await {
            Some(claims) => claims,
            None => {
                warn!("Unauthorized bots list access attempt");
                return (StatusCode::UNAUTHORIZED, Json(serde_json::json!({
                    "error": "Unauthorized access",
                    "code": "AUTH_REQUIRED"
                }))).into_response();
            }
        };

        info!("Bots list requested by admin: {}", claims.username);

        // Validate and sanitize parameters
        let page = params.page.unwrap_or(1).max(1);
        let per_page = params.per_page.unwrap_or(DEFAULT_BOTS_PER_PAGE).min(MAX_BOTS_PER_PAGE).max(1);
        let skip = (page - 1) * per_page;

        // Execute database operations with timeout
        let db_result = timeout(
            DB_OPERATION_TIMEOUT,
            controller.fetch_bots_with_stats(params, page, per_page, skip)
        ).await;

        match db_result {
            Ok(Ok(response)) => {
                let elapsed = start_time.elapsed().unwrap_or_default();
                info!("Bots list generated for {} in {:?}", claims.username, elapsed);
                (StatusCode::OK, Json(response)).into_response()
            }
            Ok(Err(e)) => {
                error!("Database error while fetching bots: {:?}", e);
                (StatusCode::INTERNAL_SERVER_ERROR, Json(serde_json::json!({
                    "error": "Failed to fetch bots",
                    "code": "DATABASE_ERROR"
                }))).into_response()
            }
            Err(_) => {
                error!("Timeout while fetching bots");
                (StatusCode::REQUEST_TIMEOUT, Json(serde_json::json!({
                    "error": "Request timeout",
                    "code": "TIMEOUT"
                }))).into_response()
            }
        }
    }

    async fn fetch_bots_with_stats(
        &self,
        params: BotQueryParams,
        page: i64,
        per_page: i64,
        skip: i64,
    ) -> Result<BotListResponse, BotsError> {
        let db = DbService::get_db();
        let collection = db.collection::<Bot>("bots");

        // Build filter with input sanitization
        let filter = self.build_bot_filter(&params)?;

        // Get total count and bots concurrently
        let count_future = collection.count_documents(filter.clone(), None);

        let find_options = FindOptions::builder()
            .skip(skip as u64)
            .limit(per_page)
            .sort(doc! { "created_at": -1 })
            .build();

        let bots_future = collection.find(filter, find_options);

        let (total_result, cursor_result) = tokio::try_join!(count_future, bots_future)?;
        let mut cursor = cursor_result;
        let mut bots = Vec::new();

        while let Some(bot) = cursor.try_next().await? {
            let bot_response = Self::bot_to_response(bot);
            bots.push(bot_response);
        }

        Ok(BotListResponse {
            bots,
            total: total_result as i64,
            page,
            per_page,
        })
    }

    fn build_bot_filter(&self, params: &BotQueryParams) -> Result<mongodb::bson::Document, BotsError> {
        let mut filter = doc! {};

        // Bot type filter
        if let Some(bot_type) = &params.bot_type {
            if bot_type.len() > 50 {
                return Err(BotsError::InvalidInput("Bot type name too long".to_string()));
            }
            filter.insert("bot_type", bot_type);
        }

        // Status filter
        if let Some(status) = &params.status {
            let valid_statuses = ["Active", "Inactive", "Error", "Maintenance"];
            if !valid_statuses.contains(&status.as_str()) {
                return Err(BotsError::InvalidInput("Invalid status filter".to_string()));
            }
            filter.insert("status", status);
        }

        // Search filter with sanitization
        if let Some(search) = &params.search {
            let sanitized_search = search.trim();
            if !sanitized_search.is_empty() {
                if sanitized_search.len() > 100 {
                    return Err(BotsError::InvalidInput("Search query too long".to_string()));
                }

                // Escape regex special characters
                let escaped_search = regex::escape(sanitized_search);
                filter.insert("$or", vec![
                    doc! { "name": { "$regex": escaped_search.clone(), "$options": "i" } },
                    doc! { "description": { "$regex": escaped_search, "$options": "i" } }
                ]);
            }
        }

        Ok(filter)
    }

    async fn get_bot(
        State(_controller): State<Arc<BotsController>>,
        Path(id): Path<String>,
    ) -> impl IntoResponse {
        if let Ok(object_id) = ObjectId::parse_str(&id) {
            let db = DbService::get_db();
            let collection = db.collection::<Bot>("bots");

            match collection.find_one(doc! { "_id": object_id }, None).await {
                Ok(Some(bot)) => {
                    (StatusCode::OK, Json(Self::bot_to_response(bot))).into_response()
                }
                Ok(None) => (StatusCode::NOT_FOUND, Json(serde_json::json!({
                    "error": "Bot not found"
                }))).into_response(),
                Err(_) => (StatusCode::INTERNAL_SERVER_ERROR, Json(serde_json::json!({
                    "error": "Failed to fetch bot"
                }))).into_response()
            }
        } else {
            (StatusCode::BAD_REQUEST, Json(serde_json::json!({
                "error": "Invalid bot ID"
            }))).into_response()
        }
    }

    async fn create_bot(
        State(controller): State<Arc<BotsController>>,
        headers: HeaderMap,
        Json(create_req): Json<CreateBotRequest>,
    ) -> impl IntoResponse {
        // Verify authentication
        let claims = match controller.verify_auth_header(&headers).await {
            Some(claims) => claims,
            None => {
                warn!("Unauthorized bot creation attempt");
                return (StatusCode::UNAUTHORIZED, Json(serde_json::json!({
                    "error": "Unauthorized access",
                    "code": "AUTH_REQUIRED"
                }))).into_response();
            }
        };

        info!("Bot creation requested by admin: {}", claims.username);

        // Validate input
        if let Err(validation_error) = controller.validate_bot_input(&create_req.name, &create_req.description) {
            warn!("Invalid bot creation input: {:?}", validation_error);
            return (StatusCode::BAD_REQUEST, Json(serde_json::json!({
                "error": validation_error.to_string(),
                "code": "INVALID_INPUT"
            }))).into_response();
        }

        let mut bot = Bot::new(create_req.name, create_req.description, create_req.bot_type);
        if let Some(config) = create_req.config {
            bot.config = config;
        }

        let db = DbService::get_db();
        let collection = db.collection::<Bot>("bots");

        match collection.insert_one(&bot, None).await {
            Ok(result) => {
                bot.id = Some(result.inserted_id.as_object_id().unwrap());
                info!("Bot created successfully by admin: {}", claims.username);
                (StatusCode::CREATED, Json(Self::bot_to_response(bot))).into_response()
            }
            Err(e) => {
                error!("Failed to create bot: {:?}", e);
                (StatusCode::INTERNAL_SERVER_ERROR, Json(serde_json::json!({
                    "error": "Failed to create bot",
                    "code": "DATABASE_ERROR"
                }))).into_response()
            }
        }
    }

    fn bot_to_response(bot: Bot) -> BotResponse {
        BotResponse {
            id: bot.id.unwrap().to_hex(),
            name: bot.name,
            description: bot.description,
            bot_type: format!("{:?}", bot.bot_type),
            status: format!("{:?}", bot.status),
            config: bot.config,
            total_users: bot.total_users,
            active_users: bot.active_users,
            total_transactions: bot.total_transactions,
            total_volume: bot.total_volume,
            success_rate: bot.success_rate,
            average_response_time: bot.average_response_time,
            uptime_percentage: bot.uptime_percentage,
            last_error: bot.last_error,
            version: bot.version,
            created_at: bot.created_at,
            updated_at: bot.updated_at,
            last_restart: bot.last_restart,
        }
    }

    async fn update_bot(
        State(controller): State<Arc<BotsController>>,
        Path(id): Path<String>,
        Json(update_req): Json<UpdateBotRequest>,
    ) -> impl IntoResponse {
        if let Ok(object_id) = ObjectId::parse_str(&id) {
            let db = DbService::get_db();
            let collection = db.collection::<Bot>("bots");

            let mut update_doc = doc! {};
            if let Some(name) = update_req.name {
                update_doc.insert("name", name);
            }
            if let Some(description) = update_req.description {
                update_doc.insert("description", description);
            }
            if let Some(status) = update_req.status {
                update_doc.insert("status", format!("{:?}", status));
            }
            if let Some(config) = update_req.config {
                update_doc.insert("config", mongodb::bson::to_bson(&config).unwrap());
            }
            update_doc.insert("updated_at", std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap()
                .as_secs() as i64);

            match collection.update_one(
                doc! { "_id": object_id },
                doc! { "$set": update_doc },
                None,
            ).await {
                Ok(result) => {
                    if result.matched_count > 0 {
                        // Fetch and return updated bot
                        if let Ok(Some(bot)) = collection.find_one(doc! { "_id": object_id }, None).await {
                            (StatusCode::OK, Json(Self::bot_to_response(bot))).into_response()
                        } else {
                            (StatusCode::INTERNAL_SERVER_ERROR, Json(serde_json::json!({
                                "error": "Failed to fetch updated bot"
                            }))).into_response()
                        }
                    } else {
                        (StatusCode::NOT_FOUND, Json(serde_json::json!({
                            "error": "Bot not found"
                        }))).into_response()
                    }
                }
                Err(_) => (StatusCode::INTERNAL_SERVER_ERROR, Json(serde_json::json!({
                    "error": "Failed to update bot"
                }))).into_response()
            }
        } else {
            (StatusCode::BAD_REQUEST, Json(serde_json::json!({
                "error": "Invalid bot ID"
            }))).into_response()
        }
    }

    async fn delete_bot(
        State(controller): State<Arc<BotsController>>,
        Path(id): Path<String>,
    ) -> impl IntoResponse {
        if let Ok(object_id) = ObjectId::parse_str(&id) {
            let db = DbService::get_db();
            let collection = db.collection::<Bot>("bots");

            match collection.delete_one(doc! { "_id": object_id }, None).await {
                Ok(result) => {
                    if result.deleted_count > 0 {
                        (StatusCode::OK, Json(serde_json::json!({
                            "message": "Bot deleted successfully"
                        })))
                    } else {
                        (StatusCode::NOT_FOUND, Json(serde_json::json!({
                            "error": "Bot not found"
                        })))
                    }
                }
                Err(_) => (StatusCode::INTERNAL_SERVER_ERROR, Json(serde_json::json!({
                    "error": "Failed to delete bot"
                })))
            }
        } else {
            (StatusCode::BAD_REQUEST, Json(serde_json::json!({
                "error": "Invalid bot ID"
            })))
        }
    }

    async fn start_bot(
        State(controller): State<Arc<BotsController>>,
        headers: HeaderMap,
        Path(id): Path<String>,
    ) -> impl IntoResponse {
        // Verify authentication
        let claims = match controller.verify_auth_header(&headers).await {
            Some(claims) => claims,
            None => {
                warn!("Unauthorized bot start attempt for bot: {}", id);
                return (StatusCode::UNAUTHORIZED, Json(serde_json::json!({
                    "error": "Unauthorized access",
                    "code": "AUTH_REQUIRED"
                }))).into_response();
            }
        };

        info!("Bot start requested for bot: {} by admin: {}", id, claims.username);
        let result = controller.update_bot_status_authenticated(&id, BotStatus::Active, &claims).await;
        result.into_response()
    }

    async fn stop_bot(
        State(controller): State<Arc<BotsController>>,
        headers: HeaderMap,
        Path(id): Path<String>,
    ) -> impl IntoResponse {
        // Verify authentication
        let claims = match controller.verify_auth_header(&headers).await {
            Some(claims) => claims,
            None => {
                warn!("Unauthorized bot stop attempt for bot: {}", id);
                return (StatusCode::UNAUTHORIZED, Json(serde_json::json!({
                    "error": "Unauthorized access",
                    "code": "AUTH_REQUIRED"
                }))).into_response();
            }
        };

        info!("Bot stop requested for bot: {} by admin: {}", id, claims.username);
        let result = controller.update_bot_status_authenticated(&id, BotStatus::Inactive, &claims).await;
        result.into_response()
    }

    async fn restart_bot(
        State(controller): State<Arc<BotsController>>,
        headers: HeaderMap,
        Path(id): Path<String>,
    ) -> impl IntoResponse {
        // Verify authentication
        let claims = match controller.verify_auth_header(&headers).await {
            Some(claims) => claims,
            None => {
                warn!("Unauthorized bot restart attempt for bot: {}", id);
                return (StatusCode::UNAUTHORIZED, Json(serde_json::json!({
                    "error": "Unauthorized access",
                    "code": "AUTH_REQUIRED"
                }))).into_response();
            }
        };

        info!("Bot restart requested for bot: {} by admin: {}", id, claims.username);

        // First stop, then start
        let stop_result = controller.update_bot_status_authenticated(&id, BotStatus::Inactive, &claims).await;
        if stop_result.0 == StatusCode::OK {
            tokio::time::sleep(tokio::time::Duration::from_millis(1000)).await;
            let start_result = controller.update_bot_status_authenticated(&id, BotStatus::Active, &claims).await;
            start_result.into_response()
        } else {
            stop_result.into_response()
        }
    }

    async fn update_bot_stats(
        State(controller): State<Arc<BotsController>>,
        headers: HeaderMap,
        Path(id): Path<String>,
        Json(stats_req): Json<BotStatsRequest>,
    ) -> impl IntoResponse {
        // Verify authentication
        let claims = match controller.verify_auth_header(&headers).await {
            Some(claims) => claims,
            None => {
                warn!("Unauthorized bot stats update attempt for bot: {}", id);
                return (StatusCode::UNAUTHORIZED, Json(serde_json::json!({
                    "error": "Unauthorized access",
                    "code": "AUTH_REQUIRED"
                }))).into_response();
            }
        };

        info!("Bot stats update requested for bot: {} by admin: {}", id, claims.username);

        if let Ok(object_id) = ObjectId::parse_str(&id) {
            let db = DbService::get_db();
            let collection = db.collection::<Bot>("bots");

            let update_doc = doc! {
                "$inc": {
                    "total_transactions": stats_req.transactions,
                    "total_volume": stats_req.volume
                },
                "$set": {
                    "success_rate": stats_req.success_rate,
                    "average_response_time": stats_req.response_time,
                    "updated_at": std::time::SystemTime::now()
                        .duration_since(std::time::UNIX_EPOCH)
                        .unwrap()
                        .as_secs() as i64
                }
            };

            match collection.update_one(doc! { "_id": object_id }, update_doc, None).await {
                Ok(result) => {
                    if result.matched_count > 0 {
                        info!("Bot stats updated successfully for bot: {} by admin: {}", id, claims.username);
                        (StatusCode::OK, Json(serde_json::json!({
                            "message": "Bot stats updated successfully"
                        }))).into_response()
                    } else {
                        warn!("Bot not found for stats update: {}", id);
                        (StatusCode::NOT_FOUND, Json(serde_json::json!({
                            "error": "Bot not found"
                        }))).into_response()
                    }
                }
                Err(e) => {
                    error!("Failed to update bot stats for bot {}: {:?}", id, e);
                    (StatusCode::INTERNAL_SERVER_ERROR, Json(serde_json::json!({
                        "error": "Failed to update bot stats"
                    }))).into_response()
                }
            }
        } else {
            warn!("Invalid bot ID format for stats update: {}", id);
            (StatusCode::BAD_REQUEST, Json(serde_json::json!({
                "error": "Invalid bot ID"
            }))).into_response()
        }
    }

    async fn get_bots_analytics(
        State(controller): State<Arc<BotsController>>,
        headers: HeaderMap,
    ) -> impl IntoResponse {
        // Verify authentication
        let claims = match controller.verify_auth_header(&headers).await {
            Some(claims) => claims,
            None => {
                warn!("Unauthorized bots analytics access attempt");
                return (StatusCode::UNAUTHORIZED, Json(serde_json::json!({
                    "error": "Unauthorized access",
                    "code": "AUTH_REQUIRED"
                }))).into_response();
            }
        };

        info!("Bots analytics requested by admin: {}", claims.username);

        let db = DbService::get_db();
        let collection = db.collection::<Bot>("bots");

        // Aggregate analytics data
        let pipeline = vec![
            doc! {
                "$group": {
                    "_id": null,
                    "total_bots": { "$sum": 1 },
                    "active_bots": {
                        "$sum": {
                            "$cond": [
                                { "$eq": ["$status", "Active"] },
                                1,
                                0
                            ]
                        }
                    },
                    "total_users": { "$sum": "$total_users" },
                    "total_transactions": { "$sum": "$total_transactions" },
                    "total_volume": { "$sum": "$total_volume" },
                    "avg_success_rate": { "$avg": "$success_rate" },
                    "avg_response_time": { "$avg": "$average_response_time" }
                }
            }
        ];

        match collection.aggregate(pipeline, None).await {
            Ok(mut cursor) => {
                if let Ok(Some(result)) = cursor.try_next().await {
                    info!("Bots analytics generated for admin: {}", claims.username);
                    (StatusCode::OK, Json(result)).into_response()
                } else {
                    let empty_result = doc! {
                        "total_bots": 0,
                        "active_bots": 0,
                        "total_users": 0,
                        "total_transactions": 0,
                        "total_volume": 0.0,
                        "avg_success_rate": 0.0,
                        "avg_response_time": 0.0
                    };
                    (StatusCode::OK, Json(empty_result)).into_response()
                }
            }
            Err(e) => {
                error!("Failed to fetch bots analytics: {:?}", e);
                let error_result = doc! {
                    "error": "Failed to fetch analytics"
                };
                (StatusCode::INTERNAL_SERVER_ERROR, Json(error_result)).into_response()
            }
        }
    }

    async fn update_bot_status_authenticated(&self, id: &str, status: BotStatus, claims: &Claims) -> (StatusCode, Json<serde_json::Value>) {
        if let Ok(object_id) = ObjectId::parse_str(id) {
            let db = DbService::get_db();
            let collection = db.collection::<Bot>("bots");

            let now = std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap()
                .as_secs();

            let mut update_doc = doc! {
                "status": format!("{:?}", status),
                "updated_at": now as i64,
                "updated_by": &claims.username
            };

            if status == BotStatus::Active {
                update_doc.insert("last_restart", now as i64);
            }

            match collection.update_one(
                doc! { "_id": object_id },
                doc! { "$set": update_doc },
                None,
            ).await {
                Ok(result) => {
                    if result.matched_count > 0 {
                        let action = match status {
                            BotStatus::Active => "started",
                            BotStatus::Inactive => "stopped",
                            _ => "updated"
                        };
                        info!("Bot {} {} by admin: {}", id, action, claims.username);
                        (StatusCode::OK, Json(serde_json::json!({
                            "message": format!("Bot {} successfully", action)
                        })))
                    } else {
                        warn!("Bot not found for status update: {}", id);
                        (StatusCode::NOT_FOUND, Json(serde_json::json!({
                            "error": "Bot not found"
                        })))
                    }
                }
                Err(e) => {
                    error!("Failed to update bot status for bot {}: {:?}", id, e);
                    (StatusCode::INTERNAL_SERVER_ERROR, Json(serde_json::json!({
                        "error": "Failed to update bot status"
                    })))
                }
            }
        } else {
            warn!("Invalid bot ID format for status update: {}", id);
            (StatusCode::BAD_REQUEST, Json(serde_json::json!({
                "error": "Invalid bot ID"
            })))
        }
    }
}

impl Clone for BotsController {
    fn clone(&self) -> Self {
        Self {
            config: self.config.clone(),
            jwt_secret: self.jwt_secret.clone(),
        }
    }
}
