use std::sync::Arc;
use ethers::signers::{LocalWallet, Signer};
use ethers::core::rand::thread_rng as ethers_thread_rng;
use crate::model::{BotError, Blockchain, Wallet};

type Result<T> = std::result::Result<T, BotError>;

pub struct EvmGenService {}

impl EvmGenService {
    pub fn new() -> Arc<Self> {
        Arc::new(Self {})
    }

    /// Generates a random wallet for the specified blockchain
    pub fn generate_wallet(&self, blockchain: Blockchain) -> Result<Wallet> {
        println!("EvmGenService::generate_wallet for {:?}", blockchain);
        let start_time = std::time::Instant::now();

        match blockchain {
            Blockchain::ETH | Blockchain::BSC | Blockchain::BASE => {
                // Generate a random wallet using ethers' thread_rng - optimized version
                println!("Creating random wallet with ethers...");
                let rng_start = std::time::Instant::now();

                // Use a more efficient method to generate the wallet
                let mut rng = ethers_thread_rng();
                let wallet = LocalWallet::new(&mut rng);
                println!("LocalWallet creation took: {:?}", rng_start.elapsed());

                // Get the private key as a hex string without 0x prefix - direct conversion
                let pk_start = std::time::Instant::now();
                let private_key = hex::encode(wallet.signer().to_bytes());
                println!("Private key encoding took: {:?}", pk_start.elapsed());

                // Get the address as a checksummed string - direct format
                let addr_start = std::time::Instant::now();
                let address = format!("{:?}", wallet.address());
                println!("Address formatting took: {:?}", addr_start.elapsed());

                println!("Total EvmGenService wallet generation took: {:?}", start_time.elapsed());
                Ok(Wallet {
                    address,
                    private_key,
                    mnemonic: None,
                })
            },
            _ => Err(BotError::blockchain_error(format!("Unsupported blockchain for EvmGenService: {:?}", blockchain))),
        }
    }
}
