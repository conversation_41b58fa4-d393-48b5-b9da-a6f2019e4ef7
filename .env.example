# Telegram Bot Configuration
TELEGRAM_BOT_TOKEN=your_telegram_bot_token_here
ADMIN_BOT_TOKEN=your_admin_bot_token_here

# JWT Authentication
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production

# =============================================================================
# API ROUTES CONFIGURATION
# =============================================================================

# Authentication Routes
API_AUTH_BASE=/api/auth
API_AUTH_LOGIN=/api/auth/login
API_AUTH_LOGOUT=/api/auth/logout
API_AUTH_REFRESH=/api/auth/refresh
API_AUTH_ME=/api/auth/me
API_AUTH_CHANGE_PASSWORD=/api/auth/change-password
API_AUTH_CREATE_ADMIN=/api/auth/create-admin

# Bot Management Routes
API_BOTS_BASE=/api/bots
API_BOTS_ANALYTICS=/api/bots/analytics
API_BOT_START=/start
API_BOT_STOP=/stop
API_BOT_RESTART=/restart
API_BOT_STATS=/stats

# Admin Panel Routes
API_ADMIN_BASE=/api/admin
API_ADMIN_DASHBOARD=/api/admin/dashboard
API_ADMIN_ANALYTICS=/api/admin/analytics
API_ADMIN_BLOCKCHAIN_ANALYTICS=/api/admin/blockchain-analytics
API_ADMIN_SYSTEM_HEALTH=/api/admin/system-health
API_ADMIN_ALERTS=/api/admin/alerts
API_ADMIN_TRANSACTIONS=/api/admin/transactions
API_ADMIN_USERS=/api/admin/users
API_ADMIN_ADMINS=/api/admin/admins
API_ADMIN_SETTINGS=/api/admin/settings

# MongoDB Configuration
MONGODB_URI=mongodb://localhost:27017

# Web Server Configuration
PORT=8000
SOCKET_PORT=3000

# Network endpoints (obfuscated)
NET_ENDPOINT_A=https://bsc-mainnet.nodereal.io/v1/your_nodereal_key
NET_ENDPOINT_B=https://base-mainnet.g.alchemy.com/v2/your_alchemy_key
NET_ENDPOINT_C=https://mainnet.infura.io/v3/your_infura_key
NET_ENDPOINT_D=https://api.mainnet-beta.solana.com

# Backup endpoints (obfuscated)
NET_BACKUP_A=https://bsc-dataseed.binance.org/
NET_BACKUP_B=https://mainnet.base.org/
NET_BACKUP_C=https://ethereum.publicnode.com
NET_BACKUP_D=https://api.mainnet-beta.solana.com

# API configuration (obfuscated)
SVC_ENDPOINT_MAIN=https://api.0x.org
SVC_ENDPOINT_PATH_QUOTE=/swap/permit2/quote
SVC_ENDPOINT_PATH_PRICE=/swap/permit2/price

# Jupiter API URLs for Solana
API_URL_JUPITER_QUOTE=https://quote-api.jup.ag/v6/quote
API_URL_JUPITER_SWAP=https://quote-api.jup.ag/v6/swap
API_URL_JUPITER_BASE=https://lite-api.jup.ag/ultra/v1
API_URL_JUPITER_ORDER=/order
API_URL_JUPITER_EXECUTE=/execute
API_URL_JUPITER_BALANCES=/balances

# Dexscreener API (obfuscated)
API_DEXSCREENER_BASE=https://api.dexscreener.com/tokens/v1
API_DEXSCREENER_TOKENS=https://api.dexscreener.com/tokens/v1
API_DEXSCREENER_CHART_ETH=https://dexscreener.com/ethereum
API_DEXSCREENER_CHART_BSC=https://dexscreener.com/bsc
API_DEXSCREENER_CHART_SOL=https://dexscreener.com/solana
API_DEXSCREENER_CHART_BASE=https://dexscreener.com/base

# Blockchain explorer URLs (obfuscated)
EXPLORER_URL_ETH=https://etherscan.io
EXPLORER_URL_BSC=https://bscscan.com
EXPLORER_URL_SOL=https://solscan.io
EXPLORER_URL_BASE=https://basescan.org

# Honeypot detection APIs (obfuscated)
SEC_ENDPOINT_HP_EVM=https://api.honeypot.is/v2/IsHoneypot
SEC_ENDPOINT_HP_SOL=https://api.rugcheck.xyz/v1/tokens

# Fee collection (production-ready)
FEE_RECIPIENT_EVM=******************************************
FEE_RECIPIENT_SOL=********************************
ROUTER_CONTRACT=******************************************

# Service authentication (obfuscated)
SVC_AUTH_KEY=your_0x_api_key
SVC_AUTH_HEADER=0x-api-key
SVC_API_VERSION=v2

# Contract addresses (obfuscated)
CONTRACT_ADDR_1=******************************************
CONTRACT_ADDR_2=******************************************
CONTRACT_ADDR_3=******************************************
CONTRACT_ADDR_0=******************************************
CONTRACT_ADDR_SOL=So********************************111111112

# Network identifiers (obfuscated)
NET_ID_C=1
NET_ID_A=56
NET_ID_B=8453

# Snipe worker configuration
SNIPE_POLLING_INTERVAL=60
SNIPE_MAX_TOKENS_PER_REQUEST=30

# Clustering Configuration
ENABLE_CLUSTERING=false

# Logging Configuration
LOG_LEVEL=INFO
LOG_TO_FILE=false
LOG_FILE_PATH=logs/app.log
