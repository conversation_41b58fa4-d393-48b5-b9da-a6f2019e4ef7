#!/bin/bash

# Development Startup Script
# Starts both backend and frontend services for development

set -e

echo "Starting EasyBot Development Environment"
echo "==========================================="

# Function to check if a command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check if a port is in use
port_in_use() {
    local port=$1
    if command_exists lsof; then
        lsof -i :$port >/dev/null 2>&1
    elif command_exists netstat; then
        netstat -an | grep ":$port " >/dev/null 2>&1
    else
        # Fallback: try to connect to the port
        timeout 1 bash -c "</dev/tcp/localhost/$port" >/dev/null 2>&1
    fi
}

# Check prerequisites
echo "1. Checking Prerequisites..."
echo "---------------------------"

if ! command_exists cargo; then
    echo "❌ Rust/Cargo not found. Please install Rust: https://rustup.rs/"
    exit 1
fi
echo "✅ Rust/Cargo found"

if ! command_exists node; then
    echo "❌ Node.js not found. Please install Node.js: https://nodejs.org/"
    exit 1
fi
echo "✅ Node.js found"

if ! command_exists npm; then
    echo "❌ npm not found. Please install npm"
    exit 1
fi
echo "✅ npm found"

# Check if .env file exists
if [ ! -f ".env" ]; then
    echo "⚠️  .env file not found. Creating from .env.example..."
    if [ -f ".env.example" ]; then
        cp .env.example .env
        echo "✅ Created .env from .env.example"
    else
        echo "❌ .env.example not found. Please create .env file manually"
        exit 1
    fi
else
    echo "✅ .env file found"
fi

# Check if frontend .env exists
if [ ! -f "frontend/.env" ]; then
    echo "⚠️  Frontend .env file not found. It should have been created by the sync fixes."
    echo "Creating frontend/.env..."
    cat > frontend/.env << EOF
VITE_API_BASE_URL=http://localhost:8000/api
VITE_APP_NAME=EasyBot Admin Panel
VITE_APP_VERSION=1.0.0
EOF
    echo "✅ Created frontend/.env"
else
    echo "✅ Frontend .env file found"
fi

# Check ports
echo ""
echo "2. Checking Ports..."
echo "-------------------"

if port_in_use 8000; then
    echo "⚠️  Port 8000 is already in use. Backend might already be running."
    read -p "Continue anyway? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
else
    echo "✅ Port 8000 is available for backend"
fi

if port_in_use 5173; then
    echo "⚠️  Port 5173 is already in use. Frontend might already be running."
    read -p "Continue anyway? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
else
    echo "✅ Port 5173 is available for frontend"
fi

# Install frontend dependencies
echo ""
echo "3. Installing Frontend Dependencies..."
echo "-------------------------------------"

cd frontend
if [ ! -d "node_modules" ]; then
    echo "Installing npm packages..."
    npm install
else
    echo "✅ node_modules exists, skipping npm install"
fi
cd ..

# Build backend (optional, for faster startup)
echo ""
echo "4. Building Backend..."
echo "---------------------"
echo "Building Rust backend (this may take a moment)..."
cargo build --release

# Start services
echo ""
echo "5. Starting Services..."
echo "----------------------"

# Function to cleanup background processes
cleanup() {
    echo ""
    echo "🛑 Shutting down services..."
    if [ ! -z "$BACKEND_PID" ]; then
        kill $BACKEND_PID 2>/dev/null || true
    fi
    if [ ! -z "$FRONTEND_PID" ]; then
        kill $FRONTEND_PID 2>/dev/null || true
    fi
    exit 0
}

# Set up signal handlers
trap cleanup SIGINT SIGTERM

# Start backend
echo "Starting backend on port 8000..."
cargo run --release &
BACKEND_PID=$!

# Wait a moment for backend to start
sleep 3

# Start frontend
echo "Starting frontend on port 5173..."
cd frontend
npm run dev &
FRONTEND_PID=$!
cd ..

# Wait a moment for frontend to start
sleep 3

echo ""
echo "🎉 Development Environment Started!"
echo "=================================="
echo "Backend:  http://localhost:8000"
echo "Frontend: http://localhost:5173"
echo "Health:   http://localhost:8000/health"
echo ""
echo "📋 Available Commands:"
echo "- Ctrl+C to stop both services"
echo "- Check logs above for any startup errors"
echo ""
echo "🔧 Troubleshooting:"
echo "- If backend fails: Check .env file and database connection"
echo "- If frontend fails: Check frontend/.env and npm dependencies"
echo "- If ports conflict: Stop other services using these ports"
echo ""
echo "Press Ctrl+C to stop all services..."

# Wait for user to stop services
wait
