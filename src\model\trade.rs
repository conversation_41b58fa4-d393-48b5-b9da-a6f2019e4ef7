use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};
use mongodb::bson::{oid::ObjectId, Decimal128, DateTime as BsonDateTime};
use crate::model::blockchain::Blockchain;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum TradeStatus {
    #[serde(rename = "pending")]
    Pending,
    #[serde(rename = "completed")]
    Completed,
    #[serde(rename = "failed")]
    Failed,
    #[serde(rename = "cancelled")]
    Cancelled,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Trade {
    #[serde(rename = "_id", skip_serializing_if = "Option::is_none")]
    pub id: Option<ObjectId>,
    pub user_id: ObjectId,
    // User information for frontend display
    pub user_first_name: Option<String>,
    pub user_username: Option<String>,
    pub blockchain: Blockchain,
    pub token_address: String,
    pub token_symbol: String,
    pub trade_type: String, // "buy" or "sell"

    // Enhanced amount tracking
    pub native_token_amount: Option<f64>, // Amount of native token (ETH, SOL, BNB) involved
    pub token_amount: Option<f64>, // Amount of the specific token involved
    pub native_token_symbol: Option<String>, // ETH, SOL, BNB, etc.

    // For buy transactions: native_token_amount = amount spent, token_amount = tokens received
    // For sell transactions: token_amount = tokens sold, native_token_amount = native tokens received

    pub status: Option<String>,
    pub timestamp: i64,
    pub gas_fee: Option<f64>,
    pub hash: Option<String>,
    pub block_number: Option<u64>,
    pub error_message: Option<String>,

    // Admin fee tracking
    pub admin_fee_transaction_id: Option<ObjectId>,
    pub admin_fee_amount: Option<f64>,
    pub admin_fee_percentage: Option<f64>,
    pub admin_fee_status: Option<String>, // "pending", "completed", "failed", "retrying"
    pub admin_fee_collection_method: Option<String>, // "native_token", "received_token", "smart_collection"
    pub admin_fee_token_symbol: Option<String>, // Symbol of token used for admin fee
    pub admin_fee_token_address: Option<String>, // Address of token used for admin fee

    // Legacy fields for backward compatibility
    pub amount_out: Option<f64>, // Deprecated - use native_token_amount or token_amount instead
    pub contract_address: Option<String>,
    pub token_name: Option<String>,
    pub amount: Option<Decimal128>,
    pub price: Option<Decimal128>,
    pub transaction_hash: Option<String>,
    pub created_at: Option<BsonDateTime>,
    pub updated_at: Option<BsonDateTime>,
}
