import { useState, useEffect, useCallback, useRef } from 'react';
import Card from '../../components/ui/Card';
import Button from '../../components/ui/Button';
import { 
  UsersIcon, 
  CurrencyDollarIcon,
  ChartBarIcon,
  ArrowUpIcon,
  ArrowDownIcon,
} from '@heroicons/react/24/outline';
import { Line, Bar, Pie } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  Tooltip,
  Legend,
  Filler,
} from 'chart.js';
import { adminApi, DashboardStats } from '../../services/adminApi';

// Register ChartJS components
ChartJS.register(
  CategoryScale, 
  LinearScale, 
  PointElement, 
  LineElement, 
  BarElement,
  ArcElement,
  Title, 
  Tooltip, 
  Legend, 
  Filler
);

export default function AdminAnalytics() {
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [timeRange, setTimeRange] = useState('7d');
  const loadingRef = useRef(false);

  const fetchData = useCallback(async () => {
    if (loadingRef.current) {
      console.log('⏸️ Skipping fetch - already loading');
      return;
    }
      try {
        loadingRef.current = true;
        setLoading(true);
        const data = await adminApi.getDashboardStats();
        setStats(data);
      } catch (error) {
        console.error('Error fetching analytics data:', error);
      } finally {
        loadingRef.current = false;
        setLoading(false);
      }
    }, [timeRange]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);
  
  if (loading || !stats) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-red-500"></div>
      </div>
    );
  }
  
  // Revenue chart data
  const revenueChartData = {
    labels: ['Day 1', 'Day 2', 'Day 3', 'Day 4', 'Day 5', 'Day 6', 'Day 7'],
    datasets: [
      {
        label: 'Revenue ($)',
        data: [
          stats.transaction_stats.fee_collected_7d / 7 * 0.8,
          stats.transaction_stats.fee_collected_7d / 7 * 1.1,
          stats.transaction_stats.fee_collected_7d / 7 * 0.9,
          stats.transaction_stats.fee_collected_7d / 7 * 1.2,
          stats.transaction_stats.fee_collected_7d / 7 * 1.0,
          stats.transaction_stats.fee_collected_7d / 7 * 1.3,
          stats.transaction_stats.fee_collected_7d / 7 * 1.1,
        ],
        fill: true,
        backgroundColor: 'rgba(220, 38, 38, 0.2)',
        borderColor: 'rgba(220, 38, 38, 1)',
        borderWidth: 2,
        tension: 0.4,
        pointBackgroundColor: 'rgba(220, 38, 38, 1)',
        pointBorderColor: '#fff',
        pointHoverBackgroundColor: '#fff',
        pointHoverBorderColor: 'rgba(220, 38, 38, 1)',
        pointRadius: 4,
      },
    ],
  };
  
  // User signups chart data
  const userSignupsChartData = {
    labels: ['Day 1', 'Day 2', 'Day 3', 'Day 4', 'Day 5', 'Day 6', 'Day 7'],
    datasets: [
      {
        label: 'New Users',
        data: [
          stats.user_stats.new_users_7d / 7 * 0.9,
          stats.user_stats.new_users_7d / 7 * 1.2,
          stats.user_stats.new_users_7d / 7 * 0.8,
          stats.user_stats.new_users_7d / 7 * 1.1,
          stats.user_stats.new_users_7d / 7 * 1.3,
          stats.user_stats.new_users_7d / 7 * 0.9,
          stats.user_stats.new_users_7d / 7 * 1.0,
        ],
        backgroundColor: 'rgba(220, 38, 38, 0.7)',
        borderColor: 'rgba(220, 38, 38, 1)',
        borderWidth: 1,
        borderRadius: 4,
      },
    ],
  };
  
  // Blockchain distribution chart data
  const blockchainData = {
    labels: Object.keys(stats.blockchain_distribution),
    datasets: [
      {
        data: Object.values(stats.blockchain_distribution),
        backgroundColor: [
          'rgba(220, 38, 38, 0.7)',
          'rgba(79, 70, 229, 0.7)',
          'rgba(16, 185, 129, 0.7)',
          'rgba(245, 158, 11, 0.7)',
        ],
        borderColor: [
          'rgba(220, 38, 38, 1)',
          'rgba(79, 70, 229, 1)',
          'rgba(16, 185, 129, 1)',
          'rgba(245, 158, 11, 1)',
        ],
        borderWidth: 1,
      },
    ],
  };
  
  // Chart options
  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top' as const,
        labels: {
          color: 'white',
        },
      },
    },
    scales: {
      x: {
        grid: {
          color: 'rgba(255, 255, 255, 0.1)',
        },
        ticks: {
          color: 'rgba(255, 255, 255, 0.7)',
        },
      },
      y: {
        grid: {
          color: 'rgba(255, 255, 255, 0.1)',
        },
        ticks: {
          color: 'rgba(255, 255, 255, 0.7)',
        },
      },
    },
  };
  
  const pieChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'right' as const,
        labels: {
          color: 'white',
        },
      },
    },
  };
  
  return (
    <div className="space-y-8">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-2xl font-bold text-white">Analytics Dashboard</h1>
          <p className="mt-1 text-sm text-gray-400">Detailed analytics and statistics</p>
        </div>
        <div className="flex space-x-2">
          <Button 
            variant={timeRange === '24h' ? 'glass' : 'outline'} 
            size="sm"
            onClick={() => setTimeRange('24h')}
          >
            24h
          </Button>
          <Button 
            variant={timeRange === '7d' ? 'glass' : 'outline'} 
            size="sm"
            onClick={() => setTimeRange('7d')}
          >
            7d
          </Button>
          <Button 
            variant={timeRange === '30d' ? 'glass' : 'outline'} 
            size="sm"
            onClick={() => setTimeRange('30d')}
          >
            30d
          </Button>
        </div>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="p-6 relative overflow-hidden">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="h-12 w-12 rounded-full bg-red-900/30 border border-red-500/30 flex items-center justify-center">
                <UsersIcon className="h-6 w-6 text-red-400" aria-hidden="true" />
              </div>
            </div>
            <div className="ml-5 w-0 flex-1">
              <dl>
                <dt className="text-sm font-medium text-gray-300 truncate">Total Users</dt>
                <dd>
                  <div className="text-lg font-semibold text-white">{stats.user_stats.total_users.toLocaleString()}</div>
                  <div className="flex items-center text-xs">
                    <span className={`flex items-center ${stats.user_stats.new_users_24h > 0 ? 'text-green-500' : 'text-red-500'}`}>
                      {stats.user_stats.new_users_24h > 0 ? (
                        <ArrowUpIcon className="h-3 w-3 mr-1" />
                      ) : (
                        <ArrowDownIcon className="h-3 w-3 mr-1" />
                      )}
                      {stats.user_stats.new_users_24h} today
                    </span>
                  </div>
                </dd>
              </dl>
            </div>
          </div>
        </Card>
        
        <Card className="p-6 relative overflow-hidden">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="h-12 w-12 rounded-full bg-red-900/30 border border-red-500/30 flex items-center justify-center">
                <ChartBarIcon className="h-6 w-6 text-red-400" aria-hidden="true" />
              </div>
            </div>
            <div className="ml-5 w-0 flex-1">
              <dl>
                <dt className="text-sm font-medium text-gray-300 truncate">Transactions (24h)</dt>
                <dd>
                  <div className="text-lg font-semibold text-white">{stats.transaction_stats.transactions_24h.toLocaleString()}</div>
                  <div className="text-xs text-gray-400">
                    Volume: ${stats.transaction_stats.volume_24h.toLocaleString()}
                  </div>
                </dd>
              </dl>
            </div>
          </div>
        </Card>
        
        <Card className="p-6 relative overflow-hidden">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="h-12 w-12 rounded-full bg-red-900/30 border border-red-500/30 flex items-center justify-center">
                <CurrencyDollarIcon className="h-6 w-6 text-red-400" aria-hidden="true" />
              </div>
            </div>
            <div className="ml-5 w-0 flex-1">
              <dl>
                <dt className="text-sm font-medium text-gray-300 truncate">Revenue (24h)</dt>
                <dd>
                  <div className="text-lg font-semibold text-white">${stats.transaction_stats.fee_collected_24h.toLocaleString()}</div>
                  <div className="text-xs text-gray-400">
                    7d: ${stats.transaction_stats.fee_collected_7d.toLocaleString()}
                  </div>
                </dd>
              </dl>
            </div>
          </div>
        </Card>
      </div>
      
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <div className="lg:col-span-2">
          <Card className="p-6 relative overflow-hidden">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-base font-semibold leading-6 text-white">Revenue Overview</h3>
              <Button variant="outline" size="sm">Export</Button>
            </div>
            <div className="h-80 relative">
              <Line data={revenueChartData} options={chartOptions} />
            </div>
          </Card>
        </div>
        
        <div className="lg:col-span-1">
          <Card className="p-6 relative overflow-hidden">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-base font-semibold leading-6 text-white">Blockchain Distribution</h3>
            </div>
            <div className="h-80 relative">
              <Pie data={blockchainData} options={pieChartOptions} />
            </div>
          </Card>
        </div>
      </div>
      
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <div className="lg:col-span-1">
          <Card className="p-6 relative overflow-hidden">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-base font-semibold leading-6 text-white">New Users</h3>
            </div>
            <div className="h-80 relative">
              <Bar data={userSignupsChartData} options={chartOptions} />
            </div>
          </Card>
        </div>
        
        <div className="lg:col-span-2">
          <Card className="p-6 relative overflow-hidden">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-base font-semibold leading-6 text-white">System Performance</h3>
            </div>
            <div className="space-y-4">
              <div>
                <div className="flex justify-between mb-1">
                  <span className="text-sm text-gray-300">CPU Usage</span>
                  <span className="text-sm text-gray-300">{stats.system_stats.cpu_usage}%</span>
                </div>
                <div className="w-full bg-gray-700 rounded-full h-2">
                  <div 
                    className="bg-red-500 h-2 rounded-full" 
                    style={{ width: `${stats.system_stats.cpu_usage}%` }}
                  ></div>
                </div>
              </div>
              
              <div>
                <div className="flex justify-between mb-1">
                  <span className="text-sm text-gray-300">Memory Usage</span>
                  <span className="text-sm text-gray-300">{stats.system_stats.memory_usage}%</span>
                </div>
                <div className="w-full bg-gray-700 rounded-full h-2">
                  <div 
                    className="bg-red-500 h-2 rounded-full" 
                    style={{ width: `${stats.system_stats.memory_usage}%` }}
                  ></div>
                </div>
              </div>
              
              <div>
                <div className="flex justify-between mb-1">
                  <span className="text-sm text-gray-300">Active Connections</span>
                  <span className="text-sm text-gray-300">{stats.system_stats.active_connections}</span>
                </div>
                <div className="w-full bg-gray-700 rounded-full h-2">
                  <div 
                    className="bg-red-500 h-2 rounded-full" 
                    style={{ width: `${Math.min(stats.system_stats.active_connections / 200 * 100, 100)}%` }}
                  ></div>
                </div>
              </div>
              
              <div>
                <div className="flex justify-between mb-1">
                  <span className="text-sm text-gray-300">API Requests (per minute)</span>
                  <span className="text-sm text-gray-300">{stats.system_stats.api_requests_per_minute.toFixed(1)}</span>
                </div>
                <div className="w-full bg-gray-700 rounded-full h-2">
                  <div 
                    className="bg-red-500 h-2 rounded-full" 
                    style={{ width: `${Math.min(stats.system_stats.api_requests_per_minute / 100 * 100, 100)}%` }}
                  ></div>
                </div>
              </div>
              
              <div className="mt-4 p-3 bg-gray-800 rounded-lg">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-300">Uptime</span>
                  <span className="text-sm text-white">
                    {Math.floor(stats.system_stats.uptime_seconds / 86400)}d {Math.floor((stats.system_stats.uptime_seconds % 86400) / 3600)}h {Math.floor((stats.system_stats.uptime_seconds % 3600) / 60)}m
                  </span>
                </div>
              </div>
            </div>
          </Card>
        </div>
      </div>
    </div>
  );
}
