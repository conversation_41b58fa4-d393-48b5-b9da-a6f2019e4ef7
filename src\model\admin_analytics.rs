use serde::{Deserialize, Serialize};
use std::collections::HashMap;

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, Serial<PERSON>, Deserialize)]
pub struct UserStats {
    pub total_users: i64,
    pub active_users_24h: i64,
    pub active_users_7d: i64,
    pub new_users_24h: i64,
    pub new_users_7d: i64,
}

impl Default for UserStats {
    fn default() -> Self {
        Self {
            total_users: 0,
            active_users_24h: 0,
            active_users_7d: 0,
            new_users_24h: 0,
            new_users_7d: 0,
        }
    }
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct TransactionStats {
    pub total_transactions: i64,
    pub transactions_24h: i64,
    pub transactions_7d: i64,
    pub volume_24h: f64,
    pub volume_7d: f64,
    pub fee_collected_24h: f64,
    pub fee_collected_7d: f64,
}

impl Default for TransactionStats {
    fn default() -> Self {
        Self {
            total_transactions: 0,
            transactions_24h: 0,
            transactions_7d: 0,
            volume_24h: 0.0,
            volume_7d: 0.0,
            fee_collected_24h: 0.0,
            fee_collected_7d: 0.0,
        }
    }
}

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, Serial<PERSON>, Deserialize)]
pub struct SystemStats {
    pub uptime_seconds: u64,
    pub cpu_usage: f64,
    pub memory_usage: f64,
    pub active_connections: i64,
    pub api_requests_per_minute: f64,
}

impl Default for SystemStats {
    fn default() -> Self {
        Self {
            uptime_seconds: 0,
            cpu_usage: 0.0,
            memory_usage: 0.0,
            active_connections: 0,
            api_requests_per_minute: 0.0,
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DashboardStats {
    pub user_stats: UserStats,
    pub transaction_stats: TransactionStats,
    pub system_stats: SystemStats,
    pub blockchain_distribution: HashMap<String, i64>,
}

impl Default for DashboardStats {
    fn default() -> Self {
        Self {
            user_stats: UserStats::default(),
            transaction_stats: TransactionStats::default(),
            system_stats: SystemStats::default(),
            blockchain_distribution: HashMap::new(),
        }
    }
}

// Time-series data for charts
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TimeSeriesDataPoint {
    pub timestamp: u64,
    pub value: f64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TimeSeriesData {
    pub name: String,
    pub data: Vec<TimeSeriesDataPoint>,
}

// User activity data
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UserActivity {
    pub user_id: String,
    pub username: String,
    pub action: String,
    pub timestamp: u64,
    pub details: Option<String>,
}

// System alert data
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum AlertSeverity {
    Low,
    Medium,
    High,
    Critical,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SystemAlert {
    pub id: String,
    pub title: String,
    pub description: String,
    pub severity: AlertSeverity,
    pub timestamp: u64,
    pub resolved: bool,
    pub resolved_at: Option<u64>,
    pub resolved_by: Option<String>,
}
