#!/bin/bash
set -e  #

echo "Starting Render build process..."

unset OPENSSL_DIR
unset OPENSSL_LIB_DIR
unset OPENSSL_INCLUDE_DIR

export OPENSSL_NO_VENDOR=0
export OPENSSL_STATIC=1
export RUSTLS_NATIVE_CERTS=1

# echo "OpenSSL environment variables set for Linux"

# Install system dependencies
echo "Installing system dependencies..."
apt-get update && apt-get install -y pkg-config libssl-dev build-essential curl

# Install Node.js
# echo "Installing Node.js..."
# curl -fsSL https://deb.nodesource.com/setup_18.x | bash -
# apt-get install -y nodejs

# Build frontend
# echo "Building frontend..."
# cd frontend
# npm ci
# npm run build:production
# cd ..

echo "Building Rust application with vendored OpenSSL..."
cargo build --release

echo "Build completed successfully!"
