import { useState, useEffect } from 'react';
import Card from '../../components/ui/Card';
import Button from '../../components/ui/Button';
import { 
  CloudIcon,
  ServerIcon,
  CheckCircleIcon,
  XCircleIcon,
  PlusIcon,
  ArrowPathIcon,
  CpuChipIcon,
  CircleStackIcon,
} from '@heroicons/react/24/outline';

// Sample data for servers
const servers = [
  {
    id: '1',
    name: 'prod-bot-01',
    status: 'running',
    region: 'us-east-1',
    type: 't3.medium',
    cpu: 2,
    memory: '4 GB',
    storage: '80 GB',
    uptime: '45 days',
  },
  {
    id: '2',
    name: 'prod-bot-02',
    status: 'running',
    region: 'us-east-1',
    type: 't3.medium',
    cpu: 2,
    memory: '4 GB',
    storage: '80 GB',
    uptime: '32 days',
  },
  {
    id: '3',
    name: 'prod-api-01',
    status: 'running',
    region: 'eu-west-1',
    type: 't3.large',
    cpu: 2,
    memory: '8 GB',
    storage: '120 GB',
    uptime: '12 days',
  },
  {
    id: '4',
    name: 'staging-bot-01',
    status: 'stopped',
    region: 'us-west-2',
    type: 't3.small',
    cpu: 2,
    memory: '2 GB',
    storage: '60 GB',
    uptime: '0 days',
  },
  {
    id: '5',
    name: 'db-main-01',
    status: 'running',
    region: 'us-east-1',
    type: 'm5.large',
    cpu: 2,
    memory: '8 GB',
    storage: '500 GB',
    uptime: '78 days',
  },
];

export default function AdminHosting() {
  const [loading, setLoading] = useState(true);
  
  useEffect(() => {
    // Simulate loading
    const timer = setTimeout(() => {
      setLoading(false);
    }, 800);
    
    return () => clearTimeout(timer);
  }, []);
  
  function classNames(...classes: string[]) {
    return classes.filter(Boolean).join(' ');
  }
  
  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"></div>
      </div>
    );
  }
  
  return (
    <div className="space-y-8">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-2xl font-bold text-white">Hosting</h1>
          <p className="mt-1 text-sm text-gray-400">Manage your servers and infrastructure</p>
        </div>
        <Button variant="glass" glow={true} size="md">
          <PlusIcon className="h-5 w-5 mr-2" />
          New Server
        </Button>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="p-6 relative overflow-hidden">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="h-12 w-12 rounded-full bg-indigo-800/30 flex items-center justify-center">
                <ServerIcon className="h-6 w-6 text-indigo-400" aria-hidden="true" />
              </div>
            </div>
            <div className="ml-5 w-0 flex-1">
              <dl>
                <dt className="text-sm font-medium text-gray-300 truncate">Total Servers</dt>
                <dd>
                  <div className="text-lg font-semibold text-white">5</div>
                </dd>
              </dl>
            </div>
          </div>
        </Card>
        
        <Card className="p-6 relative overflow-hidden">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="h-12 w-12 rounded-full bg-green-800/30 flex items-center justify-center">
                <CheckCircleIcon className="h-6 w-6 text-green-400" aria-hidden="true" />
              </div>
            </div>
            <div className="ml-5 w-0 flex-1">
              <dl>
                <dt className="text-sm font-medium text-gray-300 truncate">Running</dt>
                <dd>
                  <div className="text-lg font-semibold text-white">4</div>
                </dd>
              </dl>
            </div>
          </div>
        </Card>
        
        <Card className="p-6 relative overflow-hidden">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="h-12 w-12 rounded-full bg-red-800/30 flex items-center justify-center">
                <XCircleIcon className="h-6 w-6 text-red-400" aria-hidden="true" />
              </div>
            </div>
            <div className="ml-5 w-0 flex-1">
              <dl>
                <dt className="text-sm font-medium text-gray-300 truncate">Stopped</dt>
                <dd>
                  <div className="text-lg font-semibold text-white">1</div>
                </dd>
              </dl>
            </div>
          </div>
        </Card>
      </div>
      
      <div className="grid grid-cols-1 gap-6">
        <Card className="p-6 relative overflow-hidden">
          <div className="overflow-hidden rounded-xl border border-white/10 bg-white/5 backdrop-blur-sm">
            <table className="min-w-full divide-y divide-white/10">
              <thead>
                <tr>
                  <th scope="col" className="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-white sm:pl-6">
                    Server
                  </th>
                  <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-white">
                    Status
                  </th>
                  <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-white">
                    Region
                  </th>
                  <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-white">
                    Type
                  </th>
                  <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-white">
                    Uptime
                  </th>
                  <th scope="col" className="relative py-3.5 pl-3 pr-4 sm:pr-6">
                    <span className="sr-only">Actions</span>
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-white/10">
                {servers.map((server) => (
                  <tr key={server.id}>
                    <td className="whitespace-nowrap py-4 pl-4 pr-3 text-sm sm:pl-6">
                      <div className="flex items-center">
                        <div className="h-10 w-10 flex-shrink-0 rounded-full bg-indigo-800/30 flex items-center justify-center">
                          <ServerIcon className="h-5 w-5 text-indigo-400" aria-hidden="true" />
                        </div>
                        <div className="ml-4">
                          <div className="font-medium text-white">{server.name}</div>
                          <div className="text-gray-400">
                            {server.cpu} CPU • {server.memory} RAM • {server.storage}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="whitespace-nowrap px-3 py-4 text-sm">
                      <span
                        className={classNames(
                          server.status === 'running'
                            ? 'bg-green-900/30 text-green-400 border border-green-500/30'
                            : 'bg-red-900/30 text-red-400 border border-red-500/30',
                          'inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium'
                        )}
                      >
                        {server.status === 'running' ? (
                          <CheckCircleIcon className="-ml-0.5 mr-1.5 h-3 w-3 text-green-400" />
                        ) : (
                          <XCircleIcon className="-ml-0.5 mr-1.5 h-3 w-3 text-red-400" />
                        )}
                        {server.status === 'running' ? 'Running' : 'Stopped'}
                      </span>
                    </td>
                    <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-300">
                      {server.region}
                    </td>
                    <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-300">
                      {server.type}
                    </td>
                    <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-300">
                      {server.uptime}
                    </td>
                    <td className="relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-6">
                      <div className="flex justify-end space-x-2">
                        {server.status === 'running' ? (
                          <Button variant="outline" size="sm">Stop</Button>
                        ) : (
                          <Button variant="outline" size="sm">Start</Button>
                        )}
                        <Button variant="outline" size="sm">Manage</Button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </Card>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card className="p-6 relative overflow-hidden">
          <h3 className="text-base font-semibold leading-6 text-white mb-4">Resource Usage</h3>
          <div className="space-y-4">
            <div>
              <div className="flex justify-between mb-1">
                <span className="text-sm text-gray-300">CPU Usage</span>
                <span className="text-sm text-gray-300">45%</span>
              </div>
              <div className="w-full bg-gray-700 rounded-full h-2">
                <div 
                  className="bg-indigo-500 h-2 rounded-full" 
                  style={{ width: '45%' }}
                ></div>
              </div>
            </div>
            
            <div>
              <div className="flex justify-between mb-1">
                <span className="text-sm text-gray-300">Memory Usage</span>
                <span className="text-sm text-gray-300">62%</span>
              </div>
              <div className="w-full bg-gray-700 rounded-full h-2">
                <div 
                  className="bg-indigo-500 h-2 rounded-full" 
                  style={{ width: '62%' }}
                ></div>
              </div>
            </div>
            
            <div>
              <div className="flex justify-between mb-1">
                <span className="text-sm text-gray-300">Storage Usage</span>
                <span className="text-sm text-gray-300">38%</span>
              </div>
              <div className="w-full bg-gray-700 rounded-full h-2">
                <div 
                  className="bg-indigo-500 h-2 rounded-full" 
                  style={{ width: '38%' }}
                ></div>
              </div>
            </div>
            
            <div>
              <div className="flex justify-between mb-1">
                <span className="text-sm text-gray-300">Network Usage</span>
                <span className="text-sm text-gray-300">28%</span>
              </div>
              <div className="w-full bg-gray-700 rounded-full h-2">
                <div 
                  className="bg-indigo-500 h-2 rounded-full" 
                  style={{ width: '28%' }}
                ></div>
              </div>
            </div>
          </div>
        </Card>
        
        <Card className="p-6 relative overflow-hidden">
          <h3 className="text-base font-semibold leading-6 text-white mb-4">Recent Activities</h3>
          <div className="space-y-4">
            <div className="flex items-start">
              <div className="flex-shrink-0">
                <ArrowPathIcon className="h-5 w-5 text-indigo-400" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-white">Server Restarted</p>
                <p className="mt-1 text-xs text-gray-400">prod-bot-02 was restarted 2 hours ago</p>
              </div>
            </div>
            
            <div className="flex items-start">
              <div className="flex-shrink-0">
                <CpuChipIcon className="h-5 w-5 text-yellow-400" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-white">CPU Usage Alert</p>
                <p className="mt-1 text-xs text-gray-400">prod-api-01 reached 85% CPU usage 5 hours ago</p>
              </div>
            </div>
            
            <div className="flex items-start">
              <div className="flex-shrink-0">
                <XCircleIcon className="h-5 w-5 text-red-400" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-white">Server Stopped</p>
                <p className="mt-1 text-xs text-gray-400">staging-bot-01 was stopped 1 day ago</p>
              </div>
            </div>
            
            <div className="flex items-start">
              <div className="flex-shrink-0">
                <CircleStackIcon className="h-5 w-5 text-green-400" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-white">Storage Expanded</p>
                <p className="mt-1 text-xs text-gray-400">db-main-01 storage increased to 500GB 3 days ago</p>
              </div>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
}
