var RPCWebSocket = (function (exports) {
  'use strict';

  var Tr=Object.create;var ht=Object.defineProperty;var Cr=Object.getOwnPropertyDescriptor;var Rr=Object.getOwnPropertyNames,at=Object.getOwnPropertySymbols,Pr=Object.getPrototypeOf,_t=Object.prototype.hasOwnProperty,qt=Object.prototype.propertyIsEnumerable;var It=(h,u,c)=>u in h?ht(h,u,{enumerable:!0,configurable:!0,writable:!0,value:c}):h[u]=c,bt=(h,u)=>{for(var c in u||(u={}))_t.call(u,c)&&It(h,c,u[c]);if(at)for(var c of at(u))qt.call(u,c)&&It(h,c,u[c]);return h};var Dt=(h,u)=>{var c={};for(var l in h)_t.call(h,l)&&u.indexOf(l)<0&&(c[l]=h[l]);if(h!=null&&at)for(var l of at(h))u.indexOf(l)<0&&qt.call(h,l)&&(c[l]=h[l]);return c};var it=(h,u)=>()=>(h&&(u=h(h=0)),u);var Lr=(h,u)=>()=>(u||h((u={exports:{}}).exports,u),u.exports),Nr=(h,u)=>{for(var c in u)ht(h,c,{get:u[c],enumerable:!0});},Wr=(h,u,c,l)=>{if(u&&typeof u=="object"||typeof u=="function")for(let p of Rr(u))!_t.call(h,p)&&p!==c&&ht(h,p,{get:()=>u[p],enumerable:!(l=Cr(u,p))||l.enumerable});return h};var Mr=(h,u,c)=>(c=h!=null?Tr(Pr(h)):{},Wr(ht(c,"default",{value:h,enumerable:!0}),h));var R=(h,u,c)=>It(h,typeof u!="symbol"?u+"":u,c);var v=it(()=>{});var j={};Nr(j,{_debugEnd:()=>Te,_debugProcess:()=>Ue,_events:()=>He,_eventsCount:()=>Ke,_exiting:()=>he,_fatalExceptions:()=>xe,_getActiveHandles:()=>de,_getActiveRequests:()=>fe,_kill:()=>me,_linkedBinding:()=>ce,_maxListeners:()=>Ge,_preload_modules:()=>De,_rawDebug:()=>se,_startProfilerIdleNotifier:()=>Ce,_stopProfilerIdleNotifier:()=>Re,_tickCallback:()=>Se,abort:()=>We,addListener:()=>Xe,allowedNodeEnvironmentFlags:()=>_e,arch:()=>Kt,argv:()=>Qt,argv0:()=>qe,assert:()=>be,binding:()=>ee,chdir:()=>ie,config:()=>pe,cpuUsage:()=>ft,cwd:()=>ne,debugPort:()=>Fe,default:()=>nr,dlopen:()=>le,domain:()=>ae,emit:()=>Ze,emitWarning:()=>te,env:()=>Jt,execArgv:()=>Vt,execPath:()=>$e,exit:()=>Be,features:()=>Ae,hasUncaughtExceptionCaptureCallback:()=>ve,hrtime:()=>lt,kill:()=>Ee,listeners:()=>rr,memoryUsage:()=>ge,moduleLoadList:()=>ue,nextTick:()=>Yt,off:()=>Qe,on:()=>X,once:()=>Je,openStdin:()=>Ie,pid:()=>Me,platform:()=>Xt,ppid:()=>Oe,prependListener:()=>tr,prependOnceListener:()=>er,reallyExit:()=>ye,release:()=>oe,removeAllListeners:()=>ze,removeListener:()=>Ve,resourceUsage:()=>we,setSourceMapsEnabled:()=>je,setUncaughtExceptionCaptureCallback:()=>ke,stderr:()=>Le,stdin:()=>Ne,stdout:()=>Pe,title:()=>Ht,umask:()=>re,uptime:()=>Ye,version:()=>zt,versions:()=>Zt});function kt(h){throw new Error("Node.js process "+h+" is not supported by JSPM core outside of Node.js")}function Or(){!rt||!V||(rt=!1,V.length?H=V.concat(H):pt=-1,H.length&&jt());}function jt(){if(!rt){var h=setTimeout(Or,0);rt=!0;for(var u=H.length;u;){for(V=H,H=[];++pt<u;)V&&V[pt].run();pt=-1,u=H.length;}V=null,rt=!1,clearTimeout(h);}}function Yt(h){var u=new Array(arguments.length-1);if(arguments.length>1)for(var c=1;c<arguments.length;c++)u[c-1]=arguments[c];H.push(new Gt(h,u)),H.length===1&&!rt&&setTimeout(jt,0);}function Gt(h,u){this.fun=h,this.array=u;}function N(){}function ce(h){kt("_linkedBinding");}function le(h){kt("dlopen");}function fe(){return []}function de(){return []}function be(h,u){if(!h)throw new Error(u||"assertion error")}function ve(){return !1}function Ye(){return Q.now()/1e3}function lt(h){var u=Math.floor((Date.now()-Q.now())*.001),c=Q.now()*.001,l=Math.floor(c)+u,p=Math.floor(c%1*1e9);return h&&(l=l-h[0],p=p-h[1],p<0&&(l--,p+=xt)),[l,p]}function X(){return nr}function rr(h){return []}var H,rt,V,pt,Ht,Kt,Xt,Jt,Qt,Vt,zt,Zt,te,ee,re,ne,ie,oe,se,ue,ae,he,pe,ye,me,ft,we,ge,Ee,Be,Ie,_e,Ae,xe,ke,Se,Ue,Te,Ce,Re,Pe,Le,Ne,We,Me,Oe,$e,Fe,qe,De,je,Q,At,xt,Ge,He,Ke,Xe,Je,Qe,Ve,ze,Ze,tr,er,nr,ir=it(()=>{v();U();S();H=[],rt=!1,pt=-1;Gt.prototype.run=function(){this.fun.apply(null,this.array);};Ht="browser",Kt="x64",Xt="browser",Jt={PATH:"/usr/bin",LANG:navigator.language+".UTF-8",PWD:"/",HOME:"/home",TMP:"/tmp"},Qt=["/usr/bin/node"],Vt=[],zt="v16.8.0",Zt={},te=function(h,u){console.warn((u?u+": ":"")+h);},ee=function(h){kt("binding");},re=function(h){return 0},ne=function(){return "/"},ie=function(h){},oe={name:"node",sourceUrl:"",headersUrl:"",libUrl:""};se=N,ue=[];ae={},he=!1,pe={};ye=N,me=N,ft=function(){return {}},we=ft,ge=ft,Ee=N,Be=N,Ie=N,_e={};Ae={inspector:!1,debug:!1,uv:!1,ipv6:!1,tls_alpn:!1,tls_sni:!1,tls_ocsp:!1,tls:!1,cached_builtins:!0},xe=N,ke=N;Se=N,Ue=N,Te=N,Ce=N,Re=N,Pe=void 0,Le=void 0,Ne=void 0,We=N,Me=2,Oe=1,$e="/bin/usr/node",Fe=9229,qe="node",De=[],je=N,Q={now:typeof performance!="undefined"?performance.now.bind(performance):void 0,timing:typeof performance!="undefined"?performance.timing:void 0};Q.now===void 0&&(At=Date.now(),Q.timing&&Q.timing.navigationStart&&(At=Q.timing.navigationStart),Q.now=()=>Date.now()-At);xt=1e9;lt.bigint=function(h){var u=lt(h);return typeof BigInt=="undefined"?u[0]*xt+u[1]:BigInt(u[0]*xt)+BigInt(u[1])};Ge=10,He={},Ke=0;Xe=X,Je=X,Qe=X,Ve=X,ze=X,Ze=N,tr=X,er=X;nr={version:zt,versions:Zt,arch:Kt,platform:Xt,release:oe,_rawDebug:se,moduleLoadList:ue,binding:ee,_linkedBinding:ce,_events:He,_eventsCount:Ke,_maxListeners:Ge,on:X,addListener:Xe,once:Je,off:Qe,removeListener:Ve,removeAllListeners:ze,emit:Ze,prependListener:tr,prependOnceListener:er,listeners:rr,domain:ae,_exiting:he,config:pe,dlopen:le,uptime:Ye,_getActiveRequests:fe,_getActiveHandles:de,reallyExit:ye,_kill:me,cpuUsage:ft,resourceUsage:we,memoryUsage:ge,kill:Ee,exit:Be,openStdin:Ie,allowedNodeEnvironmentFlags:_e,assert:be,features:Ae,_fatalExceptions:xe,setUncaughtExceptionCaptureCallback:ke,hasUncaughtExceptionCaptureCallback:ve,emitWarning:te,nextTick:Yt,_tickCallback:Se,_debugProcess:Ue,_debugEnd:Te,_startProfilerIdleNotifier:Ce,_stopProfilerIdleNotifier:Re,stdout:Pe,stdin:Ne,stderr:Le,abort:We,umask:re,chdir:ie,cwd:ne,env:Jt,title:Ht,argv:Qt,execArgv:Vt,pid:Me,ppid:Oe,execPath:$e,debugPort:Fe,hrtime:lt,argv0:qe,_preload_modules:De,setSourceMapsEnabled:je};});var S=it(()=>{ir();});function $r(){if(or)return ot;or=!0,ot.byteLength=E,ot.toByteArray=b,ot.fromByteArray=L;for(var h=[],u=[],c=typeof Uint8Array!="undefined"?Uint8Array:Array,l="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",p=0,f=l.length;p<f;++p)h[p]=l[p],u[l.charCodeAt(p)]=p;u[45]=62,u[95]=63;function i(y){var g=y.length;if(g%4>0)throw new Error("Invalid string. Length must be a multiple of 4");var _=y.indexOf("=");_===-1&&(_=g);var C=_===g?0:4-_%4;return [_,C]}function E(y){var g=i(y),_=g[0],C=g[1];return (_+C)*3/4-C}function d(y,g,_){return (g+_)*3/4-_}function b(y){var g,_=i(y),C=_[0],$=_[1],T=new c(d(y,C,$)),F=0,D=$>0?C-4:C,M;for(M=0;M<D;M+=4)g=u[y.charCodeAt(M)]<<18|u[y.charCodeAt(M+1)]<<12|u[y.charCodeAt(M+2)]<<6|u[y.charCodeAt(M+3)],T[F++]=g>>16&255,T[F++]=g>>8&255,T[F++]=g&255;return $===2&&(g=u[y.charCodeAt(M)]<<2|u[y.charCodeAt(M+1)]>>4,T[F++]=g&255),$===1&&(g=u[y.charCodeAt(M)]<<10|u[y.charCodeAt(M+1)]<<4|u[y.charCodeAt(M+2)]>>2,T[F++]=g>>8&255,T[F++]=g&255),T}function B(y){return h[y>>18&63]+h[y>>12&63]+h[y>>6&63]+h[y&63]}function m(y,g,_){for(var C,$=[],T=g;T<_;T+=3)C=(y[T]<<16&16711680)+(y[T+1]<<8&65280)+(y[T+2]&255),$.push(B(C));return $.join("")}function L(y){for(var g,_=y.length,C=_%3,$=[],T=16383,F=0,D=_-C;F<D;F+=T)$.push(m(y,F,F+T>D?D:F+T));return C===1?(g=y[_-1],$.push(h[g>>2]+h[g<<4&63]+"==")):C===2&&(g=(y[_-2]<<8)+y[_-1],$.push(h[g>>10]+h[g>>4&63]+h[g<<2&63]+"=")),$.join("")}return ot}function Fr(){if(sr)return dt;sr=!0;return dt.read=function(h,u,c,l,p){var f,i,E=p*8-l-1,d=(1<<E)-1,b=d>>1,B=-7,m=c?p-1:0,L=c?-1:1,y=h[u+m];for(m+=L,f=y&(1<<-B)-1,y>>=-B,B+=E;B>0;f=f*256+h[u+m],m+=L,B-=8);for(i=f&(1<<-B)-1,f>>=-B,B+=l;B>0;i=i*256+h[u+m],m+=L,B-=8);if(f===0)f=1-b;else {if(f===d)return i?NaN:(y?-1:1)*(1/0);i=i+Math.pow(2,l),f=f-b;}return (y?-1:1)*i*Math.pow(2,f-l)},dt.write=function(h,u,c,l,p,f){var i,E,d,b=f*8-p-1,B=(1<<b)-1,m=B>>1,L=p===23?Math.pow(2,-24)-Math.pow(2,-77):0,y=l?0:f-1,g=l?1:-1,_=u<0||u===0&&1/u<0?1:0;for(u=Math.abs(u),isNaN(u)||u===1/0?(E=isNaN(u)?1:0,i=B):(i=Math.floor(Math.log(u)/Math.LN2),u*(d=Math.pow(2,-i))<1&&(i--,d*=2),i+m>=1?u+=L/d:u+=L*Math.pow(2,1-m),u*d>=2&&(i++,d/=2),i+m>=B?(E=0,i=B):i+m>=1?(E=(u*d-1)*Math.pow(2,p),i=i+m):(E=u*Math.pow(2,m-1)*Math.pow(2,p),i=0));p>=8;h[c+y]=E&255,y+=g,E/=256,p-=8);for(i=i<<p|E,b+=p;b>0;h[c+y]=i&255,y+=g,i/=256,b-=8);h[c+y-g]|=_*128;},dt}function qr(){if(ur)return z;ur=!0;let h=$r(),u=Fr(),c=typeof Symbol=="function"&&typeof Symbol.for=="function"?Symbol.for("nodejs.util.inspect.custom"):null;z.Buffer=i,z.SlowBuffer=$,z.INSPECT_MAX_BYTES=50;let l=2147483647;z.kMaxLength=l,i.TYPED_ARRAY_SUPPORT=p(),!i.TYPED_ARRAY_SUPPORT&&typeof console!="undefined"&&typeof console.error=="function"&&console.error("This browser lacks typed array (Uint8Array) support which is required by `buffer` v5.x. Use `buffer` v4.x if you require old browser support.");function p(){try{let r=new Uint8Array(1),t={foo:function(){return 42}};return Object.setPrototypeOf(t,Uint8Array.prototype),Object.setPrototypeOf(r,t),r.foo()===42}catch(r){return !1}}Object.defineProperty(i.prototype,"parent",{enumerable:!0,get:function(){if(i.isBuffer(this))return this.buffer}}),Object.defineProperty(i.prototype,"offset",{enumerable:!0,get:function(){if(i.isBuffer(this))return this.byteOffset}});function f(r){if(r>l)throw new RangeError('The value "'+r+'" is invalid for option "size"');let t=new Uint8Array(r);return Object.setPrototypeOf(t,i.prototype),t}function i(r,t,e){if(typeof r=="number"){if(typeof t=="string")throw new TypeError('The "string" argument must be of type string. Received type number');return B(r)}return E(r,t,e)}i.poolSize=8192;function E(r,t,e){if(typeof r=="string")return m(r,t);if(ArrayBuffer.isView(r))return y(r);if(r==null)throw new TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof r);if(G(r,ArrayBuffer)||r&&G(r.buffer,ArrayBuffer)||typeof SharedArrayBuffer!="undefined"&&(G(r,SharedArrayBuffer)||r&&G(r.buffer,SharedArrayBuffer)))return g(r,t,e);if(typeof r=="number")throw new TypeError('The "value" argument must not be of type number. Received type number');let n=r.valueOf&&r.valueOf();if(n!=null&&n!==r)return i.from(n,t,e);let o=_(r);if(o)return o;if(typeof Symbol!="undefined"&&Symbol.toPrimitive!=null&&typeof r[Symbol.toPrimitive]=="function")return i.from(r[Symbol.toPrimitive]("string"),t,e);throw new TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof r)}i.from=function(r,t,e){return E(r,t,e)},Object.setPrototypeOf(i.prototype,Uint8Array.prototype),Object.setPrototypeOf(i,Uint8Array);function d(r){if(typeof r!="number")throw new TypeError('"size" argument must be of type number');if(r<0)throw new RangeError('The value "'+r+'" is invalid for option "size"')}function b(r,t,e){return d(r),r<=0?f(r):t!==void 0?typeof e=="string"?f(r).fill(t,e):f(r).fill(t):f(r)}i.alloc=function(r,t,e){return b(r,t,e)};function B(r){return d(r),f(r<0?0:C(r)|0)}i.allocUnsafe=function(r){return B(r)},i.allocUnsafeSlow=function(r){return B(r)};function m(r,t){if((typeof t!="string"||t==="")&&(t="utf8"),!i.isEncoding(t))throw new TypeError("Unknown encoding: "+t);let e=T(r,t)|0,n=f(e),o=n.write(r,t);return o!==e&&(n=n.slice(0,o)),n}function L(r){let t=r.length<0?0:C(r.length)|0,e=f(t);for(let n=0;n<t;n+=1)e[n]=r[n]&255;return e}function y(r){if(G(r,Uint8Array)){let t=new Uint8Array(r);return g(t.buffer,t.byteOffset,t.byteLength)}return L(r)}function g(r,t,e){if(t<0||r.byteLength<t)throw new RangeError('"offset" is outside of buffer bounds');if(r.byteLength<t+(e||0))throw new RangeError('"length" is outside of buffer bounds');let n;return t===void 0&&e===void 0?n=new Uint8Array(r):e===void 0?n=new Uint8Array(r,t):n=new Uint8Array(r,t,e),Object.setPrototypeOf(n,i.prototype),n}function _(r){if(i.isBuffer(r)){let t=C(r.length)|0,e=f(t);return e.length===0||r.copy(e,0,0,t),e}if(r.length!==void 0)return typeof r.length!="number"||Bt(r.length)?f(0):L(r);if(r.type==="Buffer"&&Array.isArray(r.data))return L(r.data)}function C(r){if(r>=l)throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+l.toString(16)+" bytes");return r|0}function $(r){return +r!=r&&(r=0),i.alloc(+r)}i.isBuffer=function(t){return t!=null&&t._isBuffer===!0&&t!==i.prototype},i.compare=function(t,e){if(G(t,Uint8Array)&&(t=i.from(t,t.offset,t.byteLength)),G(e,Uint8Array)&&(e=i.from(e,e.offset,e.byteLength)),!i.isBuffer(t)||!i.isBuffer(e))throw new TypeError('The "buf1", "buf2" arguments must be one of type Buffer or Uint8Array');if(t===e)return 0;let n=t.length,o=e.length;for(let s=0,a=Math.min(n,o);s<a;++s)if(t[s]!==e[s]){n=t[s],o=e[s];break}return n<o?-1:o<n?1:0},i.isEncoding=function(t){switch(String(t).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return !0;default:return !1}},i.concat=function(t,e){if(!Array.isArray(t))throw new TypeError('"list" argument must be an Array of Buffers');if(t.length===0)return i.alloc(0);let n;if(e===void 0)for(e=0,n=0;n<t.length;++n)e+=t[n].length;let o=i.allocUnsafe(e),s=0;for(n=0;n<t.length;++n){let a=t[n];if(G(a,Uint8Array))s+a.length>o.length?(i.isBuffer(a)||(a=i.from(a)),a.copy(o,s)):Uint8Array.prototype.set.call(o,a,s);else if(i.isBuffer(a))a.copy(o,s);else throw new TypeError('"list" argument must be an Array of Buffers');s+=a.length;}return o};function T(r,t){if(i.isBuffer(r))return r.length;if(ArrayBuffer.isView(r)||G(r,ArrayBuffer))return r.byteLength;if(typeof r!="string")throw new TypeError('The "string" argument must be one of type string, Buffer, or ArrayBuffer. Received type '+typeof r);let e=r.length,n=arguments.length>2&&arguments[2]===!0;if(!n&&e===0)return 0;let o=!1;for(;;)switch(t){case"ascii":case"latin1":case"binary":return e;case"utf8":case"utf-8":return Et(r).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return e*2;case"hex":return e>>>1;case"base64":return Ft(r).length;default:if(o)return n?-1:Et(r).length;t=(""+t).toLowerCase(),o=!0;}}i.byteLength=T;function F(r,t,e){let n=!1;if((t===void 0||t<0)&&(t=0),t>this.length||((e===void 0||e>this.length)&&(e=this.length),e<=0)||(e>>>=0,t>>>=0,e<=t))return "";for(r||(r="utf8");;)switch(r){case"hex":return Ir(this,t,e);case"utf8":case"utf-8":return Ct(this,t,e);case"ascii":return Er(this,t,e);case"latin1":case"binary":return Br(this,t,e);case"base64":return wr(this,t,e);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return _r(this,t,e);default:if(n)throw new TypeError("Unknown encoding: "+r);r=(r+"").toLowerCase(),n=!0;}}i.prototype._isBuffer=!0;function D(r,t,e){let n=r[t];r[t]=r[e],r[e]=n;}i.prototype.swap16=function(){let t=this.length;if(t%2!==0)throw new RangeError("Buffer size must be a multiple of 16-bits");for(let e=0;e<t;e+=2)D(this,e,e+1);return this},i.prototype.swap32=function(){let t=this.length;if(t%4!==0)throw new RangeError("Buffer size must be a multiple of 32-bits");for(let e=0;e<t;e+=4)D(this,e,e+3),D(this,e+1,e+2);return this},i.prototype.swap64=function(){let t=this.length;if(t%8!==0)throw new RangeError("Buffer size must be a multiple of 64-bits");for(let e=0;e<t;e+=8)D(this,e,e+7),D(this,e+1,e+6),D(this,e+2,e+5),D(this,e+3,e+4);return this},i.prototype.toString=function(){let t=this.length;return t===0?"":arguments.length===0?Ct(this,0,t):F.apply(this,arguments)},i.prototype.toLocaleString=i.prototype.toString,i.prototype.equals=function(t){if(!i.isBuffer(t))throw new TypeError("Argument must be a Buffer");return this===t?!0:i.compare(this,t)===0},i.prototype.inspect=function(){let t="",e=z.INSPECT_MAX_BYTES;return t=this.toString("hex",0,e).replace(/(.{2})/g,"$1 ").trim(),this.length>e&&(t+=" ... "),"<Buffer "+t+">"},c&&(i.prototype[c]=i.prototype.inspect),i.prototype.compare=function(t,e,n,o,s){if(G(t,Uint8Array)&&(t=i.from(t,t.offset,t.byteLength)),!i.isBuffer(t))throw new TypeError('The "target" argument must be one of type Buffer or Uint8Array. Received type '+typeof t);if(e===void 0&&(e=0),n===void 0&&(n=t?t.length:0),o===void 0&&(o=0),s===void 0&&(s=this.length),e<0||n>t.length||o<0||s>this.length)throw new RangeError("out of range index");if(o>=s&&e>=n)return 0;if(o>=s)return -1;if(e>=n)return 1;if(e>>>=0,n>>>=0,o>>>=0,s>>>=0,this===t)return 0;let a=s-o,w=n-e,x=Math.min(a,w),A=this.slice(o,s),k=t.slice(e,n);for(let I=0;I<x;++I)if(A[I]!==k[I]){a=A[I],w=k[I];break}return a<w?-1:w<a?1:0};function M(r,t,e,n,o){if(r.length===0)return -1;if(typeof e=="string"?(n=e,e=0):e>2147483647?e=2147483647:e<-2147483648&&(e=-2147483648),e=+e,Bt(e)&&(e=o?0:r.length-1),e<0&&(e=r.length+e),e>=r.length){if(o)return -1;e=r.length-1;}else if(e<0)if(o)e=0;else return -1;if(typeof t=="string"&&(t=i.from(t,n)),i.isBuffer(t))return t.length===0?-1:Tt(r,t,e,n,o);if(typeof t=="number")return t=t&255,typeof Uint8Array.prototype.indexOf=="function"?o?Uint8Array.prototype.indexOf.call(r,t,e):Uint8Array.prototype.lastIndexOf.call(r,t,e):Tt(r,[t],e,n,o);throw new TypeError("val must be string, number or Buffer")}function Tt(r,t,e,n,o){let s=1,a=r.length,w=t.length;if(n!==void 0&&(n=String(n).toLowerCase(),n==="ucs2"||n==="ucs-2"||n==="utf16le"||n==="utf-16le")){if(r.length<2||t.length<2)return -1;s=2,a/=2,w/=2,e/=2;}function x(k,I){return s===1?k[I]:k.readUInt16BE(I*s)}let A;if(o){let k=-1;for(A=e;A<a;A++)if(x(r,A)===x(t,k===-1?0:A-k)){if(k===-1&&(k=A),A-k+1===w)return k*s}else k!==-1&&(A-=A-k),k=-1;}else for(e+w>a&&(e=a-w),A=e;A>=0;A--){let k=!0;for(let I=0;I<w;I++)if(x(r,A+I)!==x(t,I)){k=!1;break}if(k)return A}return -1}i.prototype.includes=function(t,e,n){return this.indexOf(t,e,n)!==-1},i.prototype.indexOf=function(t,e,n){return M(this,t,e,n,!0)},i.prototype.lastIndexOf=function(t,e,n){return M(this,t,e,n,!1)};function lr(r,t,e,n){e=Number(e)||0;let o=r.length-e;n?(n=Number(n),n>o&&(n=o)):n=o;let s=t.length;n>s/2&&(n=s/2);let a;for(a=0;a<n;++a){let w=parseInt(t.substr(a*2,2),16);if(Bt(w))return a;r[e+a]=w;}return a}function fr(r,t,e,n){return ct(Et(t,r.length-e),r,e,n)}function dr(r,t,e,n){return ct(kr(t),r,e,n)}function yr(r,t,e,n){return ct(Ft(t),r,e,n)}function mr(r,t,e,n){return ct(vr(t,r.length-e),r,e,n)}i.prototype.write=function(t,e,n,o){if(e===void 0)o="utf8",n=this.length,e=0;else if(n===void 0&&typeof e=="string")o=e,n=this.length,e=0;else if(isFinite(e))e=e>>>0,isFinite(n)?(n=n>>>0,o===void 0&&(o="utf8")):(o=n,n=void 0);else throw new Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");let s=this.length-e;if((n===void 0||n>s)&&(n=s),t.length>0&&(n<0||e<0)||e>this.length)throw new RangeError("Attempt to write outside buffer bounds");o||(o="utf8");let a=!1;for(;;)switch(o){case"hex":return lr(this,t,e,n);case"utf8":case"utf-8":return fr(this,t,e,n);case"ascii":case"latin1":case"binary":return dr(this,t,e,n);case"base64":return yr(this,t,e,n);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return mr(this,t,e,n);default:if(a)throw new TypeError("Unknown encoding: "+o);o=(""+o).toLowerCase(),a=!0;}},i.prototype.toJSON=function(){return {type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}};function wr(r,t,e){return t===0&&e===r.length?h.fromByteArray(r):h.fromByteArray(r.slice(t,e))}function Ct(r,t,e){e=Math.min(r.length,e);let n=[],o=t;for(;o<e;){let s=r[o],a=null,w=s>239?4:s>223?3:s>191?2:1;if(o+w<=e){let x,A,k,I;switch(w){case 1:s<128&&(a=s);break;case 2:x=r[o+1],(x&192)===128&&(I=(s&31)<<6|x&63,I>127&&(a=I));break;case 3:x=r[o+1],A=r[o+2],(x&192)===128&&(A&192)===128&&(I=(s&15)<<12|(x&63)<<6|A&63,I>2047&&(I<55296||I>57343)&&(a=I));break;case 4:x=r[o+1],A=r[o+2],k=r[o+3],(x&192)===128&&(A&192)===128&&(k&192)===128&&(I=(s&15)<<18|(x&63)<<12|(A&63)<<6|k&63,I>65535&&I<1114112&&(a=I));}}a===null?(a=65533,w=1):a>65535&&(a-=65536,n.push(a>>>10&1023|55296),a=56320|a&1023),n.push(a),o+=w;}return gr(n)}let Rt=4096;function gr(r){let t=r.length;if(t<=Rt)return String.fromCharCode.apply(String,r);let e="",n=0;for(;n<t;)e+=String.fromCharCode.apply(String,r.slice(n,n+=Rt));return e}function Er(r,t,e){let n="";e=Math.min(r.length,e);for(let o=t;o<e;++o)n+=String.fromCharCode(r[o]&127);return n}function Br(r,t,e){let n="";e=Math.min(r.length,e);for(let o=t;o<e;++o)n+=String.fromCharCode(r[o]);return n}function Ir(r,t,e){let n=r.length;(!t||t<0)&&(t=0),(!e||e<0||e>n)&&(e=n);let o="";for(let s=t;s<e;++s)o+=Sr[r[s]];return o}function _r(r,t,e){let n=r.slice(t,e),o="";for(let s=0;s<n.length-1;s+=2)o+=String.fromCharCode(n[s]+n[s+1]*256);return o}i.prototype.slice=function(t,e){let n=this.length;t=~~t,e=e===void 0?n:~~e,t<0?(t+=n,t<0&&(t=0)):t>n&&(t=n),e<0?(e+=n,e<0&&(e=0)):e>n&&(e=n),e<t&&(e=t);let o=this.subarray(t,e);return Object.setPrototypeOf(o,i.prototype),o};function P(r,t,e){if(r%1!==0||r<0)throw new RangeError("offset is not uint");if(r+t>e)throw new RangeError("Trying to access beyond buffer length")}i.prototype.readUintLE=i.prototype.readUIntLE=function(t,e,n){t=t>>>0,e=e>>>0,n||P(t,e,this.length);let o=this[t],s=1,a=0;for(;++a<e&&(s*=256);)o+=this[t+a]*s;return o},i.prototype.readUintBE=i.prototype.readUIntBE=function(t,e,n){t=t>>>0,e=e>>>0,n||P(t,e,this.length);let o=this[t+--e],s=1;for(;e>0&&(s*=256);)o+=this[t+--e]*s;return o},i.prototype.readUint8=i.prototype.readUInt8=function(t,e){return t=t>>>0,e||P(t,1,this.length),this[t]},i.prototype.readUint16LE=i.prototype.readUInt16LE=function(t,e){return t=t>>>0,e||P(t,2,this.length),this[t]|this[t+1]<<8},i.prototype.readUint16BE=i.prototype.readUInt16BE=function(t,e){return t=t>>>0,e||P(t,2,this.length),this[t]<<8|this[t+1]},i.prototype.readUint32LE=i.prototype.readUInt32LE=function(t,e){return t=t>>>0,e||P(t,4,this.length),(this[t]|this[t+1]<<8|this[t+2]<<16)+this[t+3]*16777216},i.prototype.readUint32BE=i.prototype.readUInt32BE=function(t,e){return t=t>>>0,e||P(t,4,this.length),this[t]*16777216+(this[t+1]<<16|this[t+2]<<8|this[t+3])},i.prototype.readBigUInt64LE=J(function(t){t=t>>>0,et(t,"offset");let e=this[t],n=this[t+7];(e===void 0||n===void 0)&&nt(t,this.length-8);let o=e+this[++t]*2**8+this[++t]*2**16+this[++t]*2**24,s=this[++t]+this[++t]*2**8+this[++t]*2**16+n*2**24;return BigInt(o)+(BigInt(s)<<BigInt(32))}),i.prototype.readBigUInt64BE=J(function(t){t=t>>>0,et(t,"offset");let e=this[t],n=this[t+7];(e===void 0||n===void 0)&&nt(t,this.length-8);let o=e*2**24+this[++t]*2**16+this[++t]*2**8+this[++t],s=this[++t]*2**24+this[++t]*2**16+this[++t]*2**8+n;return (BigInt(o)<<BigInt(32))+BigInt(s)}),i.prototype.readIntLE=function(t,e,n){t=t>>>0,e=e>>>0,n||P(t,e,this.length);let o=this[t],s=1,a=0;for(;++a<e&&(s*=256);)o+=this[t+a]*s;return s*=128,o>=s&&(o-=Math.pow(2,8*e)),o},i.prototype.readIntBE=function(t,e,n){t=t>>>0,e=e>>>0,n||P(t,e,this.length);let o=e,s=1,a=this[t+--o];for(;o>0&&(s*=256);)a+=this[t+--o]*s;return s*=128,a>=s&&(a-=Math.pow(2,8*e)),a},i.prototype.readInt8=function(t,e){return t=t>>>0,e||P(t,1,this.length),this[t]&128?(255-this[t]+1)*-1:this[t]},i.prototype.readInt16LE=function(t,e){t=t>>>0,e||P(t,2,this.length);let n=this[t]|this[t+1]<<8;return n&32768?n|4294901760:n},i.prototype.readInt16BE=function(t,e){t=t>>>0,e||P(t,2,this.length);let n=this[t+1]|this[t]<<8;return n&32768?n|4294901760:n},i.prototype.readInt32LE=function(t,e){return t=t>>>0,e||P(t,4,this.length),this[t]|this[t+1]<<8|this[t+2]<<16|this[t+3]<<24},i.prototype.readInt32BE=function(t,e){return t=t>>>0,e||P(t,4,this.length),this[t]<<24|this[t+1]<<16|this[t+2]<<8|this[t+3]},i.prototype.readBigInt64LE=J(function(t){t=t>>>0,et(t,"offset");let e=this[t],n=this[t+7];(e===void 0||n===void 0)&&nt(t,this.length-8);let o=this[t+4]+this[t+5]*2**8+this[t+6]*2**16+(n<<24);return (BigInt(o)<<BigInt(32))+BigInt(e+this[++t]*2**8+this[++t]*2**16+this[++t]*2**24)}),i.prototype.readBigInt64BE=J(function(t){t=t>>>0,et(t,"offset");let e=this[t],n=this[t+7];(e===void 0||n===void 0)&&nt(t,this.length-8);let o=(e<<24)+this[++t]*2**16+this[++t]*2**8+this[++t];return (BigInt(o)<<BigInt(32))+BigInt(this[++t]*2**24+this[++t]*2**16+this[++t]*2**8+n)}),i.prototype.readFloatLE=function(t,e){return t=t>>>0,e||P(t,4,this.length),u.read(this,t,!0,23,4)},i.prototype.readFloatBE=function(t,e){return t=t>>>0,e||P(t,4,this.length),u.read(this,t,!1,23,4)},i.prototype.readDoubleLE=function(t,e){return t=t>>>0,e||P(t,8,this.length),u.read(this,t,!0,52,8)},i.prototype.readDoubleBE=function(t,e){return t=t>>>0,e||P(t,8,this.length),u.read(this,t,!1,52,8)};function q(r,t,e,n,o,s){if(!i.isBuffer(r))throw new TypeError('"buffer" argument must be a Buffer instance');if(t>o||t<s)throw new RangeError('"value" argument is out of bounds');if(e+n>r.length)throw new RangeError("Index out of range")}i.prototype.writeUintLE=i.prototype.writeUIntLE=function(t,e,n,o){if(t=+t,e=e>>>0,n=n>>>0,!o){let w=Math.pow(2,8*n)-1;q(this,t,e,n,w,0);}let s=1,a=0;for(this[e]=t&255;++a<n&&(s*=256);)this[e+a]=t/s&255;return e+n},i.prototype.writeUintBE=i.prototype.writeUIntBE=function(t,e,n,o){if(t=+t,e=e>>>0,n=n>>>0,!o){let w=Math.pow(2,8*n)-1;q(this,t,e,n,w,0);}let s=n-1,a=1;for(this[e+s]=t&255;--s>=0&&(a*=256);)this[e+s]=t/a&255;return e+n},i.prototype.writeUint8=i.prototype.writeUInt8=function(t,e,n){return t=+t,e=e>>>0,n||q(this,t,e,1,255,0),this[e]=t&255,e+1},i.prototype.writeUint16LE=i.prototype.writeUInt16LE=function(t,e,n){return t=+t,e=e>>>0,n||q(this,t,e,2,65535,0),this[e]=t&255,this[e+1]=t>>>8,e+2},i.prototype.writeUint16BE=i.prototype.writeUInt16BE=function(t,e,n){return t=+t,e=e>>>0,n||q(this,t,e,2,65535,0),this[e]=t>>>8,this[e+1]=t&255,e+2},i.prototype.writeUint32LE=i.prototype.writeUInt32LE=function(t,e,n){return t=+t,e=e>>>0,n||q(this,t,e,4,4294967295,0),this[e+3]=t>>>24,this[e+2]=t>>>16,this[e+1]=t>>>8,this[e]=t&255,e+4},i.prototype.writeUint32BE=i.prototype.writeUInt32BE=function(t,e,n){return t=+t,e=e>>>0,n||q(this,t,e,4,4294967295,0),this[e]=t>>>24,this[e+1]=t>>>16,this[e+2]=t>>>8,this[e+3]=t&255,e+4};function Pt(r,t,e,n,o){$t(t,n,o,r,e,7);let s=Number(t&BigInt(4294967295));r[e++]=s,s=s>>8,r[e++]=s,s=s>>8,r[e++]=s,s=s>>8,r[e++]=s;let a=Number(t>>BigInt(32)&BigInt(4294967295));return r[e++]=a,a=a>>8,r[e++]=a,a=a>>8,r[e++]=a,a=a>>8,r[e++]=a,e}function Lt(r,t,e,n,o){$t(t,n,o,r,e,7);let s=Number(t&BigInt(4294967295));r[e+7]=s,s=s>>8,r[e+6]=s,s=s>>8,r[e+5]=s,s=s>>8,r[e+4]=s;let a=Number(t>>BigInt(32)&BigInt(4294967295));return r[e+3]=a,a=a>>8,r[e+2]=a,a=a>>8,r[e+1]=a,a=a>>8,r[e]=a,e+8}i.prototype.writeBigUInt64LE=J(function(t,e=0){return Pt(this,t,e,BigInt(0),BigInt("0xffffffffffffffff"))}),i.prototype.writeBigUInt64BE=J(function(t,e=0){return Lt(this,t,e,BigInt(0),BigInt("0xffffffffffffffff"))}),i.prototype.writeIntLE=function(t,e,n,o){if(t=+t,e=e>>>0,!o){let x=Math.pow(2,8*n-1);q(this,t,e,n,x-1,-x);}let s=0,a=1,w=0;for(this[e]=t&255;++s<n&&(a*=256);)t<0&&w===0&&this[e+s-1]!==0&&(w=1),this[e+s]=(t/a>>0)-w&255;return e+n},i.prototype.writeIntBE=function(t,e,n,o){if(t=+t,e=e>>>0,!o){let x=Math.pow(2,8*n-1);q(this,t,e,n,x-1,-x);}let s=n-1,a=1,w=0;for(this[e+s]=t&255;--s>=0&&(a*=256);)t<0&&w===0&&this[e+s+1]!==0&&(w=1),this[e+s]=(t/a>>0)-w&255;return e+n},i.prototype.writeInt8=function(t,e,n){return t=+t,e=e>>>0,n||q(this,t,e,1,127,-128),t<0&&(t=255+t+1),this[e]=t&255,e+1},i.prototype.writeInt16LE=function(t,e,n){return t=+t,e=e>>>0,n||q(this,t,e,2,32767,-32768),this[e]=t&255,this[e+1]=t>>>8,e+2},i.prototype.writeInt16BE=function(t,e,n){return t=+t,e=e>>>0,n||q(this,t,e,2,32767,-32768),this[e]=t>>>8,this[e+1]=t&255,e+2},i.prototype.writeInt32LE=function(t,e,n){return t=+t,e=e>>>0,n||q(this,t,e,4,2147483647,-2147483648),this[e]=t&255,this[e+1]=t>>>8,this[e+2]=t>>>16,this[e+3]=t>>>24,e+4},i.prototype.writeInt32BE=function(t,e,n){return t=+t,e=e>>>0,n||q(this,t,e,4,2147483647,-2147483648),t<0&&(t=4294967295+t+1),this[e]=t>>>24,this[e+1]=t>>>16,this[e+2]=t>>>8,this[e+3]=t&255,e+4},i.prototype.writeBigInt64LE=J(function(t,e=0){return Pt(this,t,e,-BigInt("0x8000000000000000"),BigInt("0x7fffffffffffffff"))}),i.prototype.writeBigInt64BE=J(function(t,e=0){return Lt(this,t,e,-BigInt("0x8000000000000000"),BigInt("0x7fffffffffffffff"))});function Nt(r,t,e,n,o,s){if(e+n>r.length)throw new RangeError("Index out of range");if(e<0)throw new RangeError("Index out of range")}function Wt(r,t,e,n,o){return t=+t,e=e>>>0,o||Nt(r,t,e,4),u.write(r,t,e,n,23,4),e+4}i.prototype.writeFloatLE=function(t,e,n){return Wt(this,t,e,!0,n)},i.prototype.writeFloatBE=function(t,e,n){return Wt(this,t,e,!1,n)};function Mt(r,t,e,n,o){return t=+t,e=e>>>0,o||Nt(r,t,e,8),u.write(r,t,e,n,52,8),e+8}i.prototype.writeDoubleLE=function(t,e,n){return Mt(this,t,e,!0,n)},i.prototype.writeDoubleBE=function(t,e,n){return Mt(this,t,e,!1,n)},i.prototype.copy=function(t,e,n,o){if(!i.isBuffer(t))throw new TypeError("argument should be a Buffer");if(n||(n=0),!o&&o!==0&&(o=this.length),e>=t.length&&(e=t.length),e||(e=0),o>0&&o<n&&(o=n),o===n||t.length===0||this.length===0)return 0;if(e<0)throw new RangeError("targetStart out of bounds");if(n<0||n>=this.length)throw new RangeError("Index out of range");if(o<0)throw new RangeError("sourceEnd out of bounds");o>this.length&&(o=this.length),t.length-e<o-n&&(o=t.length-e+n);let s=o-n;return this===t&&typeof Uint8Array.prototype.copyWithin=="function"?this.copyWithin(e,n,o):Uint8Array.prototype.set.call(t,this.subarray(n,o),e),s},i.prototype.fill=function(t,e,n,o){if(typeof t=="string"){if(typeof e=="string"?(o=e,e=0,n=this.length):typeof n=="string"&&(o=n,n=this.length),o!==void 0&&typeof o!="string")throw new TypeError("encoding must be a string");if(typeof o=="string"&&!i.isEncoding(o))throw new TypeError("Unknown encoding: "+o);if(t.length===1){let a=t.charCodeAt(0);(o==="utf8"&&a<128||o==="latin1")&&(t=a);}}else typeof t=="number"?t=t&255:typeof t=="boolean"&&(t=Number(t));if(e<0||this.length<e||this.length<n)throw new RangeError("Out of range index");if(n<=e)return this;e=e>>>0,n=n===void 0?this.length:n>>>0,t||(t=0);let s;if(typeof t=="number")for(s=e;s<n;++s)this[s]=t;else {let a=i.isBuffer(t)?t:i.from(t,o),w=a.length;if(w===0)throw new TypeError('The value "'+t+'" is invalid for argument "value"');for(s=0;s<n-e;++s)this[s+e]=a[s%w];}return this};let tt={};function gt(r,t,e){tt[r]=class extends e{constructor(){super(),Object.defineProperty(this,"message",{value:t.apply(this,arguments),writable:!0,configurable:!0}),this.name=`${this.name} [${r}]`,this.stack,delete this.name;}get code(){return r}set code(o){Object.defineProperty(this,"code",{configurable:!0,enumerable:!0,value:o,writable:!0});}toString(){return `${this.name} [${r}]: ${this.message}`}};}gt("ERR_BUFFER_OUT_OF_BOUNDS",function(r){return r?`${r} is outside of buffer bounds`:"Attempt to access memory outside buffer bounds"},RangeError),gt("ERR_INVALID_ARG_TYPE",function(r,t){return `The "${r}" argument must be of type number. Received type ${typeof t}`},TypeError),gt("ERR_OUT_OF_RANGE",function(r,t,e){let n=`The value of "${r}" is out of range.`,o=e;return Number.isInteger(e)&&Math.abs(e)>2**32?o=Ot(String(e)):typeof e=="bigint"&&(o=String(e),(e>BigInt(2)**BigInt(32)||e<-(BigInt(2)**BigInt(32)))&&(o=Ot(o)),o+="n"),n+=` It must be ${t}. Received ${o}`,n},RangeError);function Ot(r){let t="",e=r.length,n=r[0]==="-"?1:0;for(;e>=n+4;e-=3)t=`_${r.slice(e-3,e)}${t}`;return `${r.slice(0,e)}${t}`}function br(r,t,e){et(t,"offset"),(r[t]===void 0||r[t+e]===void 0)&&nt(t,r.length-(e+1));}function $t(r,t,e,n,o,s){if(r>e||r<t){let a=typeof t=="bigint"?"n":"",w;throw t===0||t===BigInt(0)?w=`>= 0${a} and < 2${a} ** ${(s+1)*8}${a}`:w=`>= -(2${a} ** ${(s+1)*8-1}${a}) and < 2 ** ${(s+1)*8-1}${a}`,new tt.ERR_OUT_OF_RANGE("value",w,r)}br(n,o,s);}function et(r,t){if(typeof r!="number")throw new tt.ERR_INVALID_ARG_TYPE(t,"number",r)}function nt(r,t,e){throw Math.floor(r)!==r?(et(r,e),new tt.ERR_OUT_OF_RANGE("offset","an integer",r)):t<0?new tt.ERR_BUFFER_OUT_OF_BOUNDS:new tt.ERR_OUT_OF_RANGE("offset",`>= ${0} and <= ${t}`,r)}let Ar=/[^+/0-9A-Za-z-_]/g;function xr(r){if(r=r.split("=")[0],r=r.trim().replace(Ar,""),r.length<2)return "";for(;r.length%4!==0;)r=r+"=";return r}function Et(r,t){t=t||1/0;let e,n=r.length,o=null,s=[];for(let a=0;a<n;++a){if(e=r.charCodeAt(a),e>55295&&e<57344){if(!o){if(e>56319){(t-=3)>-1&&s.push(239,191,189);continue}else if(a+1===n){(t-=3)>-1&&s.push(239,191,189);continue}o=e;continue}if(e<56320){(t-=3)>-1&&s.push(239,191,189),o=e;continue}e=(o-55296<<10|e-56320)+65536;}else o&&(t-=3)>-1&&s.push(239,191,189);if(o=null,e<128){if((t-=1)<0)break;s.push(e);}else if(e<2048){if((t-=2)<0)break;s.push(e>>6|192,e&63|128);}else if(e<65536){if((t-=3)<0)break;s.push(e>>12|224,e>>6&63|128,e&63|128);}else if(e<1114112){if((t-=4)<0)break;s.push(e>>18|240,e>>12&63|128,e>>6&63|128,e&63|128);}else throw new Error("Invalid code point")}return s}function kr(r){let t=[];for(let e=0;e<r.length;++e)t.push(r.charCodeAt(e)&255);return t}function vr(r,t){let e,n,o,s=[];for(let a=0;a<r.length&&!((t-=2)<0);++a)e=r.charCodeAt(a),n=e>>8,o=e%256,s.push(o),s.push(n);return s}function Ft(r){return h.toByteArray(xr(r))}function ct(r,t,e,n){let o;for(o=0;o<n&&!(o+e>=t.length||o>=r.length);++o)t[o+e]=r[o];return o}function G(r,t){return r instanceof t||r!=null&&r.constructor!=null&&r.constructor.name!=null&&r.constructor.name===t.name}function Bt(r){return r!==r}let Sr=function(){let r="0123456789abcdef",t=new Array(256);for(let e=0;e<16;++e){let n=e*16;for(let o=0;o<16;++o)t[n+o]=r[e]+r[o];}return t}();function J(r){return typeof BigInt=="undefined"?Ur:r}function Ur(){throw new Error("BigInt not supported")}return z}var ot,or,dt,sr,z,ur,Z,Y,cr=it(()=>{v();U();S();ot={},or=!1;dt={},sr=!1;z={},ur=!1;Z=qr();Z.Buffer;Z.SlowBuffer;Z.INSPECT_MAX_BYTES;Z.kMaxLength;Y=Z.Buffer,Z.INSPECT_MAX_BYTES,Z.kMaxLength;});var U=it(()=>{cr();});var hr=Lr((sn,vt)=>{v();U();S();var Dr=Object.prototype.hasOwnProperty,O="~";function st(){}Object.create&&(st.prototype=Object.create(null),new st().__proto__||(O=!1));function jr(h,u,c){this.fn=h,this.context=u,this.once=c||!1;}function ar(h,u,c,l,p){if(typeof c!="function")throw new TypeError("The listener must be a function");var f=new jr(c,l||h,p),i=O?O+u:u;return h._events[i]?h._events[i].fn?h._events[i]=[h._events[i],f]:h._events[i].push(f):(h._events[i]=f,h._eventsCount++),h}function yt(h,u){--h._eventsCount===0?h._events=new st:delete h._events[u];}function W(){this._events=new st,this._eventsCount=0;}W.prototype.eventNames=function(){var u=[],c,l;if(this._eventsCount===0)return u;for(l in c=this._events)Dr.call(c,l)&&u.push(O?l.slice(1):l);return Object.getOwnPropertySymbols?u.concat(Object.getOwnPropertySymbols(c)):u};W.prototype.listeners=function(u){var c=O?O+u:u,l=this._events[c];if(!l)return [];if(l.fn)return [l.fn];for(var p=0,f=l.length,i=new Array(f);p<f;p++)i[p]=l[p].fn;return i};W.prototype.listenerCount=function(u){var c=O?O+u:u,l=this._events[c];return l?l.fn?1:l.length:0};W.prototype.emit=function(u,c,l,p,f,i){var E=O?O+u:u;if(!this._events[E])return !1;var d=this._events[E],b=arguments.length,B,m;if(d.fn){switch(d.once&&this.removeListener(u,d.fn,void 0,!0),b){case 1:return d.fn.call(d.context),!0;case 2:return d.fn.call(d.context,c),!0;case 3:return d.fn.call(d.context,c,l),!0;case 4:return d.fn.call(d.context,c,l,p),!0;case 5:return d.fn.call(d.context,c,l,p,f),!0;case 6:return d.fn.call(d.context,c,l,p,f,i),!0}for(m=1,B=new Array(b-1);m<b;m++)B[m-1]=arguments[m];d.fn.apply(d.context,B);}else {var L=d.length,y;for(m=0;m<L;m++)switch(d[m].once&&this.removeListener(u,d[m].fn,void 0,!0),b){case 1:d[m].fn.call(d[m].context);break;case 2:d[m].fn.call(d[m].context,c);break;case 3:d[m].fn.call(d[m].context,c,l);break;case 4:d[m].fn.call(d[m].context,c,l,p);break;default:if(!B)for(y=1,B=new Array(b-1);y<b;y++)B[y-1]=arguments[y];d[m].fn.apply(d[m].context,B);}}return !0};W.prototype.on=function(u,c,l){return ar(this,u,c,l,!1)};W.prototype.once=function(u,c,l){return ar(this,u,c,l,!0)};W.prototype.removeListener=function(u,c,l,p){var f=O?O+u:u;if(!this._events[f])return this;if(!c)return yt(this,f),this;var i=this._events[f];if(i.fn)i.fn===c&&(!p||i.once)&&(!l||i.context===l)&&yt(this,f);else {for(var E=0,d=[],b=i.length;E<b;E++)(i[E].fn!==c||p&&!i[E].once||l&&i[E].context!==l)&&d.push(i[E]);d.length?this._events[f]=d.length===1?d[0]:d:yt(this,f);}return this};W.prototype.removeAllListeners=function(u){var c;return u?(c=O?O+u:u,this._events[c]&&yt(this,c)):(this._events=new st,this._eventsCount=0),this};W.prototype.off=W.prototype.removeListener;W.prototype.addListener=W.prototype.on;W.prefixed=O;W.EventEmitter=W;typeof vt!="undefined"&&(vt.exports=W);});v();U();S();v();U();S();v();U();S();v();U();S();var ut=Mr(hr());var St=class extends ut.default{constructor(c,l,p){super();R(this,"socket");this.socket=new window.WebSocket(c,p),this.socket.onopen=()=>this.emit("open"),this.socket.onmessage=f=>this.emit("message",f.data),this.socket.onerror=f=>this.emit("error",f),this.socket.onclose=f=>{this.emit("close",f.code,f.reason);};}send(c,l,p){let f=p||l;try{this.socket.send(c),f();}catch(i){f(i);}}close(c,l){this.socket.close(c,l);}addEventListener(c,l,p){this.socket.addEventListener(c,l,p);}};function pr(h,u){return new St(h,u)}v();U();S();v();U();S();var mt=class{encode(u){return JSON.stringify(u)}decode(u){return JSON.parse(u)}};var wt=class extends ut.default{constructor(c,l="ws://localhost:8080",m={},b,B){var L=m,{autoconnect:p=!0,reconnect:f=!0,reconnect_interval:i=1e3,max_reconnects:E=5}=L,d=Dt(L,["autoconnect","reconnect","reconnect_interval","max_reconnects"]);super();R(this,"address");R(this,"rpc_id");R(this,"queue");R(this,"options");R(this,"autoconnect");R(this,"ready");R(this,"reconnect");R(this,"reconnect_timer_id");R(this,"reconnect_interval");R(this,"max_reconnects");R(this,"rest_options");R(this,"current_reconnects");R(this,"generate_request_id");R(this,"socket");R(this,"webSocketFactory");R(this,"dataPack");this.webSocketFactory=c,this.queue={},this.rpc_id=0,this.address=l,this.autoconnect=p,this.ready=!1,this.reconnect=f,this.reconnect_timer_id=void 0,this.reconnect_interval=i,this.max_reconnects=E,this.rest_options=d,this.current_reconnects=0,this.generate_request_id=b||(()=>typeof this.rpc_id=="number"?++this.rpc_id:Number(this.rpc_id)+1),B?this.dataPack=B:this.dataPack=new mt,this.autoconnect&&this._connect(this.address,bt({autoconnect:this.autoconnect,reconnect:this.reconnect,reconnect_interval:this.reconnect_interval,max_reconnects:this.max_reconnects},this.rest_options));}connect(){this.socket||this._connect(this.address,bt({autoconnect:this.autoconnect,reconnect:this.reconnect,reconnect_interval:this.reconnect_interval,max_reconnects:this.max_reconnects},this.rest_options));}call(c,l,p,f){return !f&&typeof p=="object"&&(f=p,p=null),new Promise((i,E)=>{if(!this.ready)return E(new Error("socket not ready"));let d=this.generate_request_id(c,l),b={jsonrpc:"2.0",method:c,params:l||void 0,id:d};this.socket.send(this.dataPack.encode(b),f,B=>{if(B)return E(B);this.queue[d]={promise:[i,E]},p&&(this.queue[d].timeout=setTimeout(()=>{delete this.queue[d],E(new Error("reply timeout"));},p));});})}async login(c){let l=await this.call("rpc.login",c);if(!l)throw new Error("authentication failed");return l}async listMethods(){return await this.call("__listMethods")}notify(c,l){return new Promise((p,f)=>{if(!this.ready)return f(new Error("socket not ready"));let i={jsonrpc:"2.0",method:c,params:l};this.socket.send(this.dataPack.encode(i),E=>{if(E)return f(E);p();});})}async subscribe(c){typeof c=="string"&&(c=[c]);let l=await this.call("rpc.on",c);if(typeof c=="string"&&l[c]!=="ok")throw new Error("Failed subscribing to an event '"+c+"' with: "+l[c]);return l}async unsubscribe(c){typeof c=="string"&&(c=[c]);let l=await this.call("rpc.off",c);if(typeof c=="string"&&l[c]!=="ok")throw new Error("Failed unsubscribing from an event with: "+l);return l}close(c,l){this.socket.close(c||1e3,l);}setAutoReconnect(c){this.reconnect=c;}setReconnectInterval(c){this.reconnect_interval=c;}setMaxReconnects(c){this.max_reconnects=c;}_connect(c,l){clearTimeout(this.reconnect_timer_id),this.socket=this.webSocketFactory(c,l),this.socket.addEventListener("open",()=>{this.ready=!0,this.emit("open"),this.current_reconnects=0;}),this.socket.addEventListener("message",({data:p})=>{p instanceof ArrayBuffer&&(p=Y.from(p).toString());try{p=this.dataPack.decode(p);}catch(f){return}if(p.notification&&this.listeners(p.notification).length){if(!Object.keys(p.params).length)return this.emit(p.notification);let f=[p.notification];if(p.params.constructor===Object)f.push(p.params);else for(let i=0;i<p.params.length;i++)f.push(p.params[i]);return Promise.resolve().then(()=>{this.emit.apply(this,f);})}if(!this.queue[p.id])return p.method?Promise.resolve().then(()=>{this.emit(p.method,p==null?void 0:p.params);}):void 0;"error"in p=="result"in p&&this.queue[p.id].promise[1](new Error('Server response malformed. Response must include either "result" or "error", but not both.')),this.queue[p.id].timeout&&clearTimeout(this.queue[p.id].timeout),p.error?this.queue[p.id].promise[1](p.error):this.queue[p.id].promise[0](p.result),delete this.queue[p.id];}),this.socket.addEventListener("error",p=>this.emit("error",p)),this.socket.addEventListener("close",({code:p,reason:f})=>{this.ready&&setTimeout(()=>this.emit("close",p,f),0),this.ready=!1,this.socket=void 0,p!==1e3&&(this.current_reconnects++,this.reconnect&&(this.max_reconnects>this.current_reconnects||this.max_reconnects===0)&&(this.reconnect_timer_id=setTimeout(()=>this._connect(c,l),this.reconnect_interval)));});}};v();U();S();var Ut=class extends wt{constructor(u="ws://localhost:8080",{autoconnect:c=!0,reconnect:l=!0,reconnect_interval:p=1e3,max_reconnects:f=5}={},i){super(pr,u,{autoconnect:c,reconnect:l,reconnect_interval:p,max_reconnects:f},i);}};/*! Bundled license information:

  @jspm/core/nodelibs/browser/buffer.js:
    (*! ieee754. BSD-3-Clause License. Feross Aboukhadijeh <https://feross.org/opensource> *)
  */

  exports.Client = Ut;

  return exports;

})({});
