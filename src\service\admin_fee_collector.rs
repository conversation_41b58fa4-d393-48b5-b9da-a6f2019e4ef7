use crate::model::{Blockchain, Trade, Wallet};
use crate::service::{
    price_service::{PriceService, FeeCalculationResult},
    db_service::DbService,
};
use crate::service::solana_trader_service::SolanaTraderService;
use crate::service::evm_trader_service::EvmTraderService;
use mongodb::bson::{doc, oid::ObjectId};
use solana_sdk::signature::Keypair;
use std::str::FromStr;
use tracing::{error, info, warn};
use serde::{Deserialize, Serialize};
use std::time::{Duration, SystemTime};
use chrono;
use hex;

#[derive(Debug, Serialize, Deserialize)]
pub struct AdminFeeCollectionResult {
    pub success: bool,
    pub transaction_hash: Option<String>,
    pub fee_amount: f64,
    pub fee_amount_formatted: String,
    pub fee_usd_value: f64,
    pub blockchain: String,
    pub native_symbol: String,
    pub collection_method: String,
    pub error_message: Option<String>,
    pub skipped_reason: Option<String>,
}

pub struct AdminFeeCollector {
    price_service: PriceService,
}

impl AdminFeeCollector {
    pub fn new() -> Self {
        Self {
            price_service: PriceService::new(),
        }
    }

    /// Main entry point for collecting admin fees
    pub async fn collect_admin_fee(&self, transaction_id: &str) -> Result<AdminFeeCollectionResult, Box<dyn std::error::Error + Send + Sync>> {
        info!("🚀 Starting high-performance admin fee collection for transaction: {}", transaction_id);

        // Get transaction from database
        let trade = self.get_transaction(transaction_id).await?;
        
        // Check if fee collection is needed
        if let Some(status) = &trade.admin_fee_status {
            if status == "completed" {
                return Ok(AdminFeeCollectionResult {
                    success: true,
                    transaction_hash: trade.admin_fee_transaction_id.map(|id| id.to_hex()),
                    fee_amount: trade.admin_fee_amount.unwrap_or(0.0),
                    fee_amount_formatted: "Already collected".to_string(),
                    fee_usd_value: 0.0,
                    blockchain: format!("{:?}", trade.blockchain),
                    native_symbol: self.get_native_symbol(&trade.blockchain),
                    collection_method: "already_collected".to_string(),
                    error_message: None,
                    skipped_reason: Some("Fee already collected".to_string()),
                });
            }
        }

        // Calculate and validate fee
        let mut fee_calculation = self.calculate_fee_for_trade(&trade).await?;

        if !fee_calculation.meets_minimum {
            info!("💰 Adjusting fee to minimum $0.25 equivalent: {} → {} USD",
                  fee_calculation.fee_usd_value, 0.25);

            // Get adjusted fee amount (minimum $0.25 equivalent)
            let adjusted_fee_amount = match self.price_service.get_adjusted_fee_amount(&trade.blockchain, fee_calculation.fee_amount).await {
                Ok(adjusted_amount) => adjusted_amount,
                Err(e) => {
                    error!("Failed to get adjusted fee amount: {}", e);
                    return Err(e);
                }
            };

            // Update fee calculation with adjusted amount
            fee_calculation.fee_amount = adjusted_fee_amount;
            fee_calculation.fee_amount_formatted = self.price_service.format_fee_amount(adjusted_fee_amount, &trade.blockchain);
            fee_calculation.fee_usd_value = 0.25; // Minimum USD value
            fee_calculation.meets_minimum = true;

            info!("✅ Fee adjusted to minimum: {} {} (${:.2})",
                  fee_calculation.fee_amount_formatted, fee_calculation.native_symbol, fee_calculation.fee_usd_value);
        } else {
            info!("✅ Fee meets minimum threshold, using calculated amount: {} {} (${:.4})",
                  fee_calculation.fee_amount_formatted, fee_calculation.native_symbol, fee_calculation.fee_usd_value);
        }

        // Collect fee based on blockchain
        let result = match trade.blockchain {
            Blockchain::SOL => self.collect_solana_admin_fee(&trade, &fee_calculation).await,
            Blockchain::ETH | Blockchain::BSC | Blockchain::BASE => {
                self.collect_evm_admin_fee(&trade, &fee_calculation).await
            }
        };

        match result {
            Ok(mut collection_result) => {
                // Update database with success
                self.update_fee_status(&trade, "completed", collection_result.transaction_hash.as_deref()).await?;
                collection_result.success = true;
                info!("✅ Admin fee collection completed successfully for transaction: {}", transaction_id);
                Ok(collection_result)
            }
            Err(e) => {
                error!("❌ Admin fee collection failed for transaction {}: {}", transaction_id, e);
                
                // Update database with failure
                self.update_fee_status(&trade, "failed", None).await?;
                
                Ok(AdminFeeCollectionResult {
                    success: false,
                    transaction_hash: None,
                    fee_amount: fee_calculation.fee_amount,
                    fee_amount_formatted: fee_calculation.fee_amount_formatted,
                    fee_usd_value: fee_calculation.fee_usd_value,
                    blockchain: fee_calculation.blockchain,
                    native_symbol: fee_calculation.native_symbol,
                    collection_method: "failed".to_string(),
                    error_message: Some(e.to_string()),
                    skipped_reason: None,
                })
            }
        }
    }

    /// Get transaction from database
    async fn get_transaction(&self, transaction_id: &str) -> Result<Trade, Box<dyn std::error::Error + Send + Sync>> {
        let object_id = ObjectId::parse_str(transaction_id)?;
        let db = DbService::get_db();
        let collection = db.collection::<Trade>("trades");
        
        let trade = collection
            .find_one(doc! { "_id": object_id }, None)
            .await?
            .ok_or("Transaction not found")?;
            
        Ok(trade)
    }

    /// Calculate fee for a trade
    async fn calculate_fee_for_trade(&self, trade: &Trade) -> Result<FeeCalculationResult, Box<dyn std::error::Error + Send + Sync>> {
        // Use existing admin fee if available, otherwise calculate
        if let Some(existing_fee) = trade.admin_fee_amount {
            let fee_percentage = trade.admin_fee_percentage.unwrap_or(2.5);
            return self.price_service.calculate_admin_fee(&trade.blockchain, existing_fee, 100.0).await;
        }

        // Calculate based on transaction type and amount
        let base_amount = if trade.trade_type == "buy" {
            trade.native_token_amount.or(trade.amount_out).unwrap_or(0.0)
        } else {
            trade.native_token_amount.unwrap_or(0.0)
        };

        let fee_percentage = trade.admin_fee_percentage.unwrap_or(2.5);
        self.price_service.calculate_admin_fee(&trade.blockchain, base_amount, fee_percentage).await
    }

    /// Update fee status in database
    async fn update_fee_status(
        &self,
        trade: &Trade,
        status: &str,
        transaction_hash: Option<&str>,
    ) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        let db = DbService::get_db();
        let collection = db.collection::<Trade>("trades");
        
        let mut update_doc = doc! {
            "$set": {
                "admin_fee_status": status,
                "updated_at": mongodb::bson::DateTime::from_chrono(chrono::Utc::now())
            }
        };

        if let Some(hash) = transaction_hash {
            if let Ok(object_id) = ObjectId::parse_str(hash) {
                update_doc.get_document_mut("$set").unwrap().insert("admin_fee_transaction_id", object_id);
            }
        }

        collection
            .update_one(doc! { "_id": trade.id }, update_doc, None)
            .await?;

        Ok(())
    }

    /// Get native symbol for blockchain
    fn get_native_symbol(&self, blockchain: &Blockchain) -> String {
        match blockchain {
            Blockchain::SOL => "SOL".to_string(),
            Blockchain::ETH => "ETH".to_string(),
            Blockchain::BSC => "BNB".to_string(),
            Blockchain::BASE => "ETH".to_string(),
        }
    }

    /// Collect Solana admin fee using high-performance methods
    async fn collect_solana_admin_fee(
        &self,
        trade: &Trade,
        fee_calculation: &FeeCalculationResult,
    ) -> Result<AdminFeeCollectionResult, Box<dyn std::error::Error + Send + Sync>> {
        info!("🔥 High-performance Solana admin fee collection starting");

        // Get user's wallet keypair
        let user_keypair = self.get_user_solana_keypair(&trade.user_id).await?;

        // Get admin wallet address from settings
        let admin_address = self.get_solana_admin_address().await?;

        // Convert fee amount to lamports
        let fee_lamports = (fee_calculation.fee_amount * 1_000_000_000.0) as u64;

        info!("Transferring {} lamports ({} SOL) to admin address: {}",
              fee_lamports, fee_calculation.fee_amount, admin_address);

        // Create Solana service instance
        let solana_service = SolanaTraderService::new(
            "https://api.mainnet-beta.solana.com",
            None,
            10
        );

        // Try high-performance transfer first
        match solana_service.execute_high_performance_sol_transfer(
            &user_keypair,
            &admin_address,
            fee_lamports,
        ).await {
            Ok(signature) => {
                info!("✅ High-performance SOL transfer successful: {}", signature);
                Ok(AdminFeeCollectionResult {
                    success: true,
                    transaction_hash: Some(signature.to_string()),
                    fee_amount: fee_calculation.fee_amount,
                    fee_amount_formatted: fee_calculation.fee_amount_formatted.clone(),
                    fee_usd_value: fee_calculation.fee_usd_value,
                    blockchain: fee_calculation.blockchain.clone(),
                    native_symbol: fee_calculation.native_symbol.clone(),
                    collection_method: "high_performance_transfer".to_string(),
                    error_message: None,
                    skipped_reason: None,
                })
            }
            Err(e) => {
                warn!("⚠️ High-performance transfer failed, trying fallback: {}", e);

                // Fallback to standard transfer
                match solana_service.transfer_sol_to_address(
                    &user_keypair,
                    &admin_address,
                    fee_lamports,
                ).await {
                    Ok(signature) => {
                        info!("✅ Fallback SOL transfer successful: {}", signature);
                        Ok(AdminFeeCollectionResult {
                            success: true,
                            transaction_hash: Some(signature.to_string()),
                            fee_amount: fee_calculation.fee_amount,
                            fee_amount_formatted: fee_calculation.fee_amount_formatted.clone(),
                            fee_usd_value: fee_calculation.fee_usd_value,
                            blockchain: fee_calculation.blockchain.clone(),
                            native_symbol: fee_calculation.native_symbol.clone(),
                            collection_method: "fallback_transfer".to_string(),
                            error_message: None,
                            skipped_reason: None,
                        })
                    }
                    Err(fallback_error) => {
                        error!("❌ Both high-performance and fallback transfers failed: {}", fallback_error);
                        Err(fallback_error.into())
                    }
                }
            }
        }
    }

    /// Collect EVM admin fee using high-performance methods
    async fn collect_evm_admin_fee(
        &self,
        trade: &Trade,
        fee_calculation: &FeeCalculationResult,
    ) -> Result<AdminFeeCollectionResult, Box<dyn std::error::Error + Send + Sync>> {
        info!("🔥 High-performance EVM admin fee collection starting");

        // Get user's wallet private key
        let user_private_key = self.get_user_evm_private_key(&trade.user_id).await?;

        // Get admin wallet address from settings
        let admin_address = self.get_evm_admin_address(&trade.blockchain).await?;

        // Convert fee amount to wei (for ETH/BNB)
        let fee_wei = (fee_calculation.fee_amount * 1e18) as u64;

        info!("Transferring {} wei ({} {}) to admin address: {}",
              fee_wei, fee_calculation.fee_amount, fee_calculation.native_symbol, admin_address);

        // Create EVM service instance
        let evm_service = EvmTraderService::new();

        // Try high-performance transfer first
        match evm_service.execute_high_performance_native_transfer(
            &trade.blockchain,
            &user_private_key,
            &admin_address,
            fee_wei,
        ).await {
            Ok(tx_hash) => {
                info!("✅ High-performance EVM transfer successful: {}", tx_hash);
                Ok(AdminFeeCollectionResult {
                    success: true,
                    transaction_hash: Some(tx_hash),
                    fee_amount: fee_calculation.fee_amount,
                    fee_amount_formatted: fee_calculation.fee_amount_formatted.clone(),
                    fee_usd_value: fee_calculation.fee_usd_value,
                    blockchain: fee_calculation.blockchain.clone(),
                    native_symbol: fee_calculation.native_symbol.clone(),
                    collection_method: "high_performance_transfer".to_string(),
                    error_message: None,
                    skipped_reason: None,
                })
            }
            Err(e) => {
                warn!("⚠️ High-performance EVM transfer failed, trying fallback: {}", e);

                // Fallback to standard transfer
                match evm_service.transfer_native_token(
                    &trade.blockchain,
                    &user_private_key,
                    &admin_address,
                    fee_wei,
                ).await {
                    Ok(tx_hash) => {
                        info!("✅ Fallback EVM transfer successful: {}", tx_hash);
                        Ok(AdminFeeCollectionResult {
                            success: true,
                            transaction_hash: Some(tx_hash),
                            fee_amount: fee_calculation.fee_amount,
                            fee_amount_formatted: fee_calculation.fee_amount_formatted.clone(),
                            fee_usd_value: fee_calculation.fee_usd_value,
                            blockchain: fee_calculation.blockchain.clone(),
                            native_symbol: fee_calculation.native_symbol.clone(),
                            collection_method: "fallback_transfer".to_string(),
                            error_message: None,
                            skipped_reason: None,
                        })
                    }
                    Err(fallback_error) => {
                        error!("❌ Both high-performance and fallback EVM transfers failed: {}", fallback_error);
                        Err(fallback_error.into())
                    }
                }
            }
        }
    }

    /// Get user's Solana keypair from wallet database
    async fn get_user_solana_keypair(&self, user_id: &ObjectId) -> Result<Keypair, Box<dyn std::error::Error + Send + Sync>> {
        let db = DbService::get_db();
        let collection = db.collection::<crate::model::Wallet>("wallets");

        // Find user's Solana wallet
        let wallet = collection
            .find_one(doc! {
                "user_id": user_id,
                "blockchain": "SOL"
            }, None)
            .await?
            .ok_or("User has no Solana wallet")?;

        // Get private key from wallet
        let private_key = wallet.private_key;

        // Convert hex private key to bytes for Solana keypair
        let private_key_bytes = if private_key.starts_with("0x") {
            hex::decode(&private_key[2..])
                .map_err(|e| format!("Invalid hex private key: {}", e))?
        } else {
            // Assume it's already in the correct format
            private_key.as_bytes().to_vec()
        };

        let keypair = Keypair::from_bytes(&private_key_bytes)
            .map_err(|e| format!("Invalid Solana private key: {}", e))?;

        Ok(keypair)
    }

    /// Get user's EVM private key from wallet database
    async fn get_user_evm_private_key(&self, user_id: &ObjectId) -> Result<String, Box<dyn std::error::Error + Send + Sync>> {
        let db = DbService::get_db();
        let collection = db.collection::<crate::model::Wallet>("wallets");

        // Find user's EVM wallet (try ETH first, then BSC, then BASE)
        let blockchains = ["ETH", "BSC", "BASE"];

        for blockchain in &blockchains {
            if let Ok(Some(wallet)) = collection
                .find_one(doc! {
                    "user_id": user_id,
                    "blockchain": blockchain
                }, None)
                .await
            {
                return Ok(wallet.private_key);
            }
        }

        Err("User has no EVM wallet".into())
    }

    /// Get Solana admin address from settings
    async fn get_solana_admin_address(&self) -> Result<String, Box<dyn std::error::Error + Send + Sync>> {
        let db = DbService::get_db();
        let collection = db.collection::<crate::model::AdminSettings>("admin_settings");

        let settings = collection
            .find_one(doc! {}, None)
            .await?
            .ok_or("Admin settings not found")?;

        settings.admin_wallet_sol
            .ok_or("Solana admin wallet not configured".into())
    }

    /// Get EVM admin address from settings
    async fn get_evm_admin_address(&self, blockchain: &Blockchain) -> Result<String, Box<dyn std::error::Error + Send + Sync>> {
        let db = DbService::get_db();
        let collection = db.collection::<crate::model::AdminSettings>("admin_settings");

        let settings = collection
            .find_one(doc! {}, None)
            .await?
            .ok_or("Admin settings not found")?;

        let admin_wallet = match blockchain {
            Blockchain::ETH => settings.admin_wallet_eth,
            Blockchain::BSC => settings.admin_wallet_bsc,
            Blockchain::BASE => settings.admin_wallet_base,
            Blockchain::SOL => return Err("Use get_solana_admin_address for SOL".into()),
        };

        admin_wallet.ok_or(format!("{:?} admin wallet not configured", blockchain).into())
    }
}
