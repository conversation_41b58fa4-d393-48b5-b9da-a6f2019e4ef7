use dotenv::dotenv;
use std::env;
use std::sync::OnceLock;

// Production-ready configuration constants
pub mod defaults {
    // Admin fee configuration
    pub const DEFAULT_ADMIN_FEE_PERCENTAGE: f64 = 0.5; // 0.5%
    pub const MIN_ADMIN_FEE_PERCENTAGE: f64 = 0.0;     // 0%
    pub const MAX_ADMIN_FEE_PERCENTAGE: f64 = 5.0;     // 5%

    // Buffer percentages for different operations
    pub const SLIPPAGE_BUFFER_PERCENTAGE: f64 = 2.0;   // 2% buffer for slippage
    pub const GAS_BUFFER_PERCENTAGE: f64 = 0.5;        // 0.5% buffer for gas
    pub const DUST_BUFFER_PERCENTAGE: f64 = 0.005;     // 0.005% for dust amounts

    // Solana specific defaults
    pub const SOL_RENT_EXEMPTION_DEFAULT: u64 = 2_039_280; // Default rent exemption
    pub const SOL_MIN_BALANCE_RESERVE: u64 = 5_000_000;    // 0.005 SOL reserve

    // EVM specific defaults
    pub const ETH_GAS_RESERVE: f64 = 0.01;    // 0.01 ETH
    pub const BSC_GAS_RESERVE: f64 = 0.005;   // 0.005 BNB
    pub const BASE_GAS_RESERVE: f64 = 0.001;  // 0.001 ETH

    // Dynamic buffer thresholds
    pub const LARGE_BALANCE_THRESHOLD: u64 = 1_000_000_000;  // 1B tokens
    pub const MEDIUM_BALANCE_THRESHOLD: u64 = 1_000_000;     // 1M tokens
    pub const SMALL_BALANCE_THRESHOLD: u64 = 10_000;        // 10K tokens

    // Buffer percentages by balance size
    pub const LARGE_BALANCE_BUFFER: f64 = 0.005;  // 0.5% for large balances
    pub const MEDIUM_BALANCE_BUFFER: f64 = 0.0075; // 0.75% for medium balances
    pub const SMALL_BALANCE_BUFFER: f64 = 0.01;    // 1% for small balances
    pub const TINY_BALANCE_BUFFER: f64 = 0.02;     // 2% for tiny balances

    // Transaction timeouts (optimized for trading performance)
    pub const SWAP_TIMEOUT_SECONDS: u64 = 120;        // Increased for complex swaps
    pub const FEE_COLLECTION_TIMEOUT_SECONDS: u64 = 60; // Increased for reliability
    pub const SETTLEMENT_DELAY_MS: u64 = 200;         // Reduced for faster execution

    // Performance optimization constants
    pub const MAX_CONCURRENT_TRADES: usize = 50;      // Increased concurrent trades
    pub const TRADE_RETRY_ATTEMPTS: u32 = 5;          // More retry attempts
    pub const TRADE_RETRY_DELAY_MS: u64 = 1000;       // Retry delay
    pub const PRIORITY_GAS_MULTIPLIER: f64 = 1.5;     // Higher gas for priority
    pub const SLIPPAGE_TOLERANCE: f64 = 3.0;          // Increased slippage tolerance

    // Resource allocation for trading operations
    pub const TRADING_THREAD_POOL_SIZE: usize = 16;   // Dedicated trading threads
    pub const RPC_CONNECTION_POOL_SIZE: usize = 20;   // More RPC connections
    pub const DATABASE_CONNECTION_POOL: usize = 25;   // More DB connections

    // Memory allocation for trading
    pub const TRADE_CACHE_SIZE: usize = 10000;        // Large trade cache
    pub const PRICE_CACHE_SIZE: usize = 5000;         // Price data cache
    pub const BALANCE_CACHE_TTL_SECONDS: u64 = 30;    // Balance cache TTL

    // Price fetching configuration (reduce API rate limiting)
    pub const PRICE_CACHE_TTL_SECONDS: u64 = 600;     // 10 minutes cache (longer to reduce API calls)
    pub const PRICE_REQUEST_TIMEOUT_SECONDS: u64 = 15; // 15 second timeout
    pub const PRICE_RETRY_ATTEMPTS: usize = 2;         // Reduce retry attempts
    pub const PRICE_REQUEST_DELAY_MS: u64 = 1000;     // 1 second delay between requests

    // High-performance trading configuration
    pub const USE_HIGH_PERFORMANCE_TRADER: bool = true;  // Enable high-performance trading by default
    pub const AUTO_TRIGGER_OPTIMIZATION: bool = true;    // Auto-trigger performance optimizations
    pub const ENABLE_PARALLEL_EXECUTION: bool = true;    // Enable parallel swap + fee collection

    // API rate limiting - Prevent 429 errors
    pub const PRICE_API_DELAY_MS: u64 = 2000;           // 2 second delay between price API calls
    pub const PRICE_API_RETRY_DELAY_MS: u64 = 5000;     // 5 second delay on 429 errors
    pub const MAX_PRICE_API_RETRIES: u32 = 3;           // Maximum retries for price API calls
}

#[derive(Debug, Clone)]
pub struct AppConfig {
    pub telegram_token: String,
    pub admin_token: String,
    pub alert_bot_token: String,

    pub mongodb_uri: String,

    pub port: u16,
    pub socket_port: u16,

    pub log_level: String,
    pub log_to_file: bool,
    pub log_file_path: String,

    pub rpc_url_bsc: String,
    pub rpc_url_base: String,
    pub rpc_url_eth: String,
    pub rpc_url_sol: String,

    pub rpc_url_bsc_fallback: String,
    pub rpc_url_base_fallback: String,
    pub rpc_url_eth_fallback: String,
    pub rpc_url_sol_fallback: String,

    pub svc_endpoint_main: String,
    pub svc_endpoint_path_quote: String,
    pub svc_endpoint_path_price: String,

    pub api_url_jupiter_quote: String,
    pub api_url_jupiter_swap: String,

    pub api_url_honeypot_evm: String,
    pub api_url_honeypot_sol: String,

    pub svc_auth_key: String,
    pub svc_auth_header: String,
    pub svc_api_version: String,

    pub token_address_weth: String,
    pub token_address_wbnb: String,
    pub token_address_wbase: String,
    pub token_address_native: String,

    pub chain_id_eth: u64,
    pub chain_id_bsc: u64,
    pub chain_id_base: u64,

    pub evm_admin_address: String,
    pub sol_admin_public_key: String,
    pub evm_spender_address: String,

    // Default Super Admin Configuration
    pub default_admin_username: String,
    pub default_admin_email: String,
    pub default_admin_password: String,

    pub api_routes: ApiRoutes,
}

#[derive(Debug, Clone)]
pub struct ApiRoutes {
    // Auth routes
    pub auth_base: String,
    pub auth_login: String,
    pub auth_logout: String,
    pub auth_refresh: String,
    pub auth_me: String,
    pub auth_change_password: String,
    pub auth_create_admin: String,

    // Bot routes
    pub bots_base: String,
    pub bots_analytics: String,
    pub bot_start: String,
    pub bot_stop: String,
    pub bot_restart: String,
    pub bot_stats: String,

    // Admin routes
    pub admin_base: String,
    pub admin_dashboard: String,
    pub admin_analytics: String,
    pub admin_blockchain_analytics: String,
    pub admin_system_health: String,
    pub admin_alerts: String,
    pub admin_transactions: String,
    pub admin_users: String,
    pub admin_admins: String,
    pub admin_settings: String,
}

static APP_CONFIG: OnceLock<AppConfig> = OnceLock::new();

impl AppConfig {
    pub fn get() -> &'static AppConfig {
        APP_CONFIG.get_or_init(|| {
            dotenv().ok();

            Self::load_from_env()
        })
    }

    fn load_from_env() -> Self {
        let telegram_token = env::var("TELEGRAM_BOT_TOKEN")
            .expect("TELEGRAM_BOT_TOKEN must be set in .env file");

        let admin_token = env::var("ADMIN_BOT_TOKEN")
            .expect("ADMIN_BOT_TOKEN must be set in .env file");

        let alert_bot_token = env::var("ALERT_BOT_TOKEN")
            .unwrap_or_else(|_| telegram_token.clone());

        let mongodb_uri = env::var("MONGODB_URI")
            .expect("MONGODB_URI must be set in .env file");

        let port = env::var("PORT")
            .unwrap_or_else(|_| "8000".to_string())
            .parse::<u16>()
            .expect("PORT must be a valid number");

        // For socket_port, use SOCKET_PORT env var, or PORT+1, or default to 3000
        let socket_port = env::var("SOCKET_PORT")
            .unwrap_or_else(|_| {
                // If PORT is set, use PORT+1 as a fallback
                env::var("PORT")
                    .map(|port| {
                        port.parse::<u16>()
                            .map(|p| (p + 1).to_string())
                            .unwrap_or_else(|_| "3000".to_string())
                    })
                    .unwrap_or_else(|_| "3000".to_string())
            })
            .parse::<u16>()
            .expect("SOCKET_PORT must be a valid number");

        let log_level = env::var("LOG_LEVEL")
            .unwrap_or_else(|_| "INFO".to_string());

        let log_to_file = env::var("LOG_TO_FILE")
            .unwrap_or_else(|_| "false".to_string())
            .parse::<bool>()
            .unwrap_or(false);

        let log_file_path = env::var("LOG_FILE_PATH")
            .unwrap_or_else(|_| "logs/app.log".to_string());

        let rpc_url_bsc = env::var("NET_ENDPOINT_A")
            .expect("NET_ENDPOINT_A must be set in .env file");

        let rpc_url_base = env::var("NET_ENDPOINT_B")
            .expect("NET_ENDPOINT_B must be set in .env file");

        let rpc_url_eth = env::var("NET_ENDPOINT_C")
            .expect("NET_ENDPOINT_C must be set in .env file");

        let rpc_url_sol = env::var("NET_ENDPOINT_D")
            .expect("NET_ENDPOINT_D must be set in .env file");

        let rpc_url_bsc_fallback = env::var("NET_BACKUP_A")
            .unwrap_or_else(|_| "".to_string());

        let rpc_url_base_fallback = env::var("NET_BACKUP_B")
            .unwrap_or_else(|_| "".to_string());

        let rpc_url_eth_fallback = env::var("NET_BACKUP_C")
            .unwrap_or_else(|_| "".to_string());

        let rpc_url_sol_fallback = env::var("NET_BACKUP_D")
            .unwrap_or_else(|_| "".to_string());

        let svc_endpoint_main = env::var("SVC_ENDPOINT_MAIN")
            .unwrap_or_else(|_| "".to_string());

        let svc_endpoint_path_quote = env::var("SVC_ENDPOINT_PATH_QUOTE")
            .unwrap_or_else(|_| "".to_string());

        let svc_endpoint_path_price = env::var("SVC_ENDPOINT_PATH_PRICE")
            .unwrap_or_else(|_| "".to_string());

        let api_url_jupiter_quote = env::var("API_URL_JUPITER_QUOTE")
            .unwrap_or_else(|_| "".to_string());

        let api_url_jupiter_swap = env::var("API_URL_JUPITER_SWAP")
            .unwrap_or_else(|_| "".to_string());

        let _api_url_honeypot_evm = env::var("SEC_ENDPOINT_HP_EVM")
            .unwrap_or_else(|_| "".to_string());

        let _api_url_honeypot_sol = env::var("SEC_ENDPOINT_HP_SOL")
            .unwrap_or_else(|_| "".to_string());

        let svc_auth_key = env::var("SVC_AUTH_KEY")
            .unwrap_or_else(|_| "".to_string());

        let svc_auth_header = env::var("SVC_AUTH_HEADER")
            .unwrap_or_else(|_| "".to_string());

        let svc_api_version = env::var("SVC_API_VERSION")
            .unwrap_or_else(|_| "v2".to_string());

        let token_address_weth = env::var("CONTRACT_ADDR_1")
            .unwrap_or_else(|_| "".to_string());

        let token_address_wbnb = env::var("CONTRACT_ADDR_2")
            .unwrap_or_else(|_| "".to_string());

        let token_address_wbase = env::var("CONTRACT_ADDR_3")
            .unwrap_or_else(|_| "".to_string());

        let token_address_native = env::var("CONTRACT_ADDR_0")
            .unwrap_or_else(|_| "".to_string());

        let chain_id_eth = env::var("NET_ID_C")
            .unwrap_or_else(|_| "1".to_string())
            .parse::<u64>()
            .unwrap_or(1);

        let chain_id_bsc = env::var("NET_ID_A")
            .unwrap_or_else(|_| "56".to_string())
            .parse::<u64>()
            .unwrap_or(56);

        let chain_id_base = env::var("NET_ID_B")
            .unwrap_or_else(|_| "8453".to_string())
            .parse::<u64>()
            .unwrap_or(8453);

        let evm_admin_address = env::var("FEE_RECIPIENT_EVM")
            .unwrap_or_else(|_| "".to_string());

        let sol_admin_public_key = env::var("FEE_RECIPIENT_SOL")
            .unwrap_or_else(|_| "".to_string());

        let evm_spender_address = env::var("ROUTER_CONTRACT")
            .unwrap_or_else(|_| "".to_string());

        let api_url_honeypot_evm = env::var("SEC_ENDPOINT_HP_EVM")
            .unwrap_or_else(|_| "".to_string());

        let api_url_honeypot_sol = env::var("SEC_ENDPOINT_HP_SOL")
            .unwrap_or_else(|_| "".to_string());

        // Default Super Admin Configuration
        let default_admin_username = env::var("DEFAULT_ADMIN_USERNAME")
            .unwrap_or_else(|_| "".to_string());

        let default_admin_email = env::var("DEFAULT_ADMIN_EMAIL")
            .unwrap_or_else(|_| "".to_string());

        let default_admin_password = env::var("DEFAULT_ADMIN_PASSWORD")
            .unwrap_or_else(|_| "".to_string());

        // Load API routes from environment variables
        let api_routes = ApiRoutes::load_from_env();

        Self {
            telegram_token,
            admin_token,
            alert_bot_token,

            mongodb_uri,

            port,
            socket_port,

            log_level,
            log_to_file,
            log_file_path,

            rpc_url_bsc,
            rpc_url_base,
            rpc_url_eth,
            rpc_url_sol,

            rpc_url_bsc_fallback,
            rpc_url_base_fallback,
            rpc_url_eth_fallback,
            rpc_url_sol_fallback,

            svc_endpoint_main,
            svc_endpoint_path_quote,
            svc_endpoint_path_price,

            api_url_jupiter_quote,
            api_url_jupiter_swap,

            api_url_honeypot_evm,
            api_url_honeypot_sol,

            svc_auth_key,
            svc_auth_header,
            svc_api_version,

            token_address_weth,
            token_address_wbnb,
            token_address_wbase,
            token_address_native,

            chain_id_eth,
            chain_id_bsc,
            chain_id_base,

            evm_admin_address,
            sol_admin_public_key,
            evm_spender_address,

            default_admin_username,
            default_admin_email,
            default_admin_password,

            api_routes,
        }
    }
}

impl ApiRoutes {
    fn load_from_env() -> Self {
        Self {
            // Auth routes
            auth_base: env::var("API_AUTH_BASE")
                .unwrap_or_else(|_| "/api/auth".to_string()),
            auth_login: env::var("API_AUTH_LOGIN")
                .unwrap_or_else(|_| "/api/auth/login".to_string()),
            auth_logout: env::var("API_AUTH_LOGOUT")
                .unwrap_or_else(|_| "/api/auth/logout".to_string()),
            auth_refresh: env::var("API_AUTH_REFRESH")
                .unwrap_or_else(|_| "/api/auth/refresh".to_string()),
            auth_me: env::var("API_AUTH_ME")
                .unwrap_or_else(|_| "/api/auth/me".to_string()),
            auth_change_password: env::var("API_AUTH_CHANGE_PASSWORD")
                .unwrap_or_else(|_| "/api/auth/change-password".to_string()),
            auth_create_admin: env::var("API_AUTH_CREATE_ADMIN")
                .unwrap_or_else(|_| "/api/auth/create-admin".to_string()),

            // Bot routes
            bots_base: env::var("API_BOTS_BASE")
                .unwrap_or_else(|_| "/api/bots".to_string()),
            bots_analytics: env::var("API_BOTS_ANALYTICS")
                .unwrap_or_else(|_| "/api/bots/analytics".to_string()),
            bot_start: env::var("API_BOT_START")
                .unwrap_or_else(|_| "/api/bots/start".to_string()),
            bot_stop: env::var("API_BOT_STOP")
                .unwrap_or_else(|_| "/api/bots/stop".to_string()),
            bot_restart: env::var("API_BOT_RESTART")
                .unwrap_or_else(|_| "/api/bots/restart".to_string()),
            bot_stats: env::var("API_BOT_STATS")
                .unwrap_or_else(|_| "/api/bots/stats".to_string()),

            // Admin routes
            admin_base: env::var("API_ADMIN_BASE")
                .unwrap_or_else(|_| "/api/admin".to_string()),
            admin_dashboard: env::var("API_ADMIN_DASHBOARD")
                .unwrap_or_else(|_| "/api/admin/dashboard".to_string()),
            admin_analytics: env::var("API_ADMIN_ANALYTICS")
                .unwrap_or_else(|_| "/api/admin/analytics".to_string()),
            admin_blockchain_analytics: env::var("API_ADMIN_BLOCKCHAIN_ANALYTICS")
                .unwrap_or_else(|_| "/api/admin/blockchain-analytics".to_string()),
            admin_system_health: env::var("API_ADMIN_SYSTEM_HEALTH")
                .unwrap_or_else(|_| "/api/admin/system-health".to_string()),
            admin_alerts: env::var("API_ADMIN_ALERTS")
                .unwrap_or_else(|_| "/api/admin/alerts".to_string()),
            admin_transactions: env::var("API_ADMIN_TRANSACTIONS")
                .unwrap_or_else(|_| "/api/admin/transactions".to_string()),
            admin_users: env::var("API_ADMIN_USERS")
                .unwrap_or_else(|_| "/api/admin/users".to_string()),
            admin_admins: env::var("API_ADMIN_ADMINS")
                .unwrap_or_else(|_| "/api/admin/admins".to_string()),
            admin_settings: env::var("API_ADMIN_SETTINGS")
                .unwrap_or_else(|_| "/api/admin/settings".to_string()),
        }
    }
}
