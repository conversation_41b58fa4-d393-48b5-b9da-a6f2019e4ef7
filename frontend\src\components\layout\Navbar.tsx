import { useState, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { Dialog, Disclosure, Popover, Transition } from '@headlessui/react';
import {
  Bars3Icon,
  XMarkIcon,
  ServerIcon,
  GlobeAltIcon,
  CubeIcon,
  ShieldCheckIcon,
  ChartBarIcon,
} from '@heroicons/react/24/outline';
import { ChevronDownIcon } from '@heroicons/react/20/solid';
import Button from '../ui/Button';

const products = [
  { name: 'Web Hosting', description: 'Fast and reliable web hosting', href: '#', icon: ServerIcon },
  { name: 'Domains', description: 'Register and manage domains', href: '#', icon: GlobeAltIcon },
  { name: 'Containers', description: 'Deploy containerized applications', href: '#', icon: CubeIcon },
  { name: 'SSL Certificates', description: 'Secure your websites', href: '#', icon: ShieldCheckIcon },
  { name: 'Analytics', description: 'Insights for your applications', href: '#', icon: ChartBarIcon },
];

function classNames(...classes: string[]) {
  return classes.filter(Boolean).join(' ');
}

export default function Navbar() {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [scrolled, setScrolled] = useState(false);
  const location = useLocation();

  // Handle scroll effect for transparent to solid navbar transition
  useEffect(() => {
    const handleScroll = () => {
      const isScrolled = window.scrollY > 10;
      if (isScrolled !== scrolled) {
        setScrolled(isScrolled);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, [scrolled]);

  // Check if the current path matches the navigation item
  const isActive = (href: string) => {
    if (href.startsWith('#')) return false;
    return location.pathname === href || location.pathname.startsWith(`${href}/`);
  };

  return (
    <header className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${scrolled ? 'bg-gray-900/80 backdrop-blur-lg shadow-lg' : 'bg-transparent'}`}>
      <nav className="mx-auto flex max-w-7xl items-center justify-between p-4 lg:px-8" aria-label="Global">
        <div className="flex lg:flex-1">
          <Link to="/" className="-m-1.5 p-1.5 transition-transform duration-300 hover:scale-105">
            <span className="sr-only">PoolotHost</span>
            <div className="flex items-center">
              <ServerIcon className="h-8 w-auto text-secondary-500" />
              <span className="ml-2 text-xl font-bold text-white">PoolotHost</span>
            </div>
          </Link>
        </div>
        <div className="flex lg:hidden">
          <button
            type="button"
            className="-m-2.5 inline-flex items-center justify-center rounded-md p-2.5 text-white hover:bg-white/10 transition-colors duration-200"
            onClick={() => setMobileMenuOpen(true)}
          >
            <span className="sr-only">Open main menu</span>
            <Bars3Icon className="h-6 w-6" aria-hidden="true" />
          </button>
        </div>
        <Popover.Group className="hidden lg:flex lg:gap-x-8">
          <Popover className="relative">
            {({ open }) => (
              <>
                <Popover.Button
                  className={`flex items-center gap-x-1 text-sm font-medium leading-6 px-2 py-1 rounded-md transition-all duration-200
                    ${open ? 'text-white bg-white/10' : 'text-gray-300 hover:text-white hover:bg-white/5'}`}
                >
                  Products
                  <ChevronDownIcon
                    className={`h-5 w-5 flex-none transition-transform duration-200 ${open ? 'rotate-180 text-secondary-400' : 'text-gray-400'}`}
                    aria-hidden="true"
                  />
                </Popover.Button>

                <Transition
                  enter="transition ease-out duration-200"
                  enterFrom="opacity-0 translate-y-1"
                  enterTo="opacity-100 translate-y-0"
                  leave="transition ease-in duration-150"
                  leaveFrom="opacity-100 translate-y-0"
                  leaveTo="opacity-0 translate-y-1"
                >
                  <Popover.Panel className="absolute -left-8 top-full z-10 mt-3 w-screen max-w-md overflow-hidden rounded-xl bg-gray-900/90 backdrop-blur-xl shadow-lg ring-1 ring-white/10 border border-white/10">
                    <div className="p-4 grid grid-cols-1 gap-y-2">
                      {products.map((item) => (
                        <div
                          key={item.name}
                          className="group relative flex items-center gap-x-6 rounded-lg p-4 text-sm leading-6 hover:bg-white/5 transition-colors duration-200"
                        >
                          <div className="flex h-11 w-11 flex-none items-center justify-center rounded-lg bg-gray-800/50 border border-white/5 group-hover:border-secondary-500/50 group-hover:bg-gray-800 transition-all duration-200">
                            <item.icon className="h-6 w-6 text-secondary-400 group-hover:text-secondary-300 transition-colors duration-200" aria-hidden="true" />
                          </div>
                          <div className="flex-auto">
                            <a href={item.href} className="block font-medium text-white">
                              {item.name}
                              <span className="absolute inset-0" />
                            </a>
                            <p className="mt-1 text-gray-400 group-hover:text-gray-300 transition-colors duration-200">{item.description}</p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </Popover.Panel>
                </Transition>
              </>
            )}
          </Popover>

          {[
            { name: 'Pricing', href: '/pricing' },
            { name: 'Docs', href: '/docs' },
            { name: 'Blog', href: '/blog' },
          ].map((item) => (
            <Link
              key={item.name}
              to={item.href}
              className={`relative text-sm font-medium leading-6 px-2 py-1 rounded-md transition-all duration-200
                ${isActive(item.href)
                  ? 'text-white bg-white/10'
                  : 'text-gray-300 hover:text-white hover:bg-white/5'
                }
              `}
            >
              {item.name}
              {isActive(item.href) && (
                <span className="absolute bottom-0 left-0 w-full h-0.5 bg-gradient-to-r from-secondary-500 to-primary-500 rounded-full transform animate-pulse"></span>
              )}
            </Link>
          ))}
        </Popover.Group>
        <div className="hidden lg:flex lg:flex-1 lg:justify-end lg:gap-x-6 items-center">
          <Link
            to="/login"
            className="text-sm font-medium leading-6 text-gray-300 hover:text-white transition-colors duration-200"
          >
            Log in
          </Link>
          <Button
            variant="glass"
            size="sm"
            as={Link}
            to="/signup"
            glow={true}
            className="ml-2"
          >
            Sign up
          </Button>
        </div>
      </nav>
      <Dialog as="div" className="lg:hidden" open={mobileMenuOpen} onClose={setMobileMenuOpen}>
        <div className="fixed inset-0 z-10" />
        <Dialog.Panel className="fixed inset-y-0 right-0 z-10 w-full overflow-y-auto bg-gray-900/95 backdrop-blur-xl px-6 py-6 sm:max-w-sm border-l border-white/10">
          <div className="flex items-center justify-between">
            <Link to="/" className="-m-1.5 p-1.5" onClick={() => setMobileMenuOpen(false)}>
              <span className="sr-only">PoolotHost</span>
              <div className="flex items-center">
                <ServerIcon className="h-8 w-auto text-secondary-500" />
                <span className="ml-2 text-xl font-bold text-white">PoolotHost</span>
              </div>
            </Link>
            <button
              type="button"
              className="-m-2.5 rounded-md p-2.5 text-white hover:bg-white/10 transition-colors duration-200"
              onClick={() => setMobileMenuOpen(false)}
            >
              <span className="sr-only">Close menu</span>
              <XMarkIcon className="h-6 w-6" aria-hidden="true" />
            </button>
          </div>
          <div className="mt-6 flow-root">
            <div className="-my-6 divide-y divide-white/10">
              <div className="space-y-2 py-6">
                <Disclosure as="div" className="-mx-3">
                  {({ open }) => (
                    <>
                      <Disclosure.Button className="flex w-full items-center justify-between rounded-xl py-2 pl-3 pr-3.5 text-base font-medium leading-7 text-white hover:bg-white/5 transition-colors duration-200">
                        Products
                        <ChevronDownIcon
                          className={classNames(open ? 'rotate-180 text-secondary-400' : 'text-gray-400', 'h-5 w-5 flex-none transition-transform duration-200')}
                          aria-hidden="true"
                        />
                      </Disclosure.Button>
                      <Disclosure.Panel className="mt-2 space-y-2">
                        {products.map((item, index) => (
                          <Disclosure.Button
                            key={item.name}
                            as="a"
                            href={item.href}
                            className="block rounded-lg py-2 pl-6 pr-3 text-sm font-medium leading-7 text-gray-300 hover:bg-white/5 hover:text-white transition-colors duration-200"
                            style={{ animationDelay: `${index * 50}ms` }}
                          >
                            <div className="flex items-center">
                              <item.icon className="h-5 w-5 mr-2 text-secondary-400" />
                              {item.name}
                            </div>
                          </Disclosure.Button>
                        ))}
                      </Disclosure.Panel>
                    </>
                  )}
                </Disclosure>
                {[
                  { name: 'Pricing', href: '/pricing' },
                  { name: 'Docs', href: '/docs' },
                  { name: 'Blog', href: '/blog' },
                ].map((item, index) => (
                  <Link
                    key={item.name}
                    to={item.href}
                    className={`-mx-3 block rounded-xl px-3 py-2 text-base font-medium leading-7 transition-all duration-200
                      ${isActive(item.href)
                        ? 'text-white bg-white/10 border border-white/10'
                        : 'text-gray-300 hover:bg-white/5 hover:text-white'
                      }`}
                    onClick={() => setMobileMenuOpen(false)}
                    style={{ animationDelay: `${(index + products.length) * 50}ms` }}
                  >
                    {item.name}
                  </Link>
                ))}
              </div>
              <div className="py-6">
                <Link
                  to="/login"
                  className="-mx-3 block rounded-xl px-4 py-3 text-base font-medium leading-7 text-gray-300 hover:bg-white/5 hover:text-white transition-all duration-200"
                  onClick={() => setMobileMenuOpen(false)}
                >
                  Log in
                </Link>
                <div className="mt-4">
                  <Button
                    variant="glass"
                    fullWidth
                    as={Link}
                    to="/signup"
                    onClick={() => setMobileMenuOpen(false)}
                  >
                    Sign up
                  </Button>
                </div>
              </div>
            </div>
          </div>

          {/* Decorative elements */}
          <div className="absolute -z-10 -top-20 -right-20 w-64 h-64 bg-secondary-600/10 rounded-full blur-3xl"></div>
          <div className="absolute -z-10 -bottom-20 -left-20 w-64 h-64 bg-primary-600/10 rounded-full blur-3xl"></div>
        </Dialog.Panel>
      </Dialog>
    </header>
  );
}
