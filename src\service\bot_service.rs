use teloxide::{prelude::*, types::{ParseMode, MessageId, ChatId}};
use std::sync::{Arc, OnceLock};
use crate::model::{BotError, UserData, Blockchain, Wallet};
use crate::config::AppConfig;
use crate::service::{UserService, DbService, BlockchainService, AlertService, SolanaTraderService, EvmTraderService, TokenInfoService, HoneypotService, HighPerformanceTrader, ConnectionPoolManager, TradingPerformanceMonitor};
use std::str::FromStr;
use solana_sdk::signature::Signer;

// Singleton instance of the BotService
static BOT_SERVICE_INSTANCE: OnceLock<BotService> = OnceLock::new();

/// Service for interacting with the Telegram bot
#[derive(Clone)]
pub struct BotService {
    /// Telegram bot instance
    bot: Bot,
    /// User service
    user_service: Arc<UserService>,
    /// Blockchain service
    blockchain_service: Arc<BlockchainService>,
    /// Alert service
    alert_service: Arc<AlertService>,
    /// Solana trader service
    solana_trader_service: Arc<SolanaTraderService>,
    /// EVM trader service
    evm_trader_service: Arc<EvmTraderService>,
    /// Honeypot service
    honeypot_service: Arc<HoneypotService>,
    /// High-performance trader service
    high_performance_trader: Arc<HighPerformanceTrader>,
    /// Connection pool manager
    connection_pool_manager: Arc<ConnectionPoolManager>,
    /// Trading performance monitor
    trading_performance_monitor: Arc<TradingPerformanceMonitor>,
}

impl BotService {
    /// Creates a new bot service
    pub fn new() -> Result<Self, BotError> {
        let config = AppConfig::get();
        let bot = Bot::new(&config.telegram_token);

        // Create user service with 10 minute cache expiration
        let user_service = Arc::new(UserService::with_expiration(600));

        // Create blockchain service
        let blockchain_service = BlockchainService::new();

        // Create alert service
        let alert_service = Arc::new(AlertService::new());

        // Create Solana trader service with 4 worker threads
        let solana_trader_service = Arc::new(SolanaTraderService::new(
            &config.rpc_url_sol,
            None, // No Jito API key for now
            4,    // 4 worker threads
        ));

        // Create EVM trader service
        let evm_trader_service = Arc::new(EvmTraderService::new());

        // Create Honeypot service
        let honeypot_service = Arc::new(HoneypotService::new());

        // Create high-performance trading components
        let high_performance_trader = Arc::new(HighPerformanceTrader::new());
        let connection_pool_manager = Arc::new(ConnectionPoolManager::new());
        let trading_performance_monitor = Arc::new(TradingPerformanceMonitor::new());

        // Initialize connection pools for all blockchains
        let pool_manager_clone = connection_pool_manager.clone();
        tokio::spawn(async move {
            let _ = pool_manager_clone.initialize_rpc_pool(&Blockchain::SOL).await;
            let _ = pool_manager_clone.initialize_rpc_pool(&Blockchain::ETH).await;
            let _ = pool_manager_clone.initialize_rpc_pool(&Blockchain::BSC).await;
            let _ = pool_manager_clone.initialize_rpc_pool(&Blockchain::BASE).await;
            println!("All RPC connection pools initialized");
        });

        let service = Self {
            bot,
            user_service,
            blockchain_service,
            alert_service,
            solana_trader_service,
            evm_trader_service,
            honeypot_service,
            high_performance_trader,
            connection_pool_manager,
            trading_performance_monitor,
        };

        // Set the singleton instance
        let _ = BOT_SERVICE_INSTANCE.set(service.clone());

        Ok(service)
    }

    /// Gets the singleton instance of the BotService
    pub fn get_instance() -> &'static BotService {
        BOT_SERVICE_INSTANCE.get().expect("BotService not initialized")
    }

    /// Gets a reference to the blockchain service
    pub fn blockchain_service(&self) -> &BlockchainService {
        &self.blockchain_service
    }

    /// Gets a reference to the token info service
    pub fn token_info_service(&self) -> TokenInfoService {
        TokenInfoService::new()
    }

    /// Generates a wallet for a specific blockchain
    pub async fn generate_wallet(&self, blockchain: Blockchain) -> Result<Wallet, BotError> {
        self.blockchain_service.generate_wallet(blockchain).await
    }

    /// Gets a reference to the bot
    pub fn bot(&self) -> &Bot {
        &self.bot
    }

    /// Gets a reference to the user service
    pub fn user_service(&self) -> &UserService {
        &self.user_service
    }

    /// Gets a reference to the alert service
    pub fn alert_service(&self) -> &AlertService {
        &self.alert_service
    }

    /// Gets a reference to the Solana trader service
    pub fn solana_trader_service(&self) -> &SolanaTraderService {
        &self.solana_trader_service
    }

    /// Gets a reference to the EVM trader service
    pub fn evm_trader_service(&self) -> &EvmTraderService {
        &self.evm_trader_service
    }

    /// Gets a reference to the honeypot service
    pub fn honeypot_service(&self) -> &HoneypotService {
        &self.honeypot_service
    }

    /// Gets a reference to the high-performance trader service
    pub fn high_performance_trader(&self) -> &HighPerformanceTrader {
        &self.high_performance_trader
    }

    /// Gets a reference to the connection pool manager
    pub fn connection_pool_manager(&self) -> &ConnectionPoolManager {
        &self.connection_pool_manager
    }

    /// Gets a reference to the trading performance monitor
    pub fn trading_performance_monitor(&self) -> &TradingPerformanceMonitor {
        &self.trading_performance_monitor
    }

    /// 🚀 PRODUCTION: Execute high-performance Solana buy with fallback (auto-triggered)
    pub async fn execute_high_performance_solana_buy(
        &self,
        wallet: &crate::model::Wallet,
        token_mint: &str,
        sol_amount: u64,
        slippage_bps: u16,
        chat_id: Option<i64>,
    ) -> Result<crate::service::solana_trader_service::SwapResult, crate::model::BotError> {
        if crate::config::defaults::USE_HIGH_PERFORMANCE_TRADER {
            println!("🚀 AUTO-TRIGGERING High-Performance Solana Buy");

            // Try high-performance trader first
            match self.high_performance_trader.execute_solana_buy_optimized(
                wallet,
                token_mint,
                sol_amount,
                slippage_bps,
                chat_id,
            ).await {
                Ok(result) => {
                    println!("✅ High-Performance Solana Buy SUCCESS: {}", result.signature);

                    // Convert to SwapResult format expected by UI
                    use crate::service::solana_trader_service::{SwapResult, TokenInfo};

                    // Get actual token metadata
                    let token_metadata = self.solana_trader_service.get_token_metadata(token_mint).await;

                    let swap_result = SwapResult {
                        signature: solana_sdk::signature::Signature::from_str(&result.signature)
                            .unwrap_or_else(|_| solana_sdk::signature::Signature::default()),
                        input_token: TokenInfo {
                            symbol: "SOL".to_string(),
                            name: "Solana".to_string(),
                            mint: "So11111111111111111111111111111111111111112".to_string(),
                            decimals: 9,
                            price_usd: None,
                            logo_uri: None,
                        },
                        output_token: TokenInfo {
                            symbol: token_metadata.symbol.clone(),
                            name: token_metadata.name.clone(),
                            mint: token_mint.to_string(),
                            decimals: token_metadata.decimals,
                            price_usd: token_metadata.price_usd,
                            logo_uri: token_metadata.logo_uri.clone(),
                        },
                        input_amount: result.input_amount,
                        output_amount: result.output_amount,
                        price_impact: 0.0,
                        success: true,
                        timestamp: chrono::Utc::now(),
                        success_message: Some("High-performance buy completed successfully".to_string()),
                        trade: None,
                    };

                    return Ok(swap_result);
                }
                Err(e) => {
                    println!("⚠️ High-Performance Solana Buy FAILED: {}", e);
                    println!("🔄 FALLBACK: Using normal Solana trader");
                }
            }
        }

        // Fallback to normal Solana trader
        println!("🔄 Executing normal Solana buy as fallback");

        // Convert Wallet to Keypair for normal trader
        let private_key_bytes = wallet.private_key_bytes()
            .map_err(|e| crate::model::BotError::general_error(format!("Invalid private key: {}", e)))?;
        let keypair = solana_sdk::signature::Keypair::from_bytes(&private_key_bytes)
            .map_err(|e| crate::model::BotError::general_error(format!("Failed to create keypair: {}", e)))?;
        let token_pubkey = solana_sdk::pubkey::Pubkey::from_str(token_mint)
            .map_err(|e| crate::model::BotError::general_error(format!("Invalid token mint: {}", e)))?;

        match self.solana_trader_service.buy_token_with_sol(
            &keypair,
            &token_pubkey,
            sol_amount,
            slippage_bps as u32,
            chat_id, // Pass actual chat_id from UI
        ).await {
            Ok(result) => {
                println!("✅ Normal Solana Buy SUCCESS (fallback): {}", result.signature);
                Ok(result)
            }
            Err(e) => {
                println!("❌ Normal Solana Buy FAILED (fallback): {}", e);
                // Provide more specific error message based on the error type
                let error_msg = match e {
                    crate::service::solana_trader_service::SolanaTraderError::ValidationError(msg) => {
                        format!("Validation error: {}", msg)
                    }
                    crate::service::solana_trader_service::SolanaTraderError::DatabaseError(msg) => {
                        format!("Database error: {}", msg)
                    }
                    crate::service::solana_trader_service::SolanaTraderError::InsufficientBalance => {
                        "Insufficient balance for Solana buy operation".to_string()
                    }
                    _ => format!("Trading error: {}", e)
                };
                Err(crate::model::BotError::general_error(format!("Both high-performance and normal Solana buy failed: {}", error_msg)))
            }
        }
    }

    /// 🚀 PRODUCTION: Execute high-performance Solana sell with fallback (auto-triggered)
    pub async fn execute_high_performance_solana_sell(
        &self,
        wallet: &crate::model::Wallet,
        token_mint: &str,
        token_amount: u64,
        slippage_bps: u16,
        chat_id: Option<i64>,
    ) -> Result<crate::service::solana_trader_service::SwapResult, crate::model::BotError> {
        if crate::config::defaults::USE_HIGH_PERFORMANCE_TRADER {
            println!("🚀 AUTO-TRIGGERING High-Performance Solana Sell");

            // Try high-performance trader first
            match self.high_performance_trader.execute_solana_sell_optimized(
                wallet,
                token_mint,
                token_amount,
                slippage_bps,
                chat_id,
            ).await {
                Ok(signature) => {
                    println!("✅ High-Performance Solana Sell SUCCESS: {}", signature);

                    // Convert to SwapResult format expected by UI
                    use crate::service::solana_trader_service::{SwapResult, TokenInfo};

                    // Get actual token metadata
                    let token_metadata = self.solana_trader_service.get_token_metadata(token_mint).await;

                    // Get actual SOL received from current SOL balance (estimate)
                    let private_key_bytes = wallet.private_key_bytes()
                        .map_err(|e| crate::model::BotError::general_error(format!("Invalid private key: {}", e)))?;
                    let keypair = solana_sdk::signature::Keypair::from_bytes(&private_key_bytes)
                        .map_err(|e| crate::model::BotError::general_error(format!("Failed to create keypair: {}", e)))?;

                    let sol_mint = solana_sdk::pubkey::Pubkey::from_str("So11111111111111111111111111111111111111112")
                        .unwrap_or_default();
                    let actual_sol_received = if let Ok(sol_balance) = self.solana_trader_service.get_token_balance(&keypair.pubkey(), &sol_mint).await {
                        sol_balance
                    } else {
                        0 // Fallback to 0 if balance check fails
                    };

                    let swap_result = SwapResult {
                        signature: solana_sdk::signature::Signature::from_str(&signature)
                            .unwrap_or_else(|_| solana_sdk::signature::Signature::default()),
                        input_token: TokenInfo {
                            symbol: token_metadata.symbol.clone(),
                            name: token_metadata.name.clone(),
                            mint: token_mint.to_string(),
                            decimals: token_metadata.decimals,
                            price_usd: token_metadata.price_usd,
                            logo_uri: token_metadata.logo_uri.clone(),
                        },
                        output_token: TokenInfo {
                            symbol: "SOL".to_string(),
                            name: "Solana".to_string(),
                            mint: "So11111111111111111111111111111111111111112".to_string(),
                            decimals: 9,
                            price_usd: None,
                            logo_uri: None,
                        },
                        input_amount: token_amount,
                        output_amount: actual_sol_received,
                        price_impact: 0.0,
                        success: true,
                        timestamp: chrono::Utc::now(),
                        success_message: Some("High-performance sell completed successfully".to_string()),
                        trade: None,
                    };

                    return Ok(swap_result);
                }
                Err(e) => {
                    println!("⚠️ High-Performance Solana Sell FAILED: {}", e);
                    println!("🔄 FALLBACK: Using normal Solana trader");
                }
            }
        }

        // Fallback to normal Solana trader
        println!("🔄 Executing normal Solana sell as fallback");

        // Convert Wallet to Keypair for normal trader
        let private_key_bytes = wallet.private_key_bytes()
            .map_err(|e| crate::model::BotError::general_error(format!("Invalid private key: {}", e)))?;
        let keypair = solana_sdk::signature::Keypair::from_bytes(&private_key_bytes)
            .map_err(|e| crate::model::BotError::general_error(format!("Failed to create keypair: {}", e)))?;
        let token_pubkey = solana_sdk::pubkey::Pubkey::from_str(token_mint)
            .map_err(|e| crate::model::BotError::general_error(format!("Invalid token mint: {}", e)))?;

        match self.solana_trader_service.sell_token_for_sol(
            &keypair,
            &token_pubkey,
            token_amount,
            slippage_bps as u32,
            chat_id, // Pass the actual chat_id from UI
        ).await {
            Ok(result) => {
                println!("✅ Normal Solana Sell SUCCESS (fallback): {}", result.signature);
                Ok(result)
            }
            Err(e) => {
                println!("❌ Normal Solana Sell FAILED (fallback): {}", e);
                // Provide more specific error message based on the error type
                let error_msg = match e {
                    crate::service::solana_trader_service::SolanaTraderError::ValidationError(msg) => {
                        format!("Validation error: {}", msg)
                    }
                    crate::service::solana_trader_service::SolanaTraderError::DatabaseError(msg) => {
                        format!("Database error: {}", msg)
                    }
                    crate::service::solana_trader_service::SolanaTraderError::InsufficientBalance => {
                        "Insufficient balance for Solana sell operation".to_string()
                    }
                    _ => format!("Trading error: {}", e)
                };
                Err(crate::model::BotError::general_error(format!("Both high-performance and normal Solana sell failed: {}", error_msg)))
            }
        }
    }

    /// 🚀 PRODUCTION: Execute high-performance EVM buy with fallback (auto-triggered)
    pub async fn execute_high_performance_evm_buy(
        &self,
        wallet: &crate::model::Wallet,
        quote: crate::service::evm_trader_service::ZeroExQuoteResponse,
        blockchain: &crate::model::Blockchain,
        chat_id: Option<i64>,
    ) -> Result<String, crate::model::BotError> {
        if crate::config::defaults::USE_HIGH_PERFORMANCE_TRADER {
            println!("🚀 AUTO-TRIGGERING High-Performance EVM Buy on {:?}", blockchain);

            // Try high-performance trader first
            match self.high_performance_trader.execute_evm_buy_optimized(
                wallet,
                quote.clone(),
                blockchain,
                chat_id,
            ).await {
                Ok(tx_hash) => {
                    println!("✅ High-Performance EVM Buy SUCCESS: {}", tx_hash);
                    return Ok(tx_hash);
                }
                Err(e) => {
                    println!("⚠️ High-Performance EVM Buy FAILED: {}", e);
                    println!("🔄 FALLBACK: Using normal EVM trader");
                }
            }
        }

        // Fallback to normal EVM trader
        println!("🔄 Executing normal EVM buy as fallback");

        match self.evm_trader_service.execute_buy_with_parallel_fee_collection(
            wallet,
            quote,
            blockchain,
            chat_id,
        ).await {
            Ok(tx_hash) => {
                println!("✅ Normal EVM Buy SUCCESS (fallback): {}", tx_hash);
                Ok(tx_hash)
            }
            Err(e) => {
                println!("❌ Normal EVM Buy FAILED (fallback): {}", e);
                Err(crate::model::BotError::general_error(format!("Both high-performance and normal EVM buy failed: {}", e)))
            }
        }
    }

    /// 🚀 PRODUCTION: Execute high-performance EVM sell with fallback (auto-triggered)
    pub async fn execute_high_performance_evm_sell(
        &self,
        wallet: &crate::model::Wallet,
        quote: crate::service::evm_trader_service::ZeroExQuoteResponse,
        blockchain: &crate::model::Blockchain,
        chat_id: Option<i64>,
    ) -> Result<String, crate::model::BotError> {
        if crate::config::defaults::USE_HIGH_PERFORMANCE_TRADER {
            println!("🚀 AUTO-TRIGGERING High-Performance EVM Sell on {:?}", blockchain);

            // Try high-performance trader first (using buy method for sell operations)
            match self.high_performance_trader.execute_evm_buy_optimized(
                wallet,
                quote.clone(),
                blockchain,
                chat_id,
            ).await {
                Ok(tx_hash) => {
                    println!("✅ High-Performance EVM Sell SUCCESS: {}", tx_hash);
                    return Ok(tx_hash);
                }
                Err(e) => {
                    println!("⚠️ High-Performance EVM Sell FAILED: {}", e);
                    println!("🔄 FALLBACK: Using normal EVM trader");
                }
            }
        }

        // Fallback to normal EVM trader
        println!("🔄 Executing normal EVM sell as fallback");

        match self.evm_trader_service.execute_swap(
            wallet,
            quote,
            blockchain,
            chat_id,
        ).await {
            Ok(tx_hash) => {
                println!("✅ Normal EVM Sell SUCCESS (fallback): {}", tx_hash);
                Ok(tx_hash)
            }
            Err(e) => {
                println!("❌ Normal EVM Sell FAILED (fallback): {}", e);
                Err(crate::model::BotError::general_error(format!("Both high-performance and normal EVM sell failed: {}", e)))
            }
        }
    }

    /// Find a user by chat ID or create a new one (using cache)
    pub async fn get_or_create_user(&self, chat_id: i64, tg_user: &teloxide::types::User) -> Result<UserData, BotError> {
        // Use the user service to get or create user data (with caching)
        self.user_service.get_or_create_user_data(chat_id, tg_user).await
    }

    /// Update a user's blockchain and clean up previous conversation (optimized)
    pub async fn switch_blockchain(&self, chat_id: i64, blockchain: Blockchain) -> Result<UserData, BotError> {
        // Get the user data from cache or database
        let tg_user = match self.bot.get_chat_member(ChatId(chat_id), UserId(chat_id as u64)).await {
            Ok(member) => member.user,
            Err(_) => return Err(BotError::user_error("Failed to get user information")),
        };

        // Get user data from cache or database
        let mut user_data = self.user_service.get_or_create_user_data(chat_id, &tg_user).await?;

        // Get all messages in the active conversation
        let message_ids = user_data.take_conversation_messages();

        // Delete all messages in the conversation
        if !message_ids.is_empty() {
            for message_id in message_ids {
                // Ignore errors when deleting messages
                let _ = self.delete_message(chat_id, message_id).await;
            }
        }

        // Only update if the blockchain has changed
        if user_data.current_blockchain() != blockchain {
            println!("Switching blockchain from {:?} to {:?}", user_data.current_blockchain(), blockchain);

            // Update the blockchain
            user_data.set_current_blockchain(blockchain);

            // Update in cache
            self.user_service.cache_user_data(user_data.clone()).await;

            // Only update the user document in the database, not the entire user data
            DbService::save_user(&user_data.user).await?;
        } else {
            println!("Blockchain already set to {:?}, no update needed", blockchain);
        }

        Ok(user_data)
    }

    /// Sends a text message to a chat and tracks it in the conversation
    pub async fn send_message(&self, chat_id: i64, text: &str) -> Result<Message, BotError> {
        let chat_id = ChatId(chat_id);

        let sent_msg = self.bot
            .send_message(chat_id, text)
            .parse_mode(ParseMode::Html)
            .disable_web_page_preview(true)
            .await?;

        // Track the message in the user's conversation
        self.track_conversation_message(chat_id.0, sent_msg.id.0).await?;

        Ok(sent_msg)
    }

    /// Sends a message with a keyboard and tracks it in the conversation
    pub async fn send_message_with_keyboard(
        &self,
        chat_id: i64,
        text: &str,
        keyboard: teloxide::types::InlineKeyboardMarkup,
    ) -> Result<Message, BotError> {
        println!("Sending message with keyboard. Text length: {}", text.len());
        let chat_id = ChatId(chat_id);

        let sent_msg = self.bot
            .send_message(chat_id, text)
            .parse_mode(ParseMode::Html)
            .reply_markup(keyboard)
            .disable_web_page_preview(true)
            .await?;

        // Track the message in the user's conversation
        self.track_conversation_message(chat_id.0, sent_msg.id.0).await?;

        Ok(sent_msg)
    }

    /// Edits a message and tracks it in the conversation
    pub async fn edit_message(
        &self,
        chat_id: i64,
        message_id: i32,
        text: &str,
    ) -> Result<Message, BotError> {
        let chat_id = ChatId(chat_id);

        let edited_msg = self.bot
            .edit_message_text(chat_id, MessageId(message_id), text)
            .parse_mode(ParseMode::Html)
            .disable_web_page_preview(true)
            .await?;

        // Track the message in the user's conversation if it's a new message ID
        if edited_msg.id.0 != message_id {
            self.track_conversation_message(chat_id.0, edited_msg.id.0).await?;
        }

        Ok(edited_msg)
    }

    /// Edits a message with a keyboard and tracks it in the conversation
    pub async fn edit_message_with_keyboard(
        &self,
        chat_id: i64,
        message_id: i32,
        text: &str,
        keyboard: teloxide::types::InlineKeyboardMarkup,
    ) -> Result<Message, BotError> {
        println!("Editing message with keyboard. Text length: {}", text.len());
        let chat_id = ChatId(chat_id);

        let edited_msg = self.bot
            .edit_message_text(chat_id, MessageId(message_id), text)
            .parse_mode(ParseMode::Html)
            .reply_markup(keyboard)
            .disable_web_page_preview(true)
            .await?;

        // Track the message in the user's conversation if it's a new message ID
        if edited_msg.id.0 != message_id {
            self.track_conversation_message(chat_id.0, edited_msg.id.0).await?;
        }

        Ok(edited_msg)
    }

    /// Delete all messages in a chat
    pub async fn delete_message_by_id(&self, chat_id: i64, message_id: i32) -> Result<(), BotError> {
        let chat_id = ChatId(chat_id);

        // Ignore errors when deleting messages, as some might already be deleted
        let _ = self.bot
            .delete_message(chat_id, MessageId(message_id))
            .await;

        Ok(())
    }

    /// Answers a callback query
    pub async fn answer_callback_query(
        &self,
        callback_query_id: &str,
        text: Option<&str>,
        show_alert: bool,
    ) -> Result<(), BotError> {
        let mut answer = self.bot.answer_callback_query(callback_query_id);

        if let Some(text_str) = text {
            answer = answer.text(text_str);
        }

        answer.show_alert(show_alert).await?;

        Ok(())
    }

    /// Deletes a message
    pub async fn delete_message(&self, chat_id: i64, message_id: i32) -> Result<(), BotError> {
        let chat_id = ChatId(chat_id);

        // Ignore errors when deleting messages, as some might already be deleted
        let _ = self.bot
            .delete_message(chat_id, MessageId(message_id))
            .await;

        Ok(())
    }

    /// Track a message in the user's conversation (optimized with caching)
    pub async fn track_conversation_message(&self, chat_id: i64, message_id: i32) -> Result<(), BotError> {
        // Check if we have the user in cache first
        if let Some(mut user_data) = self.user_service.get_user_data_from_cache(chat_id).await {
            // Track the message
            user_data.track_conversation_message(message_id);

            // Update in cache only, don't save to database immediately
            // This reduces database operations for frequent message tracking
            self.user_service.cache_user_data(user_data).await;
            return Ok(());
        }

        // If not in cache, get the user from the database
        let tg_user = match self.bot.get_chat_member(ChatId(chat_id), UserId(chat_id as u64)).await {
            Ok(member) => member.user,
            Err(_) => return Ok(()), // Silently ignore if user not found
        };

        // Get user data from database
        let mut user_data = self.user_service.get_or_create_user_data(chat_id, &tg_user).await?;

        // Track the message
        user_data.track_conversation_message(message_id);

        // Update in cache
        self.user_service.cache_user_data(user_data.clone()).await;

        // Only update the session document in the database, not the entire user data
        // Use a faster write concern for better performance
        DbService::save_user_session(&user_data.session).await?;

        Ok(())
    }

    /// Clean up all messages in the user's conversation (optimized with caching)
    /// Deletes all bot messages and user replies in the session
    pub async fn cleanup_conversation(&self, chat_id: i64) -> Result<(), BotError> {
        // Get the user from the database or cache
        let tg_user = match self.bot.get_chat_member(ChatId(chat_id), UserId(chat_id as u64)).await {
            Ok(member) => member.user,
            Err(_) => return Ok(()), // Silently ignore if user not found
        };

        // Get user data from cache or database
        let mut user_data = self.user_service.get_or_create_user_data(chat_id, &tg_user).await?;

        // Get all messages in the active conversation
        let message_ids = user_data.take_conversation_messages();

        // Delete all messages in the conversation
        if !message_ids.is_empty() {
            for message_id in message_ids {
                // Ignore errors when deleting messages
                let _ = self.delete_message(chat_id, message_id).await;
            }
        }

        // Send a confirmation message that will be deleted after a few seconds
        let msg = self.send_message(
            chat_id,
            "✅ Chat cleaned up! This message will disappear in 3 seconds."
        ).await?;

        // Schedule deletion of the confirmation message
        let bot_clone = self.bot.clone();
        let msg_id = msg.id.0;
        tokio::spawn(async move {
            tokio::time::sleep(tokio::time::Duration::from_secs(3)).await;
            let _ = bot_clone.delete_message(ChatId(chat_id), MessageId(msg_id)).await;
        });

        // Update in cache
        self.user_service.cache_user_data(user_data.clone()).await;

        // Only update the session document in the database, not the entire user data
        DbService::save_user_session(&user_data.session).await?;

        Ok(())
    }

    /// Send a silent alert to a user
    pub async fn send_alert(&self, chat_id: i64, message: &str, alert_type: crate::service::AlertType) -> Result<(), BotError> {
        self.alert_service.send_silent_alert(self, chat_id, message, alert_type).await
    }

    /// Broadcast an alert to multiple users
    pub async fn broadcast_alert(&self, user_ids: &[i64], message: &str, alert_type: crate::service::AlertType) -> Result<(), BotError> {
        self.alert_service.broadcast_alert(self, user_ids, message, alert_type).await
    }
}
