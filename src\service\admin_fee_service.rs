use std::sync::Arc;
use std::time::Duration;
use mongodb::bson::{doc, oid::ObjectId};
use mongodb::Collection;
use tokio::time::timeout;
use futures::stream::TryStreamExt;
use tracing::{info, warn, error, debug};
use anyhow::{Result, Context};

use crate::model::{
    AdminFeeTransaction, FeeTransactionType, FeeTransactionStatus,
    AdminSettings, Blockchain, BotError
};
use crate::service::db_service::DbService;
use crate::config::defaults;

// Production constants
const DB_OPERATION_TIMEOUT: Duration = Duration::from_secs(10);
const MAX_RETRY_ATTEMPTS: i32 = 3;
const FEE_CALCULATION_PRECISION: f64 = 1e-9; // 9 decimal places

#[derive(Debug, Clone)]
pub struct AdminFeeService {
    pub db: mongodb::Database,
}

impl AdminFeeService {
    pub fn new() -> Self {
        Self {
            db: DbService::get_db().clone(),
        }
    }

    /// Get the current admin fee percentage from database settings
    pub async fn get_admin_fee_percentage(&self) -> Result<f64, BotError> {
        let collection: Collection<AdminSettings> = self.db.collection("admin_settings");

        let settings = timeout(
            DB_OPERATION_TIMEOUT,
            collection.find_one(doc! {}, None)
        )
        .await
        .map_err(|_| BotError::database_error("Database operation timeout".to_string()))?
        .map_err(|e| BotError::database_error(format!("Failed to fetch admin settings: {}", e)))?;

        match settings {
            Some(settings) => {
                debug!("Retrieved admin fee percentage: {}%", settings.admin_fee_percentage);
                Ok(settings.admin_fee_percentage)
            }
            None => {
                warn!("No admin settings found, using default fee percentage: {}%", defaults::DEFAULT_ADMIN_FEE_PERCENTAGE);
                Ok(defaults::DEFAULT_ADMIN_FEE_PERCENTAGE)
            }
        }
    }

    /// Get the admin fee percentage for a specific blockchain with intelligent fallback
    /// Priority: 1. Blockchain-specific fee -> 2. Default admin fee -> 3. Hardcoded default (0.5%)
    pub async fn get_admin_fee_percentage_for_blockchain(&self, blockchain: &Blockchain) -> Result<f64, BotError> {
        let collection: Collection<AdminSettings> = self.db.collection("admin_settings");

        let settings = timeout(
            DB_OPERATION_TIMEOUT,
            collection.find_one(doc! {}, None)
        )
        .await
        .map_err(|_| BotError::database_error("Database operation timeout".to_string()))?
        .map_err(|e| BotError::database_error(format!("Failed to fetch admin settings: {}", e)))?;

        match settings {
            Some(settings) => {
                // 1. First priority: Try to get blockchain-specific fee percentage
                let blockchain_fee = match blockchain {
                    Blockchain::ETH => settings.admin_fee_percentage_eth,
                    Blockchain::BSC => settings.admin_fee_percentage_bsc,
                    Blockchain::BASE => settings.admin_fee_percentage_base,
                    Blockchain::SOL => settings.admin_fee_percentage_sol,
                };

                let fee_percentage = if let Some(blockchain_specific_fee) = blockchain_fee {
                    // Use blockchain-specific fee if set
                    debug!(
                        "Using blockchain-specific admin fee for {:?}: {}%",
                        blockchain, blockchain_specific_fee
                    );
                    blockchain_specific_fee
                } else {
                    // 2. Second priority: Fall back to default admin fee percentage from settings
                    debug!(
                        "No blockchain-specific fee for {:?}, using default admin fee: {}%",
                        blockchain, settings.admin_fee_percentage
                    );
                    settings.admin_fee_percentage
                };

                Ok(fee_percentage)
            }
            None => {
                // 3. Third priority: Use hardcoded default if no admin settings found
                warn!(
                    "No admin settings found for {:?}, using hardcoded default fee percentage: {}%",
                    blockchain, defaults::DEFAULT_ADMIN_FEE_PERCENTAGE
                );
                Ok(defaults::DEFAULT_ADMIN_FEE_PERCENTAGE)
            }
        }
    }

    /// Get admin wallet address for a specific blockchain
    pub async fn get_admin_wallet_address(&self, blockchain: &Blockchain) -> Result<String, BotError> {
        let collection: Collection<AdminSettings> = self.db.collection("admin_settings");

        let settings = timeout(
            DB_OPERATION_TIMEOUT,
            collection.find_one(doc! {}, None)
        )
        .await
        .map_err(|_| BotError::database_error("Database operation timeout".to_string()))?
        .map_err(|e| BotError::database_error(format!("Failed to fetch admin settings: {}", e)))?;

        match settings {
            Some(settings) => {
                let wallet_address = match blockchain {
                    Blockchain::ETH => settings.admin_wallet_eth,
                    Blockchain::BSC => settings.admin_wallet_bsc,
                    Blockchain::BASE => settings.admin_wallet_base,
                    Blockchain::SOL => settings.admin_wallet_sol,
                };

                match wallet_address {
                    Some(address) if !address.trim().is_empty() => {
                        // Validate address format
                        match blockchain {
                            Blockchain::ETH | Blockchain::BSC | Blockchain::BASE => {
                                if address.starts_with("0x") && address.len() == 42 {
                                    debug!("Retrieved valid EVM admin wallet address for {:?}: {}", blockchain, address);
                                    Ok(address)
                                } else {
                                    error!("Invalid EVM admin wallet address format for {:?}: {} (must be 0x followed by 40 hex characters)", blockchain, address);
                                    Err(BotError::validation_error(format!("Invalid EVM admin wallet address format for {:?}: {}. Please configure a valid address in admin settings.", blockchain, address)))
                                }
                            }
                            Blockchain::SOL => {
                                // Validate Solana address using Solana SDK
                                use solana_sdk::pubkey::Pubkey;
                                use std::str::FromStr;

                                if Pubkey::from_str(&address).is_ok() {
                                    debug!("Retrieved valid Solana admin wallet address for {:?}: {}", blockchain, address);
                                    Ok(address)
                                } else {
                                    error!("Invalid Solana admin wallet address format: {} (must be valid base58 format)", address);
                                    Err(BotError::validation_error(format!("Invalid Solana admin wallet address format: {}. Please configure a valid base58 address in admin settings.", address)))
                                }
                            }
                        }
                    }
                    _ => {
                        error!("No admin wallet address configured for blockchain {:?}", blockchain);
                        Err(BotError::validation_error(format!("❌ No admin wallet configured for {:?}. Please configure admin wallet address in settings before enabling fee collection.", blockchain)))
                    }
                }
            }
            None => {
                error!("No admin settings found");
                Err(BotError::general_error("No admin settings found".to_string()))
            }
        }
    }

    /// Get RPC URL for a specific blockchain from admin settings with env fallback
    pub async fn get_rpc_url_for_blockchain(&self, blockchain: &Blockchain) -> Result<String, BotError> {
        let collection: Collection<AdminSettings> = self.db.collection("admin_settings");

        let settings = timeout(
            DB_OPERATION_TIMEOUT,
            collection.find_one(doc! {}, None)
        )
        .await
        .map_err(|_| BotError::database_error("Database operation timeout".to_string()))?
        .map_err(|e| BotError::database_error(format!("Failed to fetch admin settings: {}", e)))?;

        // Get RPC URL from admin settings or fall back to environment variables
        let rpc_url: Option<String> = match settings {
            Some(settings) => {
                let admin_rpc: Option<String> = match blockchain {
                    Blockchain::ETH => settings.rpc_url_eth,
                    Blockchain::BSC => settings.rpc_url_bsc,
                    Blockchain::BASE => settings.rpc_url_base,
                    Blockchain::SOL => settings.rpc_url_sol,
                };

                // If admin setting exists and is not empty, use it
                if let Some(url) = admin_rpc {
                    if !url.trim().is_empty() {
                        debug!("Using admin-configured RPC URL for {:?}: {}", blockchain, url);
                        return Ok(url);
                    }
                }

                // Fall back to environment variables
                None
            }
            None => None,
        };

        // Use environment variables as fallback
        let env_rpc = match blockchain {
            Blockchain::ETH => std::env::var("ETH_RPC_URL")
                .unwrap_or_else(|_| "https://eth-mainnet.g.alchemy.com/v2/demo".to_string()),
            Blockchain::BSC => std::env::var("BSC_RPC_URL")
                .unwrap_or_else(|_| "https://bsc-dataseed.binance.org".to_string()),
            Blockchain::BASE => std::env::var("BASE_RPC_URL")
                .unwrap_or_else(|_| "https://mainnet.base.org".to_string()),
            Blockchain::SOL => std::env::var("SOL_RPC_URL")
                .unwrap_or_else(|_| "https://api.mainnet-beta.solana.com".to_string()),
        };

        debug!("Using environment RPC URL for {:?}: {}", blockchain, env_rpc);
        Ok(env_rpc)
    }

    /// Calculate admin fee amount based on percentage and original amount
    /// NOTE: This method does NOT apply minimum threshold - use calculate_fee_amount_with_minimum for production
    pub fn calculate_fee_amount(&self, original_amount: f64, fee_percentage: f64) -> f64 {
        let fee_amount = (original_amount * fee_percentage) / 100.0;

        // Round to avoid floating point precision issues
        (fee_amount * (1.0 / FEE_CALCULATION_PRECISION)).round() * FEE_CALCULATION_PRECISION
    }

    /// Calculate admin fee amount with automatic minimum threshold enforcement ($0.25 USD equivalent)
    /// This should be used for all production fee calculations
    pub async fn calculate_fee_amount_with_minimum(
        &self,
        original_amount: f64,
        fee_percentage: f64,
        blockchain: &crate::model::Blockchain
    ) -> Result<f64, BotError> {
        let price_service = crate::service::price_service::PriceService::new();

        match price_service.calculate_admin_fee_with_minimum_enforcement(blockchain, original_amount, fee_percentage).await {
            Ok(final_fee) => Ok(final_fee),
            Err(e) => {
                error!("Failed to calculate admin fee with minimum enforcement: {}", e);
                // Fallback to basic calculation without minimum (not recommended)
                Ok(self.calculate_fee_amount(original_amount, fee_percentage))
            }
        }
    }

    /// Calculate the amount after deducting admin fee (for buy operations)
    pub fn calculate_amount_after_fee(&self, original_amount: f64, fee_percentage: f64) -> (f64, f64) {
        let fee_amount = self.calculate_fee_amount(original_amount, fee_percentage);
        let amount_after_fee = original_amount - fee_amount;

        debug!(
            "Fee calculation: original={}, fee_percentage={}%, fee_amount={}, remaining={}",
            original_amount, fee_percentage, fee_amount, amount_after_fee
        );

        (amount_after_fee, fee_amount)
    }

    /// Get minimum gas balance required for ERC-20 transfers (dynamic per blockchain)
    pub fn get_minimum_gas_balance(&self, blockchain: &Blockchain) -> f64 {
        match blockchain {
            Blockchain::ETH => 0.002,   // Ethereum mainnet: higher gas costs
            Blockchain::BSC => 0.001,   // BSC: lower gas costs
            Blockchain::BASE => 0.0005, // Base: very low gas costs
            Blockchain::SOL => 0.001,   // Solana: low costs (in SOL)
        }
    }

    /// Get estimated gas cost for transactions (dynamic per blockchain)
    pub fn get_estimated_gas_cost(&self, blockchain: &Blockchain) -> f64 {
        match blockchain {
            Blockchain::ETH => 0.003,   // Ethereum: ~$50-100 in gas during high congestion
            Blockchain::BSC => 0.002,   // BSC: moderate gas costs
            Blockchain::BASE => 0.001,  // Base: low gas costs
            Blockchain::SOL => 0.001,   // Solana: very low costs
        }
    }

    /// Get blockchain-specific gas buffer for fee calculations
    pub fn get_gas_buffer_percentage(&self, blockchain: &Blockchain) -> f64 {
        match blockchain {
            Blockchain::ETH => 3.0,     // Ethereum: 3% buffer for high volatility
            Blockchain::BSC => 2.0,     // BSC: 2% buffer
            Blockchain::BASE => 1.5,    // Base: 1.5% buffer for low costs
            Blockchain::SOL => 1.0,     // Solana: 1% buffer for very low costs
        }
    }

    /// Validate wallet address format for a specific blockchain
    pub fn validate_wallet_address(&self, address: &str, blockchain: &Blockchain) -> bool {
        match blockchain {
            Blockchain::ETH | Blockchain::BSC | Blockchain::BASE => {
                // EVM addresses should be 42 characters long and start with 0x
                address.len() == 42 && address.starts_with("0x") &&
                address[2..].chars().all(|c| c.is_ascii_hexdigit())
            }
            Blockchain::SOL => {
                // Solana addresses are base58 encoded and typically 32-44 characters
                use solana_sdk::pubkey::Pubkey;
                use std::str::FromStr;
                Pubkey::from_str(address).is_ok()
            }
        }
    }

    /// Get admin wallet address for a specific blockchain
    pub async fn get_admin_wallet_address_for_blockchain(&self, blockchain: &Blockchain) -> Result<Option<String>, BotError> {
        use mongodb::bson::doc;
        use crate::model::AdminSettings;
        use mongodb::Collection;

        let collection: Collection<AdminSettings> = self.db.collection("admin_settings");

        // Query for the admin settings document
        let filter = doc! {};

        match collection.find_one(filter, None).await {
            Ok(Some(settings)) => {
                let wallet_address = match blockchain {
                    Blockchain::ETH => settings.admin_wallet_eth,
                    Blockchain::BSC => settings.admin_wallet_bsc,
                    Blockchain::BASE => settings.admin_wallet_base,
                    Blockchain::SOL => settings.admin_wallet_sol,
                };

                if let Some(address) = wallet_address {
                    if !address.is_empty() && self.validate_wallet_address(&address, blockchain) {
                        println!("📋 Found admin wallet address in settings for {:?}: {}", blockchain, address);
                        return Ok(Some(address));
                    }
                }
                Ok(None)
            }
            Ok(None) => {
                println!("📋 No admin settings configured for wallet addresses");
                Ok(None)
            }
            Err(e) => {
                println!("❌ Failed to query admin settings for wallet address: {}", e);
                Err(BotError::database_error(format!("Failed to get admin wallet address from settings: {}", e)))
            }
        }
    }

    /// Get RPC URL from admin settings for a specific blockchain
    pub async fn get_rpc_url_from_admin_settings(&self, blockchain: &Blockchain) -> Result<Option<String>, BotError> {
        use mongodb::bson::doc;
        use crate::model::AdminSettings;
        use mongodb::Collection;

        let collection: Collection<AdminSettings> = self.db.collection("admin_settings");

        // Query for the admin settings document
        let filter = doc! {};

        match collection.find_one(filter, None).await {
            Ok(Some(settings)) => {
                let rpc_url = match blockchain {
                    Blockchain::ETH => settings.rpc_url_eth,
                    Blockchain::BSC => settings.rpc_url_bsc,
                    Blockchain::BASE => settings.rpc_url_base,
                    Blockchain::SOL => settings.rpc_url_sol,
                };

                if let Some(url) = rpc_url {
                    if !url.is_empty() && url.starts_with("http") {
                        println!("📋 Found RPC URL in admin settings for {:?}: {}", blockchain, url);
                        return Ok(Some(url));
                    }
                }
                Ok(None)
            }
            Ok(None) => {
                println!("📋 No admin settings configured for RPC URLs");
                Ok(None)
            }
            Err(e) => {
                println!("❌ Failed to query admin settings for RPC URL: {}", e);
                Err(BotError::database_error(format!("Failed to get RPC URL from admin settings: {}", e)))
            }
        }
    }

    /// Check if fee transaction already exists for this trade
    pub async fn fee_transaction_exists(&self, trade_id: Option<ObjectId>, user_id: ObjectId, transaction_type: FeeTransactionType) -> Result<bool, BotError> {
        let collection: Collection<AdminFeeTransaction> = self.db.collection("admin_fee_transactions");

        let filter = if let Some(trade_id) = trade_id {
            doc! {
                "trade_id": trade_id,
                "user_id": user_id,
                "transaction_type": match transaction_type {
                    FeeTransactionType::BuyFee => "buy_fee",
                    FeeTransactionType::SellFee => "sell_fee",
                },
                "status": { "$in": ["pending", "completed"] } // Don't count failed transactions
            }
        } else {
            // If no trade_id, check by user and recent timestamp to avoid duplicates
            let recent_time = chrono::Utc::now().timestamp() - 300; // 5 minutes ago
            doc! {
                "user_id": user_id,
                "transaction_type": match transaction_type {
                    FeeTransactionType::BuyFee => "buy_fee",
                    FeeTransactionType::SellFee => "sell_fee",
                },
                "created_at": { "$gte": recent_time },
                "status": { "$in": ["pending", "completed"] }
            }
        };

        let existing = timeout(
            DB_OPERATION_TIMEOUT,
            collection.find_one(filter, None)
        )
        .await
        .map_err(|_| BotError::database_error("Database operation timeout".to_string()))?
        .map_err(|e| BotError::database_error(format!("Failed to check existing fee transaction: {}", e)))?;

        Ok(existing.is_some())
    }

    /// Find existing fee transaction ID for the given trade and user
    async fn find_existing_fee_transaction_id(
        &self,
        trade_id: Option<ObjectId>,
        user_id: ObjectId,
        transaction_type: FeeTransactionType,
    ) -> Result<Option<ObjectId>, BotError> {
        let collection: Collection<AdminFeeTransaction> = self.db.collection("admin_fee_transactions");

        let filter = if let Some(trade_id) = trade_id {
            doc! {
                "trade_id": trade_id,
                "user_id": user_id,
                "transaction_type": match transaction_type {
                    FeeTransactionType::BuyFee => "buy_fee",
                    FeeTransactionType::SellFee => "sell_fee",
                },
                "status": { "$in": ["pending", "completed"] }
            }
        } else {
            // If no trade_id, check by user and recent timestamp to avoid duplicates
            let recent_time = chrono::Utc::now().timestamp() - 300; // 5 minutes ago
            doc! {
                "user_id": user_id,
                "transaction_type": match transaction_type {
                    FeeTransactionType::BuyFee => "buy_fee",
                    FeeTransactionType::SellFee => "sell_fee",
                },
                "created_at": { "$gte": recent_time },
                "status": { "$in": ["pending", "completed"] }
            }
        };

        let existing_transaction = timeout(
            DB_OPERATION_TIMEOUT,
            collection.find_one(filter, None)
        )
        .await
        .map_err(|_| BotError::general_error("Database operation timeout".to_string()))?
        .map_err(|e| BotError::general_error(format!("Failed to check existing fee transaction: {}", e)))?;

        Ok(existing_transaction.and_then(|tx| tx.id))
    }

    /// Create a new admin fee transaction record
    pub async fn create_fee_transaction(
        &self,
        user_id: ObjectId,
        trade_id: Option<ObjectId>,
        blockchain: Blockchain,
        transaction_type: FeeTransactionType,
        fee_percentage: f64,
        original_amount: f64,
        fee_amount: f64,
        fee_token_symbol: String,
        fee_token_address: String,
        admin_address: String,
        user_wallet_address: String,
    ) -> Result<ObjectId, BotError> {
        // Check if fee transaction already exists to prevent duplicates
        if self.fee_transaction_exists(trade_id, user_id, transaction_type.clone()).await? {
            debug!("Fee transaction already exists for user {} and trade {:?}, skipping duplicate", user_id, trade_id);

            // Find and return the existing fee transaction ID instead of erroring
            if let Some(existing_id) = self.find_existing_fee_transaction_id(trade_id, user_id, transaction_type).await? {
                return Ok(existing_id);
            } else {
                return Err(BotError::validation_error("Fee transaction already exists for this trade".to_string()));
            }
        }

        let collection: Collection<AdminFeeTransaction> = self.db.collection("admin_fee_transactions");

        let fee_transaction = AdminFeeTransaction::new(
            user_id,
            trade_id,
            blockchain,
            transaction_type,
            fee_percentage,
            original_amount,
            fee_amount,
            fee_token_symbol,
            fee_token_address,
            admin_address,
            user_wallet_address,
        );

        let result = timeout(
            DB_OPERATION_TIMEOUT,
            collection.insert_one(&fee_transaction, None)
        )
        .await
        .map_err(|_| BotError::general_error("Database operation timeout".to_string()))?
        .map_err(|e| BotError::general_error(format!("Failed to create fee transaction: {}", e)))?;

        let fee_transaction_id = result.inserted_id
            .as_object_id()
            .ok_or_else(|| BotError::general_error("Failed to get inserted fee transaction ID".to_string()))?;

        info!(
            "Created admin fee transaction: id={}, user_id={}, blockchain={:?}, type={:?}, fee_amount={}",
            fee_transaction_id, user_id, blockchain, fee_transaction.transaction_type, fee_amount
        );

        Ok(fee_transaction_id)
    }

    /// Update fee transaction status to completed
    pub async fn mark_fee_transaction_completed(
        &self,
        fee_transaction_id: ObjectId,
        transaction_hash: String,
        block_number: Option<u64>,
        gas_fee: Option<f64>,
    ) -> Result<(), BotError> {
        let collection: Collection<AdminFeeTransaction> = self.db.collection("admin_fee_transactions");

        let update_doc = doc! {
            "$set": {
                "status": "completed",
                "transaction_hash": &transaction_hash,
                "block_number": block_number.map(|n| n as i64),
                "gas_fee": gas_fee,
                "completed_at": chrono::Utc::now().timestamp() as i64,
                "updated_at": chrono::Utc::now().timestamp() as i64,
            }
        };

        timeout(
            DB_OPERATION_TIMEOUT,
            collection.update_one(doc! { "_id": fee_transaction_id }, update_doc, None)
        )
        .await
        .map_err(|_| BotError::general_error("Database operation timeout".to_string()))?
        .map_err(|e| BotError::general_error(format!("Failed to update fee transaction: {}", e)))?;

        info!(
            "Marked fee transaction as completed: id={}, hash={}",
            fee_transaction_id, transaction_hash
        );

        Ok(())
    }

    /// Update fee transaction status to completed WITH adjusted fee amount (for threshold enforcement)
    pub async fn mark_fee_transaction_completed_with_adjusted_amount(
        &self,
        fee_transaction_id: ObjectId,
        transaction_hash: String,
        actual_fee_amount: f64, // The actual fee amount that was charged (after threshold adjustment)
        block_number: Option<u64>,
        gas_fee: Option<f64>,
    ) -> Result<(), BotError> {
        let collection: Collection<AdminFeeTransaction> = self.db.collection("admin_fee_transactions");

        let update_doc = doc! {
            "$set": {
                "status": "completed",
                "transaction_hash": &transaction_hash,
                "fee_amount": actual_fee_amount, // Update to the actual amount charged
                "block_number": block_number.map(|n| n as i64),
                "gas_fee": gas_fee,
                "completed_at": chrono::Utc::now().timestamp() as i64,
                "updated_at": chrono::Utc::now().timestamp() as i64,
            }
        };

        timeout(
            DB_OPERATION_TIMEOUT,
            collection.update_one(doc! { "_id": fee_transaction_id }, update_doc, None)
        )
        .await
        .map_err(|_| BotError::general_error("Database operation timeout".to_string()))?
        .map_err(|e| BotError::general_error(format!("Failed to update fee transaction: {}", e)))?;

        info!("✅ Fee transaction completed with adjusted amount: id={}, hash={}, actual_fee={}",
              fee_transaction_id, transaction_hash, actual_fee_amount);

        Ok(())
    }

    /// Update fee transaction status to failed
    pub async fn mark_fee_transaction_failed(
        &self,
        fee_transaction_id: ObjectId,
        error_message: String,
    ) -> Result<(), BotError> {
        let collection: Collection<AdminFeeTransaction> = self.db.collection("admin_fee_transactions");

        // Get current retry count
        let current_transaction = timeout(
            DB_OPERATION_TIMEOUT,
            collection.find_one(doc! { "_id": fee_transaction_id }, None)
        )
        .await
        .map_err(|_| BotError::general_error("Database operation timeout".to_string()))?
        .map_err(|e| BotError::general_error(format!("Failed to fetch fee transaction: {}", e)))?;

        let retry_count = current_transaction
            .map(|tx| tx.retry_count + 1)
            .unwrap_or(1);

        let update_doc = doc! {
            "$set": {
                "status": "failed",
                "error_message": &error_message,
                "retry_count": retry_count as i32,
                "updated_at": chrono::Utc::now().timestamp() as i64,
            }
        };

        timeout(
            DB_OPERATION_TIMEOUT,
            collection.update_one(doc! { "_id": fee_transaction_id }, update_doc, None)
        )
        .await
        .map_err(|_| BotError::general_error("Database operation timeout".to_string()))?
        .map_err(|e| BotError::general_error(format!("Failed to update fee transaction: {}", e)))?;

        warn!(
            "Marked fee transaction as failed: id={}, error={}, retry_count={}",
            fee_transaction_id, error_message, retry_count
        );

        Ok(())
    }

    /// Get failed fee transactions that can be retried
    pub async fn get_retryable_fee_transactions(&self) -> Result<Vec<AdminFeeTransaction>, BotError> {
        let collection: Collection<AdminFeeTransaction> = self.db.collection("admin_fee_transactions");

        let filter = doc! {
            "status": "failed",
            "retry_count": { "$lt": MAX_RETRY_ATTEMPTS }
        };

        let mut cursor = timeout(
            DB_OPERATION_TIMEOUT,
            collection.find(filter, None)
        )
        .await
        .map_err(|_| BotError::general_error("Database operation timeout".to_string()))?
        .map_err(|e| BotError::general_error(format!("Failed to fetch retryable fee transactions: {}", e)))?;

        let mut transactions = Vec::new();
        while let Some(transaction) = timeout(
            DB_OPERATION_TIMEOUT,
            cursor.try_next()
        )
        .await
        .map_err(|_| BotError::general_error("Database operation timeout".to_string()))?
        .map_err(|e| BotError::general_error(format!("Failed to iterate fee transactions: {}", e)))?
        {
            transactions.push(transaction);
        }

        debug!("Found {} retryable fee transactions", transactions.len());
        Ok(transactions)
    }

    /// Start background fee collection service
    pub async fn start_background_fee_collection(&self) {
        let service = self.clone();
        tokio::spawn(async move {
            let mut interval = tokio::time::interval(tokio::time::Duration::from_secs(300)); // 5 minutes

            loop {
                interval.tick().await;

                match service.process_failed_fee_transactions().await {
                    Ok(processed) => {
                        if processed > 0 {
                            info!("Background fee collection processed {} failed transactions", processed);
                        }
                    }
                    Err(e) => {
                        error!("Background fee collection error: {}", e);
                    }
                }
            }
        });
    }

    /// Process failed fee transactions in background
    async fn process_failed_fee_transactions(&self) -> Result<usize, BotError> {
        let failed_transactions = self.get_retryable_fee_transactions().await?;
        let mut processed = 0;

        for transaction in failed_transactions {
            // Skip if too many retries
            if transaction.retry_count >= MAX_RETRY_ATTEMPTS {
                continue;
            }

            // Add delay between retries (exponential backoff)
            let delay_minutes = 2_u64.pow(transaction.retry_count as u32);
            let created_time = chrono::DateTime::from_timestamp(transaction.created_at as i64, 0)
                .unwrap_or_else(|| chrono::Utc::now());
            let next_retry_time = created_time + chrono::Duration::minutes(delay_minutes as i64);

            if chrono::Utc::now() < next_retry_time {
                continue; // Not time to retry yet
            }

            info!(
                "Retrying failed fee transaction: id={}, blockchain={:?}, retry_count={}",
                transaction.id.map(|id| id.to_string()).unwrap_or_else(|| "unknown".to_string()),
                transaction.blockchain,
                transaction.retry_count
            );

            // Attempt to retry the fee collection based on blockchain
            let retry_result = match transaction.blockchain {
                Blockchain::ETH | Blockchain::BSC | Blockchain::BASE => {
                    self.retry_evm_fee_collection(&transaction).await
                }
                Blockchain::SOL => {
                    self.retry_solana_fee_collection(&transaction).await
                }
            };

            match retry_result {
                Ok(tx_hash) => {
                    info!("Fee collection retry successful: {}", tx_hash);
                    if let Err(e) = self.mark_fee_transaction_completed(
                        transaction.id.expect("Transaction ID should exist"),
                        tx_hash,
                        None,
                        None,
                    ).await {
                        error!("Failed to mark retried transaction as completed: {}", e);
                    }
                }
                Err(e) => {
                    error!("Fee collection retry failed: {}", e);
                    if let Err(mark_err) = self.mark_fee_transaction_failed(
                        transaction.id.expect("Transaction ID should exist"),
                        format!("Retry attempt {}: {}", transaction.retry_count + 1, e),
                    ).await {
                        error!("Failed to update retry count for transaction {:?}: {}", transaction.id, mark_err);
                    }
                }
            }

            processed += 1;
        }

        Ok(processed)
    }

    /// Get admin fee statistics for a specific time period
    pub async fn get_fee_statistics(
        &self,
        start_timestamp: u64,
        end_timestamp: u64,
    ) -> Result<FeeStatistics, BotError> {
        let collection: Collection<AdminFeeTransaction> = self.db.collection("admin_fee_transactions");

        let pipeline = vec![
            doc! {
                "$match": {
                    "created_at": {
                        "$gte": start_timestamp as i64,
                        "$lte": end_timestamp as i64
                    },
                    "status": "completed"
                }
            },
            doc! {
                "$group": {
                    "_id": null,
                    "total_fee_amount": { "$sum": "$fee_amount" },
                    "total_transactions": { "$sum": 1 },
                    "avg_fee_amount": { "$avg": "$fee_amount" },
                    "solana_fees": {
                        "$sum": {
                            "$cond": [
                                { "$eq": ["$blockchain", "SOL"] },
                                "$fee_amount",
                                0
                            ]
                        }
                    },
                    "evm_fees": {
                        "$sum": {
                            "$cond": [
                                { "$ne": ["$blockchain", "SOL"] },
                                "$fee_amount",
                                0
                            ]
                        }
                    }
                }
            }
        ];

        let mut cursor = timeout(
            DB_OPERATION_TIMEOUT,
            collection.aggregate(pipeline, None)
        )
        .await
        .map_err(|_| BotError::general_error("Database operation timeout".to_string()))?
        .map_err(|e| BotError::general_error(format!("Failed to aggregate fee statistics: {}", e)))?;

        if let Some(result) = timeout(
            DB_OPERATION_TIMEOUT,
            cursor.try_next()
        )
        .await
        .map_err(|_| BotError::general_error("Database operation timeout".to_string()))?
        .map_err(|e| BotError::general_error(format!("Failed to get fee statistics result: {}", e)))?
        {
            Ok(FeeStatistics {
                total_fee_amount: result.get_f64("total_fee_amount").unwrap_or(0.0),
                total_transactions: result.get_i32("total_transactions").unwrap_or(0) as u64,
                avg_fee_amount: result.get_f64("avg_fee_amount").unwrap_or(0.0),
                solana_fees: result.get_f64("solana_fees").unwrap_or(0.0),
                evm_fees: result.get_f64("evm_fees").unwrap_or(0.0),
            })
        } else {
            Ok(FeeStatistics::default())
        }
    }

    /// Smart fee collection for sell operations - tries native token first, then waits for sell completion
    pub async fn execute_smart_sell_fee_collection(&self, transaction: &AdminFeeTransaction) -> Result<String, BotError> {
        println!("Executing smart sell fee collection for user {} on {:?}", transaction.user_id, transaction.blockchain);

        match transaction.blockchain {
            Blockchain::ETH | Blockchain::BSC | Blockchain::BASE => {
                self.execute_smart_evm_sell_fee_collection(transaction).await
            }
            Blockchain::SOL => {
                self.execute_smart_solana_sell_fee_collection(transaction).await
            }
        }
    }

    /// Smart EVM sell fee collection - prioritize native tokens, fallback to received tokens
    async fn execute_smart_evm_sell_fee_collection(&self, transaction: &AdminFeeTransaction) -> Result<String, BotError> {
        // Get user wallet information
        let user_wallets = DbService::get_user_wallets(transaction.user_id).await
            .map_err(|e| BotError::database_error(e.to_string()))?
            .ok_or_else(|| BotError::validation_error("User wallets not found".to_string()))?;

        let wallet_info = match transaction.blockchain {
            Blockchain::ETH => &user_wallets.eth_wallet,
            Blockchain::BSC => &user_wallets.bsc_wallet,
            Blockchain::BASE => &user_wallets.base_wallet,
            _ => return Err(BotError::validation_error("Unsupported blockchain".to_string())),
        };

        let evm_service = std::sync::Arc::new(crate::service::evm_trader_service::EvmTraderService::new());

        // First, try to collect in native tokens (ETH/BNB/ETH) if user has enough
        let native_fee_amount = transaction.fee_amount;
        let native_fee_wei = (native_fee_amount * 1e18) as u64;

        // Check if user has enough native tokens for the fee + gas costs (dynamic based on blockchain)
        let estimated_gas_cost = self.get_estimated_gas_cost(&transaction.blockchain);
        let total_required_native = native_fee_amount + estimated_gas_cost;

        match evm_service.check_sufficient_balance_for_buy(
            &transaction.user_wallet_address,
            &(total_required_native * 1e18).to_string(),
            &transaction.blockchain
        ).await {
            Ok(true) => {
                println!("User has sufficient native tokens (including gas), collecting fee in native currency");
                // Collect fee in native tokens
                return evm_service.transfer_native_admin_fee(
                    &transaction.user_wallet_address,
                    &wallet_info.private_key,
                    &transaction.admin_address,
                    &(native_fee_amount * 1e18).to_string(),
                    &transaction.blockchain,
                ).await.map_err(|e| BotError::transaction_error(e.to_string()));
            }
            Ok(false) => {
                println!("User doesn't have sufficient native tokens (including gas), will collect from received tokens after sell");
            }
            Err(e) => {
                println!("Failed to check native token balance: {}", e);
            }
        }

        // If native token collection failed or insufficient, collect from received tokens
        // This happens after the sell transaction completes
        println!("Collecting fee from received tokens after sell completion");

        // Check if user has enough gas for ERC-20 transfer (dynamic based on blockchain)
        let min_gas_balance = self.get_minimum_gas_balance(&transaction.blockchain);
        match evm_service.check_sufficient_balance_for_buy(
            &transaction.user_wallet_address,
            &(min_gas_balance * 1e18).to_string(),
            &transaction.blockchain
        ).await {
            Ok(true) => {
                // User has enough gas, proceed with ERC-20 transfer
                evm_service.transfer_erc20_admin_fee(
                    &transaction.user_wallet_address,
                    &transaction.fee_token_address,
                    &transaction.admin_address,
                    &(transaction.fee_amount * 10_f64.powi(18)).to_string(),
                    &transaction.blockchain,
                ).await.map_err(|e| BotError::transaction_error(e.to_string()))
            }
            Ok(false) => {
                let error_msg = format!("Insufficient gas balance for fee collection. User has less than {} ETH for gas fees.", min_gas_balance);
                println!("❌ {}", error_msg);
                Err(BotError::transaction_error(error_msg))
            }
            Err(e) => {
                let error_msg = format!("Failed to check gas balance: {}", e);
                println!("❌ {}", error_msg);
                Err(BotError::transaction_error(error_msg))
            }
        }
    }

    /// Smart Solana sell fee collection - prioritize SOL, fallback to received tokens
    async fn execute_smart_solana_sell_fee_collection(&self, transaction: &AdminFeeTransaction) -> Result<String, BotError> {
        use solana_sdk::signature::Keypair;
        use solana_sdk::pubkey::Pubkey;
        use std::str::FromStr;

        // Get user wallet information
        let user_wallets = DbService::get_user_wallets(transaction.user_id).await
            .map_err(|e| BotError::database_error(e.to_string()))?
            .ok_or_else(|| BotError::validation_error("User wallets not found".to_string()))?;

        let wallet = Keypair::from_base58_string(&user_wallets.sol_wallet.private_key);

        // Parse admin address
        let admin_address = if transaction.admin_address.starts_with("0x") {
            // Convert hex to Pubkey
            let hex_str = &transaction.admin_address[2..];
            let bytes = hex::decode(hex_str)
                .map_err(|e| BotError::validation_error(format!("Invalid hex address: {}", e)))?;
            if bytes.len() != 32 {
                return Err(BotError::validation_error("Solana address must be 32 bytes".to_string()));
            }
            let mut pubkey_bytes = [0u8; 32];
            pubkey_bytes.copy_from_slice(&bytes);
            Pubkey::new_from_array(pubkey_bytes)
        } else {
            Pubkey::from_str(&transaction.admin_address)
                .map_err(|e| BotError::validation_error(format!("Invalid admin address: {}", e)))?
        };

        let solana_service = std::sync::Arc::new(crate::service::solana_trader_service::SolanaTraderService::new(
            "https://api.mainnet-beta.solana.com",
            None,
            4
        ));

        // First, try to collect in SOL if user has enough
        let sol_fee_lamports = (transaction.fee_amount * 1_000_000_000.0) as u64;

        // Check if user has enough SOL for the fee (including minimum rent and transaction fees)
        match solana_service.check_sufficient_balance_for_buy(&wallet, sol_fee_lamports).await {
            Ok(true) => {
                println!("User has sufficient SOL, collecting fee in SOL");
                // Collect fee in SOL
                return solana_service.transfer_sol_admin_fee(&wallet, &admin_address, sol_fee_lamports)
                    .await
                    .map(|sig| sig.to_string())
                    .map_err(|e| BotError::transaction_error(e.to_string()));
            }
            Ok(false) => {
                println!("User doesn't have sufficient SOL, will collect from received tokens after sell");
            }
            Err(e) => {
                println!("Failed to check SOL balance: {}", e);
            }
        }

        // If SOL collection failed or insufficient, collect from received tokens
        if transaction.fee_token_symbol != "SOL" {
            println!("Collecting fee from received tokens after sell completion");
            let token_mint = Pubkey::from_str(&transaction.fee_token_address)
                .map_err(|e| BotError::validation_error(format!("Invalid token mint: {}", e)))?;

            // Use 9 decimals as default for SPL tokens
            let decimals = 9u8;
            let fee_tokens = (transaction.fee_amount * 10_u64.pow(decimals as u32) as f64) as u64;

            solana_service.transfer_token_admin_fee(&wallet, &token_mint, &admin_address, fee_tokens, decimals)
                .await
                .map(|sig| sig.to_string())
                .map_err(|e| BotError::transaction_error(e.to_string()))
        } else {
            // This shouldn't happen as we already tried SOL collection above
            Err(BotError::validation_error("SOL fee collection already attempted".to_string()))
        }
    }

    /// Retry EVM fee collection
    async fn retry_evm_fee_collection(&self, transaction: &AdminFeeTransaction) -> Result<String, BotError> {
        use crate::service::evm_trader_service::EvmTraderService;
        use crate::service::db_service::DbService;
        use std::sync::Arc;

        // Get user wallet information
        let user_wallets = DbService::get_user_wallets(transaction.user_id).await
            .map_err(|e| BotError::database_error(e.to_string()))?
            .ok_or_else(|| BotError::validation_error("User wallets not found".to_string()))?;

        // Create EVM trader service instance
        let evm_service = Arc::new(EvmTraderService::new());

        // Determine transaction type and execute appropriate retry
        match transaction.transaction_type {
            FeeTransactionType::BuyFee => {
                // For buy fees, we collect from received tokens
                evm_service.transfer_erc20_admin_fee(
                    &transaction.user_wallet_address,
                    &transaction.fee_token_address,
                    &transaction.admin_address,
                    &(transaction.fee_amount * 10_f64.powi(18)).to_string(), // Convert to wei
                    &transaction.blockchain,
                ).await.map_err(|e| BotError::transaction_error(e.to_string()))
            }
            FeeTransactionType::SellFee => {
                // For sell fees, use smart collection (native tokens first, then received tokens)
                self.execute_smart_evm_sell_fee_collection(transaction).await
            }
        }
    }

    /// Retry Solana fee collection
    async fn retry_solana_fee_collection(&self, transaction: &AdminFeeTransaction) -> Result<String, BotError> {
        use crate::service::solana_trader_service::SolanaTraderService;
        use crate::service::db_service::DbService;
        use solana_sdk::signature::Keypair;
        use solana_sdk::pubkey::Pubkey;
        use std::str::FromStr;
        use std::sync::Arc;

        // Get user wallet information
        let user_wallets = DbService::get_user_wallets(transaction.user_id).await
            .map_err(|e| BotError::database_error(e.to_string()))?
            .ok_or_else(|| BotError::validation_error("User wallets not found".to_string()))?;

        // Create wallet keypair from private key
        let wallet = Keypair::from_base58_string(&user_wallets.sol_wallet.private_key);

        // Parse admin address
        let admin_address = if transaction.admin_address.starts_with("0x") {
            // Convert hex to Pubkey
            let hex_str = &transaction.admin_address[2..];
            let bytes = hex::decode(hex_str)
                .map_err(|e| BotError::validation_error(format!("Invalid hex address: {}", e)))?;
            if bytes.len() != 32 {
                return Err(BotError::validation_error("Solana address must be 32 bytes".to_string()));
            }
            let mut pubkey_bytes = [0u8; 32];
            pubkey_bytes.copy_from_slice(&bytes);
            Pubkey::new_from_array(pubkey_bytes)
        } else {
            Pubkey::from_str(&transaction.admin_address)
                .map_err(|e| BotError::validation_error(format!("Invalid admin address: {}", e)))?
        };

        // Create Solana trader service instance
        let solana_service = Arc::new(SolanaTraderService::new(
            "https://api.mainnet-beta.solana.com",
            None,
            4
        ));

        // Execute retry based on transaction type
        match transaction.transaction_type {
            FeeTransactionType::BuyFee => {
                // For buy fees, collect from received tokens
                if transaction.fee_token_symbol == "SOL" {
                    // SOL transfer
                    let fee_lamports = (transaction.fee_amount * 1_000_000_000.0) as u64;
                    solana_service.transfer_sol_admin_fee(&wallet, &admin_address, fee_lamports)
                        .await
                        .map(|sig| sig.to_string())
                        .map_err(|e| BotError::transaction_error(e.to_string()))
                } else {
                    // Token transfer - get actual decimals from token mint
                    let token_mint = Pubkey::from_str(&transaction.fee_token_address)
                        .map_err(|e| BotError::validation_error(format!("Invalid token mint: {}", e)))?;

                    // Use 9 decimals as default for SPL tokens
                    let decimals = 9u8;
                    let fee_tokens = (transaction.fee_amount * 10_u64.pow(decimals as u32) as f64) as u64;

                    solana_service.transfer_token_admin_fee(&wallet, &token_mint, &admin_address, fee_tokens, decimals)
                        .await
                        .map(|sig| sig.to_string())
                        .map_err(|e| BotError::transaction_error(e.to_string()))
                }
            }
            FeeTransactionType::SellFee => {
                // For sell fees, use smart collection (SOL first, then received tokens)
                self.execute_smart_solana_sell_fee_collection(transaction).await
            }
        }
    }

    /// Update fee transaction with trade_id after trade is saved
    pub async fn update_fee_transaction_trade_id(
        &self,
        fee_transaction_id: ObjectId,
        trade_id: ObjectId,
    ) -> Result<(), BotError> {
        let collection: Collection<AdminFeeTransaction> = self.db.collection("admin_fee_transactions");

        let update_doc = doc! {
            "$set": {
                "trade_id": trade_id,
                "updated_at": chrono::Utc::now().timestamp()
            }
        };

        match timeout(
            DB_OPERATION_TIMEOUT,
            collection.update_one(doc! { "_id": fee_transaction_id }, update_doc, None)
        ).await {
            Ok(Ok(result)) => {
                if result.matched_count > 0 {
                    debug!("Updated fee transaction {} with trade_id {}", fee_transaction_id, trade_id);
                    Ok(())
                } else {
                    warn!("Fee transaction {} not found for trade_id update", fee_transaction_id);
                    Err(BotError::database_error("Fee transaction not found".to_string()))
                }
            }
            Ok(Err(e)) => {
                error!("Failed to update fee transaction with trade_id: {}", e);
                Err(BotError::database_error(format!("Failed to update fee transaction: {}", e)))
            }
            Err(_) => {
                error!("Database operation timeout while updating fee transaction trade_id");
                Err(BotError::database_error("Database operation timeout".to_string()))
            }
        }
    }

    /// Update trade record with admin fee collection status
    pub async fn update_trade_admin_fee_status(&self, trade_id: ObjectId, fee_status: String, fee_hash: Option<String>) -> Result<(), BotError> {
        let collection: Collection<crate::model::trade::Trade> = self.db.collection("trades");

        let mut update_doc = doc! {
            "$set": {
                "admin_fee_status": fee_status.clone(),
                "updated_at": chrono::Utc::now()
            }
        };

        // Add fee transaction hash if provided
        if let Some(hash) = fee_hash {
            update_doc.get_document_mut("$set").unwrap().insert("admin_fee_hash", hash);
        }

        match timeout(
            DB_OPERATION_TIMEOUT,
            collection.update_one(doc! { "_id": trade_id }, update_doc, None)
        ).await {
            Ok(Ok(result)) => {
                if result.matched_count > 0 {
                    debug!("Updated trade {} admin fee status to {}", trade_id, fee_status);
                    Ok(())
                } else {
                    warn!("Trade {} not found for admin fee status update", trade_id);
                    Err(BotError::database_error("Trade not found".to_string()))
                }
            }
            Ok(Err(e)) => {
                error!("Failed to update trade admin fee status: {}", e);
                Err(BotError::database_error(format!("Failed to update trade: {}", e)))
            }
            Err(_) => {
                error!("Database operation timeout while updating trade admin fee status");
                Err(BotError::database_error("Database operation timeout".to_string()))
            }
        }
    }

    /// Update trade record with admin fee collection status AND actual fee amount (for threshold adjustments)
    pub async fn update_trade_admin_fee_status_with_amount(&self, trade_id: ObjectId, fee_status: String, actual_fee_amount: f64, fee_hash: Option<String>) -> Result<(), BotError> {
        let collection: Collection<crate::model::trade::Trade> = self.db.collection("trades");

        let mut update_doc = doc! {
            "$set": {
                "admin_fee_status": fee_status.clone(),
                "admin_fee_amount": actual_fee_amount, // Update to the actual amount charged (threshold-adjusted)
                "updated_at": chrono::Utc::now()
            }
        };

        // Add fee transaction hash if provided
        if let Some(hash) = fee_hash {
            update_doc.get_document_mut("$set").unwrap().insert("admin_fee_hash", hash);
        }

        match timeout(
            DB_OPERATION_TIMEOUT,
            collection.update_one(doc! { "_id": trade_id }, update_doc, None)
        ).await {
            Ok(Ok(result)) => {
                if result.matched_count > 0 {
                    debug!("Updated trade {} admin fee status to {} with actual amount: {}", trade_id, fee_status, actual_fee_amount);
                    Ok(())
                } else {
                    warn!("Trade {} not found for admin fee status update", trade_id);
                    Err(BotError::database_error("Trade not found".to_string()))
                }
            }
            Ok(Err(e)) => {
                error!("Failed to update trade admin fee status with amount: {}", e);
                Err(BotError::database_error(format!("Failed to update trade: {}", e)))
            }
            Err(_) => {
                error!("Database operation timeout while updating trade admin fee status with amount");
                Err(BotError::database_error("Database operation timeout".to_string()))
            }
        }
    }
}

#[derive(Debug, Clone)]
pub struct FeeStatistics {
    pub total_fee_amount: f64,
    pub total_transactions: u64,
    pub avg_fee_amount: f64,
    pub solana_fees: f64,
    pub evm_fees: f64,
}

impl Default for FeeStatistics {
    fn default() -> Self {
        Self {
            total_fee_amount: 0.0,
            total_transactions: 0,
            avg_fee_amount: 0.0,
            solana_fees: 0.0,
            evm_fees: 0.0,
        }
    }
}
