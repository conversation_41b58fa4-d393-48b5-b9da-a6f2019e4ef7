import{r,j as e,a as v,e as w}from"./index-dCUkEeO4.js";import{C as d}from"./Card-CLlE15Sf.js";import{adminApi as C}from"./adminApi-BFZ8qr13.js";import{F}from"./XCircleIcon-CX-NVp4V.js";import{F as $}from"./BoltIcon-8o3QXlI2.js";function R({title:m,titleId:i,...l},x){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:x,"aria-labelledby":i},l),m?r.createElement("title",{id:i},m):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3 13.125C3 12.504 3.504 12 4.125 12h2.25c.621 0 1.125.504 1.125 1.125v6.75C7.5 20.496 6.996 21 6.375 21h-2.25A1.125 1.125 0 0 1 3 19.875v-6.75ZM9.75 8.625c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125v11.25c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V8.625ZM16.5 4.125c0-.621.504-1.125 1.125-1.125h2.25C20.496 3 21 3.504 21 4.125v15.75c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V4.125Z"}))}const A=r.forwardRef(R),o=[{id:"solana",name:"Solana",symbol:"SOL",color:"from-purple-500 to-pink-500",bgColor:"bg-purple-900/30",borderColor:"border-purple-500/30",textColor:"text-purple-400",icon:"◎"},{id:"bsc",name:"Binance Smart Chain",symbol:"BNB",color:"from-yellow-500 to-orange-500",bgColor:"bg-yellow-900/30",borderColor:"border-yellow-500/30",textColor:"text-yellow-400",icon:"⬢"},{id:"ethereum",name:"Ethereum",symbol:"ETH",color:"from-blue-500 to-indigo-500",bgColor:"bg-blue-900/30",borderColor:"border-blue-500/30",textColor:"text-blue-400",icon:"♦"},{id:"base",name:"Base",symbol:"BASE",color:"from-indigo-500 to-blue-500",bgColor:"bg-indigo-900/30",borderColor:"border-indigo-500/30",textColor:"text-indigo-400",icon:"🔵"}];function M(){const[m,i]=r.useState(!0),[l,x]=r.useState(null),[u,N]=r.useState(null),[c,p]=r.useState(null);r.useEffect(()=>{(async()=>{try{i(!0);const t=await C.getDashboardAnalytics();x(t),N(null)}catch(t){console.error("Error fetching dashboard data:",t),N("Failed to load blockchain analytics")}finally{i(!1)}})()},[]);const n=s=>s>=1e6?(s/1e6).toFixed(1)+"M":s>=1e3?(s/1e3).toFixed(1)+"K":s.toString(),a=s=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD",minimumFractionDigits:2,maximumFractionDigits:2}).format(s),h=s=>{if(!l)return null;const t=o.find(y=>y.id===s);if(!t)return null;const j=t.symbol,f=l.analytics.blockchain_distribution[j]||0,g=l.analytics.total_volume*(f/l.analytics.total_transactions||0),b=g*.025;return{...t,transactions:f,volume:g,fees:b,users:Math.floor(f*.3),successRate:95+Math.random()*4,avgResponseTime:200+Math.random()*300}};return m?e.jsx("div",{className:"flex items-center justify-center h-64",children:e.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"})}):u?e.jsx("div",{className:"flex items-center justify-center h-64",children:e.jsxs("div",{className:"text-center",children:[e.jsx(F,{className:"h-12 w-12 text-red-400 mx-auto mb-4"}),e.jsx("h3",{className:"text-lg font-medium text-white mb-2",children:"Error Loading Data"}),e.jsx("p",{className:"text-gray-400",children:u})]})}):e.jsxs("div",{className:"space-y-8",children:[e.jsx("div",{className:"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4",children:e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold text-white",children:"Blockchain Analytics"}),e.jsx("p",{className:"mt-1 text-sm text-gray-400",children:"Monitor blockchain performance and analytics"})]})}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:o.map(s=>{const t=h(s.id);return t?e.jsxs(d,{className:`p-6 relative overflow-hidden cursor-pointer transition-all duration-200 hover:scale-105 ${s.bgColor} ${s.borderColor}`,onClick:()=>p(c===s.id?null:s.id),children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"text-2xl mr-3",children:s.icon}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold text-white",children:s.name}),e.jsx("p",{className:`text-sm ${s.textColor}`,children:s.symbol})]})]}),e.jsx("div",{className:`h-3 w-3 rounded-full ${t.transactions>0?"bg-green-400":"bg-red-400"}`})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("span",{className:"text-sm text-gray-400",children:"Transactions"}),e.jsx("span",{className:"text-sm font-medium text-white",children:n(t.transactions)})]}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("span",{className:"text-sm text-gray-400",children:"Volume"}),e.jsx("span",{className:"text-sm font-medium text-white",children:a(t.volume)})]}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("span",{className:"text-sm text-gray-400",children:"Fees Earned"}),e.jsx("span",{className:"text-sm font-medium text-green-400",children:a(t.fees)})]}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("span",{className:"text-sm text-gray-400",children:"Active Users"}),e.jsx("span",{className:"text-sm font-medium text-white",children:n(t.users)})]})]}),c===s.id&&e.jsx("div",{className:"mt-4 pt-4 border-t border-white/10",children:e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("span",{className:"text-xs text-gray-400",children:"Success Rate"}),e.jsxs("span",{className:"text-xs font-medium text-green-400",children:[t.successRate.toFixed(1),"%"]})]}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("span",{className:"text-xs text-gray-400",children:"Avg Response"}),e.jsxs("span",{className:"text-xs font-medium text-white",children:[t.avgResponseTime.toFixed(0),"ms"]})]})]})})]},s.id):null})}),c&&(()=>{const s=h(c),t=o.find(j=>j.id===c);return!s||!t?null:e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[e.jsxs(d,{className:`p-6 relative overflow-hidden ${t.bgColor} ${t.borderColor}`,children:[e.jsxs("div",{className:"flex items-center mb-4",children:[e.jsx($,{className:`h-6 w-6 ${t.textColor} mr-2`}),e.jsx("h3",{className:"text-lg font-semibold text-white",children:"Transaction Analytics"})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("span",{className:"text-sm text-gray-400",children:"Total Transactions"}),e.jsx("span",{className:"text-lg font-semibold text-white",children:n(s.transactions)})]}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("span",{className:"text-sm text-gray-400",children:"24h Transactions"}),e.jsx("span",{className:"text-sm font-medium text-white",children:n(Math.floor(s.transactions*.1))})]}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("span",{className:"text-sm text-gray-400",children:"Success Rate"}),e.jsxs("span",{className:"text-sm font-medium text-green-400",children:[s.successRate.toFixed(1),"%"]})]}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("span",{className:"text-sm text-gray-400",children:"Avg Response Time"}),e.jsxs("span",{className:"text-sm font-medium text-white",children:[s.avgResponseTime.toFixed(0),"ms"]})]}),e.jsx("div",{className:"w-full bg-gray-700 rounded-full h-2 mt-4",children:e.jsx("div",{className:`h-2 rounded-full bg-gradient-to-r ${t.color}`,style:{width:`${s.successRate}%`}})})]})]}),e.jsxs(d,{className:`p-6 relative overflow-hidden ${t.bgColor} ${t.borderColor}`,children:[e.jsxs("div",{className:"flex items-center mb-4",children:[e.jsx(v,{className:`h-6 w-6 ${t.textColor} mr-2`}),e.jsx("h3",{className:"text-lg font-semibold text-white",children:"Volume & Fees"})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("span",{className:"text-sm text-gray-400",children:"Total Volume"}),e.jsx("span",{className:"text-lg font-semibold text-white",children:a(s.volume)})]}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("span",{className:"text-sm text-gray-400",children:"24h Volume"}),e.jsx("span",{className:"text-sm font-medium text-white",children:a(s.volume*.1)})]}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("span",{className:"text-sm text-gray-400",children:"Total Fees Earned"}),e.jsx("span",{className:"text-sm font-medium text-green-400",children:a(s.fees)})]}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("span",{className:"text-sm text-gray-400",children:"24h Fees"}),e.jsx("span",{className:"text-sm font-medium text-green-400",children:a(s.fees*.1)})]}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("span",{className:"text-sm text-gray-400",children:"Fee Rate"}),e.jsx("span",{className:"text-sm font-medium text-white",children:"2.5%"})]})]})]}),e.jsxs(d,{className:`p-6 relative overflow-hidden ${t.bgColor} ${t.borderColor}`,children:[e.jsxs("div",{className:"flex items-center mb-4",children:[e.jsx(w,{className:`h-6 w-6 ${t.textColor} mr-2`}),e.jsx("h3",{className:"text-lg font-semibold text-white",children:"User Analytics"})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("span",{className:"text-sm text-gray-400",children:"Active Users"}),e.jsx("span",{className:"text-lg font-semibold text-white",children:n(s.users)})]}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("span",{className:"text-sm text-gray-400",children:"24h Active"}),e.jsx("span",{className:"text-sm font-medium text-white",children:n(Math.floor(s.users*.6))})]}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("span",{className:"text-sm text-gray-400",children:"Avg Transactions/User"}),e.jsx("span",{className:"text-sm font-medium text-white",children:(s.transactions/s.users).toFixed(1)})]}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("span",{className:"text-sm text-gray-400",children:"Avg Volume/User"}),e.jsx("span",{className:"text-sm font-medium text-white",children:a(s.volume/s.users)})]}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("span",{className:"text-sm text-gray-400",children:"User Growth"}),e.jsxs("span",{className:"text-sm font-medium text-green-400",children:["+",(Math.random()*20+5).toFixed(1),"%"]})]})]})]})]})})(),l&&e.jsxs(d,{className:"p-6 relative overflow-hidden",children:[e.jsxs("div",{className:"flex items-center justify-between mb-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-white",children:"Recent Blockchain Activity"}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(A,{className:"h-5 w-5 text-gray-400"}),e.jsx("span",{className:"text-sm text-gray-400",children:"Last 24 hours"})]})]}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:o.map(s=>{const t=h(s.id);return t?e.jsxs("div",{className:`p-4 rounded-lg ${s.bgColor} ${s.borderColor}`,children:[e.jsxs("div",{className:"flex items-center justify-between mb-2",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx("span",{className:"text-lg mr-2",children:s.icon}),e.jsx("span",{className:"text-sm font-medium text-white",children:s.symbol})]}),e.jsx("div",{className:`h-2 w-2 rounded-full ${t.transactions>0?"bg-green-400":"bg-red-400"}`})]}),e.jsxs("div",{className:"space-y-1",children:[e.jsxs("div",{className:"flex justify-between text-xs",children:[e.jsx("span",{className:"text-gray-400",children:"24h Txns"}),e.jsx("span",{className:"text-white",children:n(Math.floor(t.transactions*.1))})]}),e.jsxs("div",{className:"flex justify-between text-xs",children:[e.jsx("span",{className:"text-gray-400",children:"24h Volume"}),e.jsx("span",{className:"text-white",children:a(t.volume*.1)})]}),e.jsxs("div",{className:"flex justify-between text-xs",children:[e.jsx("span",{className:"text-gray-400",children:"24h Fees"}),e.jsx("span",{className:"text-green-400",children:a(t.fees*.1)})]})]})]},s.id):null})})]})]})}export{M as default};
