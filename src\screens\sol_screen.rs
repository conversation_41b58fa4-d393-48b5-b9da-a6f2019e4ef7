use crate::model::{<PERSON>r<PERSON><PERSON>, BotError, Blockchain};
use crate::service::BotService;
use crate::screens::bsc_screen::{create_dashboard_keyboard, format_config};
use std::fmt::Write;

pub async fn show_sol_dashboard(
    bot_service: &BotService,
    user_data: &UserData,
    message_id: i32,
) -> Result<(), BotError> {
    let wallet_address = &user_data.get_wallet(&Blockchain::SOL).address;
    let chat_id = user_data.chat_id();

    const DASHBOARD_HEADER: &str = "📊 <b>Solana Dashboard</b>\n\n";
    const BLOCKCHAIN_INFO: &str = "🔗 <b>Blockchain:</b> Solana (SOL)\n\n";
    const LOADING_BALANCE: &str = "💰 <b>SOL Balance:</b> <code>Loading...</code>\n\n";
    const LOADING_STATUS: &str = "📈 <b>Network Status:</b> Loading...\n\n";

    let mut initial_dashboard_text = String::with_capacity(512);
    initial_dashboard_text.push_str(DASHBOARD_HEADER);

    let _ = write!(initial_dashboard_text, "👤 <b>User:</b> {}\n\n", user_data.display_name());
    let _ = write!(initial_dashboard_text, "📍 <b>Address:</b> <code>{}</code>\n", wallet_address);

    let explorer_url = crate::constants::explorer_urls::get_explorer_address_url("sol", &wallet_address);
    let _ = write!(initial_dashboard_text, "🔍 <a href=\"{}\">View on Solscan</a>\n\n", explorer_url);

    initial_dashboard_text.push_str(BLOCKCHAIN_INFO);
    initial_dashboard_text.push_str(LOADING_BALANCE);
    initial_dashboard_text.push_str(LOADING_STATUS);

    let config_text = format_config(user_data, Blockchain::SOL);
    let _ = write!(initial_dashboard_text, "⚙️ <b>Settings:</b> {}", config_text);

    let keyboard = create_dashboard_keyboard(Blockchain::SOL);

    bot_service.edit_message_with_keyboard(
        chat_id,
        message_id,
        &initial_dashboard_text,
        keyboard.clone(),
    ).await?;

    let bot_service_clone = bot_service.clone();
    let wallet_address = wallet_address.clone();
    let display_name = user_data.display_name().to_string();
    let config_text = config_text;

    tokio::spawn(async move {
        let blockchain_service = bot_service_clone.blockchain_service();
        let _token_info_service = bot_service_clone.token_info_service();

        // Get SOL price instantly from global cache (no async)
        let sol_price_usd = crate::service::token_info_service::TokenInfoService::get_price_sync("sol");

        // Get balance with timeout to prevent hanging and ensure buttons remain clickable
        let balance = match tokio::time::timeout(
            std::time::Duration::from_secs(10),
            blockchain_service.get_balance_by_address(&wallet_address, Blockchain::SOL)
        ).await {
            Ok(Ok(balance)) => balance,
            Ok(Err(_)) => 0.0,
            Err(_) => 0.0, // Timeout - don't block the UI
        };

        // Get network status separately (optional)
        let network_status_result = blockchain_service.get_network_status(Blockchain::SOL).await;

        let network_status = match network_status_result {
            Ok(status) => {
                if status.is_healthy {
                    "Active ✅"
                } else {
                    "Degraded ⚠️"
                }
            },
            Err(_) => "Unknown ❓",
        };

        let mut dashboard_text = String::with_capacity(512);
        dashboard_text.push_str(DASHBOARD_HEADER);

        let _ = write!(dashboard_text, "👤 <b>User:</b> {}\n\n", display_name);
        let _ = write!(dashboard_text, "📍 <b>Address:</b> <code>{}</code>\n", wallet_address);

        let explorer_url = crate::constants::explorer_urls::get_explorer_address_url("sol", &wallet_address);
        let _ = write!(dashboard_text, "🔍 <a href=\"{}\">View on Solscan</a>\n\n", explorer_url);

        dashboard_text.push_str(BLOCKCHAIN_INFO);

        let _ = write!(dashboard_text, "💰 <b>SOL Balance:</b> <code>{:.6} SOL</code>\n\n", balance);

        let _ = write!(dashboard_text, "📈 <b>Network Status:</b> {}\n\n", network_status);

        let _ = write!(dashboard_text, "⚙️ <b>Settings:</b> {}", config_text);

        let _ = bot_service_clone.edit_message_with_keyboard(
            chat_id,
            message_id,
            &dashboard_text,
            keyboard,
        ).await;
    });

    Ok(())
}
