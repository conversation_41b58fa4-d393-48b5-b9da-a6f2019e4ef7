use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, Serialize, Deserialize)]
pub enum MessageType {
    Text,
    Command,
    Image,
    Document,
    Other,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct Message {
    pub id: String,
    pub user_id: i64,
    pub chat_id: i64,
    pub content: String,
    pub message_type: MessageType,
    pub timestamp: DateTime<Utc>,
    pub processed: bool,
}

impl Message {
    pub fn from_telegram_message(tg_message: &teloxide::types::Message) -> Option<Self> {
        let chat_id = tg_message.chat.id.0 as i64;
        let user_id = tg_message.from()?.id.0 as i64;
        
        let (content, message_type) = if let Some(text) = tg_message.text() {
            if text.starts_with('/') {
                (text.to_string(), MessageType::Command)
            } else {
                (text.to_string(), MessageType::Text)
            }
        } else if tg_message.photo().is_some() {
            ("".to_string(), MessageType::Image)
        } else if tg_message.document().is_some() {
            ("".to_string(), MessageType::Document)
        } else {
            ("".to_string(), MessageType::Other)
        };
        
        Some(Self {
            id: tg_message.id.0.to_string(),
            user_id,
            chat_id,
            content,
            message_type,
            timestamp: Utc::now(),
            processed: false,
        })
    }
    
    pub fn mark_processed(&mut self) {
        self.processed = true;
    }
    
    pub fn is_command(&self) -> bool {
        matches!(self.message_type, MessageType::Command)
    }
    
    pub fn get_command(&self) -> Option<String> {
        if self.is_command() {
            let parts: Vec<&str> = self.content.splitn(2, ' ').collect();
            let command = parts[0].trim_start_matches('/');
            let command_parts: Vec<&str> = command.splitn(2, '@').collect();
            Some(command_parts[0].to_string())
        } else {
            None
        }
    }
}
