"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
/**
 * Production-level TypeScript implementation for token balance fetching
 * This file is designed to be called directly from Rust
 */
const ethers_1 = require("ethers");
const web3_js_1 = require("@solana/web3.js");
const spl_token_1 = require("@solana/spl-token");
// RPC endpoints from environment variables
const RPC_ENDPOINTS = {
    ETH: process.env.ETH_RPC_URL || 'https://mainnet.infura.io/v3/********************************',
    BSC: process.env.BSC_RPC_URL || 'https://bsc-dataseed.binance.org',
    SOL: process.env.SOL_RPC_URL || 'https://api.mainnet-beta.solana.com',
    BASE: process.env.BASE_RPC_URL || 'https://mainnet.base.org',
    // Add lowercase versions
    eth: process.env.ETH_RPC_URL || 'https://mainnet.infura.io/v3/********************************',
    bsc: process.env.BSC_RPC_URL || 'https://bsc-dataseed.binance.org',
    sol: process.env.SOL_RPC_URL || 'https://api.mainnet-beta.solana.com',
    base: process.env.BASE_RPC_URL || 'https://mainnet.base.org'
};
// ERC20 ABI for token balance
const ERC20_ABI = [
    'function balanceOf(address owner) view returns (uint256)',
    'function decimals() view returns (uint8)',
    'function symbol() view returns (string)'
];
/**
 * Get wallet balance (native token)
 * @param blockchain The blockchain to query
 * @param walletAddress The wallet address
 * @returns The wallet balance as a string
 */
async function getWalletBalance(blockchain, walletAddress) {
    // Minimal logging for performance
    console.log(`Getting ${blockchain} balance for ${walletAddress}`);
    try {
        // Normalize blockchain name to uppercase for consistent handling
        const normalizedBlockchain = blockchain.toUpperCase();
        switch (normalizedBlockchain) {
            case 'ETH':
            case 'BSC':
            case 'BASE': {
                // Create provider based on blockchain
                const provider = new ethers_1.ethers.JsonRpcProvider(RPC_ENDPOINTS[blockchain]);
                try {
                    // Get balance in wei
                    const balanceWei = await provider.getBalance(walletAddress);
                    // Convert to ether units and return
                    const formattedBalance = ethers_1.ethers.formatEther(balanceWei);
                    console.log(`${normalizedBlockchain} balance: ${formattedBalance}`);
                    return formattedBalance;
                }
                catch (error) {
                    console.error(`Error fetching ${normalizedBlockchain} balance:`, error);
                    throw error;
                }
            }
            case 'SOL': {
                // Create Solana connection
                const connection = new web3_js_1.Connection(RPC_ENDPOINTS[blockchain]);
                // Get balance in lamports
                const balanceLamports = await connection.getBalance(new web3_js_1.PublicKey(walletAddress));
                // Convert to SOL (1 SOL = 10^9 lamports)
                const balanceSol = balanceLamports / **********;
                console.log(`SOL balance: ${balanceSol}`);
                return balanceSol.toString();
            }
            default:
                throw new Error(`Unsupported blockchain: ${blockchain}`);
        }
    }
    catch (error) {
        console.error(`Error getting wallet balance for ${blockchain}:`, error);
        // Return a valid JSON response with error information
        console.log(JSON.stringify({
            error: true,
            message: `Error getting wallet balance for ${blockchain}: ${error?.message || 'Unknown error'}`,
            balance: '0'
        }));
        process.exit(1);
    }
}
/**
 * Get token balance
 * @param blockchain The blockchain to query
 * @param walletAddress The wallet address
 * @param tokenAddress The token contract address
 * @returns The token balance as a string
 */
async function getTokenBalance(blockchain, walletAddress, tokenAddress) {
    // Minimal logging for performance
    console.log(`Getting ${blockchain} token balance for ${walletAddress}, token: ${tokenAddress}`);
    try {
        // Normalize blockchain name to uppercase for consistent handling
        const normalizedBlockchain = blockchain.toUpperCase();
        switch (normalizedBlockchain) {
            case 'ETH':
            case 'BSC':
            case 'BASE': {
                // Create provider based on blockchain
                const provider = new ethers_1.ethers.JsonRpcProvider(RPC_ENDPOINTS[blockchain]);
                try {
                    // Create contract instance
                    const tokenContract = new ethers_1.ethers.Contract(tokenAddress, ERC20_ABI, provider);
                    // Get token decimals
                    const decimals = await tokenContract.decimals();
                    // Get balance in smallest units
                    const balance = await tokenContract.balanceOf(walletAddress);
                    // Convert to token units
                    const formattedBalance = ethers_1.ethers.formatUnits(balance, decimals);
                    console.log(`${normalizedBlockchain} token balance: ${formattedBalance}`);
                    return formattedBalance;
                }
                catch (error) {
                    console.error(`Error fetching ${normalizedBlockchain} token balance:`, error);
                    throw error;
                }
            }
            case 'SOL': {
                // Create Solana connection
                const connection = new web3_js_1.Connection(RPC_ENDPOINTS[blockchain]);
                try {
                    // Special case for SOL native token
                    if (tokenAddress === 'So11111111111111111111111111111111111111112') {
                        const balanceLamports = await connection.getBalance(new web3_js_1.PublicKey(walletAddress));
                        const balanceSol = balanceLamports / **********;
                        console.log(`SOL token balance: ${balanceSol}`);
                        return balanceSol.toString();
                    }
                    // For SPL tokens, get all token accounts owned by the wallet
                    const tokenAccounts = await connection.getParsedTokenAccountsByOwner(new web3_js_1.PublicKey(walletAddress), {
                        programId: spl_token_1.TOKEN_PROGRAM_ID
                    });
                    // Find the account for the specified token
                    const tokenAccount = tokenAccounts.value.find((account) => account.account.data.parsed.info.mint === tokenAddress);
                    if (!tokenAccount) {
                        console.log(`No token account found for ${tokenAddress}`);
                        return '0';
                    }
                    // Get the token amount
                    const tokenAmount = tokenAccount.account.data.parsed.info.tokenAmount;
                    console.log(`SOL token balance: ${tokenAmount.uiAmountString || '0'}`);
                    // Return the UI amount (already formatted with decimals)
                    return tokenAmount.uiAmountString || '0';
                }
                catch (error) {
                    console.error(`Error fetching SOL token balance:`, error);
                    throw error;
                }
            }
            default:
                throw new Error(`Unsupported blockchain: ${blockchain}`);
        }
    }
    catch (error) {
        console.error(`Error getting token balance for ${blockchain}:`, error);
        // Return a valid JSON response with error information
        console.log(JSON.stringify({
            error: true,
            message: `Error getting token balance for ${blockchain}: ${error?.message || 'Unknown error'}`,
            balance: '0'
        }));
        process.exit(1);
    }
}
/**
 * Main function to handle command line arguments
 */
async function main() {
    // Get command line arguments
    const args = process.argv.slice(2);
    if (args.length === 0) {
        console.error('No command specified');
        process.exit(1);
    }
    const command = args[0];
    try {
        switch (command) {
            case '--help': {
                console.log(`
Token Service Help
=================

Available commands:

  get-wallet-balance <blockchain> <walletAddress>
    - Get the native token balance for a wallet
    - blockchain: ETH, BSC, SOL, or BASE (case insensitive)
    - walletAddress: The wallet address to check

  get-token-balance <blockchain> <walletAddress> <tokenAddress>
    - Get the token balance for a specific token in a wallet
    - blockchain: ETH, BSC, SOL, or BASE (case insensitive)
    - walletAddress: The wallet address to check
    - tokenAddress: The token contract address

Examples:
  node dist/index.js get-wallet-balance ETH ******************************************
  node dist/index.js get-token-balance BSC ****************************************** ******************************************
        `);
                break;
            }
            case 'get-wallet-balance': {
                if (args.length !== 3) {
                    console.error('Invalid number of arguments for get-wallet-balance');
                    console.error('Usage: node dist/index.js get-wallet-balance <blockchain> <walletAddress>');
                    process.exit(1);
                }
                const blockchain = args[1];
                const walletAddress = args[2];
                try {
                    const balance = await getWalletBalance(blockchain, walletAddress);
                    console.log(JSON.stringify({ balance }));
                }
                catch (error) {
                    console.error(`Error:`, error?.message || 'Unknown error');
                    console.log(JSON.stringify({
                        error: true,
                        message: error?.message || 'Unknown error',
                        balance: '0'
                    }));
                }
                break;
            }
            case 'get-token-balance': {
                if (args.length !== 4) {
                    console.error('Invalid number of arguments for get-token-balance');
                    console.error('Usage: node dist/index.js get-token-balance <blockchain> <walletAddress> <tokenAddress>');
                    process.exit(1);
                }
                const blockchain = args[1];
                const walletAddress = args[2];
                const tokenAddress = args[3];
                try {
                    const balance = await getTokenBalance(blockchain, walletAddress, tokenAddress);
                    console.log(JSON.stringify({ balance }));
                }
                catch (error) {
                    console.error(`Error:`, error?.message || 'Unknown error');
                    console.log(JSON.stringify({
                        error: true,
                        message: error?.message || 'Unknown error',
                        balance: '0'
                    }));
                }
                break;
            }
            default:
                console.error(`Unknown command: ${command}`);
                console.error('Use --help to see available commands');
                process.exit(1);
        }
    }
    catch (error) {
        console.error('Error:', error?.message || 'Unknown error');
        // Output valid JSON with error message
        console.log(JSON.stringify({
            error: true,
            message: error?.message || 'Unknown error',
            balance: '0'
        }));
        process.exit(1);
    }
}
// Ensure we always output valid JSON, even in case of errors
process.on('uncaughtException', (error) => {
    console.error('Uncaught exception:', error?.message || 'Unknown error');
    // Output valid JSON with error message
    console.log(JSON.stringify({
        error: true,
        message: error?.message || 'Unknown error',
        balance: '0'
    }));
    process.exit(1);
});
// Run the main function
main().catch((error) => {
    console.error('Unhandled error:', error?.message || 'Unknown error');
    // Output valid JSON with error message
    console.log(JSON.stringify({
        error: true,
        message: error?.message || 'Unknown error',
        balance: '0'
    }));
    process.exit(1);
});
