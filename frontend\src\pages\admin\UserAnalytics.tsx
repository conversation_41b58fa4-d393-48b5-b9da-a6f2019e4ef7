import { useState, useEffect } from 'react';
import {
  UserIcon,
  UsersIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  GlobeAltIcon,
  ClockIcon,
  CurrencyDollarIcon,
  MagnifyingGlassIcon,
  ArrowsUpDownIcon,
  ChevronDownIcon,
  ChevronUpIcon,
  EyeIcon,
  DocumentArrowDownIcon
} from '@heroicons/react/24/outline';
import { Card } from '../../components/ui/Card';
import Button from '../../components/ui/Button';
import { Line, Bar, Doughnut } from 'react-chartjs-2';
import { adminApi } from '../../services/adminApi';
import 'chart.js/auto';

// Chart.js options
const lineOptions = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      position: 'top' as const,
      labels: {
        color: '#e5e7eb',
      },
    },
    tooltip: {
      mode: 'index' as const,
      intersect: false,
    },
  },
  scales: {
    y: {
      grid: {
        color: 'rgba(255, 255, 255, 0.1)',
      },
      ticks: {
        color: '#e5e7eb',
      },
    },
    x: {
      grid: {
        color: 'rgba(255, 255, 255, 0.1)',
      },
      ticks: {
        color: '#e5e7eb',
      },
    },
  },
};

const barOptions = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      position: 'top' as const,
      labels: {
        color: '#e5e7eb',
      },
    },
    tooltip: {
      mode: 'index' as const,
      intersect: false,
    },
  },
  scales: {
    y: {
      grid: {
        color: 'rgba(255, 255, 255, 0.1)',
      },
      ticks: {
        color: '#e5e7eb',
      },
    },
    x: {
      grid: {
        color: 'rgba(255, 255, 255, 0.1)',
      },
      ticks: {
        color: '#e5e7eb',
      },
    },
  },
};

const doughnutOptions = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      position: 'right' as const,
      labels: {
        color: '#e5e7eb',
      },
    },
  },
};

interface User {
  id: string;
  chatId: number;
  username: string;
  firstName: string;
  lastName?: string;
  joinedDate: string;
  lastActive: string;
  status: 'active' | 'inactive';
  totalTransactions: number;
  transactionVolume: number;
  profitLoss: number;
  activeBots: number;
}

interface UserTransaction {
  id: string;
  userId: string;
  botId: string;
  botName: string;
  type: 'buy' | 'sell';
  asset: string;
  amount: number;
  price: number;
  total: number;
  fee: number;
  status: 'completed' | 'pending' | 'failed';
  timestamp: string;
  blockchain: string;
}

export default function UserAnalytics() {
  const [loading, setLoading] = useState(true);
  const [timeRange, setTimeRange] = useState('7d');
  const [view, setView] = useState<'overview' | 'users'>('overview');
  const [searchQuery, setSearchQuery] = useState('');
  const [sortField, setSortField] = useState<keyof User>('totalTransactions');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [userTransactions, setUserTransactions] = useState<UserTransaction[]>([]);
  const [showUserModal, setShowUserModal] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);

  const [stats, setStats] = useState({
    totalUsers: 0,
    activeUsers: 0,
    newUsers: 0,
    userGrowth: 0,
    retentionRate: 0,
    averageSessionTime: 0,
    transactionsPerUser: 0,
    totalTransactionVolume: 0,
    avgProfitLoss: 0,
  });

  const [users, setUsers] = useState<User[]>([]);

  // Chart data
  const [userGrowthData, setUserGrowthData] = useState({
    labels: [],
    datasets: [
      {
        label: 'New Users',
        data: [],
        borderColor: 'rgb(99, 102, 241)',
        backgroundColor: 'rgba(99, 102, 241, 0.5)',
      },
      {
        label: 'Active Users',
        data: [],
        borderColor: 'rgb(34, 197, 94)',
        backgroundColor: 'rgba(34, 197, 94, 0.5)',
      },
    ],
  });

  const [userActivityData, setUserActivityData] = useState({
    labels: [],
    datasets: [
      {
        label: 'User Activity',
        data: [],
        backgroundColor: 'rgba(99, 102, 241, 0.8)',
      },
    ],
  });

  const [userLocationData, setUserLocationData] = useState({
    labels: [],
    datasets: [
      {
        label: 'User Locations',
        data: [],
        backgroundColor: [
          'rgba(99, 102, 241, 0.8)',
          'rgba(34, 197, 94, 0.8)',
          'rgba(234, 88, 12, 0.8)',
          'rgba(217, 70, 239, 0.8)',
          'rgba(59, 130, 246, 0.8)',
        ],
        borderWidth: 1,
      },
    ],
  });

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        // In a real app, you would fetch from API
        // const data = await adminApi.getUserAnalytics(timeRange);
        // setStats(data.stats);
        // setUserGrowthData(data.userGrowthData);
        // setUserActivityData(data.userActivityData);
        // setUserLocationData(data.userLocationData);
        // setUsers(data.users);

        // Mock data for development
        setTimeout(() => {
          // Mock stats
          setStats({
            totalUsers: 1250,
            activeUsers: 876,
            newUsers: 124,
            userGrowth: 12.4,
            retentionRate: 78.5,
            averageSessionTime: 8.3,
            transactionsPerUser: 3.2,
            totalTransactionVolume: 2456789.50,
            avgProfitLoss: 324.75,
          });

          // Mock user growth data
          const labels = timeRange === '7d'
            ? ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
            : timeRange === '30d'
              ? Array.from({ length: 30 }, (_, i) => `Day ${i + 1}`)
              : ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];

          setUserGrowthData({
            labels,
            datasets: [
              {
                label: 'New Users',
                data: Array.from({ length: labels.length }, () => Math.floor(Math.random() * 50) + 10),
                borderColor: 'rgb(99, 102, 241)',
                backgroundColor: 'rgba(99, 102, 241, 0.5)',
              },
              {
                label: 'Active Users',
                data: Array.from({ length: labels.length }, () => Math.floor(Math.random() * 200) + 100),
                borderColor: 'rgb(34, 197, 94)',
                backgroundColor: 'rgba(34, 197, 94, 0.5)',
              },
            ],
          });

          // Mock user activity data
          setUserActivityData({
            labels: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00'],
            datasets: [
              {
                label: 'User Activity',
                data: [65, 40, 80, 120, 150, 90],
                backgroundColor: 'rgba(99, 102, 241, 0.8)',
              },
            ],
          });

          // Mock user location data
          setUserLocationData({
            labels: ['United States', 'Europe', 'Asia', 'Africa', 'Others'],
            datasets: [
              {
                label: 'User Locations',
                data: [35, 25, 20, 10, 10],
                backgroundColor: [
                  'rgba(99, 102, 241, 0.8)',
                  'rgba(34, 197, 94, 0.8)',
                  'rgba(234, 88, 12, 0.8)',
                  'rgba(217, 70, 239, 0.8)',
                  'rgba(59, 130, 246, 0.8)',
                ],
                borderWidth: 1,
              },
            ],
          });

          // Mock users data
          const mockUsers: User[] = Array.from({ length: 50 }, (_, i) => {
            const isActive = Math.random() > 0.2;
            const transactionVolume = Math.random() * 10000 + 500;
            const profitLoss = (Math.random() * 2000) - 1000;

            return {
              id: `user-${i + 1}`,
              chatId: 1000000 + i,
              username: `user${i + 1}`,
              firstName: `User ${i + 1}`,
              lastName: Math.random() > 0.3 ? `LastName ${i + 1}` : undefined,
              joinedDate: new Date(Date.now() - Math.random() * 10000000000).toISOString(),
              lastActive: isActive
                ? new Date(Date.now() - Math.random() * 1000000).toISOString()
                : new Date(Date.now() - Math.random() * 10000000000).toISOString(),
              status: isActive ? 'active' : 'inactive',
              totalTransactions: Math.floor(Math.random() * 50) + 1,
              transactionVolume: parseFloat(transactionVolume.toFixed(2)),
              profitLoss: parseFloat(profitLoss.toFixed(2)),
              activeBots: Math.floor(Math.random() * 3),
            };
          });

          setUsers(mockUsers);
          setLoading(false);
        }, 800);
      } catch (error) {
        console.error('Error fetching user analytics:', error);
        setLoading(false);
      }
    };

    fetchData();
  }, [timeRange]);

  // Helper functions
  const fetchUserTransactions = async (userId: string) => {
    // In a real app, you would fetch from API
    // const data = await adminApi.getUserTransactions(userId);
    // setUserTransactions(data);

    // Mock data for development
    const mockTransactions: UserTransaction[] = Array.from({ length: 15 }, (_, i) => {
      const isBuy = Math.random() > 0.5;
      const amount = Math.random() * 2 + 0.1;
      const price = Math.random() * 50000 + 10000;
      const total = amount * price;
      const fee = total * 0.002;

      return {
        id: `tx-${i + 1}`,
        userId,
        botId: `bot-${Math.floor(Math.random() * 3) + 1}`,
        botName: `Trading Bot ${Math.floor(Math.random() * 3) + 1}`,
        type: isBuy ? 'buy' : 'sell',
        asset: ['BTC', 'ETH', 'SOL', 'DOGE'][Math.floor(Math.random() * 4)],
        amount: parseFloat(amount.toFixed(6)),
        price: parseFloat(price.toFixed(2)),
        total: parseFloat(total.toFixed(2)),
        fee: parseFloat(fee.toFixed(2)),
        status: Math.random() > 0.9 ? 'pending' : Math.random() > 0.95 ? 'failed' : 'completed',
        timestamp: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
        blockchain: ['Ethereum', 'Bitcoin', 'Solana'][Math.floor(Math.random() * 3)],
      };
    });

    // Sort by timestamp (newest first)
    mockTransactions.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());

    setUserTransactions(mockTransactions);
  };

  const handleViewUser = (user: User) => {
    setSelectedUser(user);
    fetchUserTransactions(user.id);
    setShowUserModal(true);
  };

  const handleSort = (field: keyof User) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('desc');
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    }).format(date);
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(value);
  };

  const formatNumber = (value: number) => {
    return new Intl.NumberFormat('en-US').format(value);
  };

  // Filter and sort users
  const filteredUsers = users.filter(user => {
    if (!searchQuery) return true;

    const query = searchQuery.toLowerCase();
    return (
      user.username.toLowerCase().includes(query) ||
      user.firstName.toLowerCase().includes(query) ||
      (user.lastName && user.lastName.toLowerCase().includes(query)) ||
      user.chatId.toString().includes(query)
    );
  });

  const sortedUsers = [...filteredUsers].sort((a, b) => {
    const aValue = a[sortField];
    const bValue = b[sortField];

    // Handle string comparisons
    if (typeof aValue === 'string' && typeof bValue === 'string') {
      const aValueLower = aValue.toLowerCase();
      const bValueLower = bValue.toLowerCase();

      if (aValueLower < bValueLower) return sortDirection === 'asc' ? -1 : 1;
      if (aValueLower > bValueLower) return sortDirection === 'asc' ? 1 : -1;
      return 0;
    }

    // Handle date comparisons
    if (sortField === 'joinedDate' || sortField === 'lastActive') {
      if (aValue && bValue) {
        return sortDirection === 'asc'
          ? new Date(aValue as string).getTime() - new Date(bValue as string).getTime()
          : new Date(bValue as string).getTime() - new Date(aValue as string).getTime();
      }
      return 0;
    }

    // Handle number comparisons
    if (typeof aValue === 'number' && typeof bValue === 'number') {
      return sortDirection === 'asc' ? aValue - bValue : bValue - aValue;
    }

    // Handle other cases
    if (aValue === undefined && bValue !== undefined) return sortDirection === 'asc' ? -1 : 1;
    if (aValue !== undefined && bValue === undefined) return sortDirection === 'asc' ? 1 : -1;
    if (aValue === undefined && bValue === undefined) return 0;

    // Default comparison
    return 0;
  });

  // Pagination
  const indexOfLastUser = currentPage * itemsPerPage;
  const indexOfFirstUser = indexOfLastUser - itemsPerPage;
  const currentUsers = sortedUsers.slice(indexOfFirstUser, indexOfLastUser);
  const totalPages = Math.ceil(sortedUsers.length / itemsPerPage);

  const paginate = (pageNumber: number) => setCurrentPage(pageNumber);

  // Export user data to CSV
  const exportUserData = () => {
    const headers = [
      'Chat ID',
      'Username',
      'First Name',
      'Last Name',
      'Joined Date',
      'Last Active',
      'Status',
      'Total Transactions',
      'Transaction Volume',
      'Profit/Loss',
      'Active Bots'
    ];

    const csvData = filteredUsers.map(user => [
      user.chatId,
      user.username,
      user.firstName,
      user.lastName || '',
      formatDate(user.joinedDate),
      formatDate(user.lastActive),
      user.status,
      user.totalTransactions,
      user.transactionVolume.toFixed(2),
      user.profitLoss.toFixed(2),
      user.activeBots
    ]);

    const csvContent = [
      headers.join(','),
      ...csvData.map(row => row.join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.setAttribute('href', url);
    link.setAttribute('download', `easybot_users_${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-2xl font-bold text-white">User Analytics</h1>
          <p className="mt-1 text-sm text-gray-400">Insights and statistics about your Telegram bot users</p>
        </div>
        <div className="flex flex-wrap gap-2">
          <div className="flex space-x-2">
            <button
              onClick={() => setTimeRange('7d')}
              className={`px-3 py-1 rounded-md text-sm font-medium ${
                timeRange === '7d'
                  ? 'bg-indigo-600 text-white'
                  : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
              }`}
            >
              7 Days
            </button>
            <button
              onClick={() => setTimeRange('30d')}
              className={`px-3 py-1 rounded-md text-sm font-medium ${
                timeRange === '30d'
                  ? 'bg-indigo-600 text-white'
                  : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
              }`}
            >
              30 Days
            </button>
            <button
              onClick={() => setTimeRange('1y')}
              className={`px-3 py-1 rounded-md text-sm font-medium ${
                timeRange === '1y'
                  ? 'bg-indigo-600 text-white'
                  : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
              }`}
            >
              1 Year
            </button>
          </div>

          <div className="flex space-x-2">
            <button
              onClick={() => setView('overview')}
              className={`px-3 py-1 rounded-md text-sm font-medium ${
                view === 'overview'
                  ? 'bg-indigo-600 text-white'
                  : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
              }`}
            >
              Overview
            </button>
            <button
              onClick={() => setView('users')}
              className={`px-3 py-1 rounded-md text-sm font-medium ${
                view === 'users'
                  ? 'bg-indigo-600 text-white'
                  : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
              }`}
            >
              User List
            </button>
          </div>
        </div>
      </div>

      {loading ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"></div>
        </div>
      ) : (
        <>
          {view === 'overview' ? (
            <>
              {/* Stats Cards */}
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                <Card className="p-4">
                  <div className="flex items-center">
                    <div className="flex-shrink-0 p-3 rounded-md bg-indigo-500/20">
                      <UsersIcon className="h-6 w-6 text-indigo-400" />
                    </div>
                    <div className="ml-4">
                      <h3 className="text-sm font-medium text-gray-400">Total Users</h3>
                      <p className="text-2xl font-semibold text-white">{formatNumber(stats.totalUsers)}</p>
                    </div>
                  </div>
                </Card>

                <Card className="p-4">
                  <div className="flex items-center">
                    <div className="flex-shrink-0 p-3 rounded-md bg-green-500/20">
                      <UserIcon className="h-6 w-6 text-green-400" />
                    </div>
                    <div className="ml-4">
                      <h3 className="text-sm font-medium text-gray-400">Active Users</h3>
                      <div className="flex items-center">
                        <p className="text-2xl font-semibold text-white">{formatNumber(stats.activeUsers)}</p>
                        <span className="ml-2 text-xs font-medium text-green-400 flex items-center">
                          <ArrowUpIcon className="h-3 w-3 mr-0.5" />
                          {stats.userGrowth}%
                        </span>
                      </div>
                    </div>
                  </div>
                </Card>

                <Card className="p-4">
                  <div className="flex items-center">
                    <div className="flex-shrink-0 p-3 rounded-md bg-blue-500/20">
                      <CurrencyDollarIcon className="h-6 w-6 text-blue-400" />
                    </div>
                    <div className="ml-4">
                      <h3 className="text-sm font-medium text-gray-400">Transaction Volume</h3>
                      <p className="text-2xl font-semibold text-white">{formatCurrency(stats.totalTransactionVolume)}</p>
                    </div>
                  </div>
                </Card>

                <Card className="p-4">
                  <div className="flex items-center">
                    <div className="flex-shrink-0 p-3 rounded-md bg-orange-500/20">
                      <CurrencyDollarIcon className="h-6 w-6 text-orange-400" />
                    </div>
                    <div className="ml-4">
                      <h3 className="text-sm font-medium text-gray-400">Avg. Profit/Loss</h3>
                      <div className="flex items-center">
                        <p className="text-2xl font-semibold text-white">{formatCurrency(stats.avgProfitLoss)}</p>
                        <span className="ml-2 text-xs font-medium text-green-400 flex items-center">
                          <ArrowUpIcon className="h-3 w-3 mr-0.5" />
                          8.2%
                        </span>
                      </div>
                    </div>
                  </div>
                </Card>
              </div>

              {/* Charts */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <Card className="p-4">
                  <h3 className="text-lg font-medium text-white mb-4">User Growth</h3>
                  <div className="h-80">
                    <Line options={lineOptions} data={userGrowthData} />
                  </div>
                </Card>

                <Card className="p-4">
                  <h3 className="text-lg font-medium text-white mb-4">User Activity by Hour</h3>
                  <div className="h-80">
                    <Bar options={barOptions} data={userActivityData} />
                  </div>
                </Card>

                <Card className="p-4">
                  <h3 className="text-lg font-medium text-white mb-4">User Locations</h3>
                  <div className="h-80">
                    <Doughnut options={doughnutOptions} data={userLocationData} />
                  </div>
                </Card>

                <Card className="p-4">
                  <h3 className="text-lg font-medium text-white mb-4">User Retention</h3>
                  <div className="flex items-center justify-center h-80">
                    <div className="text-center">
                      <div className="inline-flex items-center justify-center p-6 rounded-full bg-indigo-500/20 mb-4">
                        <span className="text-4xl font-bold text-white">{stats.retentionRate}%</span>
                      </div>
                      <p className="text-gray-400">User Retention Rate</p>
                      <p className="mt-2 text-sm text-gray-500">Percentage of users who return within 7 days</p>
                    </div>
                  </div>
                </Card>
              </div>

              {/* Additional Stats */}
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                <Card className="p-4">
                  <div className="flex items-center">
                    <div className="flex-shrink-0 p-3 rounded-md bg-purple-500/20">
                      <ClockIcon className="h-6 w-6 text-purple-400" />
                    </div>
                    <div className="ml-4">
                      <h3 className="text-sm font-medium text-gray-400">Avg. Session Time</h3>
                      <p className="text-2xl font-semibold text-white">{stats.averageSessionTime} min</p>
                    </div>
                  </div>
                </Card>

                <Card className="p-4">
                  <div className="flex items-center">
                    <div className="flex-shrink-0 p-3 rounded-md bg-pink-500/20">
                      <CurrencyDollarIcon className="h-6 w-6 text-pink-400" />
                    </div>
                    <div className="ml-4">
                      <h3 className="text-sm font-medium text-gray-400">Transactions/User</h3>
                      <p className="text-2xl font-semibold text-white">{stats.transactionsPerUser}</p>
                    </div>
                  </div>
                </Card>

                <Card className="p-4">
                  <div className="flex items-center">
                    <div className="flex-shrink-0 p-3 rounded-md bg-yellow-500/20">
                      <UserIcon className="h-6 w-6 text-yellow-400" />
                    </div>
                    <div className="ml-4">
                      <h3 className="text-sm font-medium text-gray-400">New Users</h3>
                      <div className="flex items-center">
                        <p className="text-2xl font-semibold text-white">{formatNumber(stats.newUsers)}</p>
                        <span className="ml-2 text-xs font-medium text-green-400 flex items-center">
                          <ArrowUpIcon className="h-3 w-3 mr-0.5" />
                          5.3%
                        </span>
                      </div>
                    </div>
                  </div>
                </Card>

                <Card className="p-4 flex items-center justify-center">
                  <Button
                    variant="primary"
                    onClick={() => setView('users')}
                    icon={<UsersIcon className="h-5 w-5" />}
                  >
                    View User List
                  </Button>
                </Card>
              </div>
            </>
          ) : (
            <>
              {/* User List View */}
              <Card className="p-4">
                <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 gap-4">
                  <h3 className="text-lg font-medium text-white">User List</h3>
                  <div className="flex flex-col sm:flex-row gap-4">
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
                      </div>
                      <input
                        type="text"
                        placeholder="Search users..."
                        className="block w-full pl-10 pr-3 py-2 border border-gray-700 rounded-md leading-5 bg-gray-800 text-gray-300 placeholder-gray-400 focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                      />
                    </div>
                    <Button
                      variant="outline"
                      onClick={exportUserData}
                      icon={<DocumentArrowDownIcon className="h-5 w-5" />}
                    >
                      Export Data
                    </Button>
                  </div>
                </div>

                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-700">
                    <thead className="bg-gray-800">
                      <tr>
                        <th
                          scope="col"
                          className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider cursor-pointer"
                          onClick={() => handleSort('username')}
                        >
                          <div className="flex items-center">
                            Username
                            {sortField === 'username' && (
                              sortDirection === 'asc' ?
                                <ChevronUpIcon className="h-4 w-4 ml-1" /> :
                                <ChevronDownIcon className="h-4 w-4 ml-1" />
                            )}
                          </div>
                        </th>
                        <th
                          scope="col"
                          className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider cursor-pointer"
                          onClick={() => handleSort('chatId')}
                        >
                          <div className="flex items-center">
                            Chat ID
                            {sortField === 'chatId' && (
                              sortDirection === 'asc' ?
                                <ChevronUpIcon className="h-4 w-4 ml-1" /> :
                                <ChevronDownIcon className="h-4 w-4 ml-1" />
                            )}
                          </div>
                        </th>
                        <th
                          scope="col"
                          className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider cursor-pointer"
                          onClick={() => handleSort('lastActive')}
                        >
                          <div className="flex items-center">
                            Last Active
                            {sortField === 'lastActive' && (
                              sortDirection === 'asc' ?
                                <ChevronUpIcon className="h-4 w-4 ml-1" /> :
                                <ChevronDownIcon className="h-4 w-4 ml-1" />
                            )}
                          </div>
                        </th>
                        <th
                          scope="col"
                          className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider cursor-pointer"
                          onClick={() => handleSort('status')}
                        >
                          <div className="flex items-center">
                            Status
                            {sortField === 'status' && (
                              sortDirection === 'asc' ?
                                <ChevronUpIcon className="h-4 w-4 ml-1" /> :
                                <ChevronDownIcon className="h-4 w-4 ml-1" />
                            )}
                          </div>
                        </th>
                        <th
                          scope="col"
                          className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider cursor-pointer"
                          onClick={() => handleSort('totalTransactions')}
                        >
                          <div className="flex items-center">
                            Transactions
                            {sortField === 'totalTransactions' && (
                              sortDirection === 'asc' ?
                                <ChevronUpIcon className="h-4 w-4 ml-1" /> :
                                <ChevronDownIcon className="h-4 w-4 ml-1" />
                            )}
                          </div>
                        </th>
                        <th
                          scope="col"
                          className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider cursor-pointer"
                          onClick={() => handleSort('transactionVolume')}
                        >
                          <div className="flex items-center">
                            Volume
                            {sortField === 'transactionVolume' && (
                              sortDirection === 'asc' ?
                                <ChevronUpIcon className="h-4 w-4 ml-1" /> :
                                <ChevronDownIcon className="h-4 w-4 ml-1" />
                            )}
                          </div>
                        </th>
                        <th
                          scope="col"
                          className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider cursor-pointer"
                          onClick={() => handleSort('profitLoss')}
                        >
                          <div className="flex items-center">
                            Profit/Loss
                            {sortField === 'profitLoss' && (
                              sortDirection === 'asc' ?
                                <ChevronUpIcon className="h-4 w-4 ml-1" /> :
                                <ChevronDownIcon className="h-4 w-4 ml-1" />
                            )}
                          </div>
                        </th>
                        <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-300 uppercase tracking-wider">
                          Actions
                        </th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-700 bg-gray-800/50">
                      {currentUsers.length === 0 ? (
                        <tr>
                          <td colSpan={8} className="px-6 py-4 text-center text-sm text-gray-400">
                            No users found
                          </td>
                        </tr>
                      ) : (
                        currentUsers.map((user) => (
                          <tr key={user.id} className="hover:bg-gray-700/30 transition-colors duration-150">
                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-white">
                              {user.username}
                              <div className="text-xs text-gray-400">
                                {user.firstName} {user.lastName}
                              </div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                              {user.chatId}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                              {formatDate(user.lastActive)}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                                user.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                              }`}>
                                {user.status === 'active' ? 'Active' : 'Inactive'}
                              </span>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                              {user.totalTransactions}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                              {formatCurrency(user.transactionVolume)}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm">
                              <span className={user.profitLoss >= 0 ? 'text-green-400' : 'text-red-400'}>
                                {formatCurrency(user.profitLoss)}
                              </span>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                              <button
                                onClick={() => handleViewUser(user)}
                                className="text-indigo-400 hover:text-indigo-300 transition-colors duration-150"
                              >
                                <EyeIcon className="h-5 w-5" />
                              </button>
                            </td>
                          </tr>
                        ))
                      )}
                    </tbody>
                  </table>
                </div>

                {/* Pagination */}
                {totalPages > 1 && (
                  <div className="flex items-center justify-between border-t border-gray-700 px-4 py-3 sm:px-6 mt-4">
                    <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                      <div>
                        <p className="text-sm text-gray-400">
                          Showing <span className="font-medium">{indexOfFirstUser + 1}</span> to{' '}
                          <span className="font-medium">
                            {Math.min(indexOfLastUser, filteredUsers.length)}
                          </span>{' '}
                          of <span className="font-medium">{filteredUsers.length}</span> results
                        </p>
                      </div>
                      <div>
                        <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                          <button
                            onClick={() => paginate(Math.max(1, currentPage - 1))}
                            disabled={currentPage === 1}
                            className={`relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-700 bg-gray-800 text-sm font-medium ${
                              currentPage === 1 ? 'text-gray-500 cursor-not-allowed' : 'text-gray-300 hover:bg-gray-700'
                            }`}
                          >
                            <span className="sr-only">Previous</span>
                            <ChevronDownIcon className="h-5 w-5 rotate-90" aria-hidden="true" />
                          </button>

                          {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                            // Show pages around current page
                            let pageNum;
                            if (totalPages <= 5) {
                              pageNum = i + 1;
                            } else if (currentPage <= 3) {
                              pageNum = i + 1;
                            } else if (currentPage >= totalPages - 2) {
                              pageNum = totalPages - 4 + i;
                            } else {
                              pageNum = currentPage - 2 + i;
                            }

                            return (
                              <button
                                key={pageNum}
                                onClick={() => paginate(pageNum)}
                                className={`relative inline-flex items-center px-4 py-2 border border-gray-700 text-sm font-medium ${
                                  currentPage === pageNum
                                    ? 'z-10 bg-indigo-600 border-indigo-500 text-white'
                                    : 'bg-gray-800 text-gray-300 hover:bg-gray-700'
                                }`}
                              >
                                {pageNum}
                              </button>
                            );
                          })}

                          <button
                            onClick={() => paginate(Math.min(totalPages, currentPage + 1))}
                            disabled={currentPage === totalPages}
                            className={`relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-700 bg-gray-800 text-sm font-medium ${
                              currentPage === totalPages ? 'text-gray-500 cursor-not-allowed' : 'text-gray-300 hover:bg-gray-700'
                            }`}
                          >
                            <span className="sr-only">Next</span>
                            <ChevronDownIcon className="h-5 w-5 -rotate-90" aria-hidden="true" />
                          </button>
                        </nav>
                      </div>
                    </div>
                  </div>
                )}
              </Card>
            </>
          )}
        </>
      )}

      {/* User Detail Modal */}
      {showUserModal && selectedUser && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 transition-opacity" aria-hidden="true">
              <div className="absolute inset-0 bg-gray-900 opacity-75"></div>
            </div>
            <span className="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
            <div className="inline-block align-bottom bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-5xl sm:w-full">
              <div className="bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div className="sm:flex sm:items-start">
                  <div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                    <div className="flex justify-between items-center mb-4">
                      <h3 className="text-lg leading-6 font-medium text-white">
                        User Details: {selectedUser.username}
                      </h3>
                      <button
                        type="button"
                        className="bg-gray-800 rounded-md text-gray-400 hover:text-white focus:outline-none"
                        onClick={() => setShowUserModal(false)}
                      >
                        <span className="sr-only">Close</span>
                        <svg className="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                      </button>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                      <div className="bg-gray-700/30 rounded-lg p-4">
                        <h4 className="text-sm font-medium text-gray-400 mb-2">User Information</h4>
                        <div className="space-y-2">
                          <div className="flex justify-between">
                            <span className="text-sm text-gray-400">Chat ID:</span>
                            <span className="text-sm text-white">{selectedUser.chatId}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-sm text-gray-400">Name:</span>
                            <span className="text-sm text-white">{selectedUser.firstName} {selectedUser.lastName}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-sm text-gray-400">Joined:</span>
                            <span className="text-sm text-white">{formatDate(selectedUser.joinedDate)}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-sm text-gray-400">Last Active:</span>
                            <span className="text-sm text-white">{formatDate(selectedUser.lastActive)}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-sm text-gray-400">Status:</span>
                            <span className={`text-sm ${selectedUser.status === 'active' ? 'text-green-400' : 'text-red-400'}`}>
                              {selectedUser.status === 'active' ? 'Active' : 'Inactive'}
                            </span>
                          </div>
                        </div>
                      </div>

                      <div className="bg-gray-700/30 rounded-lg p-4">
                        <h4 className="text-sm font-medium text-gray-400 mb-2">Transaction Summary</h4>
                        <div className="space-y-2">
                          <div className="flex justify-between">
                            <span className="text-sm text-gray-400">Total Transactions:</span>
                            <span className="text-sm text-white">{selectedUser.totalTransactions}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-sm text-gray-400">Transaction Volume:</span>
                            <span className="text-sm text-white">{formatCurrency(selectedUser.transactionVolume)}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-sm text-gray-400">Profit/Loss:</span>
                            <span className={`text-sm ${selectedUser.profitLoss >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                              {formatCurrency(selectedUser.profitLoss)}
                            </span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-sm text-gray-400">Active Bots:</span>
                            <span className="text-sm text-white">{selectedUser.activeBots}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-sm text-gray-400">Avg. Transaction Size:</span>
                            <span className="text-sm text-white">
                              {selectedUser.totalTransactions > 0
                                ? formatCurrency(selectedUser.transactionVolume / selectedUser.totalTransactions)
                                : '$0.00'}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="mb-4">
                      <h4 className="text-sm font-medium text-gray-400 mb-2">Transaction History</h4>
                      <div className="overflow-x-auto">
                        <table className="min-w-full divide-y divide-gray-700">
                          <thead className="bg-gray-700/50">
                            <tr>
                              <th scope="col" className="px-4 py-2 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                                Date
                              </th>
                              <th scope="col" className="px-4 py-2 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                                Bot
                              </th>
                              <th scope="col" className="px-4 py-2 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                                Type
                              </th>
                              <th scope="col" className="px-4 py-2 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                                Asset
                              </th>
                              <th scope="col" className="px-4 py-2 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                                Amount
                              </th>
                              <th scope="col" className="px-4 py-2 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                                Price
                              </th>
                              <th scope="col" className="px-4 py-2 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                                Total
                              </th>
                              <th scope="col" className="px-4 py-2 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                                Status
                              </th>
                            </tr>
                          </thead>
                          <tbody className="divide-y divide-gray-700 bg-gray-800/30">
                            {userTransactions.length === 0 ? (
                              <tr>
                                <td colSpan={8} className="px-4 py-2 text-center text-sm text-gray-400">
                                  No transactions found
                                </td>
                              </tr>
                            ) : (
                              userTransactions.map((tx) => (
                                <tr key={tx.id} className="hover:bg-gray-700/30 transition-colors duration-150">
                                  <td className="px-4 py-2 whitespace-nowrap text-xs text-gray-300">
                                    {formatDate(tx.timestamp)}
                                  </td>
                                  <td className="px-4 py-2 whitespace-nowrap text-xs text-gray-300">
                                    {tx.botName}
                                  </td>
                                  <td className="px-4 py-2 whitespace-nowrap text-xs">
                                    <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${
                                      tx.type === 'buy' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                                    }`}>
                                      {tx.type.toUpperCase()}
                                    </span>
                                  </td>
                                  <td className="px-4 py-2 whitespace-nowrap text-xs text-gray-300">
                                    {tx.asset}
                                  </td>
                                  <td className="px-4 py-2 whitespace-nowrap text-xs text-gray-300">
                                    {tx.amount.toFixed(6)}
                                  </td>
                                  <td className="px-4 py-2 whitespace-nowrap text-xs text-gray-300">
                                    {formatCurrency(tx.price)}
                                  </td>
                                  <td className="px-4 py-2 whitespace-nowrap text-xs text-gray-300">
                                    {formatCurrency(tx.total)}
                                  </td>
                                  <td className="px-4 py-2 whitespace-nowrap text-xs">
                                    <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${
                                      tx.status === 'completed' ? 'bg-green-100 text-green-800' :
                                      tx.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                                      'bg-red-100 text-red-800'
                                    }`}>
                                      {tx.status.toUpperCase()}
                                    </span>
                                  </td>
                                </tr>
                              ))
                            )}
                          </tbody>
                        </table>
                      </div>
                    </div>

                    <div className="bg-gray-700/30 rounded-lg p-4">
                      <h4 className="text-sm font-medium text-gray-400 mb-2">Performance Metrics</h4>
                      <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                        <div className="bg-gray-800/50 rounded-lg p-3">
                          <div className="text-center">
                            <div className="text-xs text-gray-400 mb-1">Success Rate</div>
                            <div className="text-xl font-semibold text-white">
                              {userTransactions.length > 0
                                ? `${Math.round((userTransactions.filter(tx => tx.status === 'completed').length / userTransactions.length) * 100)}%`
                                : '0%'}
                            </div>
                          </div>
                        </div>
                        <div className="bg-gray-800/50 rounded-lg p-3">
                          <div className="text-center">
                            <div className="text-xs text-gray-400 mb-1">Avg. Transaction Fee</div>
                            <div className="text-xl font-semibold text-white">
                              {userTransactions.length > 0
                                ? formatCurrency(userTransactions.reduce((sum, tx) => sum + tx.fee, 0) / userTransactions.length)
                                : '$0.00'}
                            </div>
                          </div>
                        </div>
                        <div className="bg-gray-800/50 rounded-lg p-3">
                          <div className="text-center">
                            <div className="text-xs text-gray-400 mb-1">Preferred Asset</div>
                            <div className="text-xl font-semibold text-white">
                              {userTransactions.length > 0
                                ? (() => {
                                    const assetCounts = userTransactions.reduce((acc, tx) => {
                                      acc[tx.asset] = (acc[tx.asset] || 0) + 1;
                                      return acc;
                                    }, {} as Record<string, number>);

                                    return Object.entries(assetCounts).sort((a, b) => b[1] - a[1])[0][0];
                                  })()
                                : 'N/A'}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div className="bg-gray-800 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button
                  type="button"
                  onClick={() => setShowUserModal(false)}
                  className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-indigo-600 text-base font-medium text-white hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:ml-3 sm:w-auto sm:text-sm"
                >
                  Close
                </button>
                <button
                  type="button"
                  onClick={() => {
                    // In a real app, you would implement export functionality
                    alert('Export functionality would be implemented here');
                  }}
                  className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-600 shadow-sm px-4 py-2 bg-gray-700 text-base font-medium text-gray-300 hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                >
                  Export User Data
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
