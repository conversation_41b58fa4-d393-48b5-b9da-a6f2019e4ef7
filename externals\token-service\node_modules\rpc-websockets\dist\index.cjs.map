{"version": 3, "sources": ["../src/lib/client/websocket.ts", "../src/lib/client.ts", "../src/lib/utils.ts", "../src/lib/server.ts", "../src/index.ts"], "names": ["EventEmitter", "response"], "mappings": ";AAIA,OAAO,mBAAmB;AAWnB,SAAS,UACZ,SACA,SAEJ;AACI,SAAO,IAAI,cAAc,SAAS,OAAO;AAC7C;;;ACZA,SAAS,oBAAoB;;;ACCtB,IAAM,kBAAN,MACP;AAAA,EACI,OAAO,OACP;AACI,WAAO,KAAK,UAAU,KAAK;AAAA,EAC/B;AAAA,EAEA,OAAO,OACP;AACI,WAAO,KAAK,MAAM,KAAK;AAAA,EAC3B;AACJ;;;ADeO,IAAM,eAAN,cAA2B,aAClC;AAAA,EACY;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EAEA;AAAA,EACA;AAAA,EAIA;AAAA,EACA;AAAA,EACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYR,YACI,kBACA,UAAU,uBACV;AAAA,IACI,cAAc;AAAA,IACd,YAAY;AAAA,IACZ,qBAAqB;AAAA,IACrB,iBAAiB;AAAA,IACjB,GAAG;AAAA,EACP,IAAI,CAAC,GACL,qBAIA,UAEJ;AACI,UAAM;AAEN,SAAK,mBAAmB;AAExB,SAAK,QAAQ,CAAC;AACd,SAAK,SAAS;AAEd,SAAK,UAAU;AACf,SAAK,cAAc;AACnB,SAAK,QAAQ;AACb,SAAK,YAAY;AACjB,SAAK,qBAAqB;AAC1B,SAAK,qBAAqB;AAC1B,SAAK,iBAAiB;AACtB,SAAK,eAAe;AACpB,SAAK,qBAAqB;AAC1B,SAAK,sBAAsB,wBAAwB,MAAM,OAAO,KAAK,WAAW,WAC1E,EAAE,KAAK,SACP,OAAO,KAAK,MAAM,IAAI;AAE5B,QAAI,CAAC,SAAU,MAAK,WAAW,IAAI,gBAAgB;AAAA,QAC9C,MAAK,WAAW;AAErB,QAAI,KAAK;AACL,WAAK,SAAS,KAAK,SAAS;AAAA,QACxB,aAAa,KAAK;AAAA,QAClB,WAAW,KAAK;AAAA,QAChB,oBAAoB,KAAK;AAAA,QACzB,gBAAgB,KAAK;AAAA,QACrB,GAAG,KAAK;AAAA,MACZ,CAAC;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,UACA;AACI,QAAI,KAAK,OAAQ;AAEjB,SAAK,SAAS,KAAK,SAAS;AAAA,MACxB,aAAa,KAAK;AAAA,MAClB,WAAW,KAAK;AAAA,MAChB,oBAAoB,KAAK;AAAA,MACzB,gBAAgB,KAAK;AAAA,MACrB,GAAG,KAAK;AAAA,IACZ,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,KACI,QACA,QACA,SACA,SAEJ;AACI,QAAI,CAAC,WAAW,aAAa,OAAO,SACpC;AACI,gBAAU;AACV,gBAAU;AAAA,IACd;AAEA,WAAO,IAAI,QAAQ,CAAC,SAAS,WAC7B;AACI,UAAI,CAAC,KAAK,MAAO,QAAO,OAAO,IAAI,MAAM,kBAAkB,CAAC;AAE5D,YAAM,SAAS,KAAK,oBAAoB,QAAQ,MAAM;AAEtD,YAAM,UAAU;AAAA,QACZ,SAAS;AAAA,QACT;AAAA,QACA,QAAQ,UAAU;AAAA,QAClB,IAAI;AAAA,MACR;AAEA,WAAK,OAAO,KAAK,KAAK,SAAS,OAAO,OAAO,GAAG,SAAS,CAAC,UAC1D;AACI,YAAI,MAAO,QAAO,OAAO,KAAK;AAE9B,aAAK,MAAM,MAAM,IAAI,EAAE,SAAS,CAAC,SAAS,MAAM,EAAE;AAElD,YAAI,SACJ;AACI,eAAK,MAAM,MAAM,EAAE,UAAU,WAAW,MACxC;AACI,mBAAO,KAAK,MAAM,MAAM;AACxB,mBAAO,IAAI,MAAM,eAAe,CAAC;AAAA,UACrC,GAAG,OAAO;AAAA,QACd;AAAA,MACJ,CAAC;AAAA,IACL,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,MAAM,MAAM,QACZ;AACI,UAAM,OAAO,MAAM,KAAK,KAAK,aAAa,MAAM;AAEhD,QAAI,CAAC,KAAM,OAAM,IAAI,MAAM,uBAAuB;AAElD,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,cACN;AACI,WAAO,MAAM,KAAK,KAAK,eAAe;AAAA,EAC1C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,OAAO,QAAgB,QACvB;AACI,WAAO,IAAI,QAAc,CAAC,SAAS,WACnC;AACI,UAAI,CAAC,KAAK,MAAO,QAAO,OAAO,IAAI,MAAM,kBAAkB,CAAC;AAE5D,YAAM,UAAU;AAAA,QACZ,SAAS;AAAA,QACT;AAAA,QACA;AAAA,MACJ;AAEA,WAAK,OAAO,KAAK,KAAK,SAAS,OAAO,OAAO,GAAG,CAAC,UACjD;AACI,YAAI,MAAO,QAAO,OAAO,KAAK;AAE9B,gBAAQ;AAAA,MACZ,CAAC;AAAA,IACL,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,MAAM,UAAU,OAChB;AACI,QAAI,OAAO,UAAU,SAAU,SAAQ,CAAC,KAAK;AAE7C,UAAM,SAAS,MAAM,KAAK,KAAK,UAAU,KAAK;AAE9C,QAAI,OAAO,UAAU,YAAY,OAAO,KAAK,MAAM;AAC/C,YAAM,IAAI;AAAA,QACN,qCAAqC,QAAQ,aAAa,OAAO,KAAK;AAAA,MAC1E;AAEJ,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,MAAM,YAAY,OAClB;AACI,QAAI,OAAO,UAAU,SAAU,SAAQ,CAAC,KAAK;AAE7C,UAAM,SAAS,MAAM,KAAK,KAAK,WAAW,KAAK;AAE/C,QAAI,OAAO,UAAU,YAAY,OAAO,KAAK,MAAM;AAC/C,YAAM,IAAI,MAAM,8CAA8C,MAAM;AAExE,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,MAAM,MAAe,MACrB;AACI,SAAK,OAAO,MAAM,QAAQ,KAAM,IAAI;AAAA,EACxC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,iBAAiB,WACjB;AACI,SAAK,YAAY;AAAA,EACrB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,qBAAqB,UACrB;AACI,SAAK,qBAAqB;AAAA,EAC9B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,iBAAiB,gBACjB;AACI,SAAK,iBAAiB;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUQ,SACJ,SACA,SAEJ;AACI,iBAAa,KAAK,kBAAkB;AACpC,SAAK,SAAS,KAAK,iBAAiB,SAAS,OAAO;AAEpD,SAAK,OAAO,iBAAiB,QAAQ,MACrC;AACI,WAAK,QAAQ;AACb,WAAK,KAAK,MAAM;AAChB,WAAK,qBAAqB;AAAA,IAC9B,CAAC;AAED,SAAK,OAAO,iBAAiB,WAAW,CAAC,EAAE,MAAM,QAAQ,MACzD;AACI,UAAI,mBAAmB;AACnB,kBAAU,OAAO,KAAK,OAAO,EAAE,SAAS;AAE5C,UACA;AACI,kBAAU,KAAK,SAAS,OAAO,OAAO;AAAA,MAC1C,SACO,OACP;AACI;AAAA,MACJ;AAGA,UAAI,QAAQ,gBAAgB,KAAK,UAAU,QAAQ,YAAY,EAAE,QACjE;AACI,YAAI,CAAC,OAAO,KAAK,QAAQ,MAAM,EAAE;AAC7B,iBAAO,KAAK,KAAK,QAAQ,YAAY;AAEzC,cAAM,OAAO,CAAC,QAAQ,YAAY;AAElC,YAAI,QAAQ,OAAO,gBAAgB,OAAQ,MAAK,KAAK,QAAQ,MAAM;AAAA;AAG/D,mBAAS,IAAI,GAAG,IAAI,QAAQ,OAAO,QAAQ;AACvC,iBAAK,KAAK,QAAQ,OAAO,CAAC,CAAC;AAInC,eAAO,QAAQ,QAAQ,EAAE,KAAK,MAC9B;AAEI,eAAK,KAAK,MAAM,MAAM,IAAI;AAAA,QAC9B,CAAC;AAAA,MACL;AAEA,UAAI,CAAC,KAAK,MAAM,QAAQ,EAAE,GAC1B;AAEI,YAAI,QAAQ,QACZ;AAEI,iBAAO,QAAQ,QAAQ,EAAE,KAAK,MAC9B;AACI,iBAAK,KAAK,QAAQ,QAAQ,SAAS,MAAM;AAAA,UAC7C,CAAC;AAAA,QACL;AAEA;AAAA,MACJ;AAGA,UAAI,WAAW,YAAY,YAAY;AACnC,aAAK,MAAM,QAAQ,EAAE,EAAE,QAAQ,CAAC;AAAA,UAC5B,IAAI;AAAA,YACA;AAAA,UAEJ;AAAA,QACJ;AAEJ,UAAI,KAAK,MAAM,QAAQ,EAAE,EAAE;AACvB,qBAAa,KAAK,MAAM,QAAQ,EAAE,EAAE,OAAO;AAE/C,UAAI,QAAQ,MAAO,MAAK,MAAM,QAAQ,EAAE,EAAE,QAAQ,CAAC,EAAE,QAAQ,KAAK;AAAA,UAC7D,MAAK,MAAM,QAAQ,EAAE,EAAE,QAAQ,CAAC,EAAE,QAAQ,MAAM;AAErD,aAAO,KAAK,MAAM,QAAQ,EAAE;AAAA,IAChC,CAAC;AAED,SAAK,OAAO,iBAAiB,SAAS,CAAC,UAAU,KAAK,KAAK,SAAS,KAAK,CAAC;AAE1E,SAAK,OAAO,iBAAiB,SAAS,CAAC,EAAE,MAAM,OAAO,MACtD;AACI,UAAI,KAAK;AAEL,mBAAW,MAAM,KAAK,KAAK,SAAS,MAAM,MAAM,GAAG,CAAC;AAExD,WAAK,QAAQ;AACb,WAAK,SAAS;AAEd,UAAI,SAAS,IAAM;AAEnB,WAAK;AAEL,UACI,KAAK,cACZ,KAAK,iBAAiB,KAAK,sBAC1B,KAAK,mBAAmB;AAElB,aAAK,qBAAqB;AAAA,UACtB,MAAM,KAAK,SAAS,SAAS,OAAO;AAAA,UACpC,KAAK;AAAA,QACT;AAAA,IACR,CAAC;AAAA,EACL;AACJ;;;AExbA,SAAS,gBAAAA,qBAAoB;AAC7B,OAAO,SAAS;AAChB,SAAS,MAAM,cAAc;AAC7B,SAAwB,uBAAuB;AAuDxC,IAAM,SAAN,cAAqBA,cAC5B;AAAA,EACY;AAAA,EACA;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,YACI,SACA,UAEJ;AACI,UAAM;AAaN,SAAK,aAAa,CAAC;AAEnB,QAAI,CAAC,SAAU,MAAK,WAAW,IAAI,gBAAgB;AAAA,QAC9C,MAAK,WAAW;AAErB,SAAK,MAAM,IAAI,gBAAgB,OAAO;AAEtC,SAAK,IAAI,GAAG,aAAa,MAAM,KAAK,KAAK,WAAW,CAAC;AAErD,SAAK,IAAI,GAAG,cAAc,CAAC,QAA0B,YACrD;AACI,YAAM,IAAI,IAAI,MAAM,QAAQ,KAAK,IAAI;AACrC,YAAM,KAAK,EAAE;AAEb,UAAI,EAAE,MAAM,UAAW,QAAO,MAAM,EAAE,MAAM;AAAA,UACvC,QAAO,MAAM,OAAO;AAGzB,aAAO,gBAAgB,IAAI;AAG3B,aAAO,GAAG,SAAS,CAAC,UAAU,KAAK,KAAK,gBAAgB,QAAQ,KAAK,CAAC;AAGtE,aAAO,GAAG,SAAS,MACnB;AACI,aAAK,WAAW,EAAE,EAAE,QAAQ,OAAO,OAAO,GAAG;AAE7C,mBAAW,SAAS,OAAO,KAAK,KAAK,WAAW,EAAE,EAAE,MAAM,GAC1D;AACI,gBAAM,QAAQ,KAAK,WAAW,EAAE,EAAE,OAAO,KAAK,EAAE,QAAQ;AAAA,YACpD,OAAO;AAAA,UACX;AAEA,cAAI,SAAS;AACT,iBAAK,WAAW,EAAE,EAAE,OAAO,KAAK,EAAE,QAAQ,OAAO,OAAO,CAAC;AAAA,QACjE;AAEA,aAAK,KAAK,iBAAiB,MAAM;AAAA,MACrC,CAAC;AAED,UAAI,CAAC,KAAK,WAAW,EAAE,EAAG,MAAK,mBAAmB,EAAE;AAGpD,WAAK,WAAW,EAAE,EAAE,QAAQ,IAAI,OAAO,KAAK,MAAM;AAElD,WAAK,KAAK,cAAc,QAAQ,OAAO;AAEvC,aAAO,KAAK,WAAW,QAAQ,EAAE;AAAA,IACrC,CAAC;AAED,SAAK,IAAI,GAAG,SAAS,CAAC,UAAU,KAAK,KAAK,SAAS,KAAK,CAAC;AAAA,EAC7D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,SACI,MACA,IACA,KAAK,KAET;AACI,QAAI,CAAC,KAAK,WAAW,EAAE,EAAG,MAAK,mBAAmB,EAAE;AAEpD,SAAK,WAAW,EAAE,EAAE,YAAY,IAAI,IAAI;AAAA,MACpC;AAAA,MACA,WAAW;AAAA,IACf;AAEA,WAAO;AAAA,MACH,WAAW,MAAM,KAAK,qBAAqB,MAAM,EAAE;AAAA,MACnD,QAAQ,MAAM,KAAK,kBAAkB,MAAM,EAAE;AAAA,IACjD;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,QACI,IACA,KAAK,KAET;AACI,SAAK,SAAS,aAAa,IAAI,EAAE;AAAA,EACrC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASQ,qBAAqB,MAAc,KAAK,KAChD;AACI,SAAK,WAAW,EAAE,EAAE,YAAY,IAAI,EAAE,YAAY;AAAA,EACtD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASQ,kBAAkB,MAAc,KAAK,KAC7C;AACI,SAAK,WAAW,EAAE,EAAE,YAAY,IAAI,EAAE,YAAY;AAAA,EACtD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASQ,oBAAoB,MAAc,KAAK,KAC/C;AACI,SAAK,WAAW,EAAE,EAAE,OAAO,IAAI,EAAE,YAAY;AAAA,EACjD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASQ,iBAAiB,MAAc,KAAK,KAC5C;AACI,SAAK,WAAW,EAAE,EAAE,OAAO,IAAI,EAAE,YAAY;AAAA,EACjD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,eAAe,IACf;AACI,UAAM,YAAY,KAAK,WAAW,EAAE;AAEpC,QAAI,WACJ;AACI,aAAO,UAAU;AACjB,aAAO,UAAU;AAEjB,iBAAW,UAAU,UAAU,QAAQ,OAAO,EAAG,QAAO,MAAM;AAE9D,aAAO,KAAK,WAAW,EAAE;AAAA,IAC7B;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,MAAM,MAAc,KAAK,KACzB;AACI,QAAI,CAAC,KAAK,WAAW,EAAE,EAAG,MAAK,mBAAmB,EAAE;AAAA,SAEpD;AACI,YAAM,QAAQ,KAAK,WAAW,EAAE,EAAE,OAAO,IAAI;AAE7C,UAAI,UAAU;AACV,cAAM,IAAI,MAAM,4BAA4B,EAAE,GAAG,IAAI,EAAE;AAAA,IAC/D;AAEA,SAAK,WAAW,EAAE,EAAE,OAAO,IAAI,IAAI;AAAA,MAC/B,SAAS,CAAC;AAAA,MACV,WAAW;AAAA,IACf;AAGA,SAAK,GAAG,MAAM,IAAI,WAClB;AAEI,UAAI,OAAO,WAAW,KAAK,OAAO,CAAC,aAAa;AAC5C,iBAAS,OAAO,CAAC;AAErB,iBAAW,aAAa,KAAK,WAAW,EAAE,EAAE,OAAO,IAAI,EAAE,SACzD;AACI,cAAM,SAAS,KAAK,WAAW,EAAE,EAAE,QAAQ,IAAI,SAAS;AAExD,YAAI,CAAC,OAAQ;AAEb,eAAO;AAAA,UACH,KAAK,SAAS,OAAO;AAAA,YACjB,cAAc;AAAA,YACd;AAAA,UACJ,CAAC;AAAA,QACL;AAAA,MACJ;AAAA,IACJ,CAAC;AAED,WAAO;AAAA,MACH,WAAW,MAAM,KAAK,oBAAoB,MAAM,EAAE;AAAA,MAClD,QAAQ,MAAM,KAAK,iBAAiB,MAAM,EAAE;AAAA,IAChD;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,GAAG,MACH;AACI,QAAI,CAAC,KAAK,WAAW,IAAI,EAAG,MAAK,mBAAmB,IAAI;AAExD,UAAM,OAAO;AAEb,WAAO;AAAA;AAAA,MAEH,SACI,SACA,IAEJ;AACI,YAAI,UAAU,WAAW;AACrB,gBAAM,IAAI,MAAM,oCAAoC;AAExD,YAAI,OAAO,YAAY;AACnB,gBAAM,IAAI,MAAM,uBAAuB;AAE3C,YAAI,OAAO,OAAO;AACd,gBAAM,IAAI,MAAM,4BAA4B;AAEhD,eAAO,KAAK,SAAS,SAAS,IAAI,IAAI;AAAA,MAC1C;AAAA;AAAA,MAGA,MAAM,SACN;AACI,YAAI,UAAU,WAAW;AACrB,gBAAM,IAAI,MAAM,mCAAmC;AAEvD,YAAI,OAAO,YAAY;AACnB,gBAAM,IAAI,MAAM,uBAAuB;AAE3C,eAAO,KAAK,MAAM,SAAS,IAAI;AAAA,MACnC;AAAA;AAAA,MAGA,IAAI,YACJ;AACI,eAAO,OAAO,KAAK,KAAK,WAAW,IAAI,EAAE,MAAM;AAAA,MACnD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAUA,KAAK,UAAkB,QACvB;AACI,cAAM,aAAa,CAAC,GAAG,KAAK,WAAW,IAAI,EAAE,QAAQ,KAAK,CAAC;AAE3D,iBAAS,IAAI,GAAG,IAAK,KAAK,WAAW,CAAC,GAAI,EAAE,GAC5C;AACI,eAAK,WAAW,IAAI,EAAE,QAAQ,IAAI,EAAE,EAAE;AAAA,YAClC,KAAK,SAAS,OAAO;AAAA,cACjB,cAAc;AAAA,cACd,QAAQ,UAAU,CAAC;AAAA,YACvB,CAAC;AAAA,UACL;AAAA,QACJ;AAAA,MACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MASA,IAAI,OACJ;AACI,eAAO;AAAA,MACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAQA,YACA;AACI,cAAM,aAAa,CAAC,GAAG,KAAK,WAAW,IAAI,EAAE,QAAQ,KAAK,CAAC;AAE3D,eAAO,WAAW;AAAA,UACd,CAAC,KAAK,UAAU;AAAA,YACZ,GAAG;AAAA,YACH,CAAC,IAAI,GAAG,KAAK,WAAW,IAAI,EAAE,QAAQ,IAAI,IAAI;AAAA,UAClD;AAAA,UACA,CAAC;AAAA,QACL;AAAA,MACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAQA,UACA;AACI,eAAO,KAAK,WAAW,IAAI;AAAA,MAC/B;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,UAAU,KAAK,KACf;AACI,QAAI,CAAC,KAAK,WAAW,EAAE,EAAG,QAAO,CAAC;AAElC,WAAO,OAAO,KAAK,KAAK,WAAW,EAAE,EAAE,MAAM;AAAA,EACjD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,YAAY,MAAc,SAAiB,MAC3C;AACI,WAAO;AAAA,MACH;AAAA,MACA;AAAA,MACA,MAAM,QAAQ;AAAA,IAClB;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,QACA;AACI,WAAO,IAAI,QAAc,CAAC,SAAS,WACnC;AACI,UACA;AACI,aAAK,IAAI,MAAM;AACf,aAAK,KAAK,OAAO;AACjB,gBAAQ;AAAA,MACZ,SACO,OACP;AACI,eAAO,KAAK;AAAA,MAChB;AAAA,IACJ,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASQ,WAAW,QAA0B,KAAK,KAClD;AACI,WAAO,GAAG,WAAW,OAAO,SAC5B;AACI,YAAM,cAAoD,CAAC;AAE3D,UAAI,gBAAgB,aACpB;AACI,oBAAY,SAAS;AAErB,eAAO,OAAO,KAAK,IAAI,EAAE,SAAS;AAAA,MACtC;AAEA,UAAI,OAAO,eAAe,EAAG;AAE7B,UAAI;AAEJ,UACA;AACI,qBAAa,KAAK,SAAS,OAAO,IAAc;AAAA,MACpD,SACO,OACP;AACI,eAAO,OAAO;AAAA,UACV,KAAK,SAAS,OAAO;AAAA,YACjB,SAAS;AAAA,YACT,OAAO,YAAY,QAAQ,MAAM,SAAS,CAAC;AAAA,YAC3C,IAAI;AAAA,UACR,CAAC;AAAA,UACD;AAAA,QACJ;AAAA,MACJ;AAEA,UAAI,MAAM,QAAQ,UAAU,GAC5B;AACI,YAAI,CAAC,WAAW;AACZ,iBAAO,OAAO;AAAA,YACV,KAAK,SAAS,OAAO;AAAA,cACjB,SAAS;AAAA,cACT,OAAO,YAAY,QAAQ,eAAe;AAAA,cAC1C,IAAI;AAAA,YACR,CAAC;AAAA,YACD;AAAA,UACJ;AAEJ,cAAM,YAAY,CAAC;AAEnB,mBAAW,WAAW,YACtB;AACI,gBAAMC,YAAW,MAAM,KAAK,WAAW,SAAS,OAAO,KAAK,EAAE;AAE9D,cAAI,CAACA,UAAU;AAEf,oBAAU,KAAKA,SAAQ;AAAA,QAC3B;AAEA,YAAI,CAAC,UAAU,OAAQ;AAEvB,eAAO,OAAO,KAAK,KAAK,SAAS,OAAO,SAAS,GAAG,WAAW;AAAA,MACnE;AAEA,YAAM,WAAW,MAAM,KAAK,WAAW,YAAY,OAAO,KAAK,EAAE;AAEjE,UAAI,CAAC,SAAU;AAEf,aAAO,OAAO,KAAK,KAAK,SAAS,OAAO,QAAQ,GAAG,WAAW;AAAA,IAClE,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,MAAc,WAAW,SAAc,WAAmB,KAAK,KAC/D;AACI,QAAI,OAAO,YAAY,YAAY,YAAY;AAC3C,aAAO;AAAA,QACH,SAAS;AAAA,QACT,OAAO,YAAY,MAAM;AAAA,QACzB,IAAI;AAAA,MACR;AAEJ,QAAI,QAAQ,YAAY;AACpB,aAAO;AAAA,QACH,SAAS;AAAA,QACT,OAAO,YAAY,QAAQ,0BAA0B;AAAA,QACrD,IAAI,QAAQ,MAAM;AAAA,MACtB;AAEJ,QAAI,CAAC,QAAQ;AACT,aAAO;AAAA,QACH,SAAS;AAAA,QACT,OAAO,YAAY,QAAQ,sBAAsB;AAAA,QACjD,IAAI,QAAQ,MAAM;AAAA,MACtB;AAEJ,QAAI,OAAO,QAAQ,WAAW;AAC1B,aAAO;AAAA,QACH,SAAS;AAAA,QACT,OAAO,YAAY,QAAQ,qBAAqB;AAAA,QAChD,IAAI,QAAQ,MAAM;AAAA,MACtB;AAEJ,QAAI,QAAQ,UAAU,OAAO,QAAQ,WAAW;AAC5C,aAAO;AAAA,QACH,SAAS;AAAA,QACT,OAAO,YAAY,MAAM;AAAA,QACzB,IAAI,QAAQ,MAAM;AAAA,MACtB;AAEJ,QAAI,QAAQ,WAAW,UACvB;AACI,UAAI,CAAC,QAAQ;AACT,eAAO;AAAA,UACH,SAAS;AAAA,UACT,OAAO,YAAY,KAAM;AAAA,UACzB,IAAI,QAAQ,MAAM;AAAA,QACtB;AAEJ,YAAM,UAA4B,CAAC;AAEnC,YAAM,cAAc,OAAO,KAAK,KAAK,WAAW,EAAE,EAAE,MAAM;AAE1D,iBAAW,QAAQ,QAAQ,QAC3B;AACI,cAAM,QAAQ,YAAY,QAAQ,IAAI;AACtC,cAAM,YAAY,KAAK,WAAW,EAAE;AAEpC,YAAI,UAAU,IACd;AACI,kBAAQ,IAAI,IAAI;AAChB;AAAA,QACJ;AAGA,YACI,UAAU,OAAO,YAAY,KAAK,CAAC,EAAE,cAAc,QAC7D,UAAU,QAAQ,IAAI,SAAS,EAAE,gBAAgB,MAAM,OAEjD;AACI,iBAAO;AAAA,YACH,SAAS;AAAA,YACT,OAAO,YAAY,MAAM;AAAA,YACzB,IAAI,QAAQ,MAAM;AAAA,UACtB;AAAA,QACJ;AAEA,cAAM,eACZ,UAAU,OAAO,YAAY,KAAK,CAAC,EAAE,QAAQ,QAAQ,SAAS;AACxD,YAAI,gBAAgB,GACpB;AACI,kBAAQ,IAAI,IAAI;AAChB;AAAA,QACJ;AACA,kBAAU,OAAO,YAAY,KAAK,CAAC,EAAE,QAAQ,KAAK,SAAS;AAE3D,gBAAQ,IAAI,IAAI;AAAA,MACpB;AAEA,aAAO;AAAA,QACH,SAAS;AAAA,QACT,QAAQ;AAAA,QACR,IAAI,QAAQ,MAAM;AAAA,MACtB;AAAA,IACJ,WACS,QAAQ,WAAW,WAC5B;AACI,UAAI,CAAC,QAAQ;AACT,eAAO;AAAA,UACH,SAAS;AAAA,UACT,OAAO,YAAY,KAAM;AAAA,UACzB,IAAI,QAAQ,MAAM;AAAA,QACtB;AAEJ,YAAM,UAAsB,CAAC;AAE7B,iBAAW,QAAQ,QAAQ,QAC3B;AACI,YAAI,CAAC,KAAK,WAAW,EAAE,EAAE,OAAO,IAAI,GACpC;AACI,kBAAQ,IAAI,IAAI;AAChB;AAAA,QACJ;AAEA,cAAM,QACZ,KAAK,WAAW,EAAE,EAAE,OAAO,IAAI,EAAE,QAAQ,QAAQ,SAAS;AAEpD,YAAI,UAAU,IACd;AACI,kBAAQ,IAAI,IAAI;AAChB;AAAA,QACJ;AAEA,aAAK,WAAW,EAAE,EAAE,OAAO,IAAI,EAAE,QAAQ,OAAO,OAAO,CAAC;AACxD,gBAAQ,IAAI,IAAI;AAAA,MACpB;AAEA,aAAO;AAAA,QACH,SAAS;AAAA,QACT,QAAQ;AAAA,QACR,IAAI,QAAQ,MAAM;AAAA,MACtB;AAAA,IACJ,WACS,QAAQ,WAAW,aAC5B;AACI,UAAI,CAAC,QAAQ;AACT,eAAO;AAAA,UACH,SAAS;AAAA,UACT,OAAO,YAAY,MAAM;AAAA,UACzB,IAAI,QAAQ,MAAM;AAAA,QACtB;AAAA,IACR;AAEA,QAAI,CAAC,KAAK,WAAW,EAAE,EAAE,YAAY,QAAQ,MAAM,GACnD;AACI,aAAO;AAAA,QACH,SAAS;AAAA,QACT,OAAO,YAAY,MAAM;AAAA,QACzB,IAAI,QAAQ,MAAM;AAAA,MACtB;AAAA,IACJ;AAEA,QAAI,WAAW;AAGf,QACI,KAAK,WAAW,EAAE,EAAE,YAAY,QAAQ,MAAM,EAAE,cAAc,QACpE,KAAK,WAAW,EAAE,EAAE,QAAQ,IAAI,SAAS,EAAE,gBAAgB,MAAM,OAE/D;AACI,aAAO;AAAA,QACH,SAAS;AAAA,QACT,OAAO,YAAY,MAAM;AAAA,QACzB,IAAI,QAAQ,MAAM;AAAA,MACtB;AAAA,IACJ;AAEA,QACA;AACI,iBAAW,MAAM,KAAK,WAAW,EAAE,EAAE,YAAY,QAAQ,MAAM,EAAE;AAAA,QAC7D,QAAQ;AAAA,QACR;AAAA,MACJ;AAAA,IACJ,SACO,OACP;AACI,UAAI,CAAC,QAAQ,GAAI;AAEjB,UAAI,iBAAiB;AACjB,eAAO;AAAA,UACH,SAAS;AAAA,UACT,OAAO;AAAA,YACH,MAAM;AAAA,YACN,SAAS,MAAM;AAAA,YACf,MAAM,MAAM;AAAA,UAChB;AAAA,UACA,IAAI,QAAQ;AAAA,QAChB;AAEJ,aAAO;AAAA,QACH,SAAS;AAAA,QACT;AAAA,QACA,IAAI,QAAQ;AAAA,MAChB;AAAA,IACJ;AAGA,QAAI,CAAC,QAAQ,GAAI;AAGjB,QAAI,QAAQ,WAAW,eAAe,aAAa,MACnD;AACI,YAAM,IAAI,KAAK,WAAW,EAAE,EAAE,QAAQ,IAAI,SAAS;AACnD,QAAE,gBAAgB,IAAI;AACtB,WAAK,WAAW,EAAE,EAAE,QAAQ,IAAI,WAAW,CAAC;AAAA,IAChD;AAEA,WAAO;AAAA,MACH,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,IAAI,QAAQ;AAAA,IAChB;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASQ,mBAAmB,MAC3B;AACI,SAAK,WAAW,IAAI,IAAI;AAAA,MACpB,aAAa;AAAA,QACT,eAAe;AAAA,UACX,IAAI,MAAM,OAAO,KAAK,KAAK,WAAW,IAAI,EAAE,WAAW;AAAA,UACvD,WAAW;AAAA,QACf;AAAA,MACJ;AAAA,MACA,SAAS,oBAAI,IAAI;AAAA,MACjB,QAAQ,CAAC;AAAA,IACb;AAAA,EACJ;AACJ;AAEA,IAAM,aAAa,oBAAI,IAAI;AAAA,EACvB,CAAC,OAAQ,oBAAoB;AAAA,EAC7B,CAAC,QAAQ,iBAAiB;AAAA,EAC1B,CAAC,QAAQ,kBAAkB;AAAA,EAC3B,CAAC,QAAQ,gBAAgB;AAAA,EACzB,CAAC,QAAQ,gBAAgB;AAAA,EACzB,CAAC,QAAQ,kBAAkB;AAAA,EAC3B,CAAC,QAAQ,kBAAkB;AAAA,EAC3B,CAAC,QAAQ,iBAAiB;AAAA,EAC1B,CAAC,QAAQ,aAAa;AAC1B,CAAC;AAQM,SAAS,YAAY,MAAc,SAC1C;AACI,QAAM,QAAmB;AAAA,IACrB;AAAA,IACA,SAAS,WAAW,IAAI,IAAI,KAAK;AAAA,EACrC;AAEA,MAAI,QAAS,OAAM,MAAM,IAAI;AAE7B,SAAO;AACX;;;ACrzBO,IAAM,SAAN,cAAqB,aAC5B;AAAA,EACI,YACI,UAAU,uBACV;AAAA,IACI,cAAc;AAAA,IACd,YAAY;AAAA,IACZ,qBAAqB;AAAA,IACrB,iBAAiB;AAAA,IACjB,GAAG;AAAA,EACP,IAA2D,CAAC,GAC5D,qBAKJ;AACI;AAAA,MACF;AAAA,MACA;AAAA,MACA;AAAA,QACI;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,GAAG;AAAA,MACP;AAAA,MACA;AAAA,IACE;AAAA,EACJ;AACJ", "sourcesContent": ["/* A wrapper for the \"qaap/uws-bindings\" library. */\n\n\"use strict\"\n\nimport WebSocketImpl from \"ws\"\n\nimport { IWSClientAdditionalOptions } from \"./client.types.js\"\n\n/**\n * factory method for common WebSocket instance\n * @method\n * @param {String} address - url to a websocket server\n * @param {(Object)} options - websocket options\n * @return {Undefined}\n */\nexport function WebSocket(\n    address: string,\n    options: IWSClientAdditionalOptions & WebSocketImpl.ClientOptions\n)\n{\n    return new WebSocketImpl(address, options)\n}\n", "/**\n * \"Client\" wraps \"ws\" or a browser-implemented \"WebSocket\" library\n * according to the environment providing JSON RPC 2.0 support on top.\n * @module Client\n */\n\n\"use strict\"\n\nimport NodeWebSocket from \"ws\"\nimport { EventEmitter } from \"eventemitter3\"\nimport {\n    ICommonWebSocket,\n    IWSClientAdditionalOptions,\n    NodeWebSocketType,\n    ICommonWebSocketFactory,\n} from \"./client/client.types.js\"\n\nimport { <PERSON>Pack, DefaultDataPack } from \"./utils.js\"\n\ninterface IQueueElement {\n  promise: [\n    Parameters<ConstructorParameters<typeof Promise>[0]>[0],\n    Parameters<ConstructorParameters<typeof Promise>[0]>[1]\n  ];\n  timeout?: ReturnType<typeof setTimeout>;\n}\n\nexport interface IQueue {\n  [x: number | string]: IQueueElement;\n}\n\nexport interface IWSRequestParams {\n  [x: string]: any;\n  [x: number]: any;\n}\n\nexport class CommonClient extends EventEmitter\n{\n    private address: string\n    private rpc_id: number | string\n    private queue: IQueue\n    private options: IWSClientAdditionalOptions & NodeWebSocket.ClientOptions\n    private autoconnect: boolean\n    private ready: boolean\n    private reconnect: boolean\n    private reconnect_timer_id: NodeJS.Timeout\n    private reconnect_interval: number\n    private max_reconnects: number\n    private rest_options: IWSClientAdditionalOptions &\n    NodeWebSocket.ClientOptions\n    private current_reconnects: number\n    private generate_request_id: (\n    method: string,\n    params: object | Array<any>\n  ) => number | string\n    private socket: ICommonWebSocket\n    private webSocketFactory: ICommonWebSocketFactory\n    private dataPack: DataPack<object, string>\n\n    /**\n   * Instantiate a Client class.\n   * @constructor\n   * @param {webSocketFactory} webSocketFactory - factory method for WebSocket\n   * @param {String} address - url to a websocket server\n   * @param {Object} options - ws options object with reconnect parameters\n   * @param {Function} generate_request_id - custom generation request Id\n   * @param {DataPack} dataPack - data pack contains encoder and decoder\n   * @return {CommonClient}\n   */\n    constructor(\n        webSocketFactory: ICommonWebSocketFactory,\n        address = \"ws://localhost:8080\",\n        {\n            autoconnect = true,\n            reconnect = true,\n            reconnect_interval = 1000,\n            max_reconnects = 5,\n            ...rest_options\n        } = {},\n        generate_request_id?: (\n      method: string,\n      params: object | Array<any>\n    ) => number | string,\n        dataPack?: DataPack<object, string>\n    )\n    {\n        super()\n\n        this.webSocketFactory = webSocketFactory\n\n        this.queue = {}\n        this.rpc_id = 0\n\n        this.address = address\n        this.autoconnect = autoconnect\n        this.ready = false\n        this.reconnect = reconnect\n        this.reconnect_timer_id = undefined\n        this.reconnect_interval = reconnect_interval\n        this.max_reconnects = max_reconnects\n        this.rest_options = rest_options\n        this.current_reconnects = 0\n        this.generate_request_id = generate_request_id || (() => typeof this.rpc_id === \"number\"\n            ? ++this.rpc_id\n            : Number(this.rpc_id) + 1)\n\n        if (!dataPack) this.dataPack = new DefaultDataPack()\n        else this.dataPack = dataPack\n\n        if (this.autoconnect)\n            this._connect(this.address, {\n                autoconnect: this.autoconnect,\n                reconnect: this.reconnect,\n                reconnect_interval: this.reconnect_interval,\n                max_reconnects: this.max_reconnects,\n                ...this.rest_options,\n            })\n    }\n\n    /**\n   * Connects to a defined server if not connected already.\n   * @method\n   * @return {Undefined}\n   */\n    connect()\n    {\n        if (this.socket) return\n\n        this._connect(this.address, {\n            autoconnect: this.autoconnect,\n            reconnect: this.reconnect,\n            reconnect_interval: this.reconnect_interval,\n            max_reconnects: this.max_reconnects,\n            ...this.rest_options,\n        })\n    }\n\n    /**\n   * Calls a registered RPC method on server.\n   * @method\n   * @param {String} method - RPC method name\n   * @param {Object|Array} params - optional method parameters\n   * @param {Number} timeout - RPC reply timeout value\n   * @param {Object} ws_opts - options passed to ws\n   * @return {Promise}\n   */\n    call(\n        method: string,\n        params?: IWSRequestParams,\n        timeout?: number,\n        ws_opts?: Parameters<NodeWebSocketType[\"send\"]>[1]\n    )\n    {\n        if (!ws_opts && \"object\" === typeof timeout)\n        {\n            ws_opts = timeout\n            timeout = null\n        }\n\n        return new Promise((resolve, reject) =>\n        {\n            if (!this.ready) return reject(new Error(\"socket not ready\"))\n\n            const rpc_id = this.generate_request_id(method, params)\n\n            const message = {\n                jsonrpc: \"2.0\",\n                method: method,\n                params: params || undefined,\n                id: rpc_id,\n            }\n\n            this.socket.send(this.dataPack.encode(message), ws_opts, (error) =>\n            {\n                if (error) return reject(error)\n\n                this.queue[rpc_id] = { promise: [resolve, reject] }\n\n                if (timeout)\n                {\n                    this.queue[rpc_id].timeout = setTimeout(() =>\n                    {\n                        delete this.queue[rpc_id]\n                        reject(new Error(\"reply timeout\"))\n                    }, timeout)\n                }\n            })\n        })\n    }\n\n    /**\n   * Logins with the other side of the connection.\n   * @method\n   * @param {Object} params - Login credentials object\n   * @return {Promise}\n   */\n    async login(params: IWSRequestParams)\n    {\n        const resp = await this.call(\"rpc.login\", params)\n\n        if (!resp) throw new Error(\"authentication failed\")\n\n        return resp\n    }\n\n    /**\n   * Fetches a list of client's methods registered on server.\n   * @method\n   * @return {Array}\n   */\n    async listMethods()\n    {\n        return await this.call(\"__listMethods\")\n    }\n\n    /**\n   * Sends a JSON-RPC 2.0 notification to server.\n   * @method\n   * @param {String} method - RPC method name\n   * @param {Object} params - optional method parameters\n   * @return {Promise}\n   */\n    notify(method: string, params?: IWSRequestParams)\n    {\n        return new Promise<void>((resolve, reject) =>\n        {\n            if (!this.ready) return reject(new Error(\"socket not ready\"))\n\n            const message = {\n                jsonrpc: \"2.0\",\n                method: method,\n                params,\n            }\n\n            this.socket.send(this.dataPack.encode(message), (error) =>\n            {\n                if (error) return reject(error)\n\n                resolve()\n            })\n        })\n    }\n\n    /**\n   * Subscribes for a defined event.\n   * @method\n   * @param {String|Array} event - event name\n   * @return {Undefined}\n   * @throws {Error}\n   */\n    async subscribe(event: string | Array<string>)\n    {\n        if (typeof event === \"string\") event = [event]\n\n        const result = await this.call(\"rpc.on\", event)\n\n        if (typeof event === \"string\" && result[event] !== \"ok\")\n            throw new Error(\n                \"Failed subscribing to an event '\" + event + \"' with: \" + result[event]\n            )\n\n        return result\n    }\n\n    /**\n   * Unsubscribes from a defined event.\n   * @method\n   * @param {String|Array} event - event name\n   * @return {Undefined}\n   * @throws {Error}\n   */\n    async unsubscribe(event: string | Array<string>)\n    {\n        if (typeof event === \"string\") event = [event]\n\n        const result = await this.call(\"rpc.off\", event)\n\n        if (typeof event === \"string\" && result[event] !== \"ok\")\n            throw new Error(\"Failed unsubscribing from an event with: \" + result)\n\n        return result\n    }\n\n    /**\n   * Closes a WebSocket connection gracefully.\n   * @method\n   * @param {Number} code - socket close code\n   * @param {String} data - optional data to be sent before closing\n   * @return {Undefined}\n   */\n    close(code?: number, data?: string)\n    {\n        this.socket.close(code || 1000, data)\n    }\n\n    /**\n   * Enable / disable automatic reconnection.\n   * @method\n   * @param {Boolean} reconnect - enable / disable reconnection\n   * @return {Undefined}\n   */\n    setAutoReconnect(reconnect: boolean)\n    {\n        this.reconnect = reconnect\n    }\n\n    /**\n   * Set the interval between reconnection attempts.\n   * @method\n   * @param {Number} interval - reconnection interval in milliseconds\n   * @return {Undefined}\n   */\n    setReconnectInterval(interval: number)\n    {\n        this.reconnect_interval = interval\n    }\n\n    /**\n   * Set the maximum number of reconnection attempts.\n   * @method\n   * @param {Number} max_reconnects - maximum reconnection attempts\n   * @return {Undefined}\n   */\n    setMaxReconnects(max_reconnects: number)\n    {\n        this.max_reconnects = max_reconnects\n    }\n\n    /**\n   * Connection/Message handler.\n   * @method\n   * @private\n   * @param {String} address - WebSocket API address\n   * @param {Object} options - ws options object\n   * @return {Undefined}\n   */\n    private _connect(\n        address: string,\n        options: IWSClientAdditionalOptions & NodeWebSocket.ClientOptions\n    )\n    {\n        clearTimeout(this.reconnect_timer_id)\n        this.socket = this.webSocketFactory(address, options)\n\n        this.socket.addEventListener(\"open\", () =>\n        {\n            this.ready = true\n            this.emit(\"open\")\n            this.current_reconnects = 0\n        })\n\n        this.socket.addEventListener(\"message\", ({ data: message }) =>\n        {\n            if (message instanceof ArrayBuffer)\n                message = Buffer.from(message).toString()\n\n            try\n            {\n                message = this.dataPack.decode(message)\n            }\n            catch (error)\n            {\n                return\n            }\n\n            // check if any listeners are attached and forward event\n            if (message.notification && this.listeners(message.notification).length)\n            {\n                if (!Object.keys(message.params).length)\n                    return this.emit(message.notification)\n\n                const args = [message.notification]\n\n                if (message.params.constructor === Object) args.push(message.params)\n                // using for-loop instead of unshift/spread because performance is better\n                else\n                    for (let i = 0; i < message.params.length; i++)\n                        args.push(message.params[i])\n\n                // run as microtask so that pending queue messages are resolved first\n                // eslint-disable-next-line prefer-spread\n                return Promise.resolve().then(() =>\n                {\n                    // eslint-disable-next-line prefer-spread\n                    this.emit.apply(this, args)\n                })\n            }\n\n            if (!this.queue[message.id])\n            {\n                // general JSON RPC 2.0 events\n                if (message.method)\n                {\n                    // run as microtask so that pending queue messages are resolved first\n                    return Promise.resolve().then(() =>\n                    {\n                        this.emit(message.method, message?.params)\n                    })\n                }\n\n                return\n            }\n\n            // reject early since server's response is invalid\n            if (\"error\" in message === \"result\" in message)\n                this.queue[message.id].promise[1](\n                    new Error(\n                        \"Server response malformed. Response must include either \\\"result\\\"\" +\n              \" or \\\"error\\\", but not both.\"\n                    )\n                )\n\n            if (this.queue[message.id].timeout)\n                clearTimeout(this.queue[message.id].timeout)\n\n            if (message.error) this.queue[message.id].promise[1](message.error)\n            else this.queue[message.id].promise[0](message.result)\n\n            delete this.queue[message.id]\n        })\n\n        this.socket.addEventListener(\"error\", (error) => this.emit(\"error\", error))\n\n        this.socket.addEventListener(\"close\", ({ code, reason }) =>\n        {\n            if (this.ready)\n            // Delay close event until internal state is updated\n                setTimeout(() => this.emit(\"close\", code, reason), 0)\n\n            this.ready = false\n            this.socket = undefined\n\n            if (code === 1000) return\n\n            this.current_reconnects++\n\n            if (\n                this.reconnect &&\n        (this.max_reconnects > this.current_reconnects ||\n          this.max_reconnects === 0)\n            )\n                this.reconnect_timer_id = setTimeout(\n                    () => this._connect(address, options),\n                    this.reconnect_interval\n                )\n        })\n    }\n}\n", "\"use strict\"\n\nexport interface DataPack<\n  T,\n  R extends string | ArrayBufferLike | Blob | ArrayBufferView\n> {\n  encode(value: T): R;\n  decode(value: R): T;\n}\n\nexport class DefaultDataPack implements DataPack<Object, string>\n{\n    encode(value: Object): string\n    {\n        return JSON.stringify(value)\n    }\n\n    decode(value: string): Object\n    {\n        return JSON.parse(value)\n    }\n}\n", "/**\n * \"Server\" wraps the \"ws\" library providing JSON RPC 2.0 support on top.\n * @module Server\n */\n\n\"use strict\"\n\nimport { EventEmitter } from \"eventemitter3\"\nimport url from \"node:url\"\nimport { v1 as uuidv1 } from \"uuid\"\nimport NodeWebSocket, { WebSocketServer } from \"ws\"\n\nimport { DataPack, DefaultDataPack } from \"./utils.js\"\n\ninterface INamespaceEvent {\n  [x: string]: {\n    sockets: Array<string>;\n    protected: boolean;\n  };\n}\n\ninterface IMethod {\n  public: () => void;\n  protected: () => void;\n}\n\ninterface IEvent {\n  public: () => void;\n  protected: () => void;\n}\n\ninterface IRPCError {\n  code: number;\n  message: string;\n  data?: string;\n}\n\ninterface IRPCMethodParams {\n  [x: string]: any;\n}\n\ninterface IRPCMethod {\n  [x: string]: {\n    fn: (params: IRPCMethodParams, socket_id: string) => any;\n    protected: boolean;\n  };\n}\n\ninterface INamespace {\n  [x: string]: {\n    rpc_methods: IRPCMethod;\n    clients: Map<string, IClientWebSocket>;\n    events: INamespaceEvent;\n  };\n}\n\ninterface IClientWebSocket extends NodeWebSocket {\n  _id: string;\n  _authenticated: boolean;\n}\n\ninterface IRPCResult {\n  [x: string]: string;\n}\n\nexport class Server extends EventEmitter\n{\n    private namespaces: INamespace\n    private dataPack: DataPack<any, string>\n    wss: InstanceType<typeof WebSocketServer>\n\n    /**\n   * Instantiate a Server class.\n   * @constructor\n   * @param {Object} options - ws constructor's parameters with rpc\n   * @param {DataPack} dataPack - data pack contains encoder and decoder\n   * @return {Server} - returns a new Server instance\n   */\n    constructor(\n        options: NodeWebSocket.ServerOptions,\n        dataPack?: DataPack<object, string>\n    )\n    {\n        super()\n\n        /**\n     * Stores all connected sockets with a universally unique identifier\n     * in the appropriate namespace.\n     * Stores all rpc methods to specific namespaces. \"/\" by default.\n     * Stores all events as keys and subscribed users in array as value\n     * @private\n     * @name namespaces\n     * @param {Object} namespaces.rpc_methods\n     * @param {Map} namespaces.clients\n     * @param {Object} namespaces.events\n     */\n        this.namespaces = {}\n\n        if (!dataPack) this.dataPack = new DefaultDataPack()\n        else this.dataPack = dataPack\n\n        this.wss = new WebSocketServer(options)\n\n        this.wss.on(\"listening\", () => this.emit(\"listening\"))\n\n        this.wss.on(\"connection\", (socket: IClientWebSocket, request) =>\n        {\n            const u = url.parse(request.url, true)\n            const ns = u.pathname\n\n            if (u.query.socket_id) socket._id = u.query.socket_id as string\n            else socket._id = uuidv1()\n\n            // unauthenticated by default\n            socket[\"_authenticated\"] = false\n\n            // propagate socket errors\n            socket.on(\"error\", (error) => this.emit(\"socket-error\", socket, error))\n\n            // cleanup after the socket gets disconnected\n            socket.on(\"close\", () =>\n            {\n                this.namespaces[ns].clients.delete(socket._id)\n\n                for (const event of Object.keys(this.namespaces[ns].events))\n                {\n                    const index = this.namespaces[ns].events[event].sockets.indexOf(\n                        socket._id\n                    )\n\n                    if (index >= 0)\n                        this.namespaces[ns].events[event].sockets.splice(index, 1)\n                }\n\n                this.emit(\"disconnection\", socket)\n            })\n\n            if (!this.namespaces[ns]) this._generateNamespace(ns)\n\n            // store socket and method\n            this.namespaces[ns].clients.set(socket._id, socket)\n\n            this.emit(\"connection\", socket, request)\n\n            return this._handleRPC(socket, ns)\n        })\n\n        this.wss.on(\"error\", (error) => this.emit(\"error\", error))\n    }\n\n    /**\n   * Registers an RPC method.\n   * @method\n   * @param {String} name - method name\n   * @param {Function} fn - a callee function\n   * @param {String} ns - namespace identifier\n   * @throws {TypeError}\n   * @return {Object} - returns an IMethod object\n   */\n    register(\n        name: string,\n        fn: (params: IRPCMethodParams, socket_id: string) => void,\n        ns = \"/\"\n    )\n    {\n        if (!this.namespaces[ns]) this._generateNamespace(ns)\n\n        this.namespaces[ns].rpc_methods[name] = {\n            fn: fn,\n            protected: false,\n        }\n\n        return {\n            protected: () => this._makeProtectedMethod(name, ns),\n            public: () => this._makePublicMethod(name, ns),\n        } as IMethod\n    }\n\n    /**\n   * Sets an auth method.\n   * @method\n   * @param {Function} fn - an arbitrary auth method\n   * @param {String} ns - namespace identifier\n   * @throws {TypeError}\n   * @return {Undefined}\n   */\n    setAuth(\n        fn: (params: IRPCMethodParams, socket_id: string) => Promise<boolean>,\n        ns = \"/\"\n    )\n    {\n        this.register(\"rpc.login\", fn, ns)\n    }\n\n    /**\n   * Marks an RPC method as protected.\n   * @method\n   * @param {String} name - method name\n   * @param {String} ns - namespace identifier\n   * @return {Undefined}\n   */\n    private _makeProtectedMethod(name: string, ns = \"/\")\n    {\n        this.namespaces[ns].rpc_methods[name].protected = true\n    }\n\n    /**\n   * Marks an RPC method as public.\n   * @method\n   * @param {String} name - method name\n   * @param {String} ns - namespace identifier\n   * @return {Undefined}\n   */\n    private _makePublicMethod(name: string, ns = \"/\")\n    {\n        this.namespaces[ns].rpc_methods[name].protected = false\n    }\n\n    /**\n   * Marks an event as protected.\n   * @method\n   * @param {String} name - event name\n   * @param {String} ns - namespace identifier\n   * @return {Undefined}\n   */\n    private _makeProtectedEvent(name: string, ns = \"/\")\n    {\n        this.namespaces[ns].events[name].protected = true\n    }\n\n    /**\n   * Marks an event as public.\n   * @method\n   * @param {String} name - event name\n   * @param {String} ns - namespace identifier\n   * @return {Undefined}\n   */\n    private _makePublicEvent(name: string, ns = \"/\")\n    {\n        this.namespaces[ns].events[name].protected = false\n    }\n\n    /**\n   * Removes a namespace and closes all connections\n   * @method\n   * @param {String} ns - namespace identifier\n   * @throws {TypeError}\n   * @return {Undefined}\n   */\n    closeNamespace(ns: string)\n    {\n        const namespace = this.namespaces[ns]\n\n        if (namespace)\n        {\n            delete namespace.rpc_methods\n            delete namespace.events\n\n            for (const socket of namespace.clients.values()) socket.close()\n\n            delete this.namespaces[ns]\n        }\n    }\n\n    /**\n   * Creates a new event that can be emitted to clients.\n   * @method\n   * @param {String} name - event name\n   * @param {String} ns - namespace identifier\n   * @throws {TypeError}\n   * @return {Object} - returns an IEvent object\n   */\n    event(name: string, ns = \"/\"): IEvent\n    {\n        if (!this.namespaces[ns]) this._generateNamespace(ns)\n        else\n        {\n            const index = this.namespaces[ns].events[name]\n\n            if (index !== undefined)\n                throw new Error(`Already registered event ${ns}${name}`)\n        }\n\n        this.namespaces[ns].events[name] = {\n            sockets: [],\n            protected: false,\n        }\n\n        // forward emitted event to subscribers\n        this.on(name, (...params) =>\n        {\n            // flatten an object if no spreading is wanted\n            if (params.length === 1 && params[0] instanceof Object)\n                params = params[0]\n\n            for (const socket_id of this.namespaces[ns].events[name].sockets)\n            {\n                const socket = this.namespaces[ns].clients.get(socket_id)\n\n                if (!socket) continue\n\n                socket.send(\n                    this.dataPack.encode({\n                        notification: name,\n                        params,\n                    })\n                )\n            }\n        })\n\n        return {\n            protected: () => this._makeProtectedEvent(name, ns),\n            public: () => this._makePublicEvent(name, ns),\n        }\n    }\n\n    /**\n   * Returns a requested namespace object\n   * @method\n   * @param {String} name - namespace identifier\n   * @throws {TypeError}\n   * @return {Object} - namespace object\n   */\n    of(name: string)\n    {\n        if (!this.namespaces[name]) this._generateNamespace(name)\n\n        const self = this\n\n        return {\n            // self.register convenience method\n            register(\n                fn_name: string,\n                fn: (params: IRPCMethodParams) => void\n            ): IMethod\n            {\n                if (arguments.length !== 2)\n                    throw new Error(\"must provide exactly two arguments\")\n\n                if (typeof fn_name !== \"string\")\n                    throw new Error(\"name must be a string\")\n\n                if (typeof fn !== \"function\")\n                    throw new Error(\"handler must be a function\")\n\n                return self.register(fn_name, fn, name)\n            },\n\n            // self.event convenience method\n            event(ev_name: string): IEvent\n            {\n                if (arguments.length !== 1)\n                    throw new Error(\"must provide exactly one argument\")\n\n                if (typeof ev_name !== \"string\")\n                    throw new Error(\"name must be a string\")\n\n                return self.event(ev_name, name)\n            },\n\n            // self.eventList convenience method\n            get eventList()\n            {\n                return Object.keys(self.namespaces[name].events)\n            },\n\n            /**\n       * Emits a specified event to this namespace.\n       * @inner\n       * @method\n       * @param {String} event - event name\n       * @param {Array} params - event parameters\n       * @return {Undefined}\n       */\n            emit(event: string, ...params: Array<string>)\n            {\n                const socket_ids = [...self.namespaces[name].clients.keys()]\n\n                for (let i = 0, id; (id = socket_ids[i]); ++i)\n                {\n                    self.namespaces[name].clients.get(id).send(\n                        self.dataPack.encode({\n                            notification: event,\n                            params: params || [],\n                        })\n                    )\n                }\n            },\n\n            /**\n       * Returns a name of this namespace.\n       * @inner\n       * @method\n       * @kind constant\n       * @return {String}\n       */\n            get name()\n            {\n                return name\n            },\n\n            /**\n       * Returns a hash of websocket objects connected to this namespace.\n       * @inner\n       * @method\n       * @return {Object}\n       */\n            connected()\n            {\n                const socket_ids = [...self.namespaces[name].clients.keys()]\n\n                return socket_ids.reduce(\n                    (acc, curr) => ({\n                        ...acc,\n                        [curr]: self.namespaces[name].clients.get(curr),\n                    }),\n                    {}\n                )\n            },\n\n            /**\n       * Returns a list of client unique identifiers connected to this namespace.\n       * @inner\n       * @method\n       * @return {Array}\n       */\n            clients()\n            {\n                return self.namespaces[name]\n            },\n        }\n    }\n\n    /**\n   * Lists all created events in a given namespace. Defaults to \"/\".\n   * @method\n   * @param {String} ns - namespaces identifier\n   * @readonly\n   * @return {Array} - returns a list of created events\n   */\n    eventList(ns = \"/\")\n    {\n        if (!this.namespaces[ns]) return []\n\n        return Object.keys(this.namespaces[ns].events)\n    }\n\n    /**\n   * Creates a JSON-RPC 2.0 compliant error\n   * @method\n   * @param {Number} code - indicates the error type that occurred\n   * @param {String} message - provides a short description of the error\n   * @param {String|Object} data - details containing additional information about the error\n   * @return {Object}\n   */\n    createError(code: number, message: string, data: string | object)\n    {\n        return {\n            code: code,\n            message: message,\n            data: data || null,\n        }\n    }\n\n    /**\n   * Closes the server and terminates all clients.\n   * @method\n   * @return {Promise}\n   */\n    close()\n    {\n        return new Promise<void>((resolve, reject) =>\n        {\n            try\n            {\n                this.wss.close()\n                this.emit(\"close\")\n                resolve()\n            }\n            catch (error)\n            {\n                reject(error)\n            }\n        })\n    }\n\n    /**\n   * Handles all WebSocket JSON RPC 2.0 requests.\n   * @private\n   * @param {Object} socket - ws socket instance\n   * @param {String} ns - namespaces identifier\n   * @return {Undefined}\n   */\n    private _handleRPC(socket: IClientWebSocket, ns = \"/\")\n    {\n        socket.on(\"message\", async (data: any) =>\n        {\n            const msg_options: Parameters<NodeWebSocket[\"send\"]>[1] = {}\n\n            if (data instanceof ArrayBuffer)\n            {\n                msg_options.binary = true\n\n                data = Buffer.from(data).toString()\n            }\n\n            if (socket.readyState !== 1) return // TODO: should have debug logs here\n\n            let parsedData: any\n\n            try\n            {\n                parsedData = this.dataPack.decode(data as string)\n            }\n            catch (error)\n            {\n                return socket.send(\n                    this.dataPack.encode({\n                        jsonrpc: \"2.0\",\n                        error: createError(-32700, error.toString()),\n                        id: null,\n                    }),\n                    msg_options\n                )\n            }\n\n            if (Array.isArray(parsedData))\n            {\n                if (!parsedData.length)\n                    return socket.send(\n                        this.dataPack.encode({\n                            jsonrpc: \"2.0\",\n                            error: createError(-32600, \"Invalid array\"),\n                            id: null,\n                        }),\n                        msg_options\n                    )\n\n                const responses = []\n\n                for (const message of parsedData)\n                {\n                    const response = await this._runMethod(message, socket._id, ns)\n\n                    if (!response) continue\n\n                    responses.push(response)\n                }\n\n                if (!responses.length) return\n\n                return socket.send(this.dataPack.encode(responses), msg_options)\n            }\n\n            const response = await this._runMethod(parsedData, socket._id, ns)\n\n            if (!response) return\n\n            return socket.send(this.dataPack.encode(response), msg_options)\n        })\n    }\n\n    /**\n   * Runs a defined RPC method.\n   * @private\n   * @param {Object} message - a message received\n   * @param {Object} socket_id - user's socket id\n   * @param {String} ns - namespaces identifier\n   * @return {Object|undefined}\n   */\n    private async _runMethod(message: any, socket_id: string, ns = \"/\")\n    {\n        if (typeof message !== \"object\" || message === null)\n            return {\n                jsonrpc: \"2.0\",\n                error: createError(-32600),\n                id: null,\n            }\n\n        if (message.jsonrpc !== \"2.0\")\n            return {\n                jsonrpc: \"2.0\",\n                error: createError(-32600, \"Invalid JSON RPC version\"),\n                id: message.id || null,\n            }\n\n        if (!message.method)\n            return {\n                jsonrpc: \"2.0\",\n                error: createError(-32602, \"Method not specified\"),\n                id: message.id || null,\n            }\n\n        if (typeof message.method !== \"string\")\n            return {\n                jsonrpc: \"2.0\",\n                error: createError(-32600, \"Invalid method name\"),\n                id: message.id || null,\n            }\n\n        if (message.params && typeof message.params === \"string\")\n            return {\n                jsonrpc: \"2.0\",\n                error: createError(-32600),\n                id: message.id || null,\n            }\n\n        if (message.method === \"rpc.on\")\n        {\n            if (!message.params)\n                return {\n                    jsonrpc: \"2.0\",\n                    error: createError(-32000),\n                    id: message.id || null,\n                }\n\n            const results: IRPCMethodParams = {}\n\n            const event_names = Object.keys(this.namespaces[ns].events)\n\n            for (const name of message.params)\n            {\n                const index = event_names.indexOf(name)\n                const namespace = this.namespaces[ns]\n\n                if (index === -1)\n                {\n                    results[name] = \"provided event invalid\"\n                    continue\n                }\n\n                // reject request if event is protected and if client is not authenticated\n                if (\n                    namespace.events[event_names[index]].protected === true &&\n          namespace.clients.get(socket_id)[\"_authenticated\"] === false\n                )\n                {\n                    return {\n                        jsonrpc: \"2.0\",\n                        error: createError(-32606),\n                        id: message.id || null,\n                    }\n                }\n\n                const socket_index =\n          namespace.events[event_names[index]].sockets.indexOf(socket_id)\n                if (socket_index >= 0)\n                {\n                    results[name] = \"socket has already been subscribed to event\"\n                    continue\n                }\n                namespace.events[event_names[index]].sockets.push(socket_id)\n\n                results[name] = \"ok\"\n            }\n\n            return {\n                jsonrpc: \"2.0\",\n                result: results,\n                id: message.id || null,\n            }\n        }\n        else if (message.method === \"rpc.off\")\n        {\n            if (!message.params)\n                return {\n                    jsonrpc: \"2.0\",\n                    error: createError(-32000),\n                    id: message.id || null,\n                }\n\n            const results: IRPCResult = {}\n\n            for (const name of message.params)\n            {\n                if (!this.namespaces[ns].events[name])\n                {\n                    results[name] = \"provided event invalid\"\n                    continue\n                }\n\n                const index =\n          this.namespaces[ns].events[name].sockets.indexOf(socket_id)\n\n                if (index === -1)\n                {\n                    results[name] = \"not subscribed\"\n                    continue\n                }\n\n                this.namespaces[ns].events[name].sockets.splice(index, 1)\n                results[name] = \"ok\"\n            }\n\n            return {\n                jsonrpc: \"2.0\",\n                result: results,\n                id: message.id || null,\n            }\n        }\n        else if (message.method === \"rpc.login\")\n        {\n            if (!message.params)\n                return {\n                    jsonrpc: \"2.0\",\n                    error: createError(-32604),\n                    id: message.id || null,\n                }\n        }\n\n        if (!this.namespaces[ns].rpc_methods[message.method])\n        {\n            return {\n                jsonrpc: \"2.0\",\n                error: createError(-32601),\n                id: message.id || null,\n            }\n        }\n\n        let response = null\n\n        // reject request if method is protected and if client is not authenticated\n        if (\n            this.namespaces[ns].rpc_methods[message.method].protected === true &&\n      this.namespaces[ns].clients.get(socket_id)[\"_authenticated\"] === false\n        )\n        {\n            return {\n                jsonrpc: \"2.0\",\n                error: createError(-32605),\n                id: message.id || null,\n            }\n        }\n\n        try\n        {\n            response = await this.namespaces[ns].rpc_methods[message.method].fn(\n                message.params,\n                socket_id\n            )\n        }\n        catch (error)\n        {\n            if (!message.id) return\n\n            if (error instanceof Error)\n                return {\n                    jsonrpc: \"2.0\",\n                    error: {\n                        code: -32000,\n                        message: error.name,\n                        data: error.message,\n                    },\n                    id: message.id,\n                }\n\n            return {\n                jsonrpc: \"2.0\",\n                error: error,\n                id: message.id,\n            }\n        }\n\n        // client sent a notification, so we won't need a reply\n        if (!message.id) return\n\n        // if login middleware returned true, set connection as authenticated\n        if (message.method === \"rpc.login\" && response === true)\n        {\n            const s = this.namespaces[ns].clients.get(socket_id)\n            s[\"_authenticated\"] = true\n            this.namespaces[ns].clients.set(socket_id, s)\n        }\n\n        return {\n            jsonrpc: \"2.0\",\n            result: response,\n            id: message.id,\n        }\n    }\n\n    /**\n   * Generate a new namespace store.\n   * Also preregister some special namespace methods.\n   * @private\n   * @param {String} name - namespaces identifier\n   * @return {undefined}\n   */\n    private _generateNamespace(name: string)\n    {\n        this.namespaces[name] = {\n            rpc_methods: {\n                __listMethods: {\n                    fn: () => Object.keys(this.namespaces[name].rpc_methods),\n                    protected: false,\n                },\n            },\n            clients: new Map(),\n            events: {},\n        }\n    }\n}\n\nconst RPC_ERRORS = new Map([\n    [-32000, \"Event not provided\"],\n    [-32600, \"Invalid Request\"],\n    [-32601, \"Method not found\"],\n    [-32602, \"Invalid params\"],\n    [-32603, \"Internal error\"],\n    [-32604, \"Params not found\"],\n    [-32605, \"Method forbidden\"],\n    [-32606, \"Event forbidden\"],\n    [-32700, \"Parse error\"],\n])\n\n/**\n * Creates a JSON-RPC 2.0-compliant error.\n * @param {Number} code - error code\n * @param {String} details - error details\n * @return {Object}\n */\nexport function createError(code: number, details?: string)\n{\n    const error: IRPCError = {\n        code: code,\n        message: RPC_ERRORS.get(code) || \"Internal Server Error\",\n    }\n\n    if (details) error[\"data\"] = details\n\n    return error\n}\n", "\"use strict\"\n\nimport { WebSocket } from \"./lib/client/websocket.js\"\nimport { CommonClient } from \"./lib/client.js\"\nimport {\n    NodeWebSocketTypeOptions,\n    IWSClientAdditionalOptions,\n    ICommonWebSocketFactory,\n} from \"./lib/client/client.types.js\"\n\nexport class Client extends CommonClient\n{\n    constructor(\n        address = \"ws://localhost:8080\",\n        {\n            autoconnect = true,\n            reconnect = true,\n            reconnect_interval = 1000,\n            max_reconnects = 5,\n            ...rest_options\n        }: IWSClientAdditionalOptions & NodeWebSocketTypeOptions = {},\n        generate_request_id?: (\n      method: string,\n      params: object | Array<any>\n    ) => number | string\n    )\n    {\n        super(\n      WebSocket as ICommonWebSocketFactory,\n      address,\n      {\n          autoconnect,\n          reconnect,\n          reconnect_interval,\n          max_reconnects,\n          ...rest_options,\n      },\n      generate_request_id\n        )\n    }\n}\n\nexport * from \"./lib/client.js\"\nexport * from \"./lib/client/websocket.js\"\nexport * from \"./lib/client/client.types.js\"\nexport * from \"./lib/server.js\"\nexport * from \"./lib/utils.js\"\n"]}