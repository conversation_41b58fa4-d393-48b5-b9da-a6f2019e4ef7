use axum::{
    routing::{post, get},
    Router,
    extract::{Json, State},
    http::{StatusC<PERSON>, HeaderMap},
    response::IntoResponse,
    middleware,
};
use serde::{Deserialize, Serialize};
use jsonwebtoken::{encode, decode, Header, Algor<PERSON>m, Validation, Encoding<PERSON><PERSON>, Decod<PERSON><PERSON><PERSON>};
use bcrypt::{hash, verify, DEFAULT_COST};
use mongodb::{
    bson::{doc, oid::ObjectId},
    options::FindOneOptions,
    error::Error as MongoError,
};
use futures::stream::TryStreamExt;
use std::{
    sync::Arc,
    time::{SystemTime, UNIX_EPOCH, Duration},
    collections::HashMap,
};
use tokio::time::timeout;
use tracing::{error, warn, info, debug};
use anyhow::{Result, Context, anyhow};
use uuid::Uuid;
use crate::model::{AdminUser, AdminRole, BotError};
use crate::service::db_service::DbService;
use crate::config::AppConfig;

// Production constants
const JWT_EXPIRY_HOURS: u64 = 24;
const MAX_LOGIN_ATTEMPTS: u32 = 5;
const LOCKOUT_DURATION_MINUTES: u64 = 30;
const PASSWORD_MIN_LENGTH: usize = 8;
const DB_OPERATION_TIMEOUT: Duration = Duration::from_secs(10);

#[derive(Debug, thiserror::Error)]
pub enum AuthError {
    #[error("Database operation failed: {0}")]
    DatabaseError(#[from] MongoError),
    #[error("Authentication failed: {0}")]
    AuthenticationFailed(String),
    #[error("Authorization failed: {0}")]
    AuthorizationFailed(String),
    #[error("Token operation failed: {0}")]
    TokenError(String),
    #[error("Password validation failed: {0}")]
    PasswordValidationError(String),
    #[error("User validation failed: {0}")]
    UserValidationError(String),
    #[error("Account locked: {0}")]
    AccountLocked(String),
    #[error("Operation timeout")]
    Timeout,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct Claims {
    pub sub: String, // Subject (user id)
    pub username: String,
    pub role: String,
    pub exp: usize, // Expiration time
    pub iat: usize, // Issued at
    pub jti: String, // JWT ID for token tracking
    pub session_id: String, // Session identifier
}

#[derive(Debug, Clone)]
struct LoginAttempt {
    attempts: u32,
    last_attempt: SystemTime,
    locked_until: Option<SystemTime>,
}

#[derive(Debug, Clone)]
struct SecurityMetrics {
    failed_logins: u64,
    successful_logins: u64,
    active_sessions: u64,
    blocked_ips: u64,
}

#[derive(Debug, Deserialize)]
pub struct LoginRequest {
    pub username: String,
    pub password: String,
}

#[derive(Debug, Serialize)]
pub struct LoginResponse {
    pub token: String,
    pub user: AdminUserResponse,
    pub expires_in: u64,
}

#[derive(Debug, Serialize)]
pub struct AdminUserResponse {
    pub id: String,
    pub username: String,
    pub email: String,
    pub role: String,
    pub created_at: u64,
    pub last_login: Option<u64>,
    pub is_active: bool,
    pub status: String,
}

#[derive(Debug, Deserialize)]
pub struct CreateAdminRequest {
    pub username: String,
    pub email: String,
    pub password: String,
    pub role: AdminRole,
}

#[derive(Debug, Deserialize)]
pub struct ChangePasswordRequest {
    pub current_password: String,
    pub new_password: String,
}

pub struct AuthController {
    config: Arc<AppConfig>,
    jwt_secret: String,
    login_attempts: Arc<tokio::sync::RwLock<HashMap<String, LoginAttempt>>>,
    active_sessions: Arc<tokio::sync::RwLock<HashMap<String, SystemTime>>>,
    token_blacklist: Arc<tokio::sync::RwLock<HashMap<String, SystemTime>>>,
    security_metrics: Arc<tokio::sync::RwLock<SecurityMetrics>>,
}

impl AuthController {
    pub fn new(config: Arc<AppConfig>) -> Self {
        let jwt_secret = std::env::var("JWT_SECRET")
            .context("JWT_SECRET environment variable is required")
            .expect("Critical configuration missing");

        if jwt_secret.len() < 32 {
            panic!("JWT_SECRET must be at least 32 characters long for security");
        }

        Self {
            config,
            jwt_secret,
            login_attempts: Arc::new(tokio::sync::RwLock::new(HashMap::new())),
            active_sessions: Arc::new(tokio::sync::RwLock::new(HashMap::new())),
            token_blacklist: Arc::new(tokio::sync::RwLock::new(HashMap::new())),
            security_metrics: Arc::new(tokio::sync::RwLock::new(SecurityMetrics {
                failed_logins: 0,
                successful_logins: 0,
                active_sessions: 0,
                blocked_ips: 0,
            })),
        }
    }

    pub fn create_router(&self) -> Router {
        let auth_controller = Arc::new(self.clone());
        let routes = &self.config.api_routes;

        Router::new()
            .route(&routes.auth_login, post(Self::login))
            .route(&routes.auth_logout, post(Self::logout))
            .route(&routes.auth_refresh, post(Self::refresh_token))
            .route(&routes.auth_me, get(Self::get_current_user))
            .route(&routes.auth_change_password, post(Self::change_password))
            .route(&routes.auth_create_admin, post(Self::create_admin))
            .with_state(auth_controller)
    }

    async fn login(
        State(controller): State<Arc<AuthController>>,
        Json(login_req): Json<LoginRequest>,
    ) -> impl IntoResponse {
        let start_time = SystemTime::now();
        let client_identifier = format!("{}:{}", login_req.username, "unknown_ip"); // In production, extract real IP

        info!("Login attempt for user: {}", login_req.username);

        // Input validation
        if login_req.username.trim().is_empty() || login_req.password.is_empty() {
            warn!("Login attempt with empty credentials");
            return (StatusCode::BAD_REQUEST, Json(serde_json::json!({
                "error": "Username and password are required",
                "code": "INVALID_INPUT"
            }))).into_response();
        }

        // Check if account is locked
        if let Err(lockout_error) = controller.check_account_lockout(&client_identifier).await {
            warn!("Login attempt on locked account: {}", client_identifier);
            return (StatusCode::TOO_MANY_REQUESTS, Json(serde_json::json!({
                "error": lockout_error.to_string(),
                "code": "ACCOUNT_LOCKED"
            }))).into_response();
        }

        // Attempt authentication with timeout
        let auth_result = timeout(
            DB_OPERATION_TIMEOUT,
            controller.authenticate_user_secure(&login_req.username, &login_req.password)
        ).await;

        match auth_result {
            Ok(Ok(user)) => {
                // Authentication successful
                controller.reset_login_attempts(&client_identifier).await;

                match controller.generate_secure_token(&user).await {
                    Ok((token, session_id)) => {
                        // Update last login and track session
                        // Execute post-login tasks concurrently (ignore errors for non-critical operations)
                        let update_future = controller.update_last_login(&user);
                        let session_future = controller.track_active_session(&session_id);
                        let metrics_future = controller.increment_successful_logins();

                        let _ = tokio::join!(update_future, session_future, metrics_future);

                        let response = LoginResponse {
                            token,
                            user: AdminUserResponse {
                                id: user.id.unwrap().to_hex(),
                                username: user.username,
                                email: user.email,
                                role: format!("{:?}", user.role),
                                created_at: user.created_at,
                                last_login: user.last_login,
                                is_active: user.is_active,
                                status: if user.is_active { "active".to_string() } else { "inactive".to_string() },
                            },
                            expires_in: JWT_EXPIRY_HOURS * 3600,
                        };

                        let elapsed = start_time.elapsed().unwrap_or_default();
                        info!("Successful login for user: {} in {:?}", login_req.username, elapsed);

                        (StatusCode::OK, Json(response)).into_response()
                    }
                    Err(e) => {
                        error!("Token generation failed for user {}: {:?}", login_req.username, e);
                        (StatusCode::INTERNAL_SERVER_ERROR, Json(serde_json::json!({
                            "error": "Authentication service temporarily unavailable",
                            "code": "TOKEN_ERROR"
                        }))).into_response()
                    }
                }
            }
            Ok(Err(auth_error)) => {
                // Authentication failed
                controller.record_failed_login(&client_identifier).await;
                controller.increment_failed_logins().await;

                warn!("Authentication failed for user {}: {:?}", login_req.username, auth_error);

                (StatusCode::UNAUTHORIZED, Json(serde_json::json!({
                    "error": "Invalid credentials",
                    "code": "AUTH_FAILED"
                }))).into_response()
            }
            Err(_) => {
                // Timeout
                error!("Authentication timeout for user: {}", login_req.username);
                (StatusCode::REQUEST_TIMEOUT, Json(serde_json::json!({
                    "error": "Authentication service timeout",
                    "code": "TIMEOUT"
                }))).into_response()
            }
        }
    }

    async fn logout(
        State(controller): State<Arc<AuthController>>,
        headers: HeaderMap,
    ) -> impl IntoResponse {
        info!("Logout request received");

        if let Some(auth_header) = headers.get("authorization") {
            if let Ok(auth_str) = auth_header.to_str() {
                if let Some(token) = auth_str.strip_prefix("Bearer ") {
                    match controller.verify_token_secure(token).await {
                        Ok(claims) => {
                            // Blacklist the token and remove active session
                            let blacklist_future = controller.blacklist_token(token);
                            let session_future = controller.remove_active_session(&claims.session_id);

                            let _ = tokio::try_join!(blacklist_future, session_future);

                            info!("User {} logged out successfully", claims.username);

                            return (StatusCode::OK, Json(serde_json::json!({
                                "message": "Logged out successfully",
                                "code": "LOGOUT_SUCCESS"
                            }))).into_response();
                        }
                        Err(e) => {
                            warn!("Invalid token in logout request: {:?}", e);
                        }
                    }
                }
            }
        }

        // Even if token is invalid, return success for security
        (StatusCode::OK, Json(serde_json::json!({
            "message": "Logged out successfully",
            "code": "LOGOUT_SUCCESS"
        }))).into_response()
    }

    async fn refresh_token(
        State(controller): State<Arc<AuthController>>,
        headers: HeaderMap,
    ) -> impl IntoResponse {
        if let Some(auth_header) = headers.get("authorization") {
            if let Ok(auth_str) = auth_header.to_str() {
                if let Some(token) = auth_str.strip_prefix("Bearer ") {
                    match controller.verify_token_secure(token).await {
                        Ok(claims) => {
                            // Generate new token
                            if let Ok(user) = controller.get_user_by_username(&claims.username).await {
                                if let Ok((new_token, _)) = controller.generate_secure_token(&user).await {
                                    return (StatusCode::OK, Json(serde_json::json!({
                                        "token": new_token,
                                        "expires_in": 86400
                                    })));
                                }
                            }
                        }
                        Err(_) => {}
                    }
                }
            }
        }

        (StatusCode::UNAUTHORIZED, Json(serde_json::json!({
            "error": "Invalid or expired token"
        })))
    }

    async fn get_current_user(
        State(controller): State<Arc<AuthController>>,
        headers: HeaderMap,
    ) -> impl IntoResponse {
        if let Some(auth_header) = headers.get("authorization") {
            if let Ok(auth_str) = auth_header.to_str() {
                if let Some(token) = auth_str.strip_prefix("Bearer ") {
                    match controller.verify_token_secure(token).await {
                        Ok(claims) => {
                            if let Ok(user) = controller.get_user_by_username(&claims.username).await {
                                let response = AdminUserResponse {
                                    id: user.id.unwrap().to_hex(),
                                    username: user.username,
                                    email: user.email,
                                    role: format!("{:?}", user.role),
                                    created_at: user.created_at,
                                    last_login: user.last_login,
                                    is_active: user.is_active,
                                    status: if user.is_active { "active".to_string() } else { "inactive".to_string() },
                                };
                                return (StatusCode::OK, Json(response));
                            }
                        }
                        Err(_) => {}
                    }
                }
            }
        }

        {
            let error_response = AdminUserResponse {
                id: "".to_string(),
                username: "".to_string(),
                email: "".to_string(),
                role: "".to_string(),
                created_at: 0,
                last_login: None,
                is_active: false,
                status: "inactive".to_string(),
            };
            (StatusCode::UNAUTHORIZED, Json(error_response))
        }
    }

    async fn change_password(
        State(controller): State<Arc<AuthController>>,
        headers: HeaderMap,
        Json(change_req): Json<ChangePasswordRequest>,
    ) -> impl IntoResponse {
        if let Some(auth_header) = headers.get("authorization") {
            if let Ok(auth_str) = auth_header.to_str() {
                if let Some(token) = auth_str.strip_prefix("Bearer ") {
                    match controller.verify_token_secure(token).await {
                        Ok(claims) => {
                            match controller.change_user_password(&claims.username, &change_req.current_password, &change_req.new_password).await {
                                Ok(_) => {
                                    return (StatusCode::OK, Json(serde_json::json!({
                                        "message": "Password changed successfully"
                                    })));
                                }
                                Err(_) => {
                                    return (StatusCode::BAD_REQUEST, Json(serde_json::json!({
                                        "error": "Invalid current password"
                                    })));
                                }
                            }
                        }
                        Err(_) => {}
                    }
                }
            }
        }

        (StatusCode::UNAUTHORIZED, Json(serde_json::json!({
            "error": "Invalid or expired token"
        })))
    }

    async fn create_admin(
        State(controller): State<Arc<AuthController>>,
        headers: HeaderMap,
        Json(create_req): Json<CreateAdminRequest>,
    ) -> impl IntoResponse {
        // Verify the requester is a SuperAdmin
        if let Some(auth_header) = headers.get("authorization") {
            if let Ok(auth_str) = auth_header.to_str() {
                if let Some(token) = auth_str.strip_prefix("Bearer ") {
                    match controller.verify_token_secure(token).await {
                        Ok(claims) => {
                            if claims.role != "SuperAdmin" {
                                return (StatusCode::FORBIDDEN, Json(serde_json::json!({
                                    "error": "Only SuperAdmins can create new admins"
                                }))).into_response();
                            }

                            match controller.create_new_admin(create_req).await {
                                Ok(user) => {
                                    let response = AdminUserResponse {
                                        id: user.id.unwrap().to_hex(),
                                        username: user.username,
                                        email: user.email,
                                        role: format!("{:?}", user.role),
                                        created_at: user.created_at,
                                        last_login: user.last_login,
                                        is_active: user.is_active,
                                        status: if user.is_active { "active".to_string() } else { "inactive".to_string() },
                                    };
                                    return (StatusCode::CREATED, Json(response)).into_response();
                                }
                                Err(_) => {
                                    return (StatusCode::BAD_REQUEST, Json(serde_json::json!({
                                        "error": "Failed to create admin user"
                                    }))).into_response();
                                }
                            }
                        }
                        Err(_) => {}
                    }
                }
            }
        }

        (StatusCode::UNAUTHORIZED, Json(serde_json::json!({
            "error": "Invalid or expired token"
        }))).into_response()
    }

    // Production security helper methods
    async fn check_account_lockout(&self, client_identifier: &str) -> Result<(), AuthError> {
        let attempts = self.login_attempts.read().await;

        if let Some(attempt_info) = attempts.get(client_identifier) {
            if let Some(locked_until) = attempt_info.locked_until {
                if SystemTime::now() < locked_until {
                    let remaining = locked_until.duration_since(SystemTime::now())
                        .unwrap_or_default()
                        .as_secs();
                    return Err(AuthError::AccountLocked(
                        format!("Account locked for {} more seconds", remaining)
                    ));
                }
            }
        }

        Ok(())
    }

    async fn record_failed_login(&self, client_identifier: &str) {
        let mut attempts = self.login_attempts.write().await;
        let now = SystemTime::now();

        let attempt_info = attempts.entry(client_identifier.to_string()).or_insert(LoginAttempt {
            attempts: 0,
            last_attempt: now,
            locked_until: None,
        });

        attempt_info.attempts += 1;
        attempt_info.last_attempt = now;

        if attempt_info.attempts >= MAX_LOGIN_ATTEMPTS {
            attempt_info.locked_until = Some(now + Duration::from_secs(LOCKOUT_DURATION_MINUTES * 60));
            warn!("Account locked due to too many failed attempts: {}", client_identifier);
        }
    }

    async fn reset_login_attempts(&self, client_identifier: &str) {
        let mut attempts = self.login_attempts.write().await;
        attempts.remove(client_identifier);
    }

    async fn authenticate_user_secure(&self, username_or_email: &str, password: &str) -> Result<AdminUser, AuthError> {
        let db = DbService::get_db();
        let collection = db.collection::<AdminUser>("admin_users");

        // Input validation
        if username_or_email.len() > 255 || password.len() > 1000 {
            return Err(AuthError::UserValidationError("Input too long".to_string()));
        }

        // Try to find user by username first, then by email
        let query = doc! {
            "$or": [
                { "username": username_or_email },
                { "email": username_or_email }
            ],
            "is_active": true
        };

        let user_option = timeout(
            DB_OPERATION_TIMEOUT,
            collection.find_one(query, None)
        ).await
        .map_err(|_| AuthError::Timeout)?
        .map_err(AuthError::DatabaseError)?;

        if let Some(user) = user_option {
            // Verify password with timing attack protection
            let password_valid = tokio::task::spawn_blocking({
                let password = password.to_string();
                let hash = user.password_hash.clone();
                move || verify(&password, &hash).unwrap_or(false)
            }).await
            .map_err(|_| AuthError::AuthenticationFailed("Password verification failed".to_string()))?;

            if password_valid {
                debug!("Authentication successful for user: {}", user.username);
                return Ok(user);
            }
        }

        // Constant time delay to prevent timing attacks
        tokio::time::sleep(Duration::from_millis(100)).await;
        Err(AuthError::AuthenticationFailed("Invalid credentials".to_string()))
    }

    async fn generate_secure_token(&self, user: &AdminUser) -> Result<(String, String), AuthError> {
        let now = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .map_err(|_| AuthError::TokenError("System time error".to_string()))?
            .as_secs() as usize;

        let session_id = Uuid::new_v4().to_string();
        let jti = Uuid::new_v4().to_string();

        let claims = Claims {
            sub: user.id.unwrap().to_hex(),
            username: user.username.clone(),
            role: format!("{:?}", user.role),
            exp: now + (JWT_EXPIRY_HOURS * 3600) as usize,
            iat: now,
            jti: jti.clone(),
            session_id: session_id.clone(),
        };

        let token = encode(
            &Header::default(),
            &claims,
            &EncodingKey::from_secret(self.jwt_secret.as_ref()),
        ).map_err(|e| AuthError::TokenError(format!("Token encoding failed: {}", e)))?;

        debug!("Generated secure token for user: {} with session: {}", user.username, session_id);
        Ok((token, session_id))
    }

    async fn verify_token_secure(&self, token: &str) -> Result<Claims, AuthError> {
        // Check if token is blacklisted
        let blacklist = self.token_blacklist.read().await;
        if blacklist.contains_key(token) {
            return Err(AuthError::TokenError("Token has been revoked".to_string()));
        }
        drop(blacklist);

        let mut validation = Validation::new(Algorithm::HS256);
        validation.validate_exp = true;
        validation.validate_nbf = true;
        validation.leeway = 60; // 1 minute leeway for clock skew

        let token_data = decode::<Claims>(
            token,
            &DecodingKey::from_secret(self.jwt_secret.as_ref()),
            &validation,
        ).map_err(|e| AuthError::TokenError(format!("Token validation failed: {}", e)))?;

        // Verify session is still active
        let sessions = self.active_sessions.read().await;
        if !sessions.contains_key(&token_data.claims.session_id) {
            return Err(AuthError::TokenError("Session has expired".to_string()));
        }

        Ok(token_data.claims)
    }

    async fn blacklist_token(&self, token: &str) -> Result<(), AuthError> {
        let mut blacklist = self.token_blacklist.write().await;
        blacklist.insert(token.to_string(), SystemTime::now());

        // Clean old entries (tokens expire after JWT_EXPIRY_HOURS)
        let cutoff = SystemTime::now() - Duration::from_secs(JWT_EXPIRY_HOURS * 3600 + 3600); // +1 hour buffer
        blacklist.retain(|_, timestamp| *timestamp > cutoff);

        debug!("Token blacklisted successfully");
        Ok(())
    }

    async fn track_active_session(&self, session_id: &str) -> Result<(), AuthError> {
        let mut sessions = self.active_sessions.write().await;
        sessions.insert(session_id.to_string(), SystemTime::now());

        // Update metrics
        let mut metrics = self.security_metrics.write().await;
        metrics.active_sessions = sessions.len() as u64;

        debug!("Active session tracked: {}", session_id);
        Ok(())
    }

    async fn remove_active_session(&self, session_id: &str) -> Result<(), AuthError> {
        let mut sessions = self.active_sessions.write().await;
        sessions.remove(session_id);

        // Update metrics
        let mut metrics = self.security_metrics.write().await;
        metrics.active_sessions = sessions.len() as u64;

        debug!("Active session removed: {}", session_id);
        Ok(())
    }

    async fn increment_successful_logins(&self) -> Result<(), AuthError> {
        let mut metrics = self.security_metrics.write().await;
        metrics.successful_logins += 1;
        Ok(())
    }

    async fn increment_failed_logins(&self) -> Result<(), AuthError> {
        let mut metrics = self.security_metrics.write().await;
        metrics.failed_logins += 1;
        Ok(())
    }

    async fn get_user_by_username(&self, username: &str) -> Result<AdminUser, BotError> {
        let db = DbService::get_db();
        let collection = db.collection::<AdminUser>("admin_users");

        collection
            .find_one(doc! { "username": username }, None)
            .await
            .map_err(|_| BotError::DatabaseError)?
            .ok_or(BotError::UserNotFound)
    }

    async fn update_last_login(&self, user: &AdminUser) -> Result<(), BotError> {
        let db = DbService::get_db();
        let collection = db.collection::<AdminUser>("admin_users");
        let now = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_secs();

        collection
            .update_one(
                doc! { "_id": user.id.unwrap() },
                doc! { "$set": { "last_login": now as i64 } },
                None,
            )
            .await
            .map_err(|_| BotError::DatabaseError)?;

        Ok(())
    }

    async fn change_user_password(&self, username: &str, current_password: &str, new_password: &str) -> Result<(), BotError> {
        let user = self.authenticate_user_secure(username, current_password).await
            .map_err(|_| BotError::AuthenticationFailed)?;
        let new_hash = hash(new_password, DEFAULT_COST).map_err(|_| BotError::PasswordHashFailed)?;

        let db = DbService::get_db();
        let collection = db.collection::<AdminUser>("admin_users");

        collection
            .update_one(
                doc! { "_id": user.id.unwrap() },
                doc! { "$set": { "password_hash": new_hash } },
                None,
            )
            .await
            .map_err(|_| BotError::DatabaseError)?;

        Ok(())
    }

    async fn create_new_admin(&self, create_req: CreateAdminRequest) -> Result<AdminUser, BotError> {
        let password_hash = hash(&create_req.password, DEFAULT_COST).map_err(|_| BotError::PasswordHashFailed)?;
        let now = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_secs();

        let admin_user = AdminUser {
            id: None,
            username: create_req.username,
            email: create_req.email,
            password_hash,
            role: create_req.role,
            created_at: now,
            last_login: None,
            is_active: true,
            created_by: None,
            updated_at: now,
        };

        let db = DbService::get_db();
        let collection = db.collection::<AdminUser>("admin_users");

        let result = collection
            .insert_one(&admin_user, None)
            .await
            .map_err(|_| BotError::DatabaseError)?;

        let mut created_user = admin_user;
        created_user.id = Some(result.inserted_id.as_object_id().unwrap());

        Ok(created_user)
    }
}

impl Clone for AuthController {
    fn clone(&self) -> Self {
        Self {
            config: self.config.clone(),
            jwt_secret: self.jwt_secret.clone(),
            login_attempts: self.login_attempts.clone(),
            active_sessions: self.active_sessions.clone(),
            token_blacklist: self.token_blacklist.clone(),
            security_metrics: self.security_metrics.clone(),
        }
    }
}
