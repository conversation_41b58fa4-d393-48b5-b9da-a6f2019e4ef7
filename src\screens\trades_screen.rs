use teloxide::types::{InlineKeyboardButton, InlineKeyboardMarkup};
use serde_json::json;
use crate::model::{UserData, BotError, Blockchain, Trade};
use crate::model::trade::TradeStatus;
use futures_util::TryStreamExt;
use crate::service::{BotService, DbService};
use mongodb::bson::doc;

pub async fn show_trades_screen(
    bot_service: &BotService,
    user_data: &UserData,
    message_id: i32,
    blockchain: Blockchain,
) -> Result<(), BotError> {
    println!("Showing active trades for {:?}", blockchain);

    let chat_id = user_data.chat_id();

    let filter = doc! {
        "user_id": chat_id,
        "blockchain": blockchain.to_string(),
        "status": "completed"
    };

    let trades_collection = DbService::get_trades_collection();
    let cursor = trades_collection.find(filter, None).await
        .map_err(|e| BotError::GeneralError(format!("Database error: {}", e)))?;

    let trades: Vec<Trade> = cursor.try_collect().await
        .map_err(|e| BotError::GeneralError(format!("Database error: {}", e)))?;
    let trades_text = if trades.is_empty() {
        format!(
            "🔄 <b>{} Active Trades</b>\n\n\
            👤 <b>User:</b> {}\n\n\
            <b>No active trades found.</b>\n\n\
            You can start trading by using the /scan command to scan a contract.",
            blockchain.to_string().to_uppercase(),
            user_data.display_name()
        )
    } else {
        let trades_list = trades.iter()
            .take(10)
            .map(|trade| {
                let amount_str = match blockchain {
                    Blockchain::BSC => format!("{} BNB", trade.price.as_ref().map(|p| p.to_string()).unwrap_or_else(|| "0".to_string())),
                    Blockchain::ETH => format!("{} ETH", trade.price.as_ref().map(|p| p.to_string()).unwrap_or_else(|| "0".to_string())),
                    Blockchain::BASE => format!("{} ETH", trade.price.as_ref().map(|p| p.to_string()).unwrap_or_else(|| "0".to_string())),
                    Blockchain::SOL => format!("{} SOL", trade.price.as_ref().map(|p| p.to_string()).unwrap_or_else(|| "0".to_string())),
                };

                let token_name = trade.token_name.clone().unwrap_or_else(|| {
                    trade.contract_address.as_ref()
                        .map(|addr| format!("Token {}...", &addr[..8.min(addr.len())]))
                        .unwrap_or_else(|| "Unknown Token".to_string())
                });
                format!(
                    "• <b>Token:</b> {}\n  <b>Contract:</b> <code>{}</code>\n  <b>Amount:</b> {}\n  <b>Status:</b> {}",
                    token_name,
                    trade.contract_address.as_ref().unwrap_or(&"Unknown".to_string()),
                    amount_str,
                    match trade.status.as_ref().map(|s| s.as_str()) {
                        Some("pending") => "⏳ Pending",
                        Some("completed") => "✅ Completed",
                        Some("failed") => "❌ Failed",
                        Some("cancelled") => "🚫 Cancelled",
                        _ => "❓ Unknown",
                    }
                )
            })
            .collect::<Vec<String>>()
            .join("\n\n");

        format!(
            "🔄 <b>{} Active Trades</b>\n\n\
            👤 <b>User:</b> {}\n\n\
            <b>Recent Trades:</b>\n\n\
            {}\n\n\
            <i>Showing most recent {} trades</i>",
            blockchain.to_string().to_uppercase(),
            user_data.display_name(),
            trades_list,
            trades.len().min(10)
        )
    };

    let keyboard = create_trades_keyboard(blockchain);
    bot_service.edit_message_with_keyboard(
        user_data.chat_id(),
        message_id,
        &trades_text,
        keyboard,
    ).await?;

    Ok(())
}

fn create_trades_keyboard(blockchain: Blockchain) -> InlineKeyboardMarkup {
    let keyboard = vec![
        vec![
            InlineKeyboardButton::callback(
                "🔄 Refresh Trades".to_string(),
                json!({
                    "command": format!("refresh_trades_{}", blockchain)
                }).to_string(),
            )
        ],
        vec![
            InlineKeyboardButton::callback(
                "◀️ Back to Dashboard".to_string(),
                json!({
                    "command": format!("view_{}", blockchain.as_str())
                }).to_string(),
            )
        ]
    ];

    InlineKeyboardMarkup::new(keyboard)
}
