import{r,j as e,e as G,a as I,B as W}from"./index-dCUkEeO4.js";import{C as m}from"./Card-CLlE15Sf.js";import{L as de,B as me,D as xe}from"./auto-BiC8v7eM.js";import"./adminApi-BFZ8qr13.js";import{F as _}from"./UserIcon-DIEnBeEF.js";import{F as V}from"./ArrowUpIcon-D56z6YfI.js";import{F as he}from"./ClockIcon-BpyFwEBR.js";import{F as ge,a as pe}from"./MagnifyingGlassIcon-C56FJ4Gh.js";import{F as N}from"./ChevronDownIcon-CFiNowBg.js";function ue({title:b,titleId:f,...g},w){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:w,"aria-labelledby":f},g),b?r.createElement("title",{id:f},b):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m4.5 15.75 7.5-7.5 7.5 7.5"}))}const k=r.forwardRef(ue);function fe({title:b,titleId:f,...g},w){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:w,"aria-labelledby":f},g),b?r.createElement("title",{id:f},b):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m.75 12 3 3m0 0 3-3m-3 3v-6m-1.5-9H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z"}))}const je=r.forwardRef(fe),Ne={responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"top",labels:{color:"#e5e7eb"}},tooltip:{mode:"index",intersect:!1}},scales:{y:{grid:{color:"rgba(255, 255, 255, 0.1)"},ticks:{color:"#e5e7eb"}},x:{grid:{color:"rgba(255, 255, 255, 0.1)"},ticks:{color:"#e5e7eb"}}}},ye={responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"top",labels:{color:"#e5e7eb"}},tooltip:{mode:"index",intersect:!1}},scales:{y:{grid:{color:"rgba(255, 255, 255, 0.1)"},ticks:{color:"#e5e7eb"}},x:{grid:{color:"rgba(255, 255, 255, 0.1)"},ticks:{color:"#e5e7eb"}}}},be={responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"right",labels:{color:"#e5e7eb"}}}};function Le(){const[b,f]=r.useState(!0),[g,w]=r.useState("7d"),[D,T]=r.useState("overview"),[A,H]=r.useState(""),[x,J]=r.useState("totalTransactions"),[o,P]=r.useState("desc"),[l,z]=r.useState(null),[p,Q]=r.useState([]),[q,L]=r.useState(!1),[h,Y]=r.useState(1),[F]=r.useState(10),[j,Z]=r.useState({totalUsers:0,activeUsers:0,newUsers:0,userGrowth:0,retentionRate:0,averageSessionTime:0,transactionsPerUser:0,totalTransactionVolume:0,avgProfitLoss:0}),[K,X]=r.useState([]),[ee,se]=r.useState({labels:[],datasets:[{label:"New Users",data:[],borderColor:"rgb(99, 102, 241)",backgroundColor:"rgba(99, 102, 241, 0.5)"},{label:"Active Users",data:[],borderColor:"rgb(34, 197, 94)",backgroundColor:"rgba(34, 197, 94, 0.5)"}]}),[te,ae]=r.useState({labels:[],datasets:[{label:"User Activity",data:[],backgroundColor:"rgba(99, 102, 241, 0.8)"}]}),[re,ie]=r.useState({labels:[],datasets:[{label:"User Locations",data:[],backgroundColor:["rgba(99, 102, 241, 0.8)","rgba(34, 197, 94, 0.8)","rgba(234, 88, 12, 0.8)","rgba(217, 70, 239, 0.8)","rgba(59, 130, 246, 0.8)"],borderWidth:1}]});r.useEffect(()=>{(async()=>{f(!0);try{setTimeout(()=>{Z({totalUsers:1250,activeUsers:876,newUsers:124,userGrowth:12.4,retentionRate:78.5,averageSessionTime:8.3,transactionsPerUser:3.2,totalTransactionVolume:24567895e-1,avgProfitLoss:324.75});const t=g==="7d"?["Mon","Tue","Wed","Thu","Fri","Sat","Sun"]:g==="30d"?Array.from({length:30},(i,d)=>`Day ${d+1}`):["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"];se({labels:t,datasets:[{label:"New Users",data:Array.from({length:t.length},()=>Math.floor(Math.random()*50)+10),borderColor:"rgb(99, 102, 241)",backgroundColor:"rgba(99, 102, 241, 0.5)"},{label:"Active Users",data:Array.from({length:t.length},()=>Math.floor(Math.random()*200)+100),borderColor:"rgb(34, 197, 94)",backgroundColor:"rgba(34, 197, 94, 0.5)"}]}),ae({labels:["00:00","04:00","08:00","12:00","16:00","20:00"],datasets:[{label:"User Activity",data:[65,40,80,120,150,90],backgroundColor:"rgba(99, 102, 241, 0.8)"}]}),ie({labels:["United States","Europe","Asia","Africa","Others"],datasets:[{label:"User Locations",data:[35,25,20,10,10],backgroundColor:["rgba(99, 102, 241, 0.8)","rgba(34, 197, 94, 0.8)","rgba(234, 88, 12, 0.8)","rgba(217, 70, 239, 0.8)","rgba(59, 130, 246, 0.8)"],borderWidth:1}]});const a=Array.from({length:50},(i,d)=>{const c=Math.random()>.2,n=Math.random()*1e4+500,S=Math.random()*2e3-1e3;return{id:`user-${d+1}`,chatId:1e6+d,username:`user${d+1}`,firstName:`User ${d+1}`,lastName:Math.random()>.3?`LastName ${d+1}`:void 0,joinedDate:new Date(Date.now()-Math.random()*1e10).toISOString(),lastActive:c?new Date(Date.now()-Math.random()*1e6).toISOString():new Date(Date.now()-Math.random()*1e10).toISOString(),status:c?"active":"inactive",totalTransactions:Math.floor(Math.random()*50)+1,transactionVolume:parseFloat(n.toFixed(2)),profitLoss:parseFloat(S.toFixed(2)),activeBots:Math.floor(Math.random()*3)}});X(a),f(!1)},800)}catch(t){console.error("Error fetching user analytics:",t),f(!1)}})()},[g]);const ne=async s=>{const t=Array.from({length:15},(a,i)=>{const d=Math.random()>.5,c=Math.random()*2+.1,n=Math.random()*5e4+1e4,S=c*n,oe=S*.002;return{id:`tx-${i+1}`,userId:s,botId:`bot-${Math.floor(Math.random()*3)+1}`,botName:`Trading Bot ${Math.floor(Math.random()*3)+1}`,type:d?"buy":"sell",asset:["BTC","ETH","SOL","DOGE"][Math.floor(Math.random()*4)],amount:parseFloat(c.toFixed(6)),price:parseFloat(n.toFixed(2)),total:parseFloat(S.toFixed(2)),fee:parseFloat(oe.toFixed(2)),status:Math.random()>.9?"pending":Math.random()>.95?"failed":"completed",timestamp:new Date(Date.now()-Math.random()*30*24*60*60*1e3).toISOString(),blockchain:["Ethereum","Bitcoin","Solana"][Math.floor(Math.random()*3)]}});t.sort((a,i)=>new Date(i.timestamp).getTime()-new Date(a.timestamp).getTime()),Q(t)},le=s=>{z(s),ne(s.id),L(!0)},v=s=>{x===s?P(o==="asc"?"desc":"asc"):(J(s),P("desc"))},U=s=>{const t=new Date(s);return new Intl.DateTimeFormat("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}).format(t)},u=s=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD",minimumFractionDigits:2,maximumFractionDigits:2}).format(s),M=s=>new Intl.NumberFormat("en-US").format(s),C=K.filter(s=>{if(!A)return!0;const t=A.toLowerCase();return s.username.toLowerCase().includes(t)||s.firstName.toLowerCase().includes(t)||s.lastName&&s.lastName.toLowerCase().includes(t)||s.chatId.toString().includes(t)}),O=[...C].sort((s,t)=>{const a=s[x],i=t[x];if(typeof a=="string"&&typeof i=="string"){const d=a.toLowerCase(),c=i.toLowerCase();return d<c?o==="asc"?-1:1:d>c?o==="asc"?1:-1:0}return x==="joinedDate"||x==="lastActive"?a&&i?o==="asc"?new Date(a).getTime()-new Date(i).getTime():new Date(i).getTime()-new Date(a).getTime():0:typeof a=="number"&&typeof i=="number"?o==="asc"?a-i:i-a:a===void 0&&i!==void 0?o==="asc"?-1:1:a!==void 0&&i===void 0?o==="asc"?1:-1:0}),$=h*F,B=$-F,E=O.slice(B,$),y=Math.ceil(O.length/F),R=s=>Y(s),ce=()=>{const s=["Chat ID","Username","First Name","Last Name","Joined Date","Last Active","Status","Total Transactions","Transaction Volume","Profit/Loss","Active Bots"],t=C.map(n=>[n.chatId,n.username,n.firstName,n.lastName||"",U(n.joinedDate),U(n.lastActive),n.status,n.totalTransactions,n.transactionVolume.toFixed(2),n.profitLoss.toFixed(2),n.activeBots]),a=[s.join(","),...t.map(n=>n.join(","))].join(`
`),i=new Blob([a],{type:"text/csv;charset=utf-8;"}),d=URL.createObjectURL(i),c=document.createElement("a");c.setAttribute("href",d),c.setAttribute("download",`easybot_users_${new Date().toISOString().split("T")[0]}.csv`),c.style.visibility="hidden",document.body.appendChild(c),c.click(),document.body.removeChild(c)};return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold text-white",children:"User Analytics"}),e.jsx("p",{className:"mt-1 text-sm text-gray-400",children:"Insights and statistics about your Telegram bot users"})]}),e.jsxs("div",{className:"flex flex-wrap gap-2",children:[e.jsxs("div",{className:"flex space-x-2",children:[e.jsx("button",{onClick:()=>w("7d"),className:`px-3 py-1 rounded-md text-sm font-medium ${g==="7d"?"bg-indigo-600 text-white":"bg-gray-700 text-gray-300 hover:bg-gray-600"}`,children:"7 Days"}),e.jsx("button",{onClick:()=>w("30d"),className:`px-3 py-1 rounded-md text-sm font-medium ${g==="30d"?"bg-indigo-600 text-white":"bg-gray-700 text-gray-300 hover:bg-gray-600"}`,children:"30 Days"}),e.jsx("button",{onClick:()=>w("1y"),className:`px-3 py-1 rounded-md text-sm font-medium ${g==="1y"?"bg-indigo-600 text-white":"bg-gray-700 text-gray-300 hover:bg-gray-600"}`,children:"1 Year"})]}),e.jsxs("div",{className:"flex space-x-2",children:[e.jsx("button",{onClick:()=>T("overview"),className:`px-3 py-1 rounded-md text-sm font-medium ${D==="overview"?"bg-indigo-600 text-white":"bg-gray-700 text-gray-300 hover:bg-gray-600"}`,children:"Overview"}),e.jsx("button",{onClick:()=>T("users"),className:`px-3 py-1 rounded-md text-sm font-medium ${D==="users"?"bg-indigo-600 text-white":"bg-gray-700 text-gray-300 hover:bg-gray-600"}`,children:"User List"})]})]})]}),b?e.jsx("div",{className:"flex justify-center items-center h-64",children:e.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"})}):e.jsx(e.Fragment,{children:D==="overview"?e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4",children:[e.jsx(m,{className:"p-4",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"flex-shrink-0 p-3 rounded-md bg-indigo-500/20",children:e.jsx(G,{className:"h-6 w-6 text-indigo-400"})}),e.jsxs("div",{className:"ml-4",children:[e.jsx("h3",{className:"text-sm font-medium text-gray-400",children:"Total Users"}),e.jsx("p",{className:"text-2xl font-semibold text-white",children:M(j.totalUsers)})]})]})}),e.jsx(m,{className:"p-4",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"flex-shrink-0 p-3 rounded-md bg-green-500/20",children:e.jsx(_,{className:"h-6 w-6 text-green-400"})}),e.jsxs("div",{className:"ml-4",children:[e.jsx("h3",{className:"text-sm font-medium text-gray-400",children:"Active Users"}),e.jsxs("div",{className:"flex items-center",children:[e.jsx("p",{className:"text-2xl font-semibold text-white",children:M(j.activeUsers)}),e.jsxs("span",{className:"ml-2 text-xs font-medium text-green-400 flex items-center",children:[e.jsx(V,{className:"h-3 w-3 mr-0.5"}),j.userGrowth,"%"]})]})]})]})}),e.jsx(m,{className:"p-4",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"flex-shrink-0 p-3 rounded-md bg-blue-500/20",children:e.jsx(I,{className:"h-6 w-6 text-blue-400"})}),e.jsxs("div",{className:"ml-4",children:[e.jsx("h3",{className:"text-sm font-medium text-gray-400",children:"Transaction Volume"}),e.jsx("p",{className:"text-2xl font-semibold text-white",children:u(j.totalTransactionVolume)})]})]})}),e.jsx(m,{className:"p-4",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"flex-shrink-0 p-3 rounded-md bg-orange-500/20",children:e.jsx(I,{className:"h-6 w-6 text-orange-400"})}),e.jsxs("div",{className:"ml-4",children:[e.jsx("h3",{className:"text-sm font-medium text-gray-400",children:"Avg. Profit/Loss"}),e.jsxs("div",{className:"flex items-center",children:[e.jsx("p",{className:"text-2xl font-semibold text-white",children:u(j.avgProfitLoss)}),e.jsxs("span",{className:"ml-2 text-xs font-medium text-green-400 flex items-center",children:[e.jsx(V,{className:"h-3 w-3 mr-0.5"}),"8.2%"]})]})]})]})})]}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[e.jsxs(m,{className:"p-4",children:[e.jsx("h3",{className:"text-lg font-medium text-white mb-4",children:"User Growth"}),e.jsx("div",{className:"h-80",children:e.jsx(de,{options:Ne,data:ee})})]}),e.jsxs(m,{className:"p-4",children:[e.jsx("h3",{className:"text-lg font-medium text-white mb-4",children:"User Activity by Hour"}),e.jsx("div",{className:"h-80",children:e.jsx(me,{options:ye,data:te})})]}),e.jsxs(m,{className:"p-4",children:[e.jsx("h3",{className:"text-lg font-medium text-white mb-4",children:"User Locations"}),e.jsx("div",{className:"h-80",children:e.jsx(xe,{options:be,data:re})})]}),e.jsxs(m,{className:"p-4",children:[e.jsx("h3",{className:"text-lg font-medium text-white mb-4",children:"User Retention"}),e.jsx("div",{className:"flex items-center justify-center h-80",children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"inline-flex items-center justify-center p-6 rounded-full bg-indigo-500/20 mb-4",children:e.jsxs("span",{className:"text-4xl font-bold text-white",children:[j.retentionRate,"%"]})}),e.jsx("p",{className:"text-gray-400",children:"User Retention Rate"}),e.jsx("p",{className:"mt-2 text-sm text-gray-500",children:"Percentage of users who return within 7 days"})]})})]})]}),e.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4",children:[e.jsx(m,{className:"p-4",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"flex-shrink-0 p-3 rounded-md bg-purple-500/20",children:e.jsx(he,{className:"h-6 w-6 text-purple-400"})}),e.jsxs("div",{className:"ml-4",children:[e.jsx("h3",{className:"text-sm font-medium text-gray-400",children:"Avg. Session Time"}),e.jsxs("p",{className:"text-2xl font-semibold text-white",children:[j.averageSessionTime," min"]})]})]})}),e.jsx(m,{className:"p-4",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"flex-shrink-0 p-3 rounded-md bg-pink-500/20",children:e.jsx(I,{className:"h-6 w-6 text-pink-400"})}),e.jsxs("div",{className:"ml-4",children:[e.jsx("h3",{className:"text-sm font-medium text-gray-400",children:"Transactions/User"}),e.jsx("p",{className:"text-2xl font-semibold text-white",children:j.transactionsPerUser})]})]})}),e.jsx(m,{className:"p-4",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"flex-shrink-0 p-3 rounded-md bg-yellow-500/20",children:e.jsx(_,{className:"h-6 w-6 text-yellow-400"})}),e.jsxs("div",{className:"ml-4",children:[e.jsx("h3",{className:"text-sm font-medium text-gray-400",children:"New Users"}),e.jsxs("div",{className:"flex items-center",children:[e.jsx("p",{className:"text-2xl font-semibold text-white",children:M(j.newUsers)}),e.jsxs("span",{className:"ml-2 text-xs font-medium text-green-400 flex items-center",children:[e.jsx(V,{className:"h-3 w-3 mr-0.5"}),"5.3%"]})]})]})]})}),e.jsx(m,{className:"p-4 flex items-center justify-center",children:e.jsx(W,{variant:"primary",onClick:()=>T("users"),icon:e.jsx(G,{className:"h-5 w-5"}),children:"View User List"})})]})]}):e.jsx(e.Fragment,{children:e.jsxs(m,{className:"p-4",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 gap-4",children:[e.jsx("h3",{className:"text-lg font-medium text-white",children:"User List"}),e.jsxs("div",{className:"flex flex-col sm:flex-row gap-4",children:[e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:e.jsx(ge,{className:"h-5 w-5 text-gray-400"})}),e.jsx("input",{type:"text",placeholder:"Search users...",className:"block w-full pl-10 pr-3 py-2 border border-gray-700 rounded-md leading-5 bg-gray-800 text-gray-300 placeholder-gray-400 focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm",value:A,onChange:s=>H(s.target.value)})]}),e.jsx(W,{variant:"outline",onClick:ce,icon:e.jsx(je,{className:"h-5 w-5"}),children:"Export Data"})]})]}),e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:"min-w-full divide-y divide-gray-700",children:[e.jsx("thead",{className:"bg-gray-800",children:e.jsxs("tr",{children:[e.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider cursor-pointer",onClick:()=>v("username"),children:e.jsxs("div",{className:"flex items-center",children:["Username",x==="username"&&(o==="asc"?e.jsx(k,{className:"h-4 w-4 ml-1"}):e.jsx(N,{className:"h-4 w-4 ml-1"}))]})}),e.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider cursor-pointer",onClick:()=>v("chatId"),children:e.jsxs("div",{className:"flex items-center",children:["Chat ID",x==="chatId"&&(o==="asc"?e.jsx(k,{className:"h-4 w-4 ml-1"}):e.jsx(N,{className:"h-4 w-4 ml-1"}))]})}),e.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider cursor-pointer",onClick:()=>v("lastActive"),children:e.jsxs("div",{className:"flex items-center",children:["Last Active",x==="lastActive"&&(o==="asc"?e.jsx(k,{className:"h-4 w-4 ml-1"}):e.jsx(N,{className:"h-4 w-4 ml-1"}))]})}),e.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider cursor-pointer",onClick:()=>v("status"),children:e.jsxs("div",{className:"flex items-center",children:["Status",x==="status"&&(o==="asc"?e.jsx(k,{className:"h-4 w-4 ml-1"}):e.jsx(N,{className:"h-4 w-4 ml-1"}))]})}),e.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider cursor-pointer",onClick:()=>v("totalTransactions"),children:e.jsxs("div",{className:"flex items-center",children:["Transactions",x==="totalTransactions"&&(o==="asc"?e.jsx(k,{className:"h-4 w-4 ml-1"}):e.jsx(N,{className:"h-4 w-4 ml-1"}))]})}),e.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider cursor-pointer",onClick:()=>v("transactionVolume"),children:e.jsxs("div",{className:"flex items-center",children:["Volume",x==="transactionVolume"&&(o==="asc"?e.jsx(k,{className:"h-4 w-4 ml-1"}):e.jsx(N,{className:"h-4 w-4 ml-1"}))]})}),e.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider cursor-pointer",onClick:()=>v("profitLoss"),children:e.jsxs("div",{className:"flex items-center",children:["Profit/Loss",x==="profitLoss"&&(o==="asc"?e.jsx(k,{className:"h-4 w-4 ml-1"}):e.jsx(N,{className:"h-4 w-4 ml-1"}))]})}),e.jsx("th",{scope:"col",className:"px-6 py-3 text-right text-xs font-medium text-gray-300 uppercase tracking-wider",children:"Actions"})]})}),e.jsx("tbody",{className:"divide-y divide-gray-700 bg-gray-800/50",children:E.length===0?e.jsx("tr",{children:e.jsx("td",{colSpan:8,className:"px-6 py-4 text-center text-sm text-gray-400",children:"No users found"})}):E.map(s=>e.jsxs("tr",{className:"hover:bg-gray-700/30 transition-colors duration-150",children:[e.jsxs("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-white",children:[s.username,e.jsxs("div",{className:"text-xs text-gray-400",children:[s.firstName," ",s.lastName]})]}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-300",children:s.chatId}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-300",children:U(s.lastActive)}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-300",children:e.jsx("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${s.status==="active"?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:s.status==="active"?"Active":"Inactive"})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-300",children:s.totalTransactions}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-300",children:u(s.transactionVolume)}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm",children:e.jsx("span",{className:s.profitLoss>=0?"text-green-400":"text-red-400",children:u(s.profitLoss)})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium",children:e.jsx("button",{onClick:()=>le(s),className:"text-indigo-400 hover:text-indigo-300 transition-colors duration-150",children:e.jsx(pe,{className:"h-5 w-5"})})})]},s.id))})]})}),y>1&&e.jsx("div",{className:"flex items-center justify-between border-t border-gray-700 px-4 py-3 sm:px-6 mt-4",children:e.jsxs("div",{className:"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between",children:[e.jsx("div",{children:e.jsxs("p",{className:"text-sm text-gray-400",children:["Showing ",e.jsx("span",{className:"font-medium",children:B+1})," to"," ",e.jsx("span",{className:"font-medium",children:Math.min($,C.length)})," ","of ",e.jsx("span",{className:"font-medium",children:C.length})," results"]})}),e.jsx("div",{children:e.jsxs("nav",{className:"relative z-0 inline-flex rounded-md shadow-sm -space-x-px","aria-label":"Pagination",children:[e.jsxs("button",{onClick:()=>R(Math.max(1,h-1)),disabled:h===1,className:`relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-700 bg-gray-800 text-sm font-medium ${h===1?"text-gray-500 cursor-not-allowed":"text-gray-300 hover:bg-gray-700"}`,children:[e.jsx("span",{className:"sr-only",children:"Previous"}),e.jsx(N,{className:"h-5 w-5 rotate-90","aria-hidden":"true"})]}),Array.from({length:Math.min(5,y)},(s,t)=>{let a;return y<=5||h<=3?a=t+1:h>=y-2?a=y-4+t:a=h-2+t,e.jsx("button",{onClick:()=>R(a),className:`relative inline-flex items-center px-4 py-2 border border-gray-700 text-sm font-medium ${h===a?"z-10 bg-indigo-600 border-indigo-500 text-white":"bg-gray-800 text-gray-300 hover:bg-gray-700"}`,children:a},a)}),e.jsxs("button",{onClick:()=>R(Math.min(y,h+1)),disabled:h===y,className:`relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-700 bg-gray-800 text-sm font-medium ${h===y?"text-gray-500 cursor-not-allowed":"text-gray-300 hover:bg-gray-700"}`,children:[e.jsx("span",{className:"sr-only",children:"Next"}),e.jsx(N,{className:"h-5 w-5 -rotate-90","aria-hidden":"true"})]})]})})]})})]})})}),q&&l&&e.jsx("div",{className:"fixed inset-0 z-50 overflow-y-auto",children:e.jsxs("div",{className:"flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0",children:[e.jsx("div",{className:"fixed inset-0 transition-opacity","aria-hidden":"true",children:e.jsx("div",{className:"absolute inset-0 bg-gray-900 opacity-75"})}),e.jsx("span",{className:"hidden sm:inline-block sm:align-middle sm:h-screen","aria-hidden":"true",children:"​"}),e.jsxs("div",{className:"inline-block align-bottom bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-5xl sm:w-full",children:[e.jsx("div",{className:"bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4",children:e.jsx("div",{className:"sm:flex sm:items-start",children:e.jsxs("div",{className:"mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full",children:[e.jsxs("div",{className:"flex justify-between items-center mb-4",children:[e.jsxs("h3",{className:"text-lg leading-6 font-medium text-white",children:["User Details: ",l.username]}),e.jsxs("button",{type:"button",className:"bg-gray-800 rounded-md text-gray-400 hover:text-white focus:outline-none",onClick:()=>L(!1),children:[e.jsx("span",{className:"sr-only",children:"Close"}),e.jsx("svg",{className:"h-6 w-6",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor","aria-hidden":"true",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M6 18L18 6M6 6l12 12"})})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-6",children:[e.jsxs("div",{className:"bg-gray-700/30 rounded-lg p-4",children:[e.jsx("h4",{className:"text-sm font-medium text-gray-400 mb-2",children:"User Information"}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-sm text-gray-400",children:"Chat ID:"}),e.jsx("span",{className:"text-sm text-white",children:l.chatId})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-sm text-gray-400",children:"Name:"}),e.jsxs("span",{className:"text-sm text-white",children:[l.firstName," ",l.lastName]})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-sm text-gray-400",children:"Joined:"}),e.jsx("span",{className:"text-sm text-white",children:U(l.joinedDate)})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-sm text-gray-400",children:"Last Active:"}),e.jsx("span",{className:"text-sm text-white",children:U(l.lastActive)})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-sm text-gray-400",children:"Status:"}),e.jsx("span",{className:`text-sm ${l.status==="active"?"text-green-400":"text-red-400"}`,children:l.status==="active"?"Active":"Inactive"})]})]})]}),e.jsxs("div",{className:"bg-gray-700/30 rounded-lg p-4",children:[e.jsx("h4",{className:"text-sm font-medium text-gray-400 mb-2",children:"Transaction Summary"}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-sm text-gray-400",children:"Total Transactions:"}),e.jsx("span",{className:"text-sm text-white",children:l.totalTransactions})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-sm text-gray-400",children:"Transaction Volume:"}),e.jsx("span",{className:"text-sm text-white",children:u(l.transactionVolume)})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-sm text-gray-400",children:"Profit/Loss:"}),e.jsx("span",{className:`text-sm ${l.profitLoss>=0?"text-green-400":"text-red-400"}`,children:u(l.profitLoss)})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-sm text-gray-400",children:"Active Bots:"}),e.jsx("span",{className:"text-sm text-white",children:l.activeBots})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-sm text-gray-400",children:"Avg. Transaction Size:"}),e.jsx("span",{className:"text-sm text-white",children:l.totalTransactions>0?u(l.transactionVolume/l.totalTransactions):"$0.00"})]})]})]})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("h4",{className:"text-sm font-medium text-gray-400 mb-2",children:"Transaction History"}),e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:"min-w-full divide-y divide-gray-700",children:[e.jsx("thead",{className:"bg-gray-700/50",children:e.jsxs("tr",{children:[e.jsx("th",{scope:"col",className:"px-4 py-2 text-left text-xs font-medium text-gray-300 uppercase tracking-wider",children:"Date"}),e.jsx("th",{scope:"col",className:"px-4 py-2 text-left text-xs font-medium text-gray-300 uppercase tracking-wider",children:"Bot"}),e.jsx("th",{scope:"col",className:"px-4 py-2 text-left text-xs font-medium text-gray-300 uppercase tracking-wider",children:"Type"}),e.jsx("th",{scope:"col",className:"px-4 py-2 text-left text-xs font-medium text-gray-300 uppercase tracking-wider",children:"Asset"}),e.jsx("th",{scope:"col",className:"px-4 py-2 text-left text-xs font-medium text-gray-300 uppercase tracking-wider",children:"Amount"}),e.jsx("th",{scope:"col",className:"px-4 py-2 text-left text-xs font-medium text-gray-300 uppercase tracking-wider",children:"Price"}),e.jsx("th",{scope:"col",className:"px-4 py-2 text-left text-xs font-medium text-gray-300 uppercase tracking-wider",children:"Total"}),e.jsx("th",{scope:"col",className:"px-4 py-2 text-left text-xs font-medium text-gray-300 uppercase tracking-wider",children:"Status"})]})}),e.jsx("tbody",{className:"divide-y divide-gray-700 bg-gray-800/30",children:p.length===0?e.jsx("tr",{children:e.jsx("td",{colSpan:8,className:"px-4 py-2 text-center text-sm text-gray-400",children:"No transactions found"})}):p.map(s=>e.jsxs("tr",{className:"hover:bg-gray-700/30 transition-colors duration-150",children:[e.jsx("td",{className:"px-4 py-2 whitespace-nowrap text-xs text-gray-300",children:U(s.timestamp)}),e.jsx("td",{className:"px-4 py-2 whitespace-nowrap text-xs text-gray-300",children:s.botName}),e.jsx("td",{className:"px-4 py-2 whitespace-nowrap text-xs",children:e.jsx("span",{className:`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${s.type==="buy"?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:s.type.toUpperCase()})}),e.jsx("td",{className:"px-4 py-2 whitespace-nowrap text-xs text-gray-300",children:s.asset}),e.jsx("td",{className:"px-4 py-2 whitespace-nowrap text-xs text-gray-300",children:s.amount.toFixed(6)}),e.jsx("td",{className:"px-4 py-2 whitespace-nowrap text-xs text-gray-300",children:u(s.price)}),e.jsx("td",{className:"px-4 py-2 whitespace-nowrap text-xs text-gray-300",children:u(s.total)}),e.jsx("td",{className:"px-4 py-2 whitespace-nowrap text-xs",children:e.jsx("span",{className:`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${s.status==="completed"?"bg-green-100 text-green-800":s.status==="pending"?"bg-yellow-100 text-yellow-800":"bg-red-100 text-red-800"}`,children:s.status.toUpperCase()})})]},s.id))})]})})]}),e.jsxs("div",{className:"bg-gray-700/30 rounded-lg p-4",children:[e.jsx("h4",{className:"text-sm font-medium text-gray-400 mb-2",children:"Performance Metrics"}),e.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-3 gap-4",children:[e.jsx("div",{className:"bg-gray-800/50 rounded-lg p-3",children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-xs text-gray-400 mb-1",children:"Success Rate"}),e.jsx("div",{className:"text-xl font-semibold text-white",children:p.length>0?`${Math.round(p.filter(s=>s.status==="completed").length/p.length*100)}%`:"0%"})]})}),e.jsx("div",{className:"bg-gray-800/50 rounded-lg p-3",children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-xs text-gray-400 mb-1",children:"Avg. Transaction Fee"}),e.jsx("div",{className:"text-xl font-semibold text-white",children:p.length>0?u(p.reduce((s,t)=>s+t.fee,0)/p.length):"$0.00"})]})}),e.jsx("div",{className:"bg-gray-800/50 rounded-lg p-3",children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-xs text-gray-400 mb-1",children:"Preferred Asset"}),e.jsx("div",{className:"text-xl font-semibold text-white",children:p.length>0?(()=>{const s=p.reduce((t,a)=>(t[a.asset]=(t[a.asset]||0)+1,t),{});return Object.entries(s).sort((t,a)=>a[1]-t[1])[0][0]})():"N/A"})]})})]})]})]})})}),e.jsxs("div",{className:"bg-gray-800 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse",children:[e.jsx("button",{type:"button",onClick:()=>L(!1),className:"w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-indigo-600 text-base font-medium text-white hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:ml-3 sm:w-auto sm:text-sm",children:"Close"}),e.jsx("button",{type:"button",onClick:()=>{alert("Export functionality would be implemented here")},className:"mt-3 w-full inline-flex justify-center rounded-md border border-gray-600 shadow-sm px-4 py-2 bg-gray-700 text-base font-medium text-gray-300 hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm",children:"Export User Data"})]})]})]})})]})}export{Le as default};
