import bs58 from 'bs58';

/**
 * Convert Base58 Solana address to hex format
 * @param base58Address - The Base58 encoded Solana address
 * @returns Hex formatted address (0x...)
 */
export function base58ToHex(base58Address: string): string {
  try {
    // Decode Base58 to bytes
    const bytes = bs58.decode(base58Address);
    
    // Convert bytes to hex string
    const hex = Array.from(bytes)
      .map(byte => byte.toString(16).padStart(2, '0'))
      .join('');
    
    return `0x${hex}`;
  } catch (error) {
    throw new Error(`Invalid Base58 address: ${error}`);
  }
}

/**
 * Convert hex format to Base58 Solana address
 * @param hexAddress - The hex formatted address (0x...)
 * @returns Base58 encoded Solana address
 */
export function hexToBase58(hexAddress: string): string {
  try {
    // Remove 0x prefix if present
    const hex = hexAddress.startsWith('0x') ? hexAddress.slice(2) : hexAddress;
    
    // Convert hex to bytes
    const bytes = new Uint8Array(hex.match(/.{1,2}/g)!.map(byte => parseInt(byte, 16)));
    
    // Encode bytes to Base58
    return bs58.encode(bytes);
  } catch (error) {
    throw new Error(`Invalid hex address: ${error}`);
  }
}

/**
 * Validate if a string is a valid Base58 Solana address
 * @param address - The address to validate
 * @returns true if valid Base58 address
 */
export function isValidBase58Address(address: string): boolean {
  try {
    const decoded = bs58.decode(address);
    return decoded.length === 32; // Solana addresses are 32 bytes
  } catch {
    return false;
  }
}

/**
 * Validate if a string is a valid hex Solana address
 * @param address - The address to validate
 * @returns true if valid hex address
 */
export function isValidHexAddress(address: string): boolean {
  try {
    const hex = address.startsWith('0x') ? address.slice(2) : address;
    return hex.length === 64 && /^[0-9a-fA-F]+$/.test(hex);
  } catch {
    return false;
  }
}
