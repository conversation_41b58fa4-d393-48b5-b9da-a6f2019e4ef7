use reqwest::Client;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::time::{Duration, SystemTime, UNIX_EPOCH};
use tokio::sync::RwLock;
use tracing::{error, info, warn};
use crate::model::Blockchain;

const COINGECKO_API_URL: &str = "https://api.coingecko.com/api/v3/simple/price";
const CACHE_DURATION_SECONDS: u64 = 300; // 5 minutes
const MIN_FEE_USD: f64 = 0.25; // $0.25 minimum fee

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PriceData {
    pub price_usd: f64,
    pub last_updated: u64,
}

#[derive(Debug)]
pub struct PriceService {
    client: Client,
    cache: RwLock<HashMap<String, PriceData>>,
}

impl PriceService {
    pub fn new() -> Self {
        Self {
            client: Client::new(),
            cache: RwLock::new(HashMap::new()),
        }
    }

    /// Get current price for a blockchain's native token using DexScreener with CoinGecko fallback
    pub async fn get_native_token_price(&self, blockchain: &Blockchain) -> Result<f64, Box<dyn std::error::Error + Send + Sync>> {
        let now = SystemTime::now().duration_since(UNIX_EPOCH)?.as_secs();
        let cache_key = format!("native_price_{:?}", blockchain);

        // Check cache first (2 minute cache for native token prices)
        {
            let cache = self.cache.read().await;
            if let Some(price_data) = cache.get(&cache_key) {
                if now - price_data.last_updated < 120 { // 2 minutes cache
                    info!("Using cached native price for {:?}: ${:.4}", blockchain, price_data.price_usd);
                    return Ok(price_data.price_usd);
                }
            }
        }

        // Try DexScreener API first for real-time prices
        let token_info_service = crate::service::token_info_service::TokenInfoService::new();
        match token_info_service.get_native_token_price_dexscreener(blockchain).await {
            Ok(price) => {
                info!("DexScreener price for {:?}: ${:.8}", blockchain, price);

                // Update cache with fresh price data
                {
                    let mut cache = self.cache.write().await;
                    cache.insert(cache_key, PriceData {
                        price_usd: price,
                        last_updated: now,
                    });
                }

                return Ok(price);
            }
            Err(e) => {
                info!("DexScreener failed for {:?}: {}, falling back to CoinGecko", blockchain, e);
            }
        }

        // Fallback to CoinGecko API
        let coin_id = match blockchain {
            Blockchain::SOL => "solana",
            Blockchain::ETH => "ethereum",
            Blockchain::BSC => "binancecoin",
            Blockchain::BASE => "ethereum", // Base uses ETH
        };

        info!("Using CoinGecko fallback for {:?}", blockchain);
        let price = self.get_price(coin_id).await?;

        // Update cache with CoinGecko price
        {
            let mut cache = self.cache.write().await;
            cache.insert(cache_key, PriceData {
                price_usd: price,
                last_updated: now,
            });
        }

        Ok(price)
    }

    /// Get price from cache or fetch from CoinGecko
    async fn get_price(&self, coin_id: &str) -> Result<f64, Box<dyn std::error::Error + Send + Sync>> {
        let now = SystemTime::now().duration_since(UNIX_EPOCH)?.as_secs();

        // Check cache first
        {
            let cache = self.cache.read().await;
            if let Some(price_data) = cache.get(coin_id) {
                if now - price_data.last_updated < CACHE_DURATION_SECONDS {
                    info!("Using cached price for {}: ${:.4}", coin_id, price_data.price_usd);
                    return Ok(price_data.price_usd);
                }
            }
        }

        // Fetch from CoinGecko
        info!("Fetching fresh price for {} from CoinGecko", coin_id);
        let url = format!("{}?ids={}&vs_currencies=usd", COINGECKO_API_URL, coin_id);
        
        let response = self.client
            .get(&url)
            .timeout(Duration::from_secs(10))
            .send()
            .await?;

        if !response.status().is_success() {
            return Err(format!("CoinGecko API error: {}", response.status()).into());
        }

        let price_response: HashMap<String, HashMap<String, f64>> = response.json().await?;
        
        let price_usd = price_response
            .get(coin_id)
            .and_then(|prices| prices.get("usd"))
            .copied()
            .ok_or("Price not found in response")?;

        // Update cache
        {
            let mut cache = self.cache.write().await;
            cache.insert(coin_id.to_string(), PriceData {
                price_usd,
                last_updated: now,
            });
        }

        info!("Fresh price for {}: ${:.4}", coin_id, price_usd);
        Ok(price_usd)
    }

    /// Calculate minimum fee amount in native tokens for $0.25 USD
    pub async fn get_minimum_fee_amount(&self, blockchain: &Blockchain) -> Result<f64, Box<dyn std::error::Error + Send + Sync>> {
        let price_usd = self.get_native_token_price(blockchain).await?;
        
        if price_usd <= 0.0 {
            return Err("Invalid price received".into());
        }

        let min_amount = MIN_FEE_USD / price_usd;
        info!("Minimum fee for {:?}: {:.8} tokens (${:.2} USD at ${:.4}/token)", 
              blockchain, min_amount, MIN_FEE_USD, price_usd);
        
        Ok(min_amount)
    }

    /// Check if fee amount meets minimum threshold and return adjusted amount ONLY if below minimum
    pub async fn get_adjusted_fee_amount(&self, blockchain: &Blockchain, fee_amount: f64) -> Result<f64, Box<dyn std::error::Error + Send + Sync>> {
        let min_amount = self.get_minimum_fee_amount(blockchain).await?;

        if fee_amount < min_amount {
            info!("💰 Fee amount {:.8} below minimum {:.8} for {:?}, adjusting to minimum $0.25 equivalent",
                  fee_amount, min_amount, blockchain);
            Ok(min_amount)
        } else {
            info!("✅ Fee amount {:.8} meets minimum {:.8} for {:?}, using calculated amount",
                  fee_amount, min_amount, blockchain);
            Ok(fee_amount)
        }
    }

    /// Check if fee amount meets minimum threshold
    pub async fn meets_minimum_threshold(&self, blockchain: &Blockchain, fee_amount: f64) -> Result<bool, Box<dyn std::error::Error + Send + Sync>> {
        let min_amount = self.get_minimum_fee_amount(blockchain).await?;
        let meets_threshold = fee_amount >= min_amount;

        if !meets_threshold {
            info!("Fee amount {:.8} below minimum {:.8} for {:?}",
                  fee_amount, min_amount, blockchain);
        }

        Ok(meets_threshold)
    }

    /// Get USD value of a token amount
    pub async fn get_usd_value(&self, blockchain: &Blockchain, amount: f64) -> Result<f64, Box<dyn std::error::Error + Send + Sync>> {
        let price_usd = self.get_native_token_price(blockchain).await?;
        Ok(amount * price_usd)
    }

    /// Format fee amount with proper decimals
    pub fn format_fee_amount(&self, amount: f64, blockchain: &Blockchain) -> String {
        match blockchain {
            Blockchain::SOL => format!("{:.6}", amount),
            Blockchain::ETH | Blockchain::BASE => format!("{:.8}", amount),
            Blockchain::BSC => format!("{:.8}", amount),
        }
    }

    /// Calculate admin fee with automatic minimum threshold enforcement
    /// This is the MAIN method that should be used for all admin fee calculations
    pub async fn calculate_admin_fee_with_minimum_enforcement(
        &self,
        blockchain: &Blockchain,
        base_amount: f64,
        fee_percentage: f64,
    ) -> Result<f64, Box<dyn std::error::Error + Send + Sync>> {
        // Calculate percentage-based fee
        let calculated_fee = base_amount * (fee_percentage / 100.0);

        // Apply minimum threshold enforcement
        let final_fee = self.get_adjusted_fee_amount(blockchain, calculated_fee).await?;

        info!("🔥 Admin fee calculation: base={:.8}, percentage={:.2}%, calculated={:.8}, final={:.8} {} (${:.4})",
              base_amount, fee_percentage, calculated_fee, final_fee,
              blockchain.get_native_symbol(), self.get_usd_value(blockchain, final_fee).await.unwrap_or(0.0));

        Ok(final_fee)
    }
}

#[derive(Debug, Serialize, Deserialize)]
pub struct FeeCalculationResult {
    pub fee_amount: f64,
    pub fee_amount_formatted: String,
    pub fee_usd_value: f64,
    pub meets_minimum: bool,
    pub minimum_required: f64,
    pub blockchain: String,
    pub native_symbol: String,
}

impl PriceService {
    /// Calculate and validate admin fee with all details - AUTOMATICALLY applies minimum threshold
    pub async fn calculate_admin_fee(
        &self,
        blockchain: &Blockchain,
        base_amount: f64,
        fee_percentage: f64,
    ) -> Result<FeeCalculationResult, Box<dyn std::error::Error + Send + Sync>> {
        // Calculate percentage-based fee amount
        let calculated_fee_amount = base_amount * (fee_percentage / 100.0);

        // Get minimum required amount ($0.25 equivalent)
        let minimum_required = self.get_minimum_fee_amount(blockchain).await?;

        // ALWAYS apply minimum threshold - use whichever is higher
        let final_fee_amount = if calculated_fee_amount < minimum_required {
            info!("💰 Admin fee {:.8} below minimum {:.8} for {:?}, using minimum $0.25 equivalent",
                  calculated_fee_amount, minimum_required, blockchain);
            minimum_required
        } else {
            info!("✅ Admin fee {:.8} meets minimum {:.8} for {:?}, using calculated amount",
                  calculated_fee_amount, minimum_required, blockchain);
            calculated_fee_amount
        };

        let meets_minimum = final_fee_amount >= minimum_required;
        let fee_usd_value = self.get_usd_value(blockchain, final_fee_amount).await?;

        let native_symbol = match blockchain {
            Blockchain::SOL => "SOL",
            Blockchain::ETH => "ETH",
            Blockchain::BSC => "BNB",
            Blockchain::BASE => "ETH",
        };

        Ok(FeeCalculationResult {
            fee_amount: final_fee_amount, // Use the adjusted amount
            fee_amount_formatted: self.format_fee_amount(final_fee_amount, blockchain),
            fee_usd_value,
            meets_minimum,
            minimum_required,
            blockchain: format!("{:?}", blockchain),
            native_symbol: native_symbol.to_string(),
        })
    }
}
