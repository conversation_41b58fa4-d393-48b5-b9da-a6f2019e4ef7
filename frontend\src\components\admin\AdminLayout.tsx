import { useState, useEffect, Suspense } from 'react';
import { Outlet, useLocation, Link } from 'react-router-dom';
import { Bars3Icon, XMarkIcon } from '@heroicons/react/24/outline';
import {
  HomeIcon,
  UsersIcon,
  CreditCardIcon,
  Cog6ToothIcon,
  ArrowRightOnRectangleIcon,
  ShieldCheckIcon,
} from '@heroicons/react/24/outline';
import { Dialog, Transition } from '@headlessui/react';
import { Fragment } from 'react';
import { useAdminStore } from '../../store/adminStore';

const navigation = [
  { name: 'Dashboard', href: '/dashboard', icon: HomeIcon },
  { name: 'Transactions', href: '/dashboard/transactions', icon: CreditCardIcon },
  { name: 'Users', href: '/dashboard/users', icon: UsersIcon },
  { name: 'Admins', href: '/dashboard/admins', icon: ShieldCheckIcon },
  { name: 'Settings', href: '/dashboard/settings', icon: Cog6ToothIcon },
];

function classNames(...classes: string[]) {
  return classes.filter(Boolean).join(' ');
}

export default function AdminLayout() {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [pageTitle, setPageTitle] = useState('Admin Dashboard');
  const location = useLocation();
  const { currentUser, logout } = useAdminStore();

  // Update page title based on current route
  useEffect(() => {
    const path = location.pathname.split('/').pop() || 'dashboard';
    let formattedTitle = path === 'dashboard' ? 'Dashboard' : path.charAt(0).toUpperCase() + path.slice(1);

    // Handle special cases
    // (No special cases currently)

    setPageTitle(`${formattedTitle}`);
    document.title = `EasyBot | ${formattedTitle}`;
  }, [location]);

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-100 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      {/* Mobile sidebar */}
      <Transition.Root show={sidebarOpen} as={Fragment}>
        <Dialog as="div" className="relative z-50 lg:hidden" onClose={setSidebarOpen}>
          <Transition.Child
            as={Fragment}
            enter="transition-opacity ease-linear duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="transition-opacity ease-linear duration-300"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-gray-900/80 dark:bg-gray-900/80 backdrop-blur-sm" />
          </Transition.Child>

          <div className="fixed inset-0 flex">
            <Transition.Child
              as={Fragment}
              enter="transition ease-in-out duration-300 transform"
              enterFrom="-translate-x-full"
              enterTo="translate-x-0"
              leave="transition ease-in-out duration-300 transform"
              leaveFrom="translate-x-0"
              leaveTo="-translate-x-full"
            >
              <Dialog.Panel className="relative mr-16 flex w-full max-w-xs flex-1">
                <Transition.Child
                  as={Fragment}
                  enter="ease-in-out duration-300"
                  enterFrom="opacity-0"
                  enterTo="opacity-100"
                  leave="ease-in-out duration-300"
                  leaveFrom="opacity-100"
                  leaveTo="opacity-0"
                >
                  <div className="absolute left-full top-0 flex w-16 justify-center pt-5">
                    <button
                      type="button"
                      className="-m-2.5 p-2.5 text-gray-900 dark:text-white hover:bg-gray-100 dark:hover:bg-white/10 rounded-md transition-colors duration-200"
                      onClick={() => setSidebarOpen(false)}
                    >
                      <span className="sr-only">Close sidebar</span>
                      <XMarkIcon className="h-6 w-6" aria-hidden="true" />
                    </button>
                  </div>
                </Transition.Child>
                <div className="flex grow flex-col gap-y-5 overflow-y-auto bg-white/95 dark:bg-gray-900/95 backdrop-blur-xl px-6 pb-4 border-r border-gray-200/50 dark:border-white/10">
                  <div className="flex h-16 shrink-0 items-center">
                    <Link to="/dashboard" className="flex items-center transition-transform duration-300 hover:scale-105">
                      <ShieldCheckIcon className="h-8 w-auto text-indigo-500" />
                      <span className="ml-2 text-xl font-bold text-gray-900 dark:text-white">EasyBot</span>
                    </Link>
                  </div>
                  <nav className="flex flex-1 flex-col">
                    <ul role="list" className="flex flex-1 flex-col gap-y-7">
                      <li>
                        <ul role="list" className="-mx-2 space-y-2">
                          {navigation.map((item, index) => (
                            <li key={item.name} style={{ animationDelay: `${index * 50}ms` }} className="animate-fade-in">
                              <Link
                                to={item.href}
                                className={classNames(
                                  location.pathname === item.href || (item.href === '/dashboard' && location.pathname === '/dashboard')
                                    ? 'bg-indigo-100 text-indigo-900 dark:bg-indigo-800/20 dark:text-white'
                                    : 'text-gray-700 hover:text-gray-900 hover:bg-gray-100 dark:text-gray-300 dark:hover:text-white dark:hover:bg-gray-800/40',
                                  'group flex items-center p-3 text-sm font-medium transition-all duration-200 rounded-md'
                                )}
                                onClick={() => setSidebarOpen(false)}
                              >
                                <item.icon
                                  className={classNames(
                                    location.pathname === item.href || (item.href === '/dashboard' && location.pathname === '/dashboard')
                                      ? 'text-indigo-400'
                                      : 'text-gray-500 group-hover:text-indigo-500 dark:text-gray-400 dark:group-hover:text-indigo-400',
                                    'h-6 w-6 shrink-0 transition-colors duration-200 mr-3'
                                  )}
                                  aria-hidden="true"
                                />
                                <span className="text-sm">{item.name}</span>
                              </Link>
                            </li>
                          ))}
                        </ul>
                      </li>
                      <li className="mt-auto">
                        <button
                          onClick={() => {
                            logout();
                            setSidebarOpen(false);
                          }}
                          className="group -mx-2 flex gap-x-3 rounded-xl p-2 text-sm font-medium leading-6 text-gray-700 hover:text-gray-900 hover:bg-gray-100 dark:text-gray-300 dark:hover:text-white dark:hover:bg-white/5 transition-all duration-200 w-full text-left"
                        >
                          <ArrowRightOnRectangleIcon
                            className="h-5 w-5 shrink-0 text-gray-500 group-hover:text-indigo-500 dark:text-gray-400 dark:group-hover:text-indigo-400 transition-colors duration-200"
                            aria-hidden="true"
                          />
                          Sign out
                        </button>
                      </li>
                    </ul>
                  </nav>
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </Dialog>
      </Transition.Root>

      {/* Desktop sidebar */}
      <div className="hidden lg:fixed lg:inset-y-0 lg:z-40 lg:flex lg:w-64 lg:flex-col">
        <div className="flex grow flex-col gap-y-5 overflow-y-auto bg-white/90 dark:bg-gray-900/50 backdrop-blur-md border-r border-gray-200/50 dark:border-white/10 px-6 pb-4">
          <div className="flex h-16 shrink-0 items-center">
            <Link to="/dashboard" className="flex items-center transition-transform duration-300 hover:scale-105">
              <ShieldCheckIcon className="h-8 w-auto text-indigo-500" />
              <span className="ml-2 text-xl font-bold text-gray-900 dark:text-white">EasyBot</span>
            </Link>
          </div>
          <nav className="flex flex-1 flex-col">
            <ul role="list" className="flex flex-1 flex-col gap-y-7">
              <li>
                <ul role="list" className="-mx-2 space-y-2">
                  {navigation.map((item, index) => (
                    <li key={item.name} style={{ animationDelay: `${index * 50}ms` }} className="animate-fade-in">
                      <Link
                        to={item.href}
                        className={classNames(
                          location.pathname === item.href || (item.href === '/dashboard' && location.pathname === '/dashboard')
                            ? 'bg-indigo-100 text-indigo-900 dark:bg-indigo-800/20 dark:text-white'
                            : 'text-gray-700 hover:text-gray-900 hover:bg-gray-100 dark:text-gray-300 dark:hover:text-white dark:hover:bg-gray-800/40',
                          'group flex items-center p-3 text-sm font-medium transition-all duration-200 relative rounded-md'
                        )}
                      >
                        <item.icon
                          className={classNames(
                            location.pathname === item.href || (item.href === '/dashboard' && location.pathname === '/dashboard')
                              ? 'text-indigo-400'
                              : 'text-gray-500 group-hover:text-indigo-500 dark:text-gray-400 dark:group-hover:text-indigo-400',
                            'h-6 w-6 shrink-0 transition-colors duration-200 mr-3'
                          )}
                          aria-hidden="true"
                        />
                        <span className="text-sm">{item.name}</span>
                      </Link>
                    </li>
                  ))}
                </ul>
              </li>
              <li className="mt-auto">
                <button
                  onClick={logout}
                  className="group -mx-2 flex gap-x-3 rounded-xl p-2 text-sm font-medium leading-6 text-gray-700 hover:text-gray-900 hover:bg-gray-100 dark:text-gray-300 dark:hover:text-white dark:hover:bg-white/5 transition-all duration-200 w-full text-left"
                >
                  <ArrowRightOnRectangleIcon
                    className="h-5 w-5 shrink-0 text-gray-500 group-hover:text-indigo-500 dark:text-gray-400 dark:group-hover:text-indigo-400 transition-colors duration-200"
                    aria-hidden="true"
                  />
                  Sign out
                </button>
              </li>
            </ul>
          </nav>

          {/* Decorative elements */}
          <div className="absolute bottom-0 left-0 right-0 h-32 bg-gradient-to-t from-indigo-900/20 to-transparent pointer-events-none"></div>
        </div>
      </div>

      <div className="lg:pl-64">
        <header className="sticky top-0 z-40 flex h-16 items-center gap-x-4 border-b border-gray-200/50 dark:border-white/10 bg-white/80 dark:bg-gray-900/80 backdrop-blur-lg px-4 shadow-md sm:gap-x-6 sm:px-6 lg:px-8">
          <button
            type="button"
            className="-m-2.5 p-2.5 text-gray-900 dark:text-white lg:hidden hover:bg-gray-100 dark:hover:bg-white/10 rounded-md transition-colors duration-200"
            onClick={() => setSidebarOpen(true)}
          >
            <span className="sr-only">Open sidebar</span>
            <Bars3Icon className="h-6 w-6" aria-hidden="true" />
          </button>

          {/* Separator */}
          <div className="h-6 w-px bg-white/10 lg:hidden" aria-hidden="true" />

          <div className="flex flex-1 gap-x-4 self-stretch lg:gap-x-6">
            <div className="flex items-center">
              <h1 className="text-xl font-semibold text-gray-900 dark:text-white">{pageTitle}</h1>
            </div>

            <div className="flex flex-1 items-center justify-end gap-x-4">
              <div className="flex items-center gap-x-4 lg:gap-x-6">
                <div className="flex items-center">
                  <div className="h-8 w-8 rounded-full bg-indigo-600 flex items-center justify-center text-white text-sm font-medium">
                    {currentUser?.username ? currentUser.username.charAt(0).toUpperCase() : 'A'}
                  </div>
                  <span className="ml-2 text-sm font-medium text-gray-900 dark:text-white hidden sm:block">
                    {currentUser?.username || 'Admin'}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </header>

        <main className="py-10 animate-fade-in bg-gray-50/50 dark:bg-transparent min-h-screen">
          <div className="px-4 sm:px-6 lg:px-8">
            <Suspense fallback={
              <div className="flex items-center justify-center py-12">
                <div className="animate-pulse flex space-x-4 items-center">
                  <div className="h-12 w-12 rounded-full bg-indigo-200 dark:bg-indigo-500/20"></div>
                  <div className="space-y-2">
                    <div className="h-4 w-36 bg-gray-200 dark:bg-indigo-500/20 rounded"></div>
                    <div className="h-4 w-24 bg-gray-200 dark:bg-indigo-500/20 rounded"></div>
                  </div>
                </div>
              </div>
            }>
              <Outlet />
            </Suspense>
          </div>
        </main>

        {/* Decorative background elements */}
        <div className="fixed inset-0 -z-10 overflow-hidden pointer-events-none">
          <div className="absolute -top-40 -right-40 w-80 h-80 bg-indigo-600/5 rounded-full blur-3xl"></div>
          <div className="absolute top-1/4 -left-40 w-80 h-80 bg-indigo-600/5 rounded-full blur-3xl"></div>
          <div className="absolute -bottom-40 left-1/3 w-80 h-80 bg-indigo-600/5 rounded-full blur-3xl"></div>
        </div>
      </div>
    </div>
  );
}
