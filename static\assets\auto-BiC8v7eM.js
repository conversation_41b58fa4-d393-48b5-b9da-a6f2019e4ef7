import{C as h,r as a,R as C,f as I,D as K,g as L,h as B}from"./index-dCUkEeO4.js";const y="label";function b(t,e){typeof t=="function"?t(e):t&&(t.current=e)}function T(t,e){const n=t.options;n&&e&&Object.assign(n,e)}function E(t,e){t.labels=e}function R(t,e){let n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:y;const c=[];t.datasets=e.map(s=>{const u=t.datasets.find(i=>i[n]===s[n]);return!u||!s.data||c.includes(u)?{...s}:(c.push(u),Object.assign(u,s),u)})}function j(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:y;const n={labels:[],datasets:[]};return E(n,t.labels),R(n,t.datasets,e),n}function O(t,e){const{height:n=150,width:c=300,redraw:s=!1,datasetIdKey:u,type:i,data:o,options:f,plugins:w=[],fallbackContent:D,updateMode:m,...v}=t,l=a.useRef(null),r=a.useRef(null),d=()=>{l.current&&(r.current=new h(l.current,{type:i,data:j(o,u),options:f&&{...f},plugins:w}),b(e,r.current))},g=()=>{b(e,null),r.current&&(r.current.destroy(),r.current=null)};return a.useEffect(()=>{!s&&r.current&&f&&T(r.current,f)},[s,f]),a.useEffect(()=>{!s&&r.current&&E(r.current.config.data,o.labels)},[s,o.labels]),a.useEffect(()=>{!s&&r.current&&o.datasets&&R(r.current.config.data,o.datasets,u)},[s,o.datasets]),a.useEffect(()=>{r.current&&(s?(g(),setTimeout(d)):r.current.update(m))},[s,f,o.labels,o.datasets,m]),a.useEffect(()=>{r.current&&(g(),setTimeout(d))},[i]),a.useEffect(()=>(d(),()=>g()),[]),C.createElement("canvas",{ref:l,role:"img",height:n,width:c,...v},D)}const k=a.forwardRef(O);function p(t,e){return h.register(e),a.forwardRef((n,c)=>C.createElement(k,{...n,ref:c,type:t}))}const P=p("line",L),$=p("bar",I),q=p("doughnut",K);h.register(...B);export{$ as B,q as D,P as L};
