use serde::{Deserialize, Serialize};

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct Wallet {
    pub address: String,
    pub private_key: String,
    #[serde(default)]
    pub mnemonic: Option<String>,
}

impl Wallet {
    pub fn new(address: String, private_key: String) -> Self {
        Self {
            address,
            private_key,
            mnemonic: None,
        }
    }

    pub fn empty() -> Self {
        Self {
            address: String::new(),
            private_key: String::new(),
            mnemonic: None,
        }
    }

    /// Convert private key string to bytes for Solana operations
    pub fn private_key_bytes(&self) -> Result<Vec<u8>, Box<dyn std::error::Error>> {
        // Try to decode as base58 first (Solana format)
        if let Ok(bytes) = bs58::decode(&self.private_key).into_vec() {
            return Ok(bytes);
        }

        // Try to decode as hex (alternative format)
        if let Ok(bytes) = hex::decode(&self.private_key) {
            return Ok(bytes);
        }

        Err("Invalid private key format".into())
    }
}
