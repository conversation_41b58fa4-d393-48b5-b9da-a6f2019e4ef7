use crate::config::AppConfig;

use std::fs::{File, create_dir_all};
use std::path::Path;

pub fn setup_logger() {
    let config = AppConfig::get();

    std::env::set_var("RUST_LOG", &config.log_level);

    if config.log_to_file {
        if let Some(dir) = Path::new(&config.log_file_path).parent() {
            if !dir.exists() {
                create_dir_all(dir).expect("Failed to create log directory");
            }
        }

        let file = File::create(&config.log_file_path).expect("Failed to create log file");

        let env = env_logger::Builder::new()
            .parse_filters(&config.log_level)
            .target(env_logger::Target::Pipe(Box::new(file)))
            .build();

        log::set_boxed_logger(Box::new(env)).expect("Failed to set logger");
        log::set_max_level(log::LevelFilter::Info);
    } else {
        pretty_env_logger::init();
    }

    log::info!("Logger initialized with level: {}", config.log_level);
}
