function Ce(e,t){return function(){return e.apply(t,arguments)}}const{toString:Ze}=Object.prototype,{getPrototypeOf:le}=Object,{iterator:K,toStringTag:Ne}=Symbol,X=(e=>t=>{const n=Ze.call(t);return e[n]||(e[n]=n.slice(8,-1).toLowerCase())})(Object.create(null)),N=e=>(e=e.toLowerCase(),t=>X(t)===e),G=e=>t=>typeof t===e,{isArray:D}=Array,$=G("undefined");function Ye(e){return e!==null&&!$(e)&&e.constructor!==null&&!$(e.constructor)&&x(e.constructor.isBuffer)&&e.constructor.isBuffer(e)}const Pe=N("ArrayBuffer");function et(e){let t;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?t=ArrayBuffer.isView(e):t=e&&e.buffer&&Pe(e.buffer),t}const tt=G("string"),x=G("function"),Fe=G("number"),Q=e=>e!==null&&typeof e=="object",nt=e=>e===!0||e===!1,z=e=>{if(X(e)!=="object")return!1;const t=le(e);return(t===null||t===Object.prototype||Object.getPrototypeOf(t)===null)&&!(Ne in e)&&!(K in e)},st=N("Date"),rt=N("File"),ot=N("Blob"),it=N("FileList"),at=e=>Q(e)&&x(e.pipe),ct=e=>{let t;return e&&(typeof FormData=="function"&&e instanceof FormData||x(e.append)&&((t=X(e))==="formdata"||t==="object"&&x(e.toString)&&e.toString()==="[object FormData]"))},ut=N("URLSearchParams"),[lt,ft,dt,pt]=["ReadableStream","Request","Response","Headers"].map(N),ht=e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function I(e,t,{allOwnKeys:n=!1}={}){if(e===null||typeof e>"u")return;let s,r;if(typeof e!="object"&&(e=[e]),D(e))for(s=0,r=e.length;s<r;s++)t.call(null,e[s],s,e);else{const o=n?Object.getOwnPropertyNames(e):Object.keys(e),i=o.length;let c;for(s=0;s<i;s++)c=o[s],t.call(null,e[c],c,e)}}function _e(e,t){t=t.toLowerCase();const n=Object.keys(e);let s=n.length,r;for(;s-- >0;)if(r=n[s],t===r.toLowerCase())return r;return null}const B=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global,Ue=e=>!$(e)&&e!==B;function re(){const{caseless:e}=Ue(this)&&this||{},t={},n=(s,r)=>{const o=e&&_e(t,r)||r;z(t[o])&&z(s)?t[o]=re(t[o],s):z(s)?t[o]=re({},s):D(s)?t[o]=s.slice():t[o]=s};for(let s=0,r=arguments.length;s<r;s++)arguments[s]&&I(arguments[s],n);return t}const mt=(e,t,n,{allOwnKeys:s}={})=>(I(t,(r,o)=>{n&&x(r)?e[o]=Ce(r,n):e[o]=r},{allOwnKeys:s}),e),yt=e=>(e.charCodeAt(0)===65279&&(e=e.slice(1)),e),wt=(e,t,n,s)=>{e.prototype=Object.create(t.prototype,s),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),n&&Object.assign(e.prototype,n)},bt=(e,t,n,s)=>{let r,o,i;const c={};if(t=t||{},e==null)return t;do{for(r=Object.getOwnPropertyNames(e),o=r.length;o-- >0;)i=r[o],(!s||s(i,e,t))&&!c[i]&&(t[i]=e[i],c[i]=!0);e=n!==!1&&le(e)}while(e&&(!n||n(e,t))&&e!==Object.prototype);return t},gt=(e,t,n)=>{e=String(e),(n===void 0||n>e.length)&&(n=e.length),n-=t.length;const s=e.indexOf(t,n);return s!==-1&&s===n},Et=e=>{if(!e)return null;if(D(e))return e;let t=e.length;if(!Fe(t))return null;const n=new Array(t);for(;t-- >0;)n[t]=e[t];return n},St=(e=>t=>e&&t instanceof e)(typeof Uint8Array<"u"&&le(Uint8Array)),Rt=(e,t)=>{const s=(e&&e[K]).call(e);let r;for(;(r=s.next())&&!r.done;){const o=r.value;t.call(e,o[0],o[1])}},At=(e,t)=>{let n;const s=[];for(;(n=e.exec(t))!==null;)s.push(n);return s},Tt=N("HTMLFormElement"),Ot=e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(n,s,r){return s.toUpperCase()+r}),he=(({hasOwnProperty:e})=>(t,n)=>e.call(t,n))(Object.prototype),xt=N("RegExp"),Be=(e,t)=>{const n=Object.getOwnPropertyDescriptors(e),s={};I(n,(r,o)=>{let i;(i=t(r,o,e))!==!1&&(s[o]=i||r)}),Object.defineProperties(e,s)},Ct=e=>{Be(e,(t,n)=>{if(x(e)&&["arguments","caller","callee"].indexOf(n)!==-1)return!1;const s=e[n];if(x(s)){if(t.enumerable=!1,"writable"in t){t.writable=!1;return}t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+n+"'")})}})},Nt=(e,t)=>{const n={},s=r=>{r.forEach(o=>{n[o]=!0})};return D(e)?s(e):s(String(e).split(t)),n},Pt=()=>{},Ft=(e,t)=>e!=null&&Number.isFinite(e=+e)?e:t;function _t(e){return!!(e&&x(e.append)&&e[Ne]==="FormData"&&e[K])}const Ut=e=>{const t=new Array(10),n=(s,r)=>{if(Q(s)){if(t.indexOf(s)>=0)return;if(!("toJSON"in s)){t[r]=s;const o=D(s)?[]:{};return I(s,(i,c)=>{const f=n(i,r+1);!$(f)&&(o[c]=f)}),t[r]=void 0,o}}return s};return n(e,0)},Bt=N("AsyncFunction"),kt=e=>e&&(Q(e)||x(e))&&x(e.then)&&x(e.catch),ke=((e,t)=>e?setImmediate:t?((n,s)=>(B.addEventListener("message",({source:r,data:o})=>{r===B&&o===n&&s.length&&s.shift()()},!1),r=>{s.push(r),B.postMessage(n,"*")}))(`axios@${Math.random()}`,[]):n=>setTimeout(n))(typeof setImmediate=="function",x(B.postMessage)),Lt=typeof queueMicrotask<"u"?queueMicrotask.bind(B):typeof process<"u"&&process.nextTick||ke,Dt=e=>e!=null&&x(e[K]),a={isArray:D,isArrayBuffer:Pe,isBuffer:Ye,isFormData:ct,isArrayBufferView:et,isString:tt,isNumber:Fe,isBoolean:nt,isObject:Q,isPlainObject:z,isReadableStream:lt,isRequest:ft,isResponse:dt,isHeaders:pt,isUndefined:$,isDate:st,isFile:rt,isBlob:ot,isRegExp:xt,isFunction:x,isStream:at,isURLSearchParams:ut,isTypedArray:St,isFileList:it,forEach:I,merge:re,extend:mt,trim:ht,stripBOM:yt,inherits:wt,toFlatObject:bt,kindOf:X,kindOfTest:N,endsWith:gt,toArray:Et,forEachEntry:Rt,matchAll:At,isHTMLForm:Tt,hasOwnProperty:he,hasOwnProp:he,reduceDescriptors:Be,freezeMethods:Ct,toObjectSet:Nt,toCamelCase:Ot,noop:Pt,toFiniteNumber:Ft,findKey:_e,global:B,isContextDefined:Ue,isSpecCompliantForm:_t,toJSONObject:Ut,isAsyncFn:Bt,isThenable:kt,setImmediate:ke,asap:Lt,isIterable:Dt};function y(e,t,n,s,r){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=e,this.name="AxiosError",t&&(this.code=t),n&&(this.config=n),s&&(this.request=s),r&&(this.response=r,this.status=r.status?r.status:null)}a.inherits(y,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:a.toJSONObject(this.config),code:this.code,status:this.status}}});const Le=y.prototype,De={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{De[e]={value:e}});Object.defineProperties(y,De);Object.defineProperty(Le,"isAxiosError",{value:!0});y.from=(e,t,n,s,r,o)=>{const i=Object.create(Le);return a.toFlatObject(e,i,function(f){return f!==Error.prototype},c=>c!=="isAxiosError"),y.call(i,e.message,t,n,s,r),i.cause=e,i.name=e.name,o&&Object.assign(i,o),i};const jt=null;function oe(e){return a.isPlainObject(e)||a.isArray(e)}function je(e){return a.endsWith(e,"[]")?e.slice(0,-2):e}function me(e,t,n){return e?e.concat(t).map(function(r,o){return r=je(r),!n&&o?"["+r+"]":r}).join(n?".":""):t}function qt(e){return a.isArray(e)&&!e.some(oe)}const $t=a.toFlatObject(a,{},null,function(t){return/^is[A-Z]/.test(t)});function Z(e,t,n){if(!a.isObject(e))throw new TypeError("target must be an object");t=t||new FormData,n=a.toFlatObject(n,{metaTokens:!0,dots:!1,indexes:!1},!1,function(w,h){return!a.isUndefined(h[w])});const s=n.metaTokens,r=n.visitor||l,o=n.dots,i=n.indexes,f=(n.Blob||typeof Blob<"u"&&Blob)&&a.isSpecCompliantForm(t);if(!a.isFunction(r))throw new TypeError("visitor must be a function");function u(p){if(p===null)return"";if(a.isDate(p))return p.toISOString();if(!f&&a.isBlob(p))throw new y("Blob is not supported. Use a Buffer instead.");return a.isArrayBuffer(p)||a.isTypedArray(p)?f&&typeof Blob=="function"?new Blob([p]):Buffer.from(p):p}function l(p,w,h){let g=p;if(p&&!h&&typeof p=="object"){if(a.endsWith(w,"{}"))w=s?w:w.slice(0,-2),p=JSON.stringify(p);else if(a.isArray(p)&&qt(p)||(a.isFileList(p)||a.endsWith(w,"[]"))&&(g=a.toArray(p)))return w=je(w),g.forEach(function(A,F){!(a.isUndefined(A)||A===null)&&t.append(i===!0?me([w],F,o):i===null?w:w+"[]",u(A))}),!1}return oe(p)?!0:(t.append(me(h,w,o),u(p)),!1)}const d=[],b=Object.assign($t,{defaultVisitor:l,convertValue:u,isVisitable:oe});function S(p,w){if(!a.isUndefined(p)){if(d.indexOf(p)!==-1)throw Error("Circular reference detected in "+w.join("."));d.push(p),a.forEach(p,function(g,R){(!(a.isUndefined(g)||g===null)&&r.call(t,g,a.isString(R)?R.trim():R,w,b))===!0&&S(g,w?w.concat(R):[R])}),d.pop()}}if(!a.isObject(e))throw new TypeError("data must be an object");return S(e),t}function ye(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(s){return t[s]})}function fe(e,t){this._pairs=[],e&&Z(e,this,t)}const qe=fe.prototype;qe.append=function(t,n){this._pairs.push([t,n])};qe.toString=function(t){const n=t?function(s){return t.call(this,s,ye)}:ye;return this._pairs.map(function(r){return n(r[0])+"="+n(r[1])},"").join("&")};function It(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function $e(e,t,n){if(!t)return e;const s=n&&n.encode||It;a.isFunction(n)&&(n={serialize:n});const r=n&&n.serialize;let o;if(r?o=r(t,n):o=a.isURLSearchParams(t)?t.toString():new fe(t,n).toString(s),o){const i=e.indexOf("#");i!==-1&&(e=e.slice(0,i)),e+=(e.indexOf("?")===-1?"?":"&")+o}return e}class we{constructor(){this.handlers=[]}use(t,n,s){return this.handlers.push({fulfilled:t,rejected:n,synchronous:s?s.synchronous:!1,runWhen:s?s.runWhen:null}),this.handlers.length-1}eject(t){this.handlers[t]&&(this.handlers[t]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(t){a.forEach(this.handlers,function(s){s!==null&&t(s)})}}const Ie={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},Ht=typeof URLSearchParams<"u"?URLSearchParams:fe,Mt=typeof FormData<"u"?FormData:null,zt=typeof Blob<"u"?Blob:null,Jt={isBrowser:!0,classes:{URLSearchParams:Ht,FormData:Mt,Blob:zt},protocols:["http","https","file","blob","url","data"]},de=typeof window<"u"&&typeof document<"u",ie=typeof navigator=="object"&&navigator||void 0,Vt=de&&(!ie||["ReactNative","NativeScript","NS"].indexOf(ie.product)<0),Wt=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function",vt=de&&window.location.href||"http://localhost",Kt=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:de,hasStandardBrowserEnv:Vt,hasStandardBrowserWebWorkerEnv:Wt,navigator:ie,origin:vt},Symbol.toStringTag,{value:"Module"})),T={...Kt,...Jt};function Xt(e,t){return Z(e,new T.classes.URLSearchParams,Object.assign({visitor:function(n,s,r,o){return T.isNode&&a.isBuffer(n)?(this.append(s,n.toString("base64")),!1):o.defaultVisitor.apply(this,arguments)}},t))}function Gt(e){return a.matchAll(/\w+|\[(\w*)]/g,e).map(t=>t[0]==="[]"?"":t[1]||t[0])}function Qt(e){const t={},n=Object.keys(e);let s;const r=n.length;let o;for(s=0;s<r;s++)o=n[s],t[o]=e[o];return t}function He(e){function t(n,s,r,o){let i=n[o++];if(i==="__proto__")return!0;const c=Number.isFinite(+i),f=o>=n.length;return i=!i&&a.isArray(r)?r.length:i,f?(a.hasOwnProp(r,i)?r[i]=[r[i],s]:r[i]=s,!c):((!r[i]||!a.isObject(r[i]))&&(r[i]=[]),t(n,s,r[i],o)&&a.isArray(r[i])&&(r[i]=Qt(r[i])),!c)}if(a.isFormData(e)&&a.isFunction(e.entries)){const n={};return a.forEachEntry(e,(s,r)=>{t(Gt(s),r,n,0)}),n}return null}function Zt(e,t,n){if(a.isString(e))try{return(t||JSON.parse)(e),a.trim(e)}catch(s){if(s.name!=="SyntaxError")throw s}return(n||JSON.stringify)(e)}const H={transitional:Ie,adapter:["xhr","http","fetch"],transformRequest:[function(t,n){const s=n.getContentType()||"",r=s.indexOf("application/json")>-1,o=a.isObject(t);if(o&&a.isHTMLForm(t)&&(t=new FormData(t)),a.isFormData(t))return r?JSON.stringify(He(t)):t;if(a.isArrayBuffer(t)||a.isBuffer(t)||a.isStream(t)||a.isFile(t)||a.isBlob(t)||a.isReadableStream(t))return t;if(a.isArrayBufferView(t))return t.buffer;if(a.isURLSearchParams(t))return n.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),t.toString();let c;if(o){if(s.indexOf("application/x-www-form-urlencoded")>-1)return Xt(t,this.formSerializer).toString();if((c=a.isFileList(t))||s.indexOf("multipart/form-data")>-1){const f=this.env&&this.env.FormData;return Z(c?{"files[]":t}:t,f&&new f,this.formSerializer)}}return o||r?(n.setContentType("application/json",!1),Zt(t)):t}],transformResponse:[function(t){const n=this.transitional||H.transitional,s=n&&n.forcedJSONParsing,r=this.responseType==="json";if(a.isResponse(t)||a.isReadableStream(t))return t;if(t&&a.isString(t)&&(s&&!this.responseType||r)){const i=!(n&&n.silentJSONParsing)&&r;try{return JSON.parse(t)}catch(c){if(i)throw c.name==="SyntaxError"?y.from(c,y.ERR_BAD_RESPONSE,this,null,this.response):c}}return t}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:T.classes.FormData,Blob:T.classes.Blob},validateStatus:function(t){return t>=200&&t<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};a.forEach(["delete","get","head","post","put","patch"],e=>{H.headers[e]={}});const Yt=a.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),en=e=>{const t={};let n,s,r;return e&&e.split(`
`).forEach(function(i){r=i.indexOf(":"),n=i.substring(0,r).trim().toLowerCase(),s=i.substring(r+1).trim(),!(!n||t[n]&&Yt[n])&&(n==="set-cookie"?t[n]?t[n].push(s):t[n]=[s]:t[n]=t[n]?t[n]+", "+s:s)}),t},be=Symbol("internals");function q(e){return e&&String(e).trim().toLowerCase()}function J(e){return e===!1||e==null?e:a.isArray(e)?e.map(J):String(e)}function tn(e){const t=Object.create(null),n=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let s;for(;s=n.exec(e);)t[s[1]]=s[2];return t}const nn=e=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim());function te(e,t,n,s,r){if(a.isFunction(s))return s.call(this,t,n);if(r&&(t=n),!!a.isString(t)){if(a.isString(s))return t.indexOf(s)!==-1;if(a.isRegExp(s))return s.test(t)}}function sn(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(t,n,s)=>n.toUpperCase()+s)}function rn(e,t){const n=a.toCamelCase(" "+t);["get","set","has"].forEach(s=>{Object.defineProperty(e,s+n,{value:function(r,o,i){return this[s].call(this,t,r,o,i)},configurable:!0})})}let C=class{constructor(t){t&&this.set(t)}set(t,n,s){const r=this;function o(c,f,u){const l=q(f);if(!l)throw new Error("header name must be a non-empty string");const d=a.findKey(r,l);(!d||r[d]===void 0||u===!0||u===void 0&&r[d]!==!1)&&(r[d||f]=J(c))}const i=(c,f)=>a.forEach(c,(u,l)=>o(u,l,f));if(a.isPlainObject(t)||t instanceof this.constructor)i(t,n);else if(a.isString(t)&&(t=t.trim())&&!nn(t))i(en(t),n);else if(a.isObject(t)&&a.isIterable(t)){let c={},f,u;for(const l of t){if(!a.isArray(l))throw TypeError("Object iterator must return a key-value pair");c[u=l[0]]=(f=c[u])?a.isArray(f)?[...f,l[1]]:[f,l[1]]:l[1]}i(c,n)}else t!=null&&o(n,t,s);return this}get(t,n){if(t=q(t),t){const s=a.findKey(this,t);if(s){const r=this[s];if(!n)return r;if(n===!0)return tn(r);if(a.isFunction(n))return n.call(this,r,s);if(a.isRegExp(n))return n.exec(r);throw new TypeError("parser must be boolean|regexp|function")}}}has(t,n){if(t=q(t),t){const s=a.findKey(this,t);return!!(s&&this[s]!==void 0&&(!n||te(this,this[s],s,n)))}return!1}delete(t,n){const s=this;let r=!1;function o(i){if(i=q(i),i){const c=a.findKey(s,i);c&&(!n||te(s,s[c],c,n))&&(delete s[c],r=!0)}}return a.isArray(t)?t.forEach(o):o(t),r}clear(t){const n=Object.keys(this);let s=n.length,r=!1;for(;s--;){const o=n[s];(!t||te(this,this[o],o,t,!0))&&(delete this[o],r=!0)}return r}normalize(t){const n=this,s={};return a.forEach(this,(r,o)=>{const i=a.findKey(s,o);if(i){n[i]=J(r),delete n[o];return}const c=t?sn(o):String(o).trim();c!==o&&delete n[o],n[c]=J(r),s[c]=!0}),this}concat(...t){return this.constructor.concat(this,...t)}toJSON(t){const n=Object.create(null);return a.forEach(this,(s,r)=>{s!=null&&s!==!1&&(n[r]=t&&a.isArray(s)?s.join(", "):s)}),n}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([t,n])=>t+": "+n).join(`
`)}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(t){return t instanceof this?t:new this(t)}static concat(t,...n){const s=new this(t);return n.forEach(r=>s.set(r)),s}static accessor(t){const s=(this[be]=this[be]={accessors:{}}).accessors,r=this.prototype;function o(i){const c=q(i);s[c]||(rn(r,i),s[c]=!0)}return a.isArray(t)?t.forEach(o):o(t),this}};C.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);a.reduceDescriptors(C.prototype,({value:e},t)=>{let n=t[0].toUpperCase()+t.slice(1);return{get:()=>e,set(s){this[n]=s}}});a.freezeMethods(C);function ne(e,t){const n=this||H,s=t||n,r=C.from(s.headers);let o=s.data;return a.forEach(e,function(c){o=c.call(n,o,r.normalize(),t?t.status:void 0)}),r.normalize(),o}function Me(e){return!!(e&&e.__CANCEL__)}function j(e,t,n){y.call(this,e??"canceled",y.ERR_CANCELED,t,n),this.name="CanceledError"}a.inherits(j,y,{__CANCEL__:!0});function ze(e,t,n){const s=n.config.validateStatus;!n.status||!s||s(n.status)?e(n):t(new y("Request failed with status code "+n.status,[y.ERR_BAD_REQUEST,y.ERR_BAD_RESPONSE][Math.floor(n.status/100)-4],n.config,n.request,n))}function on(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}function an(e,t){e=e||10;const n=new Array(e),s=new Array(e);let r=0,o=0,i;return t=t!==void 0?t:1e3,function(f){const u=Date.now(),l=s[o];i||(i=u),n[r]=f,s[r]=u;let d=o,b=0;for(;d!==r;)b+=n[d++],d=d%e;if(r=(r+1)%e,r===o&&(o=(o+1)%e),u-i<t)return;const S=l&&u-l;return S?Math.round(b*1e3/S):void 0}}function cn(e,t){let n=0,s=1e3/t,r,o;const i=(u,l=Date.now())=>{n=l,r=null,o&&(clearTimeout(o),o=null),e.apply(null,u)};return[(...u)=>{const l=Date.now(),d=l-n;d>=s?i(u,l):(r=u,o||(o=setTimeout(()=>{o=null,i(r)},s-d)))},()=>r&&i(r)]}const W=(e,t,n=3)=>{let s=0;const r=an(50,250);return cn(o=>{const i=o.loaded,c=o.lengthComputable?o.total:void 0,f=i-s,u=r(f),l=i<=c;s=i;const d={loaded:i,total:c,progress:c?i/c:void 0,bytes:f,rate:u||void 0,estimated:u&&c&&l?(c-i)/u:void 0,event:o,lengthComputable:c!=null,[t?"download":"upload"]:!0};e(d)},n)},ge=(e,t)=>{const n=e!=null;return[s=>t[0]({lengthComputable:n,total:e,loaded:s}),t[1]]},Ee=e=>(...t)=>a.asap(()=>e(...t)),un=T.hasStandardBrowserEnv?((e,t)=>n=>(n=new URL(n,T.origin),e.protocol===n.protocol&&e.host===n.host&&(t||e.port===n.port)))(new URL(T.origin),T.navigator&&/(msie|trident)/i.test(T.navigator.userAgent)):()=>!0,ln=T.hasStandardBrowserEnv?{write(e,t,n,s,r,o){const i=[e+"="+encodeURIComponent(t)];a.isNumber(n)&&i.push("expires="+new Date(n).toGMTString()),a.isString(s)&&i.push("path="+s),a.isString(r)&&i.push("domain="+r),o===!0&&i.push("secure"),document.cookie=i.join("; ")},read(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function fn(e){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(e)}function dn(e,t){return t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e}function Je(e,t,n){let s=!fn(t);return e&&(s||n==!1)?dn(e,t):t}const Se=e=>e instanceof C?{...e}:e;function L(e,t){t=t||{};const n={};function s(u,l,d,b){return a.isPlainObject(u)&&a.isPlainObject(l)?a.merge.call({caseless:b},u,l):a.isPlainObject(l)?a.merge({},l):a.isArray(l)?l.slice():l}function r(u,l,d,b){if(a.isUndefined(l)){if(!a.isUndefined(u))return s(void 0,u,d,b)}else return s(u,l,d,b)}function o(u,l){if(!a.isUndefined(l))return s(void 0,l)}function i(u,l){if(a.isUndefined(l)){if(!a.isUndefined(u))return s(void 0,u)}else return s(void 0,l)}function c(u,l,d){if(d in t)return s(u,l);if(d in e)return s(void 0,u)}const f={url:o,method:o,data:o,baseURL:i,transformRequest:i,transformResponse:i,paramsSerializer:i,timeout:i,timeoutMessage:i,withCredentials:i,withXSRFToken:i,adapter:i,responseType:i,xsrfCookieName:i,xsrfHeaderName:i,onUploadProgress:i,onDownloadProgress:i,decompress:i,maxContentLength:i,maxBodyLength:i,beforeRedirect:i,transport:i,httpAgent:i,httpsAgent:i,cancelToken:i,socketPath:i,responseEncoding:i,validateStatus:c,headers:(u,l,d)=>r(Se(u),Se(l),d,!0)};return a.forEach(Object.keys(Object.assign({},e,t)),function(l){const d=f[l]||r,b=d(e[l],t[l],l);a.isUndefined(b)&&d!==c||(n[l]=b)}),n}const Ve=e=>{const t=L({},e);let{data:n,withXSRFToken:s,xsrfHeaderName:r,xsrfCookieName:o,headers:i,auth:c}=t;t.headers=i=C.from(i),t.url=$e(Je(t.baseURL,t.url,t.allowAbsoluteUrls),e.params,e.paramsSerializer),c&&i.set("Authorization","Basic "+btoa((c.username||"")+":"+(c.password?unescape(encodeURIComponent(c.password)):"")));let f;if(a.isFormData(n)){if(T.hasStandardBrowserEnv||T.hasStandardBrowserWebWorkerEnv)i.setContentType(void 0);else if((f=i.getContentType())!==!1){const[u,...l]=f?f.split(";").map(d=>d.trim()).filter(Boolean):[];i.setContentType([u||"multipart/form-data",...l].join("; "))}}if(T.hasStandardBrowserEnv&&(s&&a.isFunction(s)&&(s=s(t)),s||s!==!1&&un(t.url))){const u=r&&o&&ln.read(o);u&&i.set(r,u)}return t},pn=typeof XMLHttpRequest<"u",hn=pn&&function(e){return new Promise(function(n,s){const r=Ve(e);let o=r.data;const i=C.from(r.headers).normalize();let{responseType:c,onUploadProgress:f,onDownloadProgress:u}=r,l,d,b,S,p;function w(){S&&S(),p&&p(),r.cancelToken&&r.cancelToken.unsubscribe(l),r.signal&&r.signal.removeEventListener("abort",l)}let h=new XMLHttpRequest;h.open(r.method.toUpperCase(),r.url,!0),h.timeout=r.timeout;function g(){if(!h)return;const A=C.from("getAllResponseHeaders"in h&&h.getAllResponseHeaders()),O={data:!c||c==="text"||c==="json"?h.responseText:h.response,status:h.status,statusText:h.statusText,headers:A,config:e,request:h};ze(function(U){n(U),w()},function(U){s(U),w()},O),h=null}"onloadend"in h?h.onloadend=g:h.onreadystatechange=function(){!h||h.readyState!==4||h.status===0&&!(h.responseURL&&h.responseURL.indexOf("file:")===0)||setTimeout(g)},h.onabort=function(){h&&(s(new y("Request aborted",y.ECONNABORTED,e,h)),h=null)},h.onerror=function(){s(new y("Network Error",y.ERR_NETWORK,e,h)),h=null},h.ontimeout=function(){let F=r.timeout?"timeout of "+r.timeout+"ms exceeded":"timeout exceeded";const O=r.transitional||Ie;r.timeoutErrorMessage&&(F=r.timeoutErrorMessage),s(new y(F,O.clarifyTimeoutError?y.ETIMEDOUT:y.ECONNABORTED,e,h)),h=null},o===void 0&&i.setContentType(null),"setRequestHeader"in h&&a.forEach(i.toJSON(),function(F,O){h.setRequestHeader(O,F)}),a.isUndefined(r.withCredentials)||(h.withCredentials=!!r.withCredentials),c&&c!=="json"&&(h.responseType=r.responseType),u&&([b,p]=W(u,!0),h.addEventListener("progress",b)),f&&h.upload&&([d,S]=W(f),h.upload.addEventListener("progress",d),h.upload.addEventListener("loadend",S)),(r.cancelToken||r.signal)&&(l=A=>{h&&(s(!A||A.type?new j(null,e,h):A),h.abort(),h=null)},r.cancelToken&&r.cancelToken.subscribe(l),r.signal&&(r.signal.aborted?l():r.signal.addEventListener("abort",l)));const R=on(r.url);if(R&&T.protocols.indexOf(R)===-1){s(new y("Unsupported protocol "+R+":",y.ERR_BAD_REQUEST,e));return}h.send(o||null)})},mn=(e,t)=>{const{length:n}=e=e?e.filter(Boolean):[];if(t||n){let s=new AbortController,r;const o=function(u){if(!r){r=!0,c();const l=u instanceof Error?u:this.reason;s.abort(l instanceof y?l:new j(l instanceof Error?l.message:l))}};let i=t&&setTimeout(()=>{i=null,o(new y(`timeout ${t} of ms exceeded`,y.ETIMEDOUT))},t);const c=()=>{e&&(i&&clearTimeout(i),i=null,e.forEach(u=>{u.unsubscribe?u.unsubscribe(o):u.removeEventListener("abort",o)}),e=null)};e.forEach(u=>u.addEventListener("abort",o));const{signal:f}=s;return f.unsubscribe=()=>a.asap(c),f}},yn=function*(e,t){let n=e.byteLength;if(n<t){yield e;return}let s=0,r;for(;s<n;)r=s+t,yield e.slice(s,r),s=r},wn=async function*(e,t){for await(const n of bn(e))yield*yn(n,t)},bn=async function*(e){if(e[Symbol.asyncIterator]){yield*e;return}const t=e.getReader();try{for(;;){const{done:n,value:s}=await t.read();if(n)break;yield s}}finally{await t.cancel()}},Re=(e,t,n,s)=>{const r=wn(e,t);let o=0,i,c=f=>{i||(i=!0,s&&s(f))};return new ReadableStream({async pull(f){try{const{done:u,value:l}=await r.next();if(u){c(),f.close();return}let d=l.byteLength;if(n){let b=o+=d;n(b)}f.enqueue(new Uint8Array(l))}catch(u){throw c(u),u}},cancel(f){return c(f),r.return()}},{highWaterMark:2})},Y=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",We=Y&&typeof ReadableStream=="function",gn=Y&&(typeof TextEncoder=="function"?(e=>t=>e.encode(t))(new TextEncoder):async e=>new Uint8Array(await new Response(e).arrayBuffer())),ve=(e,...t)=>{try{return!!e(...t)}catch{return!1}},En=We&&ve(()=>{let e=!1;const t=new Request(T.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t}),Ae=64*1024,ae=We&&ve(()=>a.isReadableStream(new Response("").body)),v={stream:ae&&(e=>e.body)};Y&&(e=>{["text","arrayBuffer","blob","formData","stream"].forEach(t=>{!v[t]&&(v[t]=a.isFunction(e[t])?n=>n[t]():(n,s)=>{throw new y(`Response type '${t}' is not supported`,y.ERR_NOT_SUPPORT,s)})})})(new Response);const Sn=async e=>{if(e==null)return 0;if(a.isBlob(e))return e.size;if(a.isSpecCompliantForm(e))return(await new Request(T.origin,{method:"POST",body:e}).arrayBuffer()).byteLength;if(a.isArrayBufferView(e)||a.isArrayBuffer(e))return e.byteLength;if(a.isURLSearchParams(e)&&(e=e+""),a.isString(e))return(await gn(e)).byteLength},Rn=async(e,t)=>{const n=a.toFiniteNumber(e.getContentLength());return n??Sn(t)},An=Y&&(async e=>{let{url:t,method:n,data:s,signal:r,cancelToken:o,timeout:i,onDownloadProgress:c,onUploadProgress:f,responseType:u,headers:l,withCredentials:d="same-origin",fetchOptions:b}=Ve(e);u=u?(u+"").toLowerCase():"text";let S=mn([r,o&&o.toAbortSignal()],i),p;const w=S&&S.unsubscribe&&(()=>{S.unsubscribe()});let h;try{if(f&&En&&n!=="get"&&n!=="head"&&(h=await Rn(l,s))!==0){let O=new Request(t,{method:"POST",body:s,duplex:"half"}),_;if(a.isFormData(s)&&(_=O.headers.get("content-type"))&&l.setContentType(_),O.body){const[U,M]=ge(h,W(Ee(f)));s=Re(O.body,Ae,U,M)}}a.isString(d)||(d=d?"include":"omit");const g="credentials"in Request.prototype;p=new Request(t,{...b,signal:S,method:n.toUpperCase(),headers:l.normalize().toJSON(),body:s,duplex:"half",credentials:g?d:void 0});let R=await fetch(p);const A=ae&&(u==="stream"||u==="response");if(ae&&(c||A&&w)){const O={};["status","statusText","headers"].forEach(pe=>{O[pe]=R[pe]});const _=a.toFiniteNumber(R.headers.get("content-length")),[U,M]=c&&ge(_,W(Ee(c),!0))||[];R=new Response(Re(R.body,Ae,U,()=>{M&&M(),w&&w()}),O)}u=u||"text";let F=await v[a.findKey(v,u)||"text"](R,e);return!A&&w&&w(),await new Promise((O,_)=>{ze(O,_,{data:F,headers:C.from(R.headers),status:R.status,statusText:R.statusText,config:e,request:p})})}catch(g){throw w&&w(),g&&g.name==="TypeError"&&/Load failed|fetch/i.test(g.message)?Object.assign(new y("Network Error",y.ERR_NETWORK,e,p),{cause:g.cause||g}):y.from(g,g&&g.code,e,p)}}),ce={http:jt,xhr:hn,fetch:An};a.forEach(ce,(e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch{}Object.defineProperty(e,"adapterName",{value:t})}});const Te=e=>`- ${e}`,Tn=e=>a.isFunction(e)||e===null||e===!1,Ke={getAdapter:e=>{e=a.isArray(e)?e:[e];const{length:t}=e;let n,s;const r={};for(let o=0;o<t;o++){n=e[o];let i;if(s=n,!Tn(n)&&(s=ce[(i=String(n)).toLowerCase()],s===void 0))throw new y(`Unknown adapter '${i}'`);if(s)break;r[i||"#"+o]=s}if(!s){const o=Object.entries(r).map(([c,f])=>`adapter ${c} `+(f===!1?"is not supported by the environment":"is not available in the build"));let i=t?o.length>1?`since :
`+o.map(Te).join(`
`):" "+Te(o[0]):"as no adapter specified";throw new y("There is no suitable adapter to dispatch the request "+i,"ERR_NOT_SUPPORT")}return s},adapters:ce};function se(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new j(null,e)}function Oe(e){return se(e),e.headers=C.from(e.headers),e.data=ne.call(e,e.transformRequest),["post","put","patch"].indexOf(e.method)!==-1&&e.headers.setContentType("application/x-www-form-urlencoded",!1),Ke.getAdapter(e.adapter||H.adapter)(e).then(function(s){return se(e),s.data=ne.call(e,e.transformResponse,s),s.headers=C.from(s.headers),s},function(s){return Me(s)||(se(e),s&&s.response&&(s.response.data=ne.call(e,e.transformResponse,s.response),s.response.headers=C.from(s.response.headers))),Promise.reject(s)})}const Xe="1.9.0",ee={};["object","boolean","number","function","string","symbol"].forEach((e,t)=>{ee[e]=function(s){return typeof s===e||"a"+(t<1?"n ":" ")+e}});const xe={};ee.transitional=function(t,n,s){function r(o,i){return"[Axios v"+Xe+"] Transitional option '"+o+"'"+i+(s?". "+s:"")}return(o,i,c)=>{if(t===!1)throw new y(r(i," has been removed"+(n?" in "+n:"")),y.ERR_DEPRECATED);return n&&!xe[i]&&(xe[i]=!0,console.warn(r(i," has been deprecated since v"+n+" and will be removed in the near future"))),t?t(o,i,c):!0}};ee.spelling=function(t){return(n,s)=>(console.warn(`${s} is likely a misspelling of ${t}`),!0)};function On(e,t,n){if(typeof e!="object")throw new y("options must be an object",y.ERR_BAD_OPTION_VALUE);const s=Object.keys(e);let r=s.length;for(;r-- >0;){const o=s[r],i=t[o];if(i){const c=e[o],f=c===void 0||i(c,o,e);if(f!==!0)throw new y("option "+o+" must be "+f,y.ERR_BAD_OPTION_VALUE);continue}if(n!==!0)throw new y("Unknown option "+o,y.ERR_BAD_OPTION)}}const V={assertOptions:On,validators:ee},P=V.validators;let k=class{constructor(t){this.defaults=t||{},this.interceptors={request:new we,response:new we}}async request(t,n){try{return await this._request(t,n)}catch(s){if(s instanceof Error){let r={};Error.captureStackTrace?Error.captureStackTrace(r):r=new Error;const o=r.stack?r.stack.replace(/^.+\n/,""):"";try{s.stack?o&&!String(s.stack).endsWith(o.replace(/^.+\n.+\n/,""))&&(s.stack+=`
`+o):s.stack=o}catch{}}throw s}}_request(t,n){typeof t=="string"?(n=n||{},n.url=t):n=t||{},n=L(this.defaults,n);const{transitional:s,paramsSerializer:r,headers:o}=n;s!==void 0&&V.assertOptions(s,{silentJSONParsing:P.transitional(P.boolean),forcedJSONParsing:P.transitional(P.boolean),clarifyTimeoutError:P.transitional(P.boolean)},!1),r!=null&&(a.isFunction(r)?n.paramsSerializer={serialize:r}:V.assertOptions(r,{encode:P.function,serialize:P.function},!0)),n.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?n.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:n.allowAbsoluteUrls=!0),V.assertOptions(n,{baseUrl:P.spelling("baseURL"),withXsrfToken:P.spelling("withXSRFToken")},!0),n.method=(n.method||this.defaults.method||"get").toLowerCase();let i=o&&a.merge(o.common,o[n.method]);o&&a.forEach(["delete","get","head","post","put","patch","common"],p=>{delete o[p]}),n.headers=C.concat(i,o);const c=[];let f=!0;this.interceptors.request.forEach(function(w){typeof w.runWhen=="function"&&w.runWhen(n)===!1||(f=f&&w.synchronous,c.unshift(w.fulfilled,w.rejected))});const u=[];this.interceptors.response.forEach(function(w){u.push(w.fulfilled,w.rejected)});let l,d=0,b;if(!f){const p=[Oe.bind(this),void 0];for(p.unshift.apply(p,c),p.push.apply(p,u),b=p.length,l=Promise.resolve(n);d<b;)l=l.then(p[d++],p[d++]);return l}b=c.length;let S=n;for(d=0;d<b;){const p=c[d++],w=c[d++];try{S=p(S)}catch(h){w.call(this,h);break}}try{l=Oe.call(this,S)}catch(p){return Promise.reject(p)}for(d=0,b=u.length;d<b;)l=l.then(u[d++],u[d++]);return l}getUri(t){t=L(this.defaults,t);const n=Je(t.baseURL,t.url,t.allowAbsoluteUrls);return $e(n,t.params,t.paramsSerializer)}};a.forEach(["delete","get","head","options"],function(t){k.prototype[t]=function(n,s){return this.request(L(s||{},{method:t,url:n,data:(s||{}).data}))}});a.forEach(["post","put","patch"],function(t){function n(s){return function(o,i,c){return this.request(L(c||{},{method:t,headers:s?{"Content-Type":"multipart/form-data"}:{},url:o,data:i}))}}k.prototype[t]=n(),k.prototype[t+"Form"]=n(!0)});let xn=class Ge{constructor(t){if(typeof t!="function")throw new TypeError("executor must be a function.");let n;this.promise=new Promise(function(o){n=o});const s=this;this.promise.then(r=>{if(!s._listeners)return;let o=s._listeners.length;for(;o-- >0;)s._listeners[o](r);s._listeners=null}),this.promise.then=r=>{let o;const i=new Promise(c=>{s.subscribe(c),o=c}).then(r);return i.cancel=function(){s.unsubscribe(o)},i},t(function(o,i,c){s.reason||(s.reason=new j(o,i,c),n(s.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(t){if(this.reason){t(this.reason);return}this._listeners?this._listeners.push(t):this._listeners=[t]}unsubscribe(t){if(!this._listeners)return;const n=this._listeners.indexOf(t);n!==-1&&this._listeners.splice(n,1)}toAbortSignal(){const t=new AbortController,n=s=>{t.abort(s)};return this.subscribe(n),t.signal.unsubscribe=()=>this.unsubscribe(n),t.signal}static source(){let t;return{token:new Ge(function(r){t=r}),cancel:t}}};function Cn(e){return function(n){return e.apply(null,n)}}function Nn(e){return a.isObject(e)&&e.isAxiosError===!0}const ue={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(ue).forEach(([e,t])=>{ue[t]=e});function Qe(e){const t=new k(e),n=Ce(k.prototype.request,t);return a.extend(n,k.prototype,t,{allOwnKeys:!0}),a.extend(n,t,null,{allOwnKeys:!0}),n.create=function(r){return Qe(L(e,r))},n}const E=Qe(H);E.Axios=k;E.CanceledError=j;E.CancelToken=xn;E.isCancel=Me;E.VERSION=Xe;E.toFormData=Z;E.AxiosError=y;E.Cancel=E.CanceledError;E.all=function(t){return Promise.all(t)};E.spread=Cn;E.isAxiosError=Nn;E.mergeConfig=L;E.AxiosHeaders=C;E.formToJSON=e=>He(a.isHTMLForm(e)?new FormData(e):e);E.getAdapter=Ke.getAdapter;E.HttpStatusCode=ue;E.default=E;const{Axios:_n,AxiosError:Un,CanceledError:Bn,isCancel:kn,CancelToken:Ln,VERSION:Dn,all:jn,Cancel:qn,isAxiosError:$n,spread:In,toFormData:Hn,AxiosHeaders:Mn,HttpStatusCode:zn,formToJSON:Jn,getAdapter:Vn,mergeConfig:Wn}=E,m=E.create({baseURL:"https://easybotadmin.onrender.com/api",timeout:1e4,headers:{"Content-Type":"application/json"}});m.interceptors.request.use(e=>{const t=localStorage.getItem("admin_token");return t&&(e.headers.Authorization=`Bearer ${t}`),e},e=>Promise.reject(e));m.interceptors.response.use(e=>e,e=>{var t,n,s;return((t=e.response)==null?void 0:t.status)===401&&!((s=(n=e.config)==null?void 0:n.url)!=null&&s.includes("/auth/login"))&&(localStorage.removeItem("admin_token"),window.location.href="/login"),Promise.reject(e)});const vn={login:async(e,t)=>(await m.post("/auth/login",{username:e,password:t})).data,logout:async()=>{await m.post("/auth/logout")},refreshToken:async()=>(await m.post("/auth/refresh")).data,getCurrentUser:async()=>(await m.get("/auth/me")).data,changePassword:async(e,t)=>{await m.post("/auth/change-password",{current_password:e,new_password:t})},getDashboardAnalytics:async()=>(await m.get("/admin/dashboard")).data,getSystemHealth:async()=>(await m.get("/admin/system-health")).data,getSystemAlerts:async()=>(await m.get("/admin/alerts")).data,resolveAlert:async e=>{await m.post(`/admin/alerts/${e}/resolve`)},getBlockchainAnalytics:async()=>(await m.get("/admin/blockchain-analytics")).data,getTransactionMetrics:async()=>(await m.get("/admin/transaction-metrics")).data,getUserMetrics:async()=>(await m.get("/admin/user-metrics")).data,resetSettings:async()=>(await m.post("/admin/settings/reset")).data,backupSettings:async()=>(await m.post("/admin/settings/backup")).data,restoreSettings:async e=>(await m.post("/admin/settings/restore",{backup_id:e})).data,getFailedFeeTransactions:async()=>(await m.get("/admin/failed-fees")).data,getAdminFeeTransactions:async e=>(await m.get("/admin/fee-transactions",{params:e})).data,retryFailedFeeTransaction:async e=>{await m.post(`/admin/fee-transactions/${e}/retry`)},getAdminFeeStatistics:async e=>(await m.get("/admin/fee-statistics",{params:e})).data,updateAdminFeeSettings:async e=>{await m.put("/admin/fee-settings",e)},getBots:async e=>(await m.get("/bots",{params:e})).data,getBot:async e=>(await m.get(`/bots/${e}`)).data,createBot:async e=>(await m.post("/bots",e)).data,updateBot:async(e,t)=>(await m.put(`/bots/${e}`,t)).data,deleteBot:async e=>{await m.delete(`/bots/${e}`)},startBot:async e=>{await m.post(`/bots/${e}/start`)},stopBot:async e=>{await m.post(`/bots/${e}/stop`)},restartBot:async e=>{await m.post(`/bots/${e}/restart`)},updateBotStats:async(e,t)=>{await m.put(`/bots/${e}/stats`,t)},getBotsAnalytics:async()=>(await m.get("/bots/analytics")).data,getTransactions:async e=>(await m.get("/admin/transactions",{params:e})).data,getTransaction:async e=>(await m.get(`/admin/transactions/${e}`)).data,getTransactionsByUser:async(e,t)=>(await m.get(`/admin/users/${e}/transactions`,{params:t})).data,getUsers:async e=>(await m.get("/admin/users",{params:e})).data,getUser:async e=>(await m.get(`/admin/users/${e}`)).data,updateUser:async(e,t)=>(await m.put(`/admin/users/${e}`,t)).data,deleteUser:async e=>{await m.delete(`/admin/users/${e}`)},getUserAnalytics:async e=>(await m.get(`/admin/users/${e}/analytics`)).data,getAdmins:async()=>(await m.get("/admin/admins")).data,createAdmin:async e=>(await m.post("/auth/create-admin",e)).data,updateAdmin:async(e,t)=>(await m.put(`/admin/admins/${e}`,t)).data,deleteAdmin:async e=>{await m.delete(`/admin/admins/${e}`)},getSettings:async()=>(await m.get("/admin/settings")).data,updateSettings:async e=>(await m.put("/admin/settings",e)).data,getAnalyticsReport:async e=>(await m.get("/admin/analytics/report",{params:e})).data,exportData:async(e,t)=>(await m.get(`/admin/export/${e}`,{params:{format:t},responseType:"blob"})).data};export{vn as adminApi,vn as default};
