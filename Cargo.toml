[package]
name = "Easybot"
version = "0.1.0"
edition = "2021"

[dependencies]
teloxide = { version = "0.12", features = ["macros", "auto-send", "throttle"] }
teloxide-core = "0.9.1"
log = "0.4"
pretty_env_logger = "0.5"
tokio = { version = "1.17.0", features = ["rt-multi-thread", "macros", "time"] }
dotenv = "0.15"
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
chrono = { version = "0.4", features = ["serde"] }
thiserror = "1.0"
async-trait = "0.1"
futures = "0.3"
futures-util = "0.3"
env_logger = "0.10"
uuid = { version = "1.3", features = ["v4", "serde"] }
mongodb = { version = "2.5", features = ["tokio-runtime"], default-features = false }
bson = { version = "2.6", features = ["chrono-0_4", "uuid-1"] }

# Blockchain dependencies
ethers = { version = "1.0.2", features = ["rustls"], default-features = false }
solana-sdk = { version = "2.1.0", features = ["full"] }
solana-client = { version = "2.1.0" }
solana-transaction-status = { version = "2.1.0" }
spl-token = { version = "6.0.0", features = ["no-entrypoint"] }
spl-associated-token-account = { version = "3.0.0", features = ["no-entrypoint"] }
bs58 = "0.4"
hex = "0.4"
rand = "0.7"
rand_core = "0.5"
num-format = "0.4"
num_cpus = "1.15"
reqwest = { version = "0.11", features = ["json", "rustls-tls"], default-features = false }
elliptic-curve = "=0.12.3"
k256 = "=0.12.0"
secp256k1 = "0.20.3"
tiny-keccak = "2.0.2"
sha3 = "0.10"
getrandom = "0.2"
lazy_static = "1.4"
lru = "0.10"
base64 = "0.13"
bincode = "1.3"

# Web server dependencies
axum = { version = "0.6.20", features = ["headers", "macros"] }
tower = "0.4"
tower-http = { version = "0.4", features = ["trace", "cors", "auth", "fs"] }
jsonwebtoken = "8.3"
bcrypt = "0.15"

# Additional dependencies
anyhow = "1.0"
once_cell = "1.18"
stacker = "0.1.15"
tracing = "0.1.41"
tracing-subscriber = "0.3.19"
regex = "1.10"
# Always use vendored OpenSSL to avoid system dependency issues
openssl = { version = "0.10", features = ["vendored"] }
openssl-sys = { version = "0.9", features = ["vendored"] }
