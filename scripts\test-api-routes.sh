#!/bin/bash

# =============================================================================
# EasyBot API Routes Test Script
# =============================================================================
# This script tests all the configurable API routes to ensure they're working
# correctly with the environment variable configuration.

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default configuration
BASE_URL="http://localhost:3000"
ADMIN_USERNAME="admin"
ADMIN_PASSWORD="admin123"
JWT_TOKEN=""

# Load environment variables if .env exists
if [ -f .env ]; then
    echo -e "${BLUE}Loading environment variables from .env file...${NC}"
    export $(grep -v '^#' .env | xargs)
fi

# Function to print colored output
print_status() {
    local status=$1
    local message=$2
    case $status in
        "INFO")
            echo -e "${BLUE}[INFO]${NC} $message"
            ;;
        "SUCCESS")
            echo -e "${GREEN}[SUCCESS]${NC} $message"
            ;;
        "ERROR")
            echo -e "${RED}[ERROR]${NC} $message"
            ;;
        "WARNING")
            echo -e "${YELLOW}[WARNING]${NC} $message"
            ;;
    esac
}

# Function to test an endpoint
test_endpoint() {
    local method=$1
    local endpoint=$2
    local description=$3
    local data=$4
    local auth_required=$5

    print_status "INFO" "Testing: $method $endpoint - $description"
    
    local curl_cmd="curl -s -w '%{http_code}' -X $method"
    
    if [ "$auth_required" = "true" ] && [ -n "$JWT_TOKEN" ]; then
        curl_cmd="$curl_cmd -H 'Authorization: Bearer $JWT_TOKEN'"
    fi
    
    if [ -n "$data" ]; then
        curl_cmd="$curl_cmd -H 'Content-Type: application/json' -d '$data'"
    fi
    
    curl_cmd="$curl_cmd $BASE_URL$endpoint"
    
    local response=$(eval $curl_cmd)
    local http_code="${response: -3}"
    local body="${response%???}"
    
    case $http_code in
        200|201)
            print_status "SUCCESS" "$endpoint responded with $http_code"
            ;;
        401)
            if [ "$auth_required" = "true" ]; then
                print_status "WARNING" "$endpoint requires authentication (401) - Expected"
            else
                print_status "ERROR" "$endpoint returned 401 unexpectedly"
            fi
            ;;
        404)
            print_status "ERROR" "$endpoint not found (404) - Check route configuration"
            ;;
        500)
            print_status "ERROR" "$endpoint server error (500)"
            ;;
        *)
            print_status "WARNING" "$endpoint returned $http_code"
            ;;
    esac
    
    echo ""
}

# Function to login and get JWT token
login() {
    print_status "INFO" "Attempting to login..."
    
    local login_endpoint="${API_AUTH_LOGIN:-/api/auth/login}"
    local login_data="{\"username\":\"$ADMIN_USERNAME\",\"password\":\"$ADMIN_PASSWORD\"}"
    
    local response=$(curl -s -w '%{http_code}' -X POST \
        -H 'Content-Type: application/json' \
        -d "$login_data" \
        "$BASE_URL$login_endpoint")
    
    local http_code="${response: -3}"
    local body="${response%???}"
    
    if [ "$http_code" = "200" ]; then
        JWT_TOKEN=$(echo "$body" | grep -o '"token":"[^"]*"' | cut -d'"' -f4)
        if [ -n "$JWT_TOKEN" ]; then
            print_status "SUCCESS" "Login successful, JWT token obtained"
        else
            print_status "WARNING" "Login returned 200 but no token found"
        fi
    else
        print_status "WARNING" "Login failed with code $http_code - Will test without authentication"
    fi
    
    echo ""
}

# Main test function
run_tests() {
    echo -e "${BLUE}==============================================================================${NC}"
    echo -e "${BLUE}                    EasyBot API Routes Test Suite${NC}"
    echo -e "${BLUE}==============================================================================${NC}"
    echo ""
    
    print_status "INFO" "Base URL: $BASE_URL"
    print_status "INFO" "Testing with environment-configured routes..."
    echo ""
    
    # Test authentication endpoints
    echo -e "${YELLOW}--- Authentication Endpoints ---${NC}"
    test_endpoint "POST" "${API_AUTH_LOGIN:-/api/auth/login}" "Admin Login" "{\"username\":\"test\",\"password\":\"test\"}" "false"
    test_endpoint "POST" "${API_AUTH_LOGOUT:-/api/auth/logout}" "Admin Logout" "" "true"
    test_endpoint "POST" "${API_AUTH_REFRESH:-/api/auth/refresh}" "Token Refresh" "" "true"
    test_endpoint "GET" "${API_AUTH_ME:-/api/auth/me}" "Current User Info" "" "true"
    test_endpoint "POST" "${API_AUTH_CHANGE_PASSWORD:-/api/auth/change-password}" "Change Password" "{\"current_password\":\"old\",\"new_password\":\"new\"}" "true"
    test_endpoint "POST" "${API_AUTH_CREATE_ADMIN:-/api/auth/create-admin}" "Create Admin" "{\"username\":\"test\",\"email\":\"<EMAIL>\",\"password\":\"test\",\"role\":\"Admin\"}" "true"
    
    # Attempt login for authenticated tests
    login
    
    # Test bot management endpoints
    echo -e "${YELLOW}--- Bot Management Endpoints ---${NC}"
    test_endpoint "GET" "${API_BOTS_BASE:-/api/bots}" "List Bots" "" "true"
    test_endpoint "POST" "${API_BOTS_BASE:-/api/bots}" "Create Bot" "{\"name\":\"Test Bot\",\"description\":\"Test\",\"bot_type\":\"BSC\"}" "true"
    test_endpoint "GET" "${API_BOTS_ANALYTICS:-/api/bots/analytics}" "Bot Analytics" "" "true"
    
    # Test admin panel endpoints
    echo -e "${YELLOW}--- Admin Panel Endpoints ---${NC}"
    test_endpoint "GET" "${API_ADMIN_DASHBOARD:-/api/admin/dashboard}" "Dashboard Data" "" "true"
    test_endpoint "GET" "${API_ADMIN_ANALYTICS:-/api/admin/analytics}" "Analytics Data" "" "true"
    test_endpoint "GET" "${API_ADMIN_SYSTEM_HEALTH:-/api/admin/system-health}" "System Health" "" "true"
    test_endpoint "GET" "${API_ADMIN_ALERTS:-/api/admin/alerts}" "System Alerts" "" "true"
    test_endpoint "GET" "${API_ADMIN_TRANSACTIONS:-/api/admin/transactions}" "Transactions" "" "true"
    test_endpoint "GET" "${API_ADMIN_USERS:-/api/admin/users}" "Users" "" "true"
    test_endpoint "GET" "${API_ADMIN_ADMINS:-/api/admin/admins}" "Admin Users" "" "true"
    test_endpoint "GET" "${API_ADMIN_SETTINGS:-/api/admin/settings}" "Settings" "" "true"
    
    echo -e "${BLUE}==============================================================================${NC}"
    echo -e "${GREEN}                           Test Suite Complete${NC}"
    echo -e "${BLUE}==============================================================================${NC}"
    echo ""
    print_status "INFO" "Check the results above to verify your API routes configuration"
    print_status "INFO" "Green = Working, Yellow = Expected behavior, Red = Needs attention"
}

# Function to show current route configuration
show_config() {
    echo -e "${BLUE}==============================================================================${NC}"
    echo -e "${BLUE}                    Current API Routes Configuration${NC}"
    echo -e "${BLUE}==============================================================================${NC}"
    echo ""
    
    echo -e "${YELLOW}Authentication Routes:${NC}"
    echo "  API_AUTH_BASE: ${API_AUTH_BASE:-/api/auth}"
    echo "  API_AUTH_LOGIN: ${API_AUTH_LOGIN:-/api/auth/login}"
    echo "  API_AUTH_LOGOUT: ${API_AUTH_LOGOUT:-/api/auth/logout}"
    echo "  API_AUTH_REFRESH: ${API_AUTH_REFRESH:-/api/auth/refresh}"
    echo "  API_AUTH_ME: ${API_AUTH_ME:-/api/auth/me}"
    echo "  API_AUTH_CHANGE_PASSWORD: ${API_AUTH_CHANGE_PASSWORD:-/api/auth/change-password}"
    echo "  API_AUTH_CREATE_ADMIN: ${API_AUTH_CREATE_ADMIN:-/api/auth/create-admin}"
    echo ""
    
    echo -e "${YELLOW}Bot Management Routes:${NC}"
    echo "  API_BOTS_BASE: ${API_BOTS_BASE:-/api/bots}"
    echo "  API_BOTS_ANALYTICS: ${API_BOTS_ANALYTICS:-/api/bots/analytics}"
    echo "  API_BOT_START: ${API_BOT_START:-/start}"
    echo "  API_BOT_STOP: ${API_BOT_STOP:-/stop}"
    echo "  API_BOT_RESTART: ${API_BOT_RESTART:-/restart}"
    echo "  API_BOT_STATS: ${API_BOT_STATS:-/stats}"
    echo ""
    
    echo -e "${YELLOW}Admin Panel Routes:${NC}"
    echo "  API_ADMIN_BASE: ${API_ADMIN_BASE:-/api/admin}"
    echo "  API_ADMIN_DASHBOARD: ${API_ADMIN_DASHBOARD:-/api/admin/dashboard}"
    echo "  API_ADMIN_ANALYTICS: ${API_ADMIN_ANALYTICS:-/api/admin/analytics}"
    echo "  API_ADMIN_SYSTEM_HEALTH: ${API_ADMIN_SYSTEM_HEALTH:-/api/admin/system-health}"
    echo "  API_ADMIN_ALERTS: ${API_ADMIN_ALERTS:-/api/admin/alerts}"
    echo "  API_ADMIN_TRANSACTIONS: ${API_ADMIN_TRANSACTIONS:-/api/admin/transactions}"
    echo "  API_ADMIN_USERS: ${API_ADMIN_USERS:-/api/admin/users}"
    echo "  API_ADMIN_ADMINS: ${API_ADMIN_ADMINS:-/api/admin/admins}"
    echo "  API_ADMIN_SETTINGS: ${API_ADMIN_SETTINGS:-/api/admin/settings}"
    echo ""
}

# Parse command line arguments
case "${1:-test}" in
    "test")
        run_tests
        ;;
    "config")
        show_config
        ;;
    "help")
        echo "Usage: $0 [command]"
        echo ""
        echo "Commands:"
        echo "  test     Run API routes test suite (default)"
        echo "  config   Show current route configuration"
        echo "  help     Show this help message"
        echo ""
        echo "Environment Variables:"
        echo "  BASE_URL         API base URL (default: http://localhost:3000)"
        echo "  ADMIN_USERNAME   Admin username for testing (default: admin)"
        echo "  ADMIN_PASSWORD   Admin password for testing (default: admin123)"
        echo ""
        ;;
    *)
        echo "Unknown command: $1"
        echo "Use '$0 help' for usage information"
        exit 1
        ;;
esac
