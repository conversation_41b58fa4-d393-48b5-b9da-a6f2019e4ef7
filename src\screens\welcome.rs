use teloxide::types::{InlineKeyboardButton, InlineKeyboardMarkup};
use teloxide::prelude::Requester;
use serde_json::json;
use crate::model::{UserData, BotError, Blockchain};
use crate::service::BotService;

pub async fn handle_welcome(
    bot_service: &BotService,
    chat_id: i64,
    tg_user: &teloxide::types::User,
) -> Result<(), BotError> {
    println!("=== WELCOME HANDLER START ===");
    println!("User: {:?}", tg_user);
    println!("Chat ID: {}", chat_id);

    let start_time = std::time::Instant::now();

    println!("Getting or creating user data...");
    let user_data = bot_service.get_or_create_user(chat_id, tg_user).await?;
    println!("User data retrieval took: {:?}", start_time.elapsed());

    if user_data.wallets_generated() {
        println!("RETURNING USER: {}", user_data.display_name());

        let returning_start = std::time::Instant::now();
        handle_returning_user(bot_service, chat_id, &user_data).await?;
        println!("Returning user flow took: {:?}", returning_start.elapsed());
    } else {
        println!("NEW USER: {}", tg_user.first_name);

        let new_user_start = std::time::Instant::now();
        handle_new_user(bot_service, chat_id, &user_data).await?;
        println!("New user flow took: {:?}", new_user_start.elapsed());
    }

    println!("Total welcome handler time: {:?}", start_time.elapsed());
    println!("=== WELCOME HANDLER END ===");

    Ok(())
}

pub async fn handle_privatekeys(
    bot_service: &BotService,
    chat_id: i64,
) -> Result<(), BotError> {
    let tg_user = match bot_service.bot().get_chat_member(teloxide::types::ChatId(chat_id), teloxide::types::UserId(chat_id as u64)).await {
        Ok(member) => member.user,
        Err(_) => {
            bot_service.send_message(chat_id, "Failed to get user information. Please try again.").await?;
            return Ok(());
        }
    };

    let user_data = bot_service.get_or_create_user(chat_id, &tg_user).await?;

    if user_data.wallets_generated() {
        let sol_wallet = user_data.get_wallet(&Blockchain::SOL);
        let bsc_wallet = user_data.get_wallet(&Blockchain::BSC);
        let base_wallet = user_data.get_wallet(&Blockchain::BASE);
        let eth_wallet = user_data.get_wallet(&Blockchain::ETH);
        let private_keys_text = format!(
            "<b>Your Private Keys</b>\n\n\
            <b>WARNING:</b> Never share these with anyone!\n\n\
            SOL: <tg-spoiler><code>{}</code></tg-spoiler>\n\
            BSC: <tg-spoiler><code>{}</code></tg-spoiler>\n\
            BASE: <tg-spoiler><code>{}</code></tg-spoiler>\n\
            ETH: <tg-spoiler><code>{}</code></tg-spoiler>\n\n\
            <i>This message will be automatically deleted in 60 seconds for security.</i>",
            sol_wallet.private_key,
            bsc_wallet.private_key,
            base_wallet.private_key,
            eth_wallet.private_key
        );

        let sent_msg = bot_service.send_message(chat_id, &private_keys_text).await?;
        let bot_service_clone = bot_service.clone();
        let message_id = sent_msg.id.0;
        tokio::spawn(async move {
            tokio::time::sleep(tokio::time::Duration::from_secs(60)).await;
            let _ = bot_service_clone.delete_message(chat_id, message_id).await;
        });

        Ok(())
    } else {
        bot_service.send_message(chat_id, "Your wallets have not been generated yet. Please use /start first.").await?;
        Ok(())
    }
}

async fn handle_new_user(
    bot_service: &BotService,
    chat_id: i64,
    user_data: &UserData,
) -> Result<(), BotError> {
    let welcome_text = format!(
        "👋 <b>Welcome to EasyBot!</b>\n\n\
        Hello, {}! I'm your crypto trading assistant.\n\n\
        I'm generating your wallets now. This may take a moment...",
        user_data.user.first_name
    );

    let sent_msg = bot_service.send_message(chat_id, &welcome_text).await?;
    let progress_text = format!(
        "👋 <b>Welcome to EasyBot!</b>\n\n\
        Hello, {}! I'm your crypto trading assistant.\n\n\
        <b>Generating wallets...</b>\n\
        This may take a few seconds. Please wait...",
        user_data.user.first_name
    );

    bot_service.edit_message(chat_id, sent_msg.id.0, &progress_text).await?;

    println!("Generating wallets...");

    let blockchain_service = bot_service.blockchain_service();

    let mut updated_user_data = user_data.clone();

    println!("Generating BSC wallet...");
    let bsc_start = std::time::Instant::now();
    let bsc_wallet = blockchain_service.generate_wallet(Blockchain::BSC).await?;
    println!("BSC wallet generated: {} (took {:?})", bsc_wallet.address, bsc_start.elapsed());

    println!("Generating SOL wallet...");
    let sol_start = std::time::Instant::now();
    let sol_wallet = blockchain_service.generate_wallet(Blockchain::SOL).await?;
    println!("SOL wallet generated: {} (took {:?})", sol_wallet.address, sol_start.elapsed());

    println!("Generating ETH wallet...");
    let eth_start = std::time::Instant::now();
    let eth_wallet = blockchain_service.generate_wallet(Blockchain::ETH).await?;
    println!("ETH wallet generated: {} (took {:?})", eth_wallet.address, eth_start.elapsed());

    println!("Generating BASE wallet...");
    let base_start = std::time::Instant::now();
    let base_wallet = blockchain_service.generate_wallet(Blockchain::BASE).await?;
    println!("BASE wallet generated: {} (took {:?})", base_wallet.address, base_start.elapsed());

    updated_user_data.wallets.bsc_wallet = bsc_wallet.clone();
    updated_user_data.wallets.sol_wallet = sol_wallet.clone();
    updated_user_data.wallets.eth_wallet = eth_wallet.clone();
    updated_user_data.wallets.base_wallet = base_wallet.clone();

    updated_user_data.set_wallets_generated(true);

    println!("All wallets generated and assigned to user");
    let final_text = format!(
        "👋 Welcome to EasyBot!\n\n\
        Here are your wallet addresses:\n\n\
        <b>SOL Wallet Address:</b> <code>{}</code>\n\
        <b>BSC Wallet Address:</b> <code>{}</code>\n\
        <b>BASE Wallet Address:</b> <code>{}</code>\n\
        <b>ETH Wallet Address:</b> <code>{}</code>\n\n\
        <b>Save these private keys below:</b>\n\n\
        SOL: <tg-spoiler><code>{}</code></tg-spoiler>\n\
        BSC: <tg-spoiler><code>{}</code></tg-spoiler>\n\
        BASE: <tg-spoiler><code>{}</code></tg-spoiler>\n\
        ETH: <tg-spoiler><code>{}</code></tg-spoiler>\n\n\
        <b>To get started, please read our <a href=\"https://docs.io\">docs</a></b>",
        sol_wallet.address,
        bsc_wallet.address,
        base_wallet.address,
        eth_wallet.address,
        sol_wallet.private_key,
        bsc_wallet.private_key,
        base_wallet.private_key,
        eth_wallet.private_key
    );

    let keyboard = InlineKeyboardMarkup::new([
        vec![
            InlineKeyboardButton::callback(
                "BSC".to_string(),
                json!({ "command": "view_bsc" }).to_string(),
            ),
            InlineKeyboardButton::callback(
                "BASE".to_string(),
                json!({ "command": "view_base" }).to_string(),
            ),
        ],
        vec![
            InlineKeyboardButton::callback(
                "SOL".to_string(),
                json!({ "command": "view_sol" }).to_string(),
            ),
            InlineKeyboardButton::callback(
                "ETH".to_string(),
                json!({ "command": "view_eth" }).to_string(),
            ),
        ],
        vec![
            InlineKeyboardButton::callback(
                "* Dismiss message".to_string(),
                json!({ "command": "dismiss_message" }).to_string(),
            ),
        ],
    ]);

    bot_service.edit_message_with_keyboard(chat_id, sent_msg.id.0, &final_text, keyboard).await?;

    bot_service.user_service().cache_user_data(updated_user_data.clone()).await;

    crate::service::DbService::save_user_wallets(&updated_user_data.wallets).await?;

    Ok(())
}

async fn handle_returning_user(
    bot_service: &BotService,
    chat_id: i64,
    user_data: &UserData,
) -> Result<(), BotError> {
    let sol_wallet = user_data.get_wallet(&Blockchain::SOL);
    let bsc_wallet = user_data.get_wallet(&Blockchain::BSC);
    let base_wallet = user_data.get_wallet(&Blockchain::BASE);
    let eth_wallet = user_data.get_wallet(&Blockchain::ETH);
    let welcome_text = format!(
        "👋 Welcome back to EasyBot!\n\n\
        Here are your wallet addresses:\n\n\
        <b>SOL Wallet Address:</b> <code>{}</code>\n\
        <b>BSC Wallet Address:</b> <code>{}</code>\n\
        <b>BASE Wallet Address:</b> <code>{}</code>\n\
        <b>ETH Wallet Address:</b> <code>{}</code>\n\n\
        <b>Fire /privatekeys to view</b>\n\n",
        sol_wallet.address,
        bsc_wallet.address,
        base_wallet.address,
        eth_wallet.address
    );

    let keyboard = InlineKeyboardMarkup::new([
        vec![
            InlineKeyboardButton::callback(
                "BSC".to_string(),
                json!({ "command": "view_bsc" }).to_string(),
            ),
            InlineKeyboardButton::callback(
                "BASE".to_string(),
                json!({ "command": "view_base" }).to_string(),
            ),
        ],
        vec![
            InlineKeyboardButton::callback(
                "SOL".to_string(),
                json!({ "command": "view_sol" }).to_string(),
            ),
            InlineKeyboardButton::callback(
                "ETH".to_string(),
                json!({ "command": "view_eth" }).to_string(),
            ),
        ],
        vec![
            InlineKeyboardButton::callback(
                "* Dismiss message".to_string(),
                json!({ "command": "dismiss_message" }).to_string(),
            ),
        ],
    ]);

    bot_service.send_message_with_keyboard(chat_id, &welcome_text, keyboard).await?;

    Ok(())
}

async fn generate_wallets_for_user(user_data: &mut UserData) -> Result<(), BotError> {
    let blockchain_service = crate::service::BlockchainService::new();

    println!("Generating wallets...");

    println!("Generating BSC wallet...");
    let bsc_wallet = blockchain_service.generate_wallet(Blockchain::BSC).await?;
    println!("BSC wallet generated: {}", bsc_wallet.address);

    println!("Generating SOL wallet...");
    let sol_wallet = blockchain_service.generate_wallet(Blockchain::SOL).await?;
    println!("SOL wallet generated: {}", sol_wallet.address);

    println!("Generating ETH wallet...");
    let eth_wallet = blockchain_service.generate_wallet(Blockchain::ETH).await?;
    println!("ETH wallet generated: {}", eth_wallet.address);

    println!("Generating BASE wallet...");
    let base_wallet = blockchain_service.generate_wallet(Blockchain::BASE).await?;
    println!("BASE wallet generated: {}", base_wallet.address);

    user_data.wallets.bsc_wallet = bsc_wallet;
    user_data.wallets.sol_wallet = sol_wallet;
    user_data.wallets.eth_wallet = eth_wallet;
    user_data.wallets.base_wallet = base_wallet;

    user_data.set_wallets_generated(true);

    Ok(())
}

