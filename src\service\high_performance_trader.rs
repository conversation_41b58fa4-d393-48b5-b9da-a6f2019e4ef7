use std::sync::Arc;
use tokio::sync::{Semaphore, RwLock};
use std::collections::HashMap;
use chrono::{DateTime, Utc};
use crate::model::{BotError, Blockchain, Wallet};
use crate::service::{SolanaTraderService, EvmTraderService, AdminFeeService};
use crate::config::defaults;
use mongodb::bson::oid::ObjectId;
use serde_json;
use std::str::FromStr;
use serde::{Serialize, Deserialize};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct HighPerformanceTradeResult {
    pub signature: String,
    pub input_amount: u64,
    pub output_amount: u64,
    pub native_token_amount: Option<f64>,
    pub token_amount: Option<f64>,
    pub native_token_symbol: Option<String>,
    pub admin_fee_amount: Option<f64>,
    pub admin_fee_percentage: Option<f64>,
    pub success: bool,
    pub message: String,
}

/// High-Performance Trading Service with Resource Optimization
/// Allocates maximum resources to buy/sell operations for optimal speed
pub struct HighPerformanceTrader {
    // Resource pools for trading operations
    trading_semaphore: Arc<Semaphore>,
    solana_service: Arc<SolanaTraderService>,
    evm_service: Arc<EvmTraderService>,
    admin_fee_service: Arc<AdminFeeService>,
    
    // Performance caches
    balance_cache: Arc<RwLock<HashMap<String, (f64, DateTime<Utc>)>>>,
    price_cache: Arc<RwLock<HashMap<String, (f64, DateTime<Utc>)>>>,
    
    // Connection pools
    rpc_connections: Arc<RwLock<HashMap<Blockchain, Vec<String>>>>,
}

impl HighPerformanceTrader {
    /// Initialize high-performance trading service with maximum resource allocation
    pub fn new() -> Self {
        println!("Initializing High-Performance Trading Service");
        println!("   📊 Max Concurrent Trades: {}", defaults::MAX_CONCURRENT_TRADES);
        println!("   🧵 Trading Thread Pool: {}", defaults::TRADING_THREAD_POOL_SIZE);
        println!("   🔗 RPC Connection Pool: {}", defaults::RPC_CONNECTION_POOL_SIZE);
        println!("   💾 Database Connections: {}", defaults::DATABASE_CONNECTION_POOL);
        
        Self {
            trading_semaphore: Arc::new(Semaphore::new(defaults::MAX_CONCURRENT_TRADES)),
            solana_service: Arc::new(SolanaTraderService::new(
                "https://api.mainnet-beta.solana.com",
                None,
                defaults::TRADING_THREAD_POOL_SIZE,
            )),
            evm_service: Arc::new(EvmTraderService::new()),
            admin_fee_service: Arc::new(AdminFeeService::new()),
            balance_cache: Arc::new(RwLock::new(HashMap::with_capacity(defaults::TRADE_CACHE_SIZE))),
            price_cache: Arc::new(RwLock::new(HashMap::with_capacity(defaults::PRICE_CACHE_SIZE))),
            rpc_connections: Arc::new(RwLock::new(HashMap::new())),
        }
    }

    /// OPTIMIZED: High-performance Solana buy with maximum resource allocation
    pub async fn execute_solana_buy_optimized(
        &self,
        wallet: &Wallet,
        token_mint: &str,
        sol_amount: u64,
        slippage_bps: u16,
        chat_id: Option<i64>,
    ) -> Result<HighPerformanceTradeResult, BotError> {
        // Acquire trading semaphore for resource management
        let _permit = self.trading_semaphore.acquire().await
            .map_err(|e| BotError::general_error(format!("Trading resource unavailable: {}", e)))?;
        
        println!("OPTIMIZED SOLANA BUY - Resources Allocated");
        println!("   💰 Amount: {} SOL", sol_amount as f64 / 1e9);
        println!("   🎯 Token: {}", token_mint);
        println!("   📊 Slippage: {}%", slippage_bps as f64 / 100.0);
        
        // Create high-priority futures for parallel execution
        let balance_future = self.get_cached_balance_solana(&wallet.address);
        let price_future = self.get_cached_price_solana(token_mint);
        
        // Execute balance and price checks in parallel
        let (balance_result, price_result) = tokio::join!(balance_future, price_future);
        
        // Validate sufficient balance with cached data
        if let Ok(cached_balance) = balance_result {
            let required_amount = sol_amount as f64 / 1e9;
            if cached_balance < required_amount * 1.1 { // 10% buffer
                return Err(BotError::general_error(
                    format!("Insufficient SOL balance. Required: {}, Available: {}", required_amount, cached_balance)
                ));
            }
        }
        
        // Execute optimized buy with retry mechanism
        let mut last_error = None;
        for attempt in 1..=defaults::TRADE_RETRY_ATTEMPTS {
            println!("🔄 Solana buy attempt {}/{}", attempt, defaults::TRADE_RETRY_ATTEMPTS);
            
            // Convert wallet to Keypair and token_mint to Pubkey for Solana service
            let private_key_bytes = wallet.private_key_bytes()
                .map_err(|e| BotError::general_error(format!("Invalid private key format: {}", e)))?;
            let keypair = solana_sdk::signature::Keypair::from_bytes(&private_key_bytes)
                .map_err(|e| BotError::general_error(format!("Invalid private key: {}", e)))?;
            let token_pubkey = solana_sdk::pubkey::Pubkey::from_str(token_mint)
                .map_err(|e| BotError::general_error(format!("Invalid token mint: {}", e)))?;

            match self.solana_service.buy_token_with_sol(
                &keypair,
                &token_pubkey,
                sol_amount,
                slippage_bps as u32,
                chat_id, // Pass chat_id for proper data linking
            ).await {
                Ok(result) => {
                    let signature = result.signature.to_string();
                    println!("✅ OPTIMIZED SOLANA BUY SUCCESS: {}", signature);

                    // Update cache asynchronously (non-blocking)
                    let cache_wallet = wallet.address.clone();
                    let cache_token = token_mint.to_string();
                    let balance_cache = self.balance_cache.clone();
                    let price_cache = self.price_cache.clone();

                    tokio::spawn(async move {
                        // Invalidate balance cache for refresh
                        balance_cache.write().await.remove(&cache_wallet);
                        // Keep price cache for other trades
                    });

                    // Get admin fee percentage for SOL with intelligent fallback
                    let admin_fee_percentage = match self.admin_fee_service.get_admin_fee_percentage_for_blockchain(&crate::model::blockchain::Blockchain::SOL).await {
                        Ok(percentage) => {
                            println!("✅ High-performance trader: Retrieved admin fee percentage for SOL: {}%", percentage);
                            percentage
                        }
                        Err(e) => {
                            println!("⚠️ High-performance trader: Failed to get admin fee percentage for SOL, using hardcoded default: {}", e);
                            crate::config::defaults::DEFAULT_ADMIN_FEE_PERCENTAGE
                        }
                    };

                    // For SOL buy: native_token_amount = SOL spent, token_amount = tokens received
                    let sol_spent = result.input_amount as f64 / 1_000_000_000.0; // Convert lamports to SOL
                    let tokens_received = result.output_amount as f64;
                    let admin_fee_amount = (sol_spent * admin_fee_percentage) / 100.0;

                    return Ok(HighPerformanceTradeResult {
                        signature,
                        input_amount: result.input_amount,
                        output_amount: result.output_amount,
                        native_token_amount: Some(sol_spent),
                        token_amount: Some(tokens_received),
                        native_token_symbol: Some("SOL".to_string()),
                        admin_fee_amount: Some(admin_fee_amount),
                        admin_fee_percentage: Some(admin_fee_percentage),
                        success: true,
                        message: "High-performance buy completed successfully".to_string(),
                    });
                }
                Err(e) => {
                    println!("⚠️ Solana buy attempt {} failed: {}", attempt, e);
                    last_error = Some(BotError::general_error(format!("Solana buy failed: {}", e)));
                    
                    if attempt < defaults::TRADE_RETRY_ATTEMPTS {
                        tokio::time::sleep(tokio::time::Duration::from_millis(
                            defaults::TRADE_RETRY_DELAY_MS * attempt as u64
                        )).await;
                    }
                }
            }
        }
        
        Err(last_error.unwrap_or_else(|| BotError::general_error("All retry attempts failed".to_string())))
    }

    /// OPTIMIZED: High-performance Solana sell with maximum resource allocation
    pub async fn execute_solana_sell_optimized(
        &self,
        wallet: &Wallet,
        token_mint: &str,
        token_amount: u64,
        slippage_bps: u16,
        chat_id: Option<i64>,
    ) -> Result<String, BotError> {
        // Acquire trading semaphore for resource management
        let _permit = self.trading_semaphore.acquire().await
            .map_err(|e| BotError::general_error(format!("Trading resource unavailable: {}", e)))?;
        
        println!("OPTIMIZED SOLANA SELL - Resources Allocated");
        println!("   🪙 Amount: {} tokens", token_amount);
        println!("   🎯 Token: {}", token_mint);
        println!("   📊 Slippage: {}%", slippage_bps as f64 / 100.0);
        
        // Execute optimized sell with retry mechanism
        let mut last_error = None;
        for attempt in 1..=defaults::TRADE_RETRY_ATTEMPTS {
            println!("🔄 Solana sell attempt {}/{}", attempt, defaults::TRADE_RETRY_ATTEMPTS);
            
            // Convert wallet to Keypair and token_mint to Pubkey for Solana service
            let private_key_bytes = wallet.private_key_bytes()
                .map_err(|e| BotError::general_error(format!("Invalid private key format: {}", e)))?;
            let keypair = solana_sdk::signature::Keypair::from_bytes(&private_key_bytes)
                .map_err(|e| BotError::general_error(format!("Invalid private key: {}", e)))?;
            let token_pubkey = solana_sdk::pubkey::Pubkey::from_str(token_mint)
                .map_err(|e| BotError::general_error(format!("Invalid token mint: {}", e)))?;

            match self.solana_service.sell_token_for_sol(
                &keypair,
                &token_pubkey,
                token_amount,
                slippage_bps as u32,
                chat_id, // 🚀 FIX: Pass actual chat_id
            ).await {
                Ok(result) => {
                    let signature = result.signature.to_string();
                    println!("✅ OPTIMIZED SOLANA SELL SUCCESS: {}", signature);
                    
                    // Update cache asynchronously (non-blocking)
                    let cache_wallet = wallet.address.clone();
                    let balance_cache = self.balance_cache.clone();
                    
                    tokio::spawn(async move {
                        // Invalidate balance cache for refresh
                        balance_cache.write().await.remove(&cache_wallet);
                    });
                    
                    return Ok(signature);
                }
                Err(e) => {
                    println!("⚠️ Solana sell attempt {} failed: {}", attempt, e);
                    last_error = Some(BotError::general_error(format!("Solana sell failed: {}", e)));
                    
                    if attempt < defaults::TRADE_RETRY_ATTEMPTS {
                        tokio::time::sleep(tokio::time::Duration::from_millis(
                            defaults::TRADE_RETRY_DELAY_MS * attempt as u64
                        )).await;
                    }
                }
            }
        }
        
        Err(last_error.unwrap_or_else(|| BotError::general_error("All retry attempts failed".to_string())))
    }

    /// OPTIMIZED: High-performance EVM buy with maximum resource allocation
    pub async fn execute_evm_buy_optimized(
        &self,
        wallet: &Wallet,
        quote: crate::service::evm_trader_service::ZeroExQuoteResponse,
        blockchain: &Blockchain,
        chat_id: Option<i64>,
    ) -> Result<String, BotError> {
        // Acquire trading semaphore for resource management
        let _permit = self.trading_semaphore.acquire().await
            .map_err(|e| BotError::general_error(format!("Trading resource unavailable: {}", e)))?;
        
        println!("OPTIMIZED EVM BUY - Resources Allocated");
        println!("   💰 Amount: {} {}", quote.sellAmount.parse::<f64>().unwrap_or(0.0) / 1e18, blockchain.get_native_symbol());
        println!("   🎯 Token: {}", quote.buyToken);
        println!("   ⛓️ Chain: {:?}", blockchain);
        
        // Execute optimized buy with retry mechanism and priority gas
        let mut last_error = None;
        for attempt in 1..=defaults::TRADE_RETRY_ATTEMPTS {
            println!("🔄 EVM buy attempt {}/{}", attempt, defaults::TRADE_RETRY_ATTEMPTS);
            
            // Create modified quote with priority gas pricing
            let mut priority_quote = quote.clone();
            if attempt > 1 {
                // Increase gas price for retries
                if let Ok(gas_price) = priority_quote.transaction.gasPrice.parse::<f64>() {
                    let priority_gas = (gas_price * defaults::PRIORITY_GAS_MULTIPLIER * attempt as f64) as u64;
                    priority_quote.transaction.gasPrice = priority_gas.to_string();
                    println!("Using priority gas: {} wei", priority_gas);
                }
            }
            
            match self.evm_service.execute_buy_with_parallel_fee_collection(
                wallet,
                priority_quote,
                blockchain,
                chat_id,
            ).await {
                Ok(tx_hash) => {
                    println!("✅ OPTIMIZED EVM BUY SUCCESS: {}", tx_hash);
                    
                    // Update cache asynchronously (non-blocking)
                    let cache_wallet = wallet.address.clone();
                    let balance_cache = self.balance_cache.clone();
                    
                    tokio::spawn(async move {
                        // Invalidate balance cache for refresh
                        balance_cache.write().await.remove(&cache_wallet);
                    });
                    
                    return Ok(tx_hash);
                }
                Err(e) => {
                    println!("⚠️ EVM buy attempt {} failed: {}", attempt, e);
                    last_error = Some(e);
                    
                    if attempt < defaults::TRADE_RETRY_ATTEMPTS {
                        tokio::time::sleep(tokio::time::Duration::from_millis(
                            defaults::TRADE_RETRY_DELAY_MS * attempt as u64
                        )).await;
                    }
                }
            }
        }
        
        Err(last_error.unwrap_or_else(|| BotError::general_error("All retry attempts failed".to_string())))
    }

    /// Get cached balance for Solana (performance optimization)
    async fn get_cached_balance_solana(&self, wallet_address: &str) -> Result<f64, BotError> {
        let cache_key = format!("sol_balance_{}", wallet_address);
        
        // Check cache first
        {
            let cache = self.balance_cache.read().await;
            if let Some((balance, timestamp)) = cache.get(&cache_key) {
                let age = Utc::now().signed_duration_since(*timestamp).num_seconds() as u64;
                if age < defaults::BALANCE_CACHE_TTL_SECONDS {
                    return Ok(*balance);
                }
            }
        }
        
        // Cache miss - fetch from blockchain
        let wallet_pubkey = solana_sdk::pubkey::Pubkey::from_str(wallet_address)
            .map_err(|e| BotError::general_error(format!("Invalid wallet address: {}", e)))?;
        let sol_mint = solana_sdk::pubkey::Pubkey::from_str("So11111111111111111111111111111111111111112")
            .map_err(|e| BotError::general_error(format!("Invalid SOL mint: {}", e)))?;

        match self.solana_service.get_token_balance(&wallet_pubkey, &sol_mint).await {
            Ok(balance_lamports) => {
                let balance_sol = balance_lamports as f64 / 1e9;
                
                // Update cache
                {
                    let mut cache = self.balance_cache.write().await;
                    cache.insert(cache_key, (balance_sol, Utc::now()));
                }
                
                Ok(balance_sol)
            }
            Err(e) => Err(BotError::general_error(format!("Solana balance error: {}", e)))
        }
    }

    /// Get cached price for Solana token using DexScreener API (performance optimization)
    async fn get_cached_price_solana(&self, token_mint: &str) -> Result<f64, BotError> {
        let cache_key = format!("sol_price_{}", token_mint);

        // Check cache first
        {
            let cache = self.price_cache.read().await;
            if let Some((price, timestamp)) = cache.get(&cache_key) {
                let age = Utc::now().signed_duration_since(*timestamp).num_seconds() as u64;
                if age < 60 { // 1 minute cache for prices
                    return Ok(*price);
                }
            }
        }

        // Try DexScreener API first for real-time token prices
        let token_info_service = crate::service::token_info_service::TokenInfoService::new();
        match token_info_service.get_token_price_dexscreener(token_mint, &crate::model::Blockchain::SOL).await {
            Ok(price) => {
                println!("💰 DexScreener price for {}: ${:.8}", token_mint, price);
                // Update cache with fresh price data
                {
                    let mut cache = self.price_cache.write().await;
                    cache.insert(cache_key, (price, Utc::now()));
                }
                return Ok(price);
            }
            Err(e) => {
                println!("⚠️ DexScreener failed for {}: {}", token_mint, e);
            }
        }

        // Fallback to CoinGecko for main native tokens only
        match self.get_default_token_price_coingecko(token_mint).await {
            Ok(price) => {
                println!("💰 CoinGecko fallback price for {}: ${:.8}", token_mint, price);
                // Update cache with fresh price data
                {
                    let mut cache = self.price_cache.write().await;
                    cache.insert(cache_key, (price, Utc::now()));
                }
                Ok(price)
            }
            Err(e) => {
                println!("⚠️ Failed to get real-time price for token {}: {}", token_mint, e);
                // Try to get cached price as fallback
                {
                    let cache = self.price_cache.read().await;
                    if let Some((cached_price, _)) = cache.get(&cache_key) {
                        println!("Using cached price for {}: ${}", token_mint, cached_price);
                        return Ok(*cached_price);
                    }
                }
                // Return error instead of default 0.0 for unknown tokens
                Err(BotError::general_error(format!("Unable to determine price for token: {}", token_mint)))
            }
        }
    }

    /// Get cached price for EVM token using DexScreener API (performance optimization)
    async fn get_cached_price_evm(&self, token_address: &str, blockchain: &Blockchain) -> Result<f64, BotError> {
        let cache_key = format!("{}_price_{}", blockchain.as_str(), token_address);

        // Check cache first
        {
            let cache = self.price_cache.read().await;
            if let Some((price, timestamp)) = cache.get(&cache_key) {
                let age = Utc::now().signed_duration_since(*timestamp).num_seconds() as u64;
                if age < 60 { // 1 minute cache for prices
                    return Ok(*price);
                }
            }
        }

        // Try DexScreener API first for real-time token prices
        let token_info_service = crate::service::token_info_service::TokenInfoService::new();
        match token_info_service.get_token_price_dexscreener(token_address, blockchain).await {
            Ok(price) => {
                println!("💰 DexScreener price for {} on {}: ${:.8}", token_address, blockchain.as_str(), price);
                // Update cache with fresh price data
                {
                    let mut cache = self.price_cache.write().await;
                    cache.insert(cache_key, (price, Utc::now()));
                }
                return Ok(price);
            }
            Err(e) => {
                println!("⚠️ DexScreener failed for {} on {}: {}", token_address, blockchain.as_str(), e);
            }
        }

        // Fallback to CoinGecko for main native tokens only
        match self.get_default_token_price_coingecko(token_address).await {
            Ok(price) => {
                println!("💰 CoinGecko fallback price for {} on {}: ${:.8}", token_address, blockchain.as_str(), price);
                // Update cache with fresh price data
                {
                    let mut cache = self.price_cache.write().await;
                    cache.insert(cache_key, (price, Utc::now()));
                }
                Ok(price)
            }
            Err(e) => {
                println!("⚠️ Failed to get real-time price for token {} on {}: {}", token_address, blockchain.as_str(), e);
                // Try to get cached price as fallback
                {
                    let cache = self.price_cache.read().await;
                    if let Some((cached_price, _)) = cache.get(&cache_key) {
                        println!("Using cached price for {} on {}: ${}", token_address, blockchain.as_str(), cached_price);
                        return Ok(*cached_price);
                    }
                }
                // Return error instead of default 0.0 for unknown tokens
                Err(BotError::general_error(format!("Unable to determine price for token {} on {}", token_address, blockchain.as_str())))
            }
        }
    }

    /// 🚀 PRODUCTION: Get default token prices for main native tokens from CoinGecko
    async fn get_default_token_price_coingecko(&self, token_identifier: &str) -> Result<f64, BotError> {
        use reqwest;

        // Map token identifiers to CoinGecko IDs for main native tokens
        let coingecko_id = match token_identifier {
            // Solana native token
            "SOL" | "solana" | "So11111111111111111111111111111111111111112" => "solana",
            // Ethereum native token
            "ETH" | "ethereum" | "******************************************" => "ethereum",
            // Binance Smart Chain native token
            "BNB" | "binancecoin" => "binancecoin",
            // Base native token (ETH on Base)
            "BASE" | "base-eth" => "ethereum", // Base uses ETH as native token
            _ => return Err(BotError::network_error("Unsupported token for price fetching".to_string())),
        };

        let coingecko_url = format!(
            "https://api.coingecko.com/api/v3/simple/price?ids={}&vs_currencies=usd",
            coingecko_id
        );

        let client = reqwest::Client::builder()
            .timeout(std::time::Duration::from_secs(5))
            .build()
            .map_err(|e| BotError::network_error(format!("Failed to create HTTP client: {}", e)))?;

        // Add delay to prevent rate limiting
        tokio::time::sleep(tokio::time::Duration::from_millis(defaults::PRICE_API_DELAY_MS)).await;

        let response = client
            .get(&coingecko_url)
            .header("User-Agent", "EasyBot/1.0")
            .send()
            .await
            .map_err(|e| BotError::network_error(format!("CoinGecko API request failed: {}", e)))?;

        if !response.status().is_success() {
            // Handle rate limiting specifically
            if response.status() == 429 {
                println!("⚠️ Price API rate limited, waiting {} seconds", defaults::PRICE_API_RETRY_DELAY_MS / 1000);
                tokio::time::sleep(tokio::time::Duration::from_millis(defaults::PRICE_API_RETRY_DELAY_MS)).await;
                return Err(BotError::network_error("Price API rate limited - please try again later".to_string()));
            }
            return Err(BotError::network_error(format!("CoinGecko API returned status: {}", response.status())));
        }

        let price_data: serde_json::Value = response
            .json()
            .await
            .map_err(|e| BotError::network_error(format!("Failed to parse CoinGecko response: {}", e)))?;

        // Extract price from CoinGecko response
        if let Some(token_data) = price_data.get(coingecko_id) {
            if let Some(usd_price) = token_data.get("usd") {
                if let Some(price) = usd_price.as_f64() {
                    println!("💰 Fetched {} price: ${:.4}", coingecko_id, price);
                    return Ok(price);
                }
            }
        }

        Err(BotError::network_error("Invalid CoinGecko API response format".to_string()))
    }
}


