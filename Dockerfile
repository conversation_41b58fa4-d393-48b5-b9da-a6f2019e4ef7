FROM rust:1.70-slim-bullseye

WORKDIR /app

RUN apt-get update && apt-get install -y \
    pkg-config \
    libssl-dev \
    build-essential \
    && rm -rf /var/lib/apt/lists/*

ENV OPENSSL_NO_VENDOR=0
ENV OPENSSL_STATIC=1
ENV RUSTLS_NATIVE_CERTS=1

ENV OPENSSL_DIR=""
ENV OPENSSL_LIB_DIR=""
ENV OPENSSL_INCLUDE_DIR=""

COPY . .

RUN chmod +x build-render.sh && ./build-render.sh

CMD ["./target/release/Easybot"]
