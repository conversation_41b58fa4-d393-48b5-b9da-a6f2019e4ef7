use serde::{Deserialize, Serialize};
use mongodb::bson::oid::ObjectId;
use std::time::{SystemTime, UNIX_EPOCH};

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum AdminRole {
    SuperAdmin,
    Admin,
}

impl Default for AdminRole {
    fn default() -> Self {
        AdminRole::Admin
    }
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct AdminUser {
    #[serde(rename = "_id", skip_serializing_if = "Option::is_none")]
    pub id: Option<ObjectId>,
    pub username: String,
    pub email: String,
    pub password_hash: String,
    #[serde(default)]
    pub role: AdminRole,
    #[serde(default = "default_timestamp")]
    pub created_at: u64,
    pub last_login: Option<u64>,
    #[serde(default = "default_true")]
    pub is_active: bool,
    pub created_by: Option<String>,
    #[serde(default = "default_timestamp")]
    pub updated_at: u64,
}

fn default_timestamp() -> u64 {
    SystemTime::now()
        .duration_since(UNIX_EPOCH)
        .unwrap_or_default()
        .as_secs()
}

fn default_true() -> bool {
    true
}

impl AdminUser {
    pub fn new(username: String, email: String, password_hash: String, role: AdminRole) -> Self {
        let now = default_timestamp();

        Self {
            id: None,
            username,
            email,
            password_hash,
            role,
            created_at: now,
            last_login: None,
            is_active: true,
            created_by: None,
            updated_at: now,
        }
    }

    pub fn update_last_login(&mut self) {
        self.last_login = Some(default_timestamp());
    }

    pub fn is_super_admin(&self) -> bool {
        self.role == AdminRole::SuperAdmin
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Claims {
    pub sub: String,  // Subject (user ID)
    pub exp: u64,     // Expiration time
    pub role: String, // User role
}
