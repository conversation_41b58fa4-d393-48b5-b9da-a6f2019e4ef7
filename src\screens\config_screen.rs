use teloxide::types::{InlineKeyboardButton, InlineKeyboardMarkup};
use serde_json::json;
use crate::model::{UserData, BotError, Blockchain};
use crate::model::wallet_config::WalletConfig;
use crate::service::{BotService, DbService};
use bson::Decimal128;
use std::str::FromStr;

pub async fn show_config_screen(
    bot_service: &BotService,
    user_data: &UserData,
    message_id: i32,
    blockchain: Blockchain,
) -> Result<(), BotError> {
    println!("Showing config screen for {:?}", blockchain);

    let config = match user_data.get_config(&blockchain) {
        Some(config) => config.clone(),
        None => {
            match blockchain {
                Blockchain::BSC => WalletConfig::bsc_default(),
                Blockchain::SOL => WalletConfig::sol_default(),
                Blockchain::ETH => WalletConfig::eth_default(),
                Blockchain::BASE => WalletConfig::base_default(),
            }
        }
    };
    let config_text = format!(
        "⚙️ <b>{} Configuration</b>\n\n\
        👤 <b>User:</b> {}\n\n\
        <b>Trading Settings:</b>\n\
        • <b>Slippage:</b> {}%\n\
        • <b>Anti-Rug Protection:</b> {}\n\
        • <b>Anti-MEV Protection:</b> {}\n\
        • <b>Max Gas Price:</b> {} Gwei\n\
        • <b>Max Gas Limit:</b> {}\n\n\
        <b>Auto-Trading:</b>\n\
        • <b>Auto-Buy:</b> {}\n\
        • <b>Min Liquidity:</b> ${}\n\
        • <b>Max Market Cap:</b> ${}\n\
        • <b>Max Liquidity:</b> ${}\n\
        • <b>Auto-Sell:</b> {}\n\
        • <b>Sell High:</b> {}%\n\
        • <b>Sell Low:</b> {}%\n\
        • <b>Auto-Approve:</b> {}\n\n\
        <i>Tap a setting to change it</i>",
        blockchain.to_string().to_uppercase(),
        user_data.display_name(),
        config.slippage.to_string(),
        if config.antirug { "✅ Enabled" } else { "❌ Disabled" },
        if config.anti_mev { "✅ Enabled" } else { "❌ Disabled" },
        config.max_gas_price.to_string(),
        config.max_gas_limit,
        if config.auto_buy { "✅ Enabled" } else { "❌ Disabled" },
        config.min_liquidity.to_string(),
        config.max_market_cap.to_string(),
        config.max_liquidity.to_string(),
        if config.auto_sell { "✅ Enabled" } else { "❌ Disabled" },
        config.sell_high.to_string(),
        config.sell_low.to_string(),
        if config.auto_approve { "✅ Enabled" } else { "❌ Disabled" }
    );

    let keyboard = create_config_keyboard(blockchain);
    bot_service.edit_message_with_keyboard(
        user_data.chat_id(),
        message_id,
        &config_text,
        keyboard,
    ).await?;

    Ok(())
}

fn create_config_keyboard(blockchain: Blockchain) -> InlineKeyboardMarkup {
    let keyboard = vec![
        vec![
            InlineKeyboardButton::callback(
                "🔧 Set Slippage".to_string(),
                json!({
                    "command": format!("set_slippage_{}", blockchain)
                }).to_string(),
            )
        ],
        vec![
            InlineKeyboardButton::callback(
                "🛡️ Toggle Anti-Rug".to_string(),
                json!({
                    "command": format!("toggle_antirug_{}", blockchain)
                }).to_string(),
            ),
            InlineKeyboardButton::callback(
                "🛡️ Toggle Anti-MEV".to_string(),
                json!({
                    "command": format!("toggle_antimev_{}", blockchain)
                }).to_string(),
            )
        ],
        vec![
            InlineKeyboardButton::callback(
                "⛽ Set Max Gas Price".to_string(),
                json!({
                    "command": format!("set_gas_price_{}", blockchain)
                }).to_string(),
            ),
            InlineKeyboardButton::callback(
                "⛽ Set Gas Limit".to_string(),
                json!({
                    "command": format!("set_gas_limit_{}", blockchain)
                }).to_string(),
            )
        ],
        vec![
            InlineKeyboardButton::callback(
                "🤖 Toggle Auto-Buy".to_string(),
                json!({
                    "command": format!("toggle_auto_buy_{}", blockchain)
                }).to_string(),
            ),
            InlineKeyboardButton::callback(
                "🤖 Toggle Auto-Sell".to_string(),
                json!({
                    "command": format!("toggle_auto_sell_{}", blockchain)
                }).to_string(),
            )
        ],
        vec![
            InlineKeyboardButton::callback(
                "💰 Set Min Liquidity".to_string(),
                json!({
                    "command": format!("set_min_liquidity_{}", blockchain)
                }).to_string(),
            ),
            InlineKeyboardButton::callback(
                "💰 Set Max Liquidity".to_string(),
                json!({
                    "command": format!("set_max_liquidity_{}", blockchain)
                }).to_string(),
            )
        ],
        vec![
            InlineKeyboardButton::callback(
                "📊 Set Max Market Cap".to_string(),
                json!({
                    "command": format!("set_max_market_cap_{}", blockchain)
                }).to_string(),
            )
        ],
        vec![
            InlineKeyboardButton::callback(
                "📈 Set Sell High %".to_string(),
                json!({
                    "command": format!("set_sell_high_{}", blockchain)
                }).to_string(),
            ),
            InlineKeyboardButton::callback(
                "📉 Set Sell Low %".to_string(),
                json!({
                    "command": format!("set_sell_low_{}", blockchain)
                }).to_string(),
            )
        ],
        vec![
            InlineKeyboardButton::callback(
                "✅ Toggle Auto-Approve".to_string(),
                json!({
                    "command": format!("toggle_auto_approve_{}", blockchain)
                }).to_string(),
            )
        ],
        vec![
            InlineKeyboardButton::callback(
                "◀️ Back to Dashboard".to_string(),
                json!({
                    "command": format!("view_{}", blockchain.as_str())
                }).to_string(),
            )
        ]
    ];

    InlineKeyboardMarkup::new(keyboard)
}

pub async fn toggle_config_setting(
    bot_service: &BotService,
    user_data: &mut UserData,
    message_id: i32,
    blockchain: Blockchain,
    setting: &str,
) -> Result<(), BotError> {
    println!("Toggling {} setting for {:?}", setting, blockchain);

    let mut config = match blockchain {
        Blockchain::BSC => user_data.config.bsc_config.clone().unwrap_or_else(WalletConfig::bsc_default),
        Blockchain::SOL => user_data.config.sol_config.clone().unwrap_or_else(WalletConfig::sol_default),
        Blockchain::ETH => user_data.config.eth_config.clone().unwrap_or_else(WalletConfig::eth_default),
        Blockchain::BASE => user_data.config.base_config.clone().unwrap_or_else(WalletConfig::base_default),
    };
    match setting {
        "antirug" => config.antirug = !config.antirug,
        "antimev" => config.anti_mev = !config.anti_mev,
        "auto_buy" => config.auto_buy = !config.auto_buy,
        "auto_sell" => config.auto_sell = !config.auto_sell,
        "auto_approve" => config.auto_approve = !config.auto_approve,
        _ => return Err(BotError::user_error(format!("Unknown setting: {}", setting))),
    }

    println!("Toggled {} to {}", setting, match setting {
        "antirug" => config.antirug,
        "antimev" => config.anti_mev,
        "auto_buy" => config.auto_buy,
        "auto_sell" => config.auto_sell,
        "auto_approve" => config.auto_approve,
        _ => false,
    });

    // Update the config in the user data
    match blockchain {
        Blockchain::BSC => user_data.config.bsc_config = Some(config),
        Blockchain::SOL => user_data.config.sol_config = Some(config),
        Blockchain::ETH => user_data.config.eth_config = Some(config),
        Blockchain::BASE => user_data.config.base_config = Some(config),
    }

    DbService::save_user_config(&user_data.config).await?;

    bot_service.user_service().cache_user_data(user_data.clone()).await;

    show_config_screen(bot_service, user_data, message_id, blockchain).await?;

    Ok(())
}

pub async fn update_numeric_setting(
    bot_service: &BotService,
    user_data: &mut UserData,
    message_id: i32,
    blockchain: Blockchain,
    setting: &str,
    value: &str,
) -> Result<(), BotError> {
    println!("Updating {} setting for {:?} to {}", setting, blockchain, value);

    let mut config = match blockchain {
        Blockchain::BSC => user_data.config.bsc_config.clone().unwrap_or_else(WalletConfig::bsc_default),
        Blockchain::SOL => user_data.config.sol_config.clone().unwrap_or_else(WalletConfig::sol_default),
        Blockchain::ETH => user_data.config.eth_config.clone().unwrap_or_else(WalletConfig::eth_default),
        Blockchain::BASE => user_data.config.base_config.clone().unwrap_or_else(WalletConfig::base_default),
    };
    let result = match setting {
        "slippage" => {
            if let Ok(decimal) = Decimal128::from_str(value) {
                config.slippage = decimal;
                Ok(())
            } else {
                Err(BotError::user_error("Invalid slippage value. Please enter a number."))
            }
        },
        "gas_price" => {
            if let Ok(decimal) = Decimal128::from_str(value) {
                config.max_gas_price = decimal;
                Ok(())
            } else {
                Err(BotError::user_error("Invalid gas price value. Please enter a number."))
            }
        },
        "gas_limit" => {
            if let Ok(limit) = value.parse::<i32>() {
                config.max_gas_limit = limit;
                Ok(())
            } else {
                Err(BotError::user_error("Invalid gas limit value. Please enter a number."))
            }
        },
        "min_liquidity" => {
            if let Ok(decimal) = Decimal128::from_str(value) {
                config.min_liquidity = decimal;
                Ok(())
            } else {
                Err(BotError::user_error("Invalid minimum liquidity value. Please enter a number."))
            }
        },
        "max_market_cap" => {
            if let Ok(decimal) = Decimal128::from_str(value) {
                config.max_market_cap = decimal;
                Ok(())
            } else {
                Err(BotError::user_error("Invalid maximum market cap value. Please enter a number."))
            }
        },
        "max_liquidity" => {
            if let Ok(decimal) = Decimal128::from_str(value) {
                config.max_liquidity = decimal;
                Ok(())
            } else {
                Err(BotError::user_error("Invalid maximum liquidity value. Please enter a number."))
            }
        },
        "sell_high" => {
            if let Ok(decimal) = Decimal128::from_str(value) {
                config.sell_high = decimal;
                Ok(())
            } else {
                Err(BotError::user_error("Invalid sell high percentage. Please enter a number."))
            }
        },
        "sell_low" => {
            if let Ok(decimal) = Decimal128::from_str(value) {
                config.sell_low = decimal;
                Ok(())
            } else {
                Err(BotError::user_error("Invalid sell low percentage. Please enter a number."))
            }
        },
        _ => Err(BotError::user_error(format!("Unknown setting: {}", setting))),
    };

    if let Err(e) = result {
        return Err(e);
    }
    match blockchain {
        Blockchain::BSC => user_data.config.bsc_config = Some(config),
        Blockchain::SOL => user_data.config.sol_config = Some(config),
        Blockchain::ETH => user_data.config.eth_config = Some(config),
        Blockchain::BASE => user_data.config.base_config = Some(config),
    }

    DbService::save_user_config(&user_data.config).await?;

    bot_service.user_service().cache_user_data(user_data.clone()).await;
    show_config_screen(bot_service, user_data, message_id, blockchain).await?;

    Ok(())
}
