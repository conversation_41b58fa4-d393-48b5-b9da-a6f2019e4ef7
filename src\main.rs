mod config;
mod constants;
mod model;
mod service;
mod screens;
mod utils;
mod controllers;

use teloxide::{prelude::*, utils::command::BotCommands};
use service::{BotService, DbService, handle_message, SnipeWorker};
use service::health_service::HealthService;
use tower_http::cors::{CorsLayer, Any};
use model::BotError;
use utils::setup_logger;
use std::error::Error;
use std::sync::Arc;

fn main() -> Result<(), Box<dyn Error>> {
    let runtime = tokio::runtime::Builder::new_multi_thread()
        .worker_threads(32)
        .thread_stack_size(8 * 1024 * 1024)
        .enable_all()
        .build()?;

    runtime.block_on(async_main())
}

async fn async_main() -> Result<(), Box<dyn Error>> {
    setup_logger();

    log::info!("Starting EasyBot...");

    // Get the port from environment variable for Render compatibility
    let config = config::AppConfig::get();
    let port = config.port;

    // Log environment information for debugging
    log::info!("Environment variables for port configuration:");
    log::info!("  PORT={}", std::env::var("PORT").unwrap_or_else(|_| "not set".to_string()));
    log::info!("  SOCKET_PORT={}", std::env::var("SOCKET_PORT").unwrap_or_else(|_| "not set".to_string()));
    log::info!("Using port {} from config for health check service", port);
    log::info!("Using port {} from config for socket service", config.socket_port);

    // Start health check service for Render
    log::info!("Starting health check service on port {}...", port);
    let health_service = HealthService::new();

    // Create admin service and controllers
    log::info!("Initializing admin service and controllers...");
    let config_arc = Arc::new(config::AppConfig::get().clone());

    // Initialize all controllers
    let auth_controller = controllers::AuthController::new(config_arc.clone());
    let bots_controller = controllers::BotsController::new(config_arc.clone());
    let transactions_controller = controllers::TransactionsController::new(config_arc.clone());
    let users_controller = controllers::UsersController::new(config_arc.clone());
    let admins_controller = controllers::AdminsController::new(config_arc.clone());
    let settings_controller = controllers::SettingsController::new(config_arc.clone());
    let analytics_controller = controllers::AnalyticsController::new(config_arc.clone());

    // Initialize fee analytics controller
    let jwt_secret = std::env::var("JWT_SECRET").unwrap_or_else(|_| "default_secret".to_string());
    let fee_analytics_controller = controllers::FeeAnalyticsController::new(config_arc.as_ref().clone(), jwt_secret.clone());

    // Initialize admin settings controller
    let admin_settings_controller = controllers::AdminSettingsController::new(config_arc.as_ref().clone(), jwt_secret.clone());

    // Create CORS layer
    let cors = CorsLayer::new()
        .allow_origin(Any)
        .allow_methods(Any)
        .allow_headers(Any);

    // Merge all routers
    let merged_router = health_service.create_router()
        .merge(auth_controller.create_router())
        .merge(bots_controller.create_router())
        .merge(transactions_controller.create_router())
        .merge(users_controller.create_router())
        .merge(admins_controller.create_router())
        .merge(settings_controller.create_router())
        .merge(analytics_controller.create_router())
        .merge(fee_analytics_controller.create_router())
        .merge(admin_settings_controller.create_router())
        .layer(cors);

    // Start the combined services and wait a moment to ensure they're running
    health_service.start_with_router(port, merged_router).await;
    tokio::time::sleep(tokio::time::Duration::from_millis(500)).await;
    log::info!("Health check and admin services initialization completed");

    log::info!("Initializing database connection...");
    DbService::init().await?;
    log::info!("Database connection established");

    // Initialize default super admin
    log::info!("Checking and initializing default super admin...");
    if let Err(e) = service::admin_init_service::AdminInitService::initialize_default_admin().await {
        log::error!("Failed to initialize default admin: {}", e);
    }

    // Check for default credentials security warning
    if let Err(e) = service::admin_init_service::AdminInitService::check_default_credentials_security().await {
        log::error!("Failed to check default credentials security: {}", e);
    }

    // Initialize cached price service
    log::info!("Initializing cached price service...");
    let cached_price_service = service::CachedPriceService::new();
    if let Err(e) = cached_price_service.initialize_cache().await {
        log::error!("Failed to initialize price cache: {}", e);
    } else {
        log::info!("Price cache initialized successfully");
    }

    // Start background price updater
    log::info!("Starting background price updater...");
    cached_price_service.start_background_price_updater().await;
    log::info!("Background price updater started");

    let bot_service = BotService::new()?;
    let bot = bot_service.bot().clone();

    let bot_service_clone = bot_service.clone();
    tokio::spawn(async move {
        let user_service = bot_service_clone.user_service();
        loop {
            tokio::time::sleep(tokio::time::Duration::from_secs(86400)).await;
            user_service.cleanup_cache().await;
        }
    });

    let bot_service_clone = bot_service.clone();
    tokio::spawn(async move {
        let alert_service = bot_service_clone.alert_service();
        loop {
            tokio::time::sleep(tokio::time::Duration::from_secs(86400)).await;
            alert_service.cleanup_old_alerts().await;
        }
    });

    log::info!("Initializing Solana trader service...");
    bot_service.solana_trader_service().initialize().await;
    log::info!("Solana trader service initialized");

    log::info!("Initializing snipe worker...");
    let snipe_worker = SnipeWorker::new();
    snipe_worker.start().await;
    log::info!("Snipe worker initialized and started");

    // Start background fee collection service
    log::info!("Starting background fee collection service...");
    let admin_fee_service = service::AdminFeeService::new();
    admin_fee_service.start_background_fee_collection().await;
    log::info!("Background fee collection service started");

    let command_handler = Update::filter_message().branch(
        dptree::entry()
            .filter_command::<Command>()
            .endpoint(handle_command),
    );

    let callback_handler = Update::filter_callback_query().endpoint(handle_callback);

    let message_handler = Update::filter_message()
        .branch(command_handler.clone())
        .endpoint(handle_message);

    let handler = dptree::entry()
        .branch(command_handler)
        .branch(callback_handler)
        .branch(message_handler);

    log::info!("Starting bot dispatcher...");

    Dispatcher::builder(bot, handler)
        .dependencies(dptree::deps![bot_service])
        .enable_ctrlc_handler()
        .build()
        .dispatch()
        .await;

    log::info!("Bot stopped");

    Ok(())
}

#[derive(BotCommands, Clone, Debug, PartialEq)]
#[command(rename_rule = "lowercase")]
enum Command {
    #[command(description = "Start the bot")]
    Start,
    #[command(description = "Show help information")]
    Help,
    #[command(description = "About this bot")]
    About,
    #[command(description = "View your private keys (will be deleted after 60 seconds)")]
    PrivateKeys,
    #[command(description = "Switch to BSC blockchain")]
    Bsc,
    #[command(description = "Switch to Solana blockchain")]
    Sol,
    #[command(description = "Switch to Ethereum blockchain")]
    Eth,
    #[command(description = "Switch to Base blockchain")]
    Base,
    #[command(description = "Scan a contract address")]
    Scan,
    #[command(description = "Sniper a new token")]
    Sniper,
    #[command(description = "Test the alert system")]
    Alert,
}

async fn handle_command(
    bot_service: BotService,
    msg: Message,
    cmd: Command,
) -> Result<(), BotError> {
    let chat_id = msg.chat.id.0 as i64;

    if let Some(_tg_user) = msg.from() {
        if cmd != Command::Start {
            bot_service.track_conversation_message(chat_id, msg.id.0).await?;
        }
    }

    if let Command::PrivateKeys = cmd {
        if let Some(_tg_user) = msg.from() {
            let _ = bot_service.delete_message(chat_id, msg.id.0).await;
            return screens::welcome::handle_privatekeys(&bot_service, chat_id).await;
        }
    }

    let command_name = match cmd {
        Command::Start => "start",
        Command::Help => "help",
        Command::About => "about",
        Command::PrivateKeys => "privatekeys",
        Command::Bsc => "bsc",
        Command::Sol => "sol",
        Command::Eth => "eth",
        Command::Base => "base",
        Command::Scan => "scan",
        Command::Sniper => "sniper",
        Command::Alert => "alert",
    };

    screens::handle_command(&bot_service, msg, command_name).await
}

async fn handle_callback(
    bot_service: BotService,
    q: CallbackQuery,
) -> Result<(), BotError> {
    if let Some(data) = &q.data {
        log::info!("Received callback query with data: {}", data);
    } else {
        log::info!("Received callback query without data");
    }

    screens::handle_callback(&bot_service, q).await
}

