import{r as a,j as e,c as O,B as L}from"./index-dCUkEeO4.js";import{C as M}from"./Card-CLlE15Sf.js";import{adminApi as j}from"./adminApi-BFZ8qr13.js";function T({title:o,titleId:d,...x},m){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:m,"aria-labelledby":d},x),o?a.createElement("title",{id:d},o):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L6.832 19.82a4.5 4.5 0 0 1-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 0 1 1.13-1.897L16.863 4.487Zm0 0L19.5 7.125"}))}const q=a.forwardRef(T);function W({title:o,titleId:d,...x},m){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:m,"aria-labelledby":d},x),o?a.createElement("title",{id:d},o):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0"}))}const Z=a.forwardRef(W);function _({title:o,titleId:d,...x},m){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:m,"aria-labelledby":d},x),o?a.createElement("title",{id:d},o):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M18 7.5v3m0 0v3m0-3h3m-3 0h-3m-2.25-4.125a3.375 3.375 0 1 1-6.75 0 3.375 3.375 0 0 1 6.75 0ZM3 19.235v-.11a6.375 6.375 0 0 1 12.75 0v.109A12.318 12.318 0 0 1 9.374 21c-2.331 0-4.512-.645-6.374-1.766Z"}))}const z=a.forwardRef(_);function G(){const[o,d]=a.useState([]),[x,m]=a.useState(!0),[C,p]=a.useState(null),[S,N]=a.useState(!1),[h,v]=a.useState(!1),[u,R]=a.useState(null),[t,A]=a.useState({username:"",email:"",role:"Admin",password:"",confirmPassword:""}),[n,k]=a.useState({username:"",email:"",password:"",confirmPassword:""}),[g,P]=a.useState(!1),f=async()=>{var s,i;try{m(!0),p(null),console.log("🔄 Fetching admins...");const r=await j.getAdmins();console.log("📥 Admins response:",r);let l=[];Array.isArray(r)?l=r:r&&Array.isArray(r.admins)?l=r.admins:r&&Array.isArray(r.data)?l=r.data:(console.warn("Unexpected response structure:",r),l=[]);const y=l.filter(F=>!(F.username==="Olajosh"&&F.email==="<EMAIL>"));d(y)}catch(r){console.error("❌ Failed to fetch admins:",r),p(((i=(s=r.response)==null?void 0:s.data)==null?void 0:i.error)||"Failed to load admins"),d([])}finally{m(!1)}};a.useEffect(()=>{f()},[]);const c=s=>{const{name:i,value:r}=s.target;A({...t,[i]:r})},U=()=>{const s={username:"",email:"",password:"",confirmPassword:""};let i=!0;return t.username.trim()?t.username.length<3&&(s.username="Username must be at least 3 characters",i=!1):(s.username="Username is required",i=!1),t.email.trim()?/\S+@\S+\.\S+/.test(t.email)||(s.email="Email is invalid",i=!1):(s.email="Email is required",i=!1),!h&&!t.password?(s.password="Password is required",i=!1):!h&&t.password.length<6&&(s.password="Password must be at least 6 characters",i=!1),!h&&t.password!==t.confirmPassword&&(s.confirmPassword="Passwords do not match",i=!1),k(s),i},D=()=>{A({username:"",email:"",role:"Admin",password:"",confirmPassword:""}),k({username:"",email:"",password:"",confirmPassword:""}),N(!0)},$=s=>{R(s),A({username:s.username,email:s.email,role:s.role,password:"",confirmPassword:""}),k({username:"",email:"",password:"",confirmPassword:""}),v(!0)},B=async(s,i)=>{var r,l;if(window.confirm(`Are you sure you want to delete admin "${i}"? This action cannot be undone.`))try{m(!0),console.log("🗑️ Deleting admin:",s),await j.deleteAdmin(s),console.log("✅ Admin deleted successfully"),await f()}catch(y){console.error("❌ Failed to delete admin:",y),p(((l=(r=y.response)==null?void 0:r.data)==null?void 0:l.error)||"Failed to delete admin"),m(!1)}},w=async s=>{var i,r;if(s.preventDefault(),!!U())try{if(P(!0),p(null),S){console.log("➕ Creating new admin:",t);const l=await j.createAdmin({username:t.username,email:t.email,password:t.password,role:t.role});console.log("✅ Admin created successfully:",l),N(!1),await f()}else if(h&&u){console.log("✏️ Updating admin:",u.id,t);const l=await j.updateAdmin(u.id,{username:t.username,email:t.email,role:t.role});console.log("✅ Admin updated successfully:",l),v(!1),await f()}}catch(l){console.error("❌ Failed to save admin:",l),p(((r=(i=l.response)==null?void 0:i.data)==null?void 0:r.error)||"Failed to save admin")}finally{P(!1)}},E=s=>{if(!s)return"Never";const i=new Date(s*1e3);return new Intl.DateTimeFormat("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}).format(i)},b=s=>s.status||(s.is_active?"active":"inactive");return x?e.jsx("div",{className:"flex items-center justify-center h-64",children:e.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"})}):C?e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{className:"flex justify-between items-center",children:e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold text-white",children:"Admin Management"}),e.jsx("p",{className:"mt-1 text-sm text-gray-400",children:"Manage admin users and permissions"})]})}),e.jsx(M,{className:"p-6",children:e.jsx("div",{className:"flex items-center justify-center h-64",children:e.jsxs("div",{className:"text-center",children:[e.jsx(O,{className:"h-12 w-12 text-red-400 mx-auto mb-4"}),e.jsx("h3",{className:"text-lg font-medium text-white mb-2",children:"Failed to Load Admins"}),e.jsx("p",{className:"text-gray-400 mb-4",children:C}),e.jsx(L,{onClick:f,variant:"glass",children:"Try Again"})]})})})]}):e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold text-white",children:"Admin Management"}),e.jsx("p",{className:"mt-1 text-sm text-gray-400",children:"Manage admin users and permissions"})]}),e.jsx(L,{variant:"primary",onClick:D,icon:e.jsx(z,{className:"h-5 w-5 mr-2"}),className:"px-6 py-3 text-base",children:"Add Admin"})]}),e.jsx(M,{className:"overflow-hidden",children:e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:"min-w-full divide-y divide-gray-700",children:[e.jsx("thead",{className:"bg-gray-800",children:e.jsxs("tr",{children:[e.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider",children:"Username"}),e.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider",children:"Email"}),e.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider",children:"Role"}),e.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider",children:"Status"}),e.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider",children:"Last Login"}),e.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider",children:"Created"}),e.jsx("th",{scope:"col",className:"px-6 py-3 text-right text-xs font-medium text-gray-300 uppercase tracking-wider",children:"Actions"})]})}),e.jsx("tbody",{className:"divide-y divide-gray-700 bg-gray-800/50",children:x?e.jsx("tr",{children:e.jsx("td",{colSpan:7,className:"px-6 py-4 text-center text-sm text-gray-400",children:e.jsx("div",{className:"flex justify-center",children:e.jsx("div",{className:"animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-indigo-500"})})})}):!Array.isArray(o)||o.length===0?e.jsx("tr",{children:e.jsx("td",{colSpan:7,className:"px-6 py-4 text-center text-sm text-gray-400",children:Array.isArray(o)?"No admins found":"Error loading admins"})}):o.map(s=>e.jsxs("tr",{className:"hover:bg-gray-700/30 transition-colors duration-150",children:[e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-white",children:s.username}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-300",children:s.email}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-300",children:e.jsx("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${s.role==="SuperAdmin"?"bg-purple-900/30 border border-purple-500/30 text-purple-300":"bg-blue-900/30 border border-blue-500/30 text-blue-300"}`,children:s.role})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-300",children:e.jsx("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${b(s)==="active"?"bg-green-900/30 border border-green-500/30 text-green-300":b(s)==="suspended"?"bg-red-900/30 border border-red-500/30 text-red-300":"bg-gray-900/30 border border-gray-500/30 text-gray-300"}`,children:b(s).charAt(0).toUpperCase()+b(s).slice(1)})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-300",children:E(s.last_login)}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-300",children:E(s.created_at)}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium",children:e.jsxs("div",{className:"flex justify-end space-x-2",children:[e.jsx("button",{onClick:()=>$(s),className:"text-indigo-400 hover:text-indigo-300 transition-colors duration-150",title:"Edit Admin",children:e.jsx(q,{className:"h-5 w-5"})}),s.role!=="SuperAdmin"&&e.jsx("button",{onClick:()=>B(s.id,s.username),className:"text-red-400 hover:text-red-300 transition-colors duration-150",title:"Delete Admin",children:e.jsx(Z,{className:"h-5 w-5"})})]})})]},s.id))})]})})}),S&&e.jsx("div",{className:"fixed inset-0 z-50 overflow-y-auto",children:e.jsxs("div",{className:"flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0",children:[e.jsx("div",{className:"fixed inset-0 transition-opacity","aria-hidden":"true",children:e.jsx("div",{className:"absolute inset-0 bg-gray-900 opacity-75"})}),e.jsx("span",{className:"hidden sm:inline-block sm:align-middle sm:h-screen","aria-hidden":"true",children:"​"}),e.jsxs("div",{className:"inline-block align-bottom bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full",children:[e.jsx("div",{className:"bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4",children:e.jsx("div",{className:"sm:flex sm:items-start",children:e.jsxs("div",{className:"mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full",children:[e.jsx("h3",{className:"text-lg leading-6 font-medium text-white",children:"Add New Admin"}),e.jsx("div",{className:"mt-4",children:e.jsxs("form",{onSubmit:w,className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{htmlFor:"username",className:"block text-sm font-medium text-gray-300",children:"Username"}),e.jsx("input",{type:"text",name:"username",id:"username",value:t.username,onChange:c,className:"mt-1 block w-full rounded-md border-gray-700 bg-gray-700 text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-base py-3 px-4"}),n.username&&e.jsx("p",{className:"mt-1 text-sm text-red-400",children:n.username})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-300",children:"Email"}),e.jsx("input",{type:"email",name:"email",id:"email",value:t.email,onChange:c,className:"mt-1 block w-full rounded-md border-gray-700 bg-gray-700 text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-base py-3 px-4"}),n.email&&e.jsx("p",{className:"mt-1 text-sm text-red-400",children:n.email})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"role",className:"block text-sm font-medium text-gray-300",children:"Role"}),e.jsxs("select",{name:"role",id:"role",value:t.role,onChange:c,className:"mt-1 block w-full rounded-md border-gray-700 bg-gray-700 text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-base py-3 px-4",children:[e.jsx("option",{value:"Admin",children:"Admin"}),e.jsx("option",{value:"SuperAdmin",children:"Super Admin"})]})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-300",children:"Password"}),e.jsx("input",{type:"password",name:"password",id:"password",value:t.password,onChange:c,className:"mt-1 block w-full rounded-md border-gray-700 bg-gray-700 text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-base py-3 px-4"}),n.password&&e.jsx("p",{className:"mt-1 text-sm text-red-400",children:n.password})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"confirmPassword",className:"block text-sm font-medium text-gray-300",children:"Confirm Password"}),e.jsx("input",{type:"password",name:"confirmPassword",id:"confirmPassword",value:t.confirmPassword,onChange:c,className:"mt-1 block w-full rounded-md border-gray-700 bg-gray-700 text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-base py-3 px-4"}),n.confirmPassword&&e.jsx("p",{className:"mt-1 text-sm text-red-400",children:n.confirmPassword})]})]})})]})})}),e.jsxs("div",{className:"bg-gray-800 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse",children:[e.jsx("button",{type:"button",onClick:w,disabled:g,className:"w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-6 py-3 bg-indigo-600 text-base font-medium text-white hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:ml-3 sm:w-auto disabled:opacity-50 disabled:cursor-not-allowed",children:g?"Creating...":"Add Admin"}),e.jsx("button",{type:"button",onClick:()=>N(!1),className:"mt-3 w-full inline-flex justify-center rounded-md border border-gray-600 shadow-sm px-6 py-3 bg-gray-700 text-base font-medium text-gray-300 hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 sm:mt-0 sm:ml-3 sm:w-auto",children:"Cancel"})]})]})]})}),h&&e.jsx("div",{className:"fixed inset-0 z-50 overflow-y-auto",children:e.jsxs("div",{className:"flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0",children:[e.jsx("div",{className:"fixed inset-0 transition-opacity","aria-hidden":"true",children:e.jsx("div",{className:"absolute inset-0 bg-gray-900 opacity-75"})}),e.jsx("span",{className:"hidden sm:inline-block sm:align-middle sm:h-screen","aria-hidden":"true",children:"​"}),e.jsxs("div",{className:"inline-block align-bottom bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full",children:[e.jsx("div",{className:"bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4",children:e.jsx("div",{className:"sm:flex sm:items-start",children:e.jsxs("div",{className:"mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full",children:[e.jsx("h3",{className:"text-lg leading-6 font-medium text-white",children:"Edit Admin"}),e.jsx("div",{className:"mt-4",children:e.jsxs("form",{onSubmit:w,className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{htmlFor:"username",className:"block text-sm font-medium text-gray-300",children:"Username"}),e.jsx("input",{type:"text",name:"username",id:"username",value:t.username,onChange:c,className:"mt-1 block w-full rounded-md border-gray-700 bg-gray-700 text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-base py-3 px-4"}),n.username&&e.jsx("p",{className:"mt-1 text-sm text-red-400",children:n.username})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-300",children:"Email"}),e.jsx("input",{type:"email",name:"email",id:"email",value:t.email,onChange:c,className:"mt-1 block w-full rounded-md border-gray-700 bg-gray-700 text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-base py-3 px-4"}),n.email&&e.jsx("p",{className:"mt-1 text-sm text-red-400",children:n.email})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"role",className:"block text-sm font-medium text-gray-300",children:"Role"}),e.jsxs("select",{name:"role",id:"role",value:t.role,onChange:c,className:"mt-1 block w-full rounded-md border-gray-700 bg-gray-700 text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-base py-3 px-4",disabled:(u==null?void 0:u.role)==="SuperAdmin",children:[e.jsx("option",{value:"Admin",children:"Admin"}),e.jsx("option",{value:"SuperAdmin",children:"Super Admin"})]})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-300",children:"New Password (leave blank to keep current)"}),e.jsx("input",{type:"password",name:"password",id:"password",value:t.password,onChange:c,className:"mt-1 block w-full rounded-md border-gray-700 bg-gray-700 text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-base py-3 px-4"}),n.password&&e.jsx("p",{className:"mt-1 text-sm text-red-400",children:n.password})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"confirmPassword",className:"block text-sm font-medium text-gray-300",children:"Confirm New Password"}),e.jsx("input",{type:"password",name:"confirmPassword",id:"confirmPassword",value:t.confirmPassword,onChange:c,className:"mt-1 block w-full rounded-md border-gray-700 bg-gray-700 text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-base py-3 px-4"}),n.confirmPassword&&e.jsx("p",{className:"mt-1 text-sm text-red-400",children:n.confirmPassword})]})]})})]})})}),e.jsxs("div",{className:"bg-gray-800 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse",children:[e.jsx("button",{type:"button",onClick:w,disabled:g,className:"w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-6 py-3 bg-indigo-600 text-base font-medium text-white hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:ml-3 sm:w-auto disabled:opacity-50 disabled:cursor-not-allowed",children:g?"Saving...":"Save Changes"}),e.jsx("button",{type:"button",onClick:()=>v(!1),className:"mt-3 w-full inline-flex justify-center rounded-md border border-gray-600 shadow-sm px-6 py-3 bg-gray-700 text-base font-medium text-gray-300 hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 sm:mt-0 sm:ml-3 sm:w-auto",children:"Cancel"})]})]})]})})]})}export{G as default};
