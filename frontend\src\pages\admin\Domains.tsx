import { useState, useEffect } from 'react';
import Card from '../../components/ui/Card';
import Button from '../../components/ui/Button';
import { 
  GlobeAltIcon,
  CheckCircleIcon,
  XCircleIcon,
  PlusIcon,
  ExclamationTriangleIcon,
  ArrowPathIcon,
  ShieldCheckIcon,
} from '@heroicons/react/24/outline';

// Sample data for domains
const domains = [
  {
    id: '1',
    name: 'easybot.io',
    status: 'active',
    ssl: true,
    expiry: '2024-05-15',
    registrar: 'Namecheap',
    autoRenew: true,
    dns: 'Cloudflare',
  },
  {
    id: '2',
    name: 'easybotapi.com',
    status: 'active',
    ssl: true,
    expiry: '2024-08-22',
    registrar: 'GoDaddy',
    autoRenew: true,
    dns: 'Cloudflare',
  },
  {
    id: '3',
    name: 'easybot-docs.io',
    status: 'active',
    ssl: true,
    expiry: '2023-12-10',
    registrar: 'Namecheap',
    autoRenew: true,
    dns: 'Cloudflare',
  },
  {
    id: '4',
    name: 'easybot-staging.com',
    status: 'inactive',
    ssl: false,
    expiry: '2024-02-05',
    registrar: 'GoDaddy',
    autoRenew: false,
    dns: 'GoDaddy',
  },
  {
    id: '5',
    name: 'easybot-admin.io',
    status: 'active',
    ssl: true,
    expiry: '2024-06-30',
    registrar: 'Namecheap',
    autoRenew: true,
    dns: 'Cloudflare',
  },
];

// Sample data for DNS records
const dnsRecords = [
  { id: '1', type: 'A', name: 'easybot.io', value: '104.21.56.78', ttl: '3600' },
  { id: '2', type: 'CNAME', name: 'www.easybot.io', value: 'easybot.io', ttl: '3600' },
  { id: '3', type: 'MX', name: 'easybot.io', value: 'mail.easybot.io', ttl: '3600', priority: '10' },
  { id: '4', type: 'TXT', name: 'easybot.io', value: 'v=spf1 include:_spf.google.com ~all', ttl: '3600' },
  { id: '5', type: 'A', name: 'api.easybot.io', value: '104.21.56.79', ttl: '3600' },
];

export default function AdminDomains() {
  const [loading, setLoading] = useState(true);
  const [selectedDomain, setSelectedDomain] = useState('easybot.io');
  
  useEffect(() => {
    // Simulate loading
    const timer = setTimeout(() => {
      setLoading(false);
    }, 800);
    
    return () => clearTimeout(timer);
  }, []);
  
  function classNames(...classes: string[]) {
    return classes.filter(Boolean).join(' ');
  }
  
  // Calculate days until expiry
  const getDaysUntilExpiry = (expiryDate: string) => {
    const today = new Date();
    const expiry = new Date(expiryDate);
    const diffTime = expiry.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };
  
  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"></div>
      </div>
    );
  }
  
  return (
    <div className="space-y-8">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-2xl font-bold text-white">Domains</h1>
          <p className="mt-1 text-sm text-gray-400">Manage your domain names and DNS settings</p>
        </div>
        <Button variant="glass" glow={true} size="md">
          <PlusIcon className="h-5 w-5 mr-2" />
          Add Domain
        </Button>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="p-6 relative overflow-hidden">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="h-12 w-12 rounded-full bg-indigo-800/30 flex items-center justify-center">
                <GlobeAltIcon className="h-6 w-6 text-indigo-400" aria-hidden="true" />
              </div>
            </div>
            <div className="ml-5 w-0 flex-1">
              <dl>
                <dt className="text-sm font-medium text-gray-300 truncate">Total Domains</dt>
                <dd>
                  <div className="text-lg font-semibold text-white">5</div>
                </dd>
              </dl>
            </div>
          </div>
        </Card>
        
        <Card className="p-6 relative overflow-hidden">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="h-12 w-12 rounded-full bg-green-800/30 flex items-center justify-center">
                <ShieldCheckIcon className="h-6 w-6 text-green-400" aria-hidden="true" />
              </div>
            </div>
            <div className="ml-5 w-0 flex-1">
              <dl>
                <dt className="text-sm font-medium text-gray-300 truncate">SSL Protected</dt>
                <dd>
                  <div className="text-lg font-semibold text-white">4</div>
                </dd>
              </dl>
            </div>
          </div>
        </Card>
        
        <Card className="p-6 relative overflow-hidden">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="h-12 w-12 rounded-full bg-yellow-800/30 flex items-center justify-center">
                <ExclamationTriangleIcon className="h-6 w-6 text-yellow-400" aria-hidden="true" />
              </div>
            </div>
            <div className="ml-5 w-0 flex-1">
              <dl>
                <dt className="text-sm font-medium text-gray-300 truncate">Expiring Soon</dt>
                <dd>
                  <div className="text-lg font-semibold text-white">1</div>
                </dd>
              </dl>
            </div>
          </div>
        </Card>
      </div>
      
      <div className="grid grid-cols-1 gap-6">
        <Card className="p-6 relative overflow-hidden">
          <div className="overflow-hidden rounded-xl border border-white/10 bg-white/5 backdrop-blur-sm">
            <table className="min-w-full divide-y divide-white/10">
              <thead>
                <tr>
                  <th scope="col" className="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-white sm:pl-6">
                    Domain
                  </th>
                  <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-white">
                    Status
                  </th>
                  <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-white">
                    SSL
                  </th>
                  <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-white">
                    Expiry
                  </th>
                  <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-white">
                    Registrar
                  </th>
                  <th scope="col" className="relative py-3.5 pl-3 pr-4 sm:pr-6">
                    <span className="sr-only">Actions</span>
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-white/10">
                {domains.map((domain) => {
                  const daysUntilExpiry = getDaysUntilExpiry(domain.expiry);
                  const isExpiringSoon = daysUntilExpiry <= 30;
                  
                  return (
                    <tr 
                      key={domain.id} 
                      className={classNames(
                        selectedDomain === domain.name ? 'bg-indigo-900/20' : '',
                        'cursor-pointer hover:bg-indigo-900/10'
                      )}
                      onClick={() => setSelectedDomain(domain.name)}
                    >
                      <td className="whitespace-nowrap py-4 pl-4 pr-3 text-sm sm:pl-6">
                        <div className="flex items-center">
                          <div className="h-10 w-10 flex-shrink-0 rounded-full bg-indigo-800/30 flex items-center justify-center">
                            <GlobeAltIcon className="h-5 w-5 text-indigo-400" aria-hidden="true" />
                          </div>
                          <div className="ml-4">
                            <div className="font-medium text-white">{domain.name}</div>
                            <div className="text-gray-400">DNS: {domain.dns}</div>
                          </div>
                        </div>
                      </td>
                      <td className="whitespace-nowrap px-3 py-4 text-sm">
                        <span
                          className={classNames(
                            domain.status === 'active'
                              ? 'bg-green-900/30 text-green-400 border border-green-500/30'
                              : 'bg-red-900/30 text-red-400 border border-red-500/30',
                            'inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium'
                          )}
                        >
                          {domain.status === 'active' ? (
                            <CheckCircleIcon className="-ml-0.5 mr-1.5 h-3 w-3 text-green-400" />
                          ) : (
                            <XCircleIcon className="-ml-0.5 mr-1.5 h-3 w-3 text-red-400" />
                          )}
                          {domain.status === 'active' ? 'Active' : 'Inactive'}
                        </span>
                      </td>
                      <td className="whitespace-nowrap px-3 py-4 text-sm">
                        <span
                          className={classNames(
                            domain.ssl
                              ? 'bg-green-900/30 text-green-400 border border-green-500/30'
                              : 'bg-red-900/30 text-red-400 border border-red-500/30',
                            'inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium'
                          )}
                        >
                          {domain.ssl ? (
                            <ShieldCheckIcon className="-ml-0.5 mr-1.5 h-3 w-3 text-green-400" />
                          ) : (
                            <XCircleIcon className="-ml-0.5 mr-1.5 h-3 w-3 text-red-400" />
                          )}
                          {domain.ssl ? 'Secured' : 'Not Secured'}
                        </span>
                      </td>
                      <td className="whitespace-nowrap px-3 py-4 text-sm">
                        <div className="text-gray-300">{domain.expiry}</div>
                        {isExpiringSoon && (
                          <div className="text-yellow-400 text-xs flex items-center mt-1">
                            <ExclamationTriangleIcon className="h-3 w-3 mr-1" />
                            Expires in {daysUntilExpiry} days
                          </div>
                        )}
                      </td>
                      <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-300">
                        {domain.registrar}
                        <div className="text-xs text-gray-400">
                          {domain.autoRenew ? 'Auto-renew enabled' : 'Auto-renew disabled'}
                        </div>
                      </td>
                      <td className="relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-6">
                        <div className="flex justify-end space-x-2">
                          <Button variant="outline" size="sm">Manage</Button>
                          <Button variant="outline" size="sm">DNS</Button>
                        </div>
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        </Card>
      </div>
      
      <div className="grid grid-cols-1 gap-6">
        <Card className="p-6 relative overflow-hidden">
          <h3 className="text-base font-semibold leading-6 text-white mb-4">
            DNS Records for {selectedDomain}
          </h3>
          <div className="overflow-hidden rounded-xl border border-white/10 bg-white/5 backdrop-blur-sm">
            <table className="min-w-full divide-y divide-white/10">
              <thead>
                <tr>
                  <th scope="col" className="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-white sm:pl-6">
                    Type
                  </th>
                  <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-white">
                    Name
                  </th>
                  <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-white">
                    Value
                  </th>
                  <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-white">
                    TTL
                  </th>
                  <th scope="col" className="relative py-3.5 pl-3 pr-4 sm:pr-6">
                    <span className="sr-only">Actions</span>
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-white/10">
                {dnsRecords
                  .filter(record => record.name.includes(selectedDomain))
                  .map((record) => (
                    <tr key={record.id}>
                      <td className="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-white sm:pl-6">
                        <span className="inline-flex items-center rounded-md bg-indigo-900/30 px-2 py-1 text-xs font-medium text-indigo-300 ring-1 ring-inset ring-indigo-500/20">
                          {record.type}
                        </span>
                      </td>
                      <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-300">
                        {record.name}
                      </td>
                      <td className="px-3 py-4 text-sm text-gray-300">
                        <div className="max-w-xs truncate">{record.value}</div>
                      </td>
                      <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-300">
                        {record.ttl}
                      </td>
                      <td className="relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-6">
                        <div className="flex justify-end space-x-2">
                          <Button variant="outline" size="sm">Edit</Button>
                          <Button variant="outline" size="sm" color="red">Delete</Button>
                        </div>
                      </td>
                    </tr>
                  ))}
              </tbody>
            </table>
          </div>
          <div className="mt-4 flex justify-end">
            <Button variant="glass" size="sm">
              <PlusIcon className="h-4 w-4 mr-2" />
              Add Record
            </Button>
          </div>
        </Card>
      </div>
    </div>
  );
}
