import{r as c,j as e,F as g,a as C,L as o,b as G}from"./index-dCUkEeO4.js";import{C as t}from"./Card-CLlE15Sf.js";import{L as T}from"./auto-BiC8v7eM.js";import{adminApi as A}from"./adminApi-BFZ8qr13.js";import{F as j}from"./UserIcon-DIEnBeEF.js";import{F as d}from"./ArrowUpIcon-D56z6YfI.js";import{F as f}from"./BoltIcon-8o3QXlI2.js";const D={responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"top",labels:{color:"#e5e7eb"}},tooltip:{mode:"index",intersect:!1}},scales:{y:{grid:{color:"rgba(255, 255, 255, 0.1)"},ticks:{color:"#e5e7eb"}},x:{grid:{color:"rgba(255, 255, 255, 0.1)"},ticks:{color:"#e5e7eb"}}}};function O(){const[N,m]=c.useState(!0),[a,b]=c.useState({totalUsers:0,activeUsers:0,userGrowth:0,totalBots:0,activeBots:0,botGrowth:0,totalTransactions:0,transactionVolume:0,transactionGrowth:0,adminFeeEarned:0,adminFeeGrowth:0,systemStatus:"operational",alerts:[]}),[y,p]=c.useState({labels:[],datasets:[{label:"Transaction Volume",data:[],borderColor:"rgb(99, 102, 241)",backgroundColor:"rgba(99, 102, 241, 0.5)"},{label:"Admin Fee Earned",data:[],borderColor:"rgb(34, 197, 94)",backgroundColor:"rgba(34, 197, 94, 0.5)"}]});c.useEffect(()=>{(async()=>{m(!0);try{const s=await A.getDashboardAnalytics();b({totalUsers:s.analytics.total_users,activeUsers:s.analytics.active_users_24h,userGrowth:s.analytics.total_users>0?s.analytics.active_users_24h/s.analytics.total_users*100:0,totalBots:s.bots_summary.length,activeBots:s.bots_summary.filter(r=>r.status==="Active").length,botGrowth:s.bots_summary.length>0?s.bots_summary.filter(r=>r.status==="Active").length/s.bots_summary.length*100:0,totalTransactions:s.analytics.total_transactions,transactionVolume:s.analytics.total_volume,transactionGrowth:s.analytics.total_transactions>0?s.analytics.transactions_24h/s.analytics.total_transactions*100:0,adminFeeEarned:s.analytics.total_fees_collected,adminFeeGrowth:s.analytics.total_fees_collected>0?s.analytics.fees_collected_24h/s.analytics.total_fees_collected*100:0,systemStatus:s.system_health.status==="healthy"?"operational":"degraded",alerts:s.alerts});const i=s.analytics.hourly_stats;if(i&&i.length>0){const r=[],h=[],w=["Mon","Tue","Wed","Thu","Fri","Sat","Sun"];for(let n=0;n<7;n++){const _=n*24,F=Math.min((n+1)*24,i.length),u=i.slice(_,F).reduce((E,S)=>E+S.volume,0),k=u*(s.analytics.total_fees_collected/s.analytics.total_volume||.025);r.push(u),h.push(k)}p({labels:w,datasets:[{label:"Transaction Volume",data:r,borderColor:"rgb(99, 102, 241)",backgroundColor:"rgba(99, 102, 241, 0.5)"},{label:"Admin Fee Earned",data:h,borderColor:"rgb(34, 197, 94)",backgroundColor:"rgba(34, 197, 94, 0.5)"}]})}m(!1)}catch(s){console.error("Error fetching dashboard stats:",s),m(!1)}})()},[]);const v=l=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD",minimumFractionDigits:2,maximumFractionDigits:2}).format(l),x=l=>new Intl.NumberFormat("en-US").format(l);return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold text-white",children:"EasyBoard Dashboard"}),e.jsx("p",{className:"mt-1 text-sm text-gray-400",children:"Overview of your EasyBot system performance"})]}),e.jsx("div",{className:"flex items-center space-x-2"})]}),N?e.jsx("div",{className:"flex justify-center items-center h-64",children:e.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"})}):e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4",children:[e.jsx(t,{className:"p-4",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"flex-shrink-0 p-3 rounded-md bg-indigo-500/20",children:e.jsx(j,{className:"h-6 w-6 text-indigo-400"})}),e.jsxs("div",{className:"ml-4",children:[e.jsx("h3",{className:"text-sm font-medium text-gray-400",children:"Total Users"}),e.jsxs("div",{className:"flex items-center",children:[e.jsx("p",{className:"text-2xl font-semibold text-white",children:x(a.totalUsers)}),e.jsxs("span",{className:"ml-2 text-xs font-medium text-green-400 flex items-center",children:[e.jsx(d,{className:"h-3 w-3 mr-0.5"}),a.userGrowth,"%"]})]})]})]})}),e.jsx(t,{className:"p-4",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"flex-shrink-0 p-3 rounded-md bg-purple-500/20",children:e.jsx(g,{className:"h-6 w-6 text-purple-400"})}),e.jsxs("div",{className:"ml-4",children:[e.jsx("h3",{className:"text-sm font-medium text-gray-400",children:"Active Bots"}),e.jsxs("div",{className:"flex items-center",children:[e.jsx("p",{className:"text-2xl font-semibold text-white",children:x(a.activeBots)}),e.jsxs("span",{className:"ml-2 text-xs font-medium text-green-400 flex items-center",children:[e.jsx(d,{className:"h-3 w-3 mr-0.5"}),a.botGrowth,"%"]})]})]})]})}),e.jsx(t,{className:"p-4",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"flex-shrink-0 p-3 rounded-md bg-blue-500/20",children:e.jsx(f,{className:"h-6 w-6 text-blue-400"})}),e.jsxs("div",{className:"ml-4",children:[e.jsx("h3",{className:"text-sm font-medium text-gray-400",children:"Transactions"}),e.jsxs("div",{className:"flex items-center",children:[e.jsx("p",{className:"text-2xl font-semibold text-white",children:x(a.totalTransactions)}),e.jsxs("span",{className:"ml-2 text-xs font-medium text-green-400 flex items-center",children:[e.jsx(d,{className:"h-3 w-3 mr-0.5"}),a.transactionGrowth,"%"]})]})]})]})}),e.jsx(t,{className:"p-4",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"flex-shrink-0 p-3 rounded-md bg-green-500/20",children:e.jsx(C,{className:"h-6 w-6 text-green-400"})}),e.jsxs("div",{className:"ml-4",children:[e.jsx("h3",{className:"text-sm font-medium text-gray-400",children:"Admin Fee Earned"}),e.jsxs("div",{className:"flex items-center",children:[e.jsx("p",{className:"text-2xl font-semibold text-white",children:v(a.adminFeeEarned)}),e.jsxs("span",{className:"ml-2 text-xs font-medium text-green-400 flex items-center",children:[e.jsx(d,{className:"h-3 w-3 mr-0.5"}),a.adminFeeGrowth,"%"]})]})]})]})})]}),e.jsx("div",{className:"grid grid-cols-1 gap-6",children:e.jsxs(t,{className:"p-4",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsx("h3",{className:"text-lg font-medium text-white",children:"Transaction Overview"}),e.jsx("div",{className:"text-sm text-gray-400",children:"Last 7 days"})]}),e.jsx("div",{className:"h-80",children:e.jsx(T,{options:D,data:y})})]})}),e.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4",children:[e.jsx(o,{to:"/dashboard/users",children:e.jsx(t,{className:"p-4 hover:bg-gray-800/50 transition-colors duration-200",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"flex-shrink-0 p-3 rounded-md bg-indigo-500/20",children:e.jsx(j,{className:"h-6 w-6 text-indigo-400"})}),e.jsxs("div",{className:"ml-4",children:[e.jsx("h3",{className:"text-sm font-medium text-white",children:"User Analytics"}),e.jsx("p",{className:"text-xs text-gray-400 mt-1",children:"View detailed user statistics"})]})]})})}),e.jsx(o,{to:"/dashboard/bots",children:e.jsx(t,{className:"p-4 hover:bg-gray-800/50 transition-colors duration-200",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"flex-shrink-0 p-3 rounded-md bg-purple-500/20",children:e.jsx(g,{className:"h-6 w-6 text-purple-400"})}),e.jsxs("div",{className:"ml-4",children:[e.jsx("h3",{className:"text-sm font-medium text-white",children:"Bot Management"}),e.jsx("p",{className:"text-xs text-gray-400 mt-1",children:"Configure and monitor bots"})]})]})})}),e.jsx(o,{to:"/dashboard/transactions",children:e.jsx(t,{className:"p-4 hover:bg-gray-800/50 transition-colors duration-200",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"flex-shrink-0 p-3 rounded-md bg-blue-500/20",children:e.jsx(f,{className:"h-6 w-6 text-blue-400"})}),e.jsxs("div",{className:"ml-4",children:[e.jsx("h3",{className:"text-sm font-medium text-white",children:"Transactions"}),e.jsx("p",{className:"text-xs text-gray-400 mt-1",children:"View transaction history"})]})]})})}),e.jsx(o,{to:"/dashboard/settings",children:e.jsx(t,{className:"p-4 hover:bg-gray-800/50 transition-colors duration-200",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"flex-shrink-0 p-3 rounded-md bg-green-500/20",children:e.jsx(G,{className:"h-6 w-6 text-green-400"})}),e.jsxs("div",{className:"ml-4",children:[e.jsx("h3",{className:"text-sm font-medium text-white",children:"Admin Settings"}),e.jsx("p",{className:"text-xs text-gray-400 mt-1",children:"Configure system settings"})]})]})})})]})]})]})}export{O as default};
