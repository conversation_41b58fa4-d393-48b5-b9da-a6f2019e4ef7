use axum::{
    routing::{get, post, put, delete},
    Router,
    extract::{Json, Path, Query, State},
    http::{StatusCode, HeaderMap},
    response::{IntoResponse, Response},
};
use serde::{Deserialize, Serialize};
use mongodb::{
    bson::{doc, oid::ObjectId, DateTime},
    options::{FindOptions, AggregateOptions},
    error::Error as MongoError,
};
use futures_util::TryStreamExt;
use std::{
    sync::Arc,
    collections::HashMap,
    time::{SystemTime, UNIX_EPOCH, Duration},
};
use tokio::time::timeout;
use tracing::{error, warn, info, debug};
use anyhow::{Result, Context, anyhow};
use crate::model::{Trade, BotError, Blockchain, User};
use crate::service::{
    db_service::DbService,
    admin_fee_collector::AdminFeeCollector,
    price_service::PriceService,
};

#[derive(Debug, Clone)]
struct UserInfo {
    pub username: Option<String>,
    pub first_name: String,
    pub last_name: Option<String>,
}
use crate::config::AppConfig;
use crate::controllers::auth_controller::Claims;
use jsonwebtoken::{decode, DecodingKey, Validation, Algorithm};

// Production constants
const DB_OPERATION_TIMEOUT: Duration = Duration::from_secs(30);
const MAX_TRANSACTIONS_PER_PAGE: i64 = 100;
const DEFAULT_TRANSACTIONS_PER_PAGE: i64 = 20;
const MAX_SEARCH_LENGTH: usize = 100;
const MAX_EXPORT_LIMIT: i64 = 10000;

#[derive(Debug, thiserror::Error)]
pub enum TransactionsError {
    #[error("Database operation failed: {0}")]
    DatabaseError(#[from] MongoError),
    #[error("Transaction not found: {0}")]
    TransactionNotFound(String),
    #[error("Invalid input: {0}")]
    InvalidInput(String),
    #[error("Authorization failed: {0}")]
    AuthorizationFailed(String),
    #[error("Operation timeout")]
    Timeout,
    #[error("Export failed: {0}")]
    ExportError(String),
    #[error("Validation failed: {0}")]
    ValidationError(String),
}

#[derive(Debug, Serialize)]
pub struct TransactionResponse {
    pub id: String,
    pub user_id: String,
    pub username: Option<String>,
    pub first_name: Option<String>,
    pub last_name: Option<String>,
    pub bot_type: String,
    pub transaction_type: String,
    pub direction: String, // "buy" or "sell"

    // Enhanced amount tracking
    pub native_token_amount: Option<f64>, // Amount of native token (ETH, SOL, BNB)
    pub token_amount: Option<f64>, // Amount of the specific token
    pub native_token_symbol: Option<String>, // ETH, SOL, BNB, etc.

    // Legacy amount field for backward compatibility
    pub amount: f64,

    pub token_symbol: String,
    pub token_address: String,
    pub status: String,
    pub timestamp: u64,
    pub blockchain: String,
    pub gas_fee: Option<f64>,
    pub success: bool,
    pub hash: Option<String>,
    pub block_number: Option<u64>,
    pub error_message: Option<String>,

    // Admin fee information
    pub admin_fee_amount: Option<f64>,
    pub admin_fee_percentage: Option<f64>,
    pub admin_fee_status: Option<String>,
    pub admin_fee_collection_method: Option<String>,
    pub admin_fee_token_symbol: Option<String>,
    pub admin_fee_token_address: Option<String>,
    pub admin_fee_transaction_id: Option<String>,
}

#[derive(Debug, Serialize)]
pub struct BlockchainVolume {
    pub blockchain: String,
    pub total_volume: f64,
    pub total_volume_usd: Option<f64>,
    pub native_symbol: String,
    pub transaction_count: i64,
}

#[derive(Debug, Serialize)]
pub struct FeeStatistics {
    pub blockchain: String,
    pub native_symbol: String,
    pub today_fees_native: f64,
    pub today_fees_usd: f64,
    pub weekly_fees_native: f64,
    pub weekly_fees_usd: f64,
    pub monthly_fees_native: f64,
    pub monthly_fees_usd: f64,
    pub total_fees_native: f64,
    pub total_fees_usd: f64,
}

#[derive(Debug, Serialize)]
pub struct FeeStatisticsResponse {
    pub fee_statistics: Vec<FeeStatistics>,
    pub total_fees_usd: f64,
}

#[derive(Debug, Serialize)]
pub struct TransactionListResponse {
    pub transactions: Vec<TransactionResponse>,
    pub total: i64,
    pub page: i64,
    pub per_page: i64,
    pub success_rate: f64,
    pub blockchain_volumes: Vec<BlockchainVolume>,
}

#[derive(Debug, Deserialize, Serialize)]
pub struct TransactionQueryParams {
    pub page: Option<i64>,
    pub per_page: Option<i64>,
    pub bot_type: Option<String>,
    pub status: Option<String>,
    pub blockchain: Option<String>,
    pub user_id: Option<String>,
    pub start_date: Option<String>,
    pub end_date: Option<String>,
    pub search: Option<String>,
    pub sort_by: Option<String>,
    pub sort_order: Option<String>,
}

#[derive(Debug, Serialize)]
pub struct TransactionAnalytics {
    pub total_transactions: i64,
    pub total_volume: f64,
    pub success_rate: f64,
    pub average_gas_fee: f64,
    pub transactions_by_blockchain: HashMap<String, i64>,
    pub transactions_by_type: HashMap<String, i64>,
    pub volume_by_blockchain: HashMap<String, f64>,
    pub hourly_stats: Vec<HourlyTransactionStats>,
    pub top_tokens: Vec<TokenStats>,
}

#[derive(Debug, Serialize)]
pub struct HourlyTransactionStats {
    pub hour: u64,
    pub transactions: i64,
    pub volume: f64,
    pub success_rate: f64,
    pub average_gas_fee: f64,
}

#[derive(Debug, Serialize)]
pub struct TokenStats {
    pub symbol: String,
    pub address: String,
    pub transactions: i64,
    pub volume: f64,
    pub blockchain: String,
}

pub struct TransactionsController {
    config: Arc<AppConfig>,
    jwt_secret: String,
}

impl TransactionsController {
    pub fn new(config: Arc<AppConfig>) -> Self {
        let jwt_secret = std::env::var("JWT_SECRET")
            .context("JWT_SECRET environment variable is required")
            .expect("Critical configuration missing");

        Self { config, jwt_secret }
    }

    /// Verify authentication header and extract claims
    async fn verify_auth_header(&self, headers: &HeaderMap) -> Option<Claims> {
        if let Some(auth_header) = headers.get("authorization") {
            if let Ok(auth_str) = auth_header.to_str() {
                if let Some(token) = auth_str.strip_prefix("Bearer ") {
                    let validation = Validation::new(Algorithm::HS256);
                    if let Ok(token_data) = decode::<Claims>(
                        token,
                        &DecodingKey::from_secret(self.jwt_secret.as_ref()),
                        &validation,
                    ) {
                        return Some(token_data.claims);
                    }
                }
            }
        }
        None
    }

    /// Validate query parameters
    fn validate_query_params(&self, params: &TransactionQueryParams) -> Result<(), TransactionsError> {
        if let Some(per_page) = params.per_page {
            if per_page <= 0 || per_page > MAX_TRANSACTIONS_PER_PAGE {
                return Err(TransactionsError::InvalidInput(
                    format!("per_page must be between 1 and {}", MAX_TRANSACTIONS_PER_PAGE)
                ));
            }
        }

        if let Some(page) = params.page {
            if page <= 0 {
                return Err(TransactionsError::InvalidInput("page must be greater than 0".to_string()));
            }
        }

        if let Some(search) = &params.search {
            if search.len() > MAX_SEARCH_LENGTH {
                return Err(TransactionsError::InvalidInput(
                    format!("search query too long (max {} characters)", MAX_SEARCH_LENGTH)
                ));
            }
        }

        // Validate date range
        if let (Some(start_date), Some(end_date)) = (&params.start_date, &params.end_date) {
            if let (Ok(start), Ok(end)) = (start_date.parse::<i64>(), end_date.parse::<i64>()) {
                if start > end {
                    return Err(TransactionsError::InvalidInput("start_date must be before end_date".to_string()));
                }

                let now = SystemTime::now()
                    .duration_since(UNIX_EPOCH)
                    .map_err(|_| TransactionsError::ValidationError("System time error".to_string()))?
                    .as_secs() as i64;

                if start > now || end > now {
                    return Err(TransactionsError::InvalidInput("Date cannot be in the future".to_string()));
                }
            } else {
                return Err(TransactionsError::InvalidInput("Invalid date format".to_string()));
            }
        }

        Ok(())
    }

    pub fn create_router(&self) -> Router {
        let controller = Arc::new(self.clone());
        let routes = &self.config.api_routes;

        Router::new()
            .route(&routes.admin_transactions, get(Self::get_transactions))
            .route(&format!("{}/:id", routes.admin_transactions), get(Self::get_transaction))
            .route(&format!("{}/:id/collect-fee", routes.admin_transactions), post(Self::collect_admin_fee))
            .route(&format!("{}/analytics", routes.admin_transactions), get(Self::get_analytics))
            .route(&format!("{}/fee-statistics", routes.admin_transactions), get(Self::get_fee_statistics))
            .route(&format!("{}/export", routes.admin_transactions), get(Self::export_transactions))
            .route(&format!("{}/:user_id/transactions", routes.admin_users), get(Self::get_user_transactions))
            .with_state(controller)
    }

    async fn get_transactions(
        State(controller): State<Arc<TransactionsController>>,
        headers: HeaderMap,
        Query(params): Query<TransactionQueryParams>,
    ) -> impl IntoResponse {
        let start_time = SystemTime::now();

        // Verify authentication
        let claims = match controller.verify_auth_header(&headers).await {
            Some(claims) => claims,
            None => {
                warn!("Unauthorized transactions list access attempt");
                return (StatusCode::UNAUTHORIZED, Json(serde_json::json!({
                    "error": "Unauthorized access",
                    "code": "AUTH_REQUIRED"
                }))).into_response();
            }
        };

        info!("Transactions list requested by admin: {}", claims.username);

        // Validate query parameters
        if let Err(validation_error) = controller.validate_query_params(&params) {
            warn!("Invalid query parameters: {:?}", validation_error);
            return (StatusCode::BAD_REQUEST, Json(serde_json::json!({
                "error": validation_error.to_string(),
                "code": "INVALID_PARAMS"
            }))).into_response();
        }

        let page = params.page.unwrap_or(1);
        let per_page = params.per_page.unwrap_or(DEFAULT_TRANSACTIONS_PER_PAGE).min(MAX_TRANSACTIONS_PER_PAGE);
        let skip = (page - 1) * per_page;

        // Execute database operations with timeout
        let db_result = timeout(
            DB_OPERATION_TIMEOUT,
            controller.fetch_transactions_with_stats(params, page, per_page, skip)
        ).await;

        match db_result {
            Ok(Ok(response)) => {
                let elapsed = start_time.elapsed().unwrap_or_default();
                info!("Transactions list generated for {} in {:?}", claims.username, elapsed);
                (StatusCode::OK, Json(response)).into_response()
            }
            Ok(Err(e)) => {
                error!("Database error while fetching transactions: {:?}", e);
                (StatusCode::INTERNAL_SERVER_ERROR, Json(serde_json::json!({
                    "error": "Failed to fetch transactions",
                    "code": "DATABASE_ERROR"
                }))).into_response()
            }
            Err(_) => {
                error!("Timeout while fetching transactions");
                (StatusCode::REQUEST_TIMEOUT, Json(serde_json::json!({
                    "error": "Request timeout",
                    "code": "TIMEOUT"
                }))).into_response()
            }
        }
    }

    async fn fetch_transactions_with_stats(
        &self,
        params: TransactionQueryParams,
        page: i64,
        per_page: i64,
        skip: i64,
    ) -> Result<TransactionListResponse, TransactionsError> {
        let db = DbService::get_db();
        let trades_collection = db.collection::<Trade>("trades");
        let users_collection = db.collection::<User>("users");

        // Build filter with input sanitization
        let filter = self.build_transaction_filter(&params)?;

        // Get total count and transactions concurrently
        let count_future = trades_collection.count_documents(filter.clone(), None);

        // Build sort options with validation
        let sort_field = params.sort_by.as_deref().unwrap_or("timestamp");
        let valid_sort_fields = ["timestamp", "amount_out", "status", "blockchain"];
        if !valid_sort_fields.contains(&sort_field) {
            return Err(TransactionsError::InvalidInput("Invalid sort field".to_string()));
        }

        let sort_order = if params.sort_order.as_deref() == Some("asc") { 1 } else { -1 };

        let find_options = FindOptions::builder()
            .skip(skip as u64)
            .limit(per_page)
            .sort(doc! { sort_field: sort_order })
            .build();

        let trades_future = trades_collection.find(filter, find_options);

        let (total_result, cursor_result): (u64, mongodb::Cursor<Trade>) = tokio::try_join!(count_future, trades_future)?;
        let mut cursor = cursor_result;
        let mut transactions = Vec::new();
        let mut successful_transactions = 0;

        // Collect user IDs for batch lookup and calculate blockchain volumes
        let mut user_ids = Vec::new();
        let mut trades_data = Vec::new();
        let mut blockchain_volume_map: std::collections::HashMap<String, (f64, i64, String)> = std::collections::HashMap::new();

        while let Some(trade) = cursor.try_next().await? {
            // Use native_token_amount for volume calculation (more accurate)
            let volume_amount = trade.native_token_amount.or(trade.amount_out).unwrap_or(0.0);

            if trade.status.as_deref() == Some("completed") {
                successful_transactions += 1;
            }

            // Aggregate by blockchain
            let blockchain_str = format!("{:?}", trade.blockchain);
            let native_symbol = match trade.blockchain {
                crate::model::blockchain::Blockchain::ETH => "ETH",
                crate::model::blockchain::Blockchain::BSC => "BNB",
                crate::model::blockchain::Blockchain::BASE => "ETH",
                crate::model::blockchain::Blockchain::SOL => "SOL",
            };

            let entry = blockchain_volume_map.entry(blockchain_str.clone()).or_insert((0.0, 0, native_symbol.to_string()));
            entry.0 += volume_amount;
            entry.1 += 1;

            user_ids.push(trade.user_id);
            trades_data.push(trade);
        }

        // Convert blockchain volume map to vector with USD conversion
        let mut blockchain_volumes: Vec<BlockchainVolume> = Vec::new();
        for (blockchain, (volume, count, symbol)) in blockchain_volume_map {
            // Skip USD conversion for now to avoid API timeouts
            // TODO: Implement cached price service for USD conversion
            blockchain_volumes.push(BlockchainVolume {
                blockchain,
                total_volume: volume,
                total_volume_usd: None, // Temporarily disable USD conversion
                native_symbol: symbol,
                transaction_count: count,
            });
        }

        // Batch fetch user info
        let user_info_map = self.fetch_user_info_batch(&users_collection, &user_ids).await?;

        // Convert trades to responses with user info
        for trade in trades_data {
            let user_info = user_info_map.get(&trade.user_id).cloned();
            transactions.push(self.trade_to_response_with_user_info(trade, user_info));
        }

        let success_rate = if transactions.is_empty() {
            0.0
        } else {
            (successful_transactions as f64 / transactions.len() as f64) * 100.0
        };

        Ok(TransactionListResponse {
            transactions,
            total: total_result as i64,
            page,
            per_page,
            success_rate,
            blockchain_volumes,
        })
    }

    async fn get_transaction(
        State(controller): State<Arc<TransactionsController>>,
        headers: HeaderMap,
        Path(id): Path<String>,
    ) -> Response {
        // Verify authentication
        let claims = match controller.verify_auth_header(&headers).await {
            Some(claims) => claims,
            None => {
                warn!("Unauthorized transaction access attempt for ID: {}", id);
                return (StatusCode::UNAUTHORIZED, Json(serde_json::json!({
                    "error": "Unauthorized access",
                    "code": "AUTH_REQUIRED"
                }))).into_response();
            }
        };

        info!("Transaction details requested for ID: {} by admin: {}", id, claims.username);

        if let Ok(object_id) = ObjectId::parse_str(&id) {
            let db = DbService::get_db();
            let collection = db.collection::<Trade>("trades");

            match collection.find_one(doc! { "_id": object_id }, None).await {
                Ok(Some(trade)) => {
                    info!("Transaction found for ID: {} by admin: {}", id, claims.username);
                    (StatusCode::OK, Json(controller.trade_to_response_with_user_info(trade, None))).into_response()
                }
                Ok(None) => {
                    warn!("Transaction not found for ID: {}", id);
                    let error_response = TransactionResponse {
                        id: "".to_string(),
                        user_id: "".to_string(),
                        username: None,
                        first_name: None,
                        last_name: None,
                        bot_type: "".to_string(),
                        transaction_type: "".to_string(),
                        direction: "".to_string(),

                        // Enhanced amount tracking
                        native_token_amount: None,
                        token_amount: None,
                        native_token_symbol: None,

                        // Legacy amount field
                        amount: 0.0,

                        token_symbol: "".to_string(),
                        token_address: "".to_string(),
                        status: "not_found".to_string(),
                        timestamp: 0,
                        blockchain: "".to_string(),
                        gas_fee: None,
                        success: false,
                        hash: None,
                        block_number: None,
                        error_message: Some("Transaction not found".to_string()),

                        // Admin fee information
                        admin_fee_amount: None,
                        admin_fee_percentage: None,
                        admin_fee_status: None,
                        admin_fee_collection_method: None,
                        admin_fee_token_symbol: None,
                        admin_fee_token_address: None,
                        admin_fee_transaction_id: None,
                    };
                    (StatusCode::NOT_FOUND, Json(error_response)).into_response()
                },
                Err(_) => {
                    let error_response = TransactionResponse {
                        id: "".to_string(),
                        user_id: "".to_string(),
                        username: None,
                        first_name: None,
                        last_name: None,
                        bot_type: "".to_string(),
                        transaction_type: "".to_string(),
                        direction: "".to_string(),

                        // Enhanced amount tracking
                        native_token_amount: None,
                        token_amount: None,
                        native_token_symbol: None,

                        // Legacy amount field
                        amount: 0.0,

                        token_symbol: "".to_string(),
                        token_address: "".to_string(),
                        status: "error".to_string(),
                        timestamp: 0,
                        blockchain: "".to_string(),
                        gas_fee: None,
                        success: false,
                        hash: None,
                        block_number: None,
                        error_message: Some("Failed to fetch transaction".to_string()),

                        // Admin fee information
                        admin_fee_amount: None,
                        admin_fee_percentage: None,
                        admin_fee_status: None,
                        admin_fee_collection_method: None,
                        admin_fee_token_symbol: None,
                        admin_fee_token_address: None,
                        admin_fee_transaction_id: None,
                    };
                    (StatusCode::INTERNAL_SERVER_ERROR, Json(error_response)).into_response()
                }
            }
        } else {
            let error_response = TransactionResponse {
                id: "".to_string(),
                user_id: "".to_string(),
                username: None,
                first_name: None,
                last_name: None,
                bot_type: "".to_string(),
                transaction_type: "".to_string(),
                direction: "".to_string(),

                // Enhanced amount tracking
                native_token_amount: None,
                token_amount: None,
                native_token_symbol: None,

                // Legacy amount field
                amount: 0.0,

                token_symbol: "".to_string(),
                token_address: "".to_string(),
                status: "error".to_string(),
                timestamp: 0,
                blockchain: "".to_string(),
                gas_fee: None,
                success: false,
                hash: None,
                block_number: None,
                error_message: Some("Invalid transaction ID".to_string()),

                // Admin fee information
                admin_fee_amount: None,
                admin_fee_percentage: None,
                admin_fee_status: None,
                admin_fee_collection_method: None,
                admin_fee_token_symbol: None,
                admin_fee_token_address: None,
                admin_fee_transaction_id: None,
            };
            (StatusCode::BAD_REQUEST, Json(error_response)).into_response()
        }
    }

    async fn get_user_transactions(
        State(controller): State<Arc<TransactionsController>>,
        headers: HeaderMap,
        Path(user_id): Path<String>,
        Query(params): Query<TransactionQueryParams>,
    ) -> Response {
        // Verify authentication
        let claims = match controller.verify_auth_header(&headers).await {
            Some(claims) => claims,
            None => {
                warn!("Unauthorized user transactions access attempt for user: {}", user_id);
                return (StatusCode::UNAUTHORIZED, Json(serde_json::json!({
                    "error": "Unauthorized access",
                    "code": "AUTH_REQUIRED"
                }))).into_response();
            }
        };

        info!("User transactions requested for user: {} by admin: {}", user_id, claims.username);

        if let Ok(_user_object_id) = ObjectId::parse_str(&user_id) {
            let mut modified_params = params;
            modified_params.user_id = Some(user_id);

            Self::get_transactions(State(controller), headers, Query(modified_params)).await.into_response()
        } else {
            warn!("Invalid user ID format: {}", user_id);
            let error_response = TransactionListResponse {
                transactions: Vec::new(),
                total: 0,
                page: params.page.unwrap_or(1),
                per_page: params.per_page.unwrap_or(20),
                success_rate: 0.0,
                blockchain_volumes: Vec::new(),
            };
            (StatusCode::BAD_REQUEST, Json(error_response)).into_response()
        }
    }

    async fn collect_admin_fee(
        State(controller): State<Arc<TransactionsController>>,
        headers: HeaderMap,
        Path(transaction_id): Path<String>,
    ) -> Response {
        let start_time = SystemTime::now();

        // Verify authentication
        let claims = match controller.verify_auth_header(&headers).await {
            Some(claims) => claims,
            None => {
                warn!("Unauthorized admin fee collection attempt for transaction: {}", transaction_id);
                return (StatusCode::UNAUTHORIZED, Json(serde_json::json!({
                    "error": "Unauthorized access",
                    "code": "AUTH_REQUIRED"
                }))).into_response();
            }
        };

        info!("🚀 High-performance admin fee collection requested for transaction: {} by admin: {}", transaction_id, claims.username);

        // Validate transaction ID format
        if ObjectId::parse_str(&transaction_id).is_err() {
            warn!("Invalid transaction ID format for admin fee collection: {}", transaction_id);
            return (StatusCode::BAD_REQUEST, Json(serde_json::json!({
                "success": false,
                "message": "Invalid transaction ID format",
                "code": "INVALID_ID"
            }))).into_response();
        }

        // Initialize high-performance admin fee collector
        let fee_collector = AdminFeeCollector::new();

        // Execute admin fee collection with timeout
        let collection_result = timeout(
            Duration::from_secs(60), // 60 second timeout for fee collection
            fee_collector.collect_admin_fee(&transaction_id)
        ).await;

        match collection_result {
            Ok(Ok(result)) => {
                let elapsed = start_time.elapsed().unwrap_or_default();

                if result.success {
                    info!("✅ Admin fee collection completed successfully for transaction: {} in {:?}", transaction_id, elapsed);

                    let response_data = serde_json::json!({
                        "success": true,
                        "message": "Admin fee collected successfully",
                        "data": {
                            "transaction_hash": result.transaction_hash,
                            "fee_amount": result.fee_amount,
                            "fee_amount_formatted": result.fee_amount_formatted,
                            "fee_usd_value": result.fee_usd_value,
                            "blockchain": result.blockchain,
                            "native_symbol": result.native_symbol,
                            "collection_method": result.collection_method,
                            "processing_time_ms": elapsed.as_millis()
                        }
                    });

                    if let Some(skipped_reason) = result.skipped_reason {
                        info!("⏭️ Admin fee collection skipped for transaction: {} - {}", transaction_id, skipped_reason);
                        return (StatusCode::OK, Json(serde_json::json!({
                            "success": true,
                            "message": "Admin fee collection skipped",
                            "reason": skipped_reason,
                            "data": response_data["data"]
                        }))).into_response();
                    }

                    (StatusCode::OK, Json(response_data)).into_response()
                } else {
                    error!("❌ Admin fee collection failed for transaction: {} - {}", transaction_id, result.error_message.as_deref().unwrap_or("Unknown error"));
                    (StatusCode::INTERNAL_SERVER_ERROR, Json(serde_json::json!({
                        "success": false,
                        "message": "Admin fee collection failed",
                        "error": result.error_message.unwrap_or_else(|| "Unknown error occurred".to_string()),
                        "code": "COLLECTION_FAILED",
                        "data": {
                            "fee_amount": result.fee_amount,
                            "fee_amount_formatted": result.fee_amount_formatted,
                            "fee_usd_value": result.fee_usd_value,
                            "blockchain": result.blockchain,
                            "native_symbol": result.native_symbol
                        }
                    }))).into_response()
                }
            }
            Ok(Err(e)) => {
                error!("❌ Admin fee collection service error for transaction {}: {}", transaction_id, e);
                (StatusCode::INTERNAL_SERVER_ERROR, Json(serde_json::json!({
                    "success": false,
                    "message": "Admin fee collection service error",
                    "error": e.to_string(),
                    "code": "SERVICE_ERROR"
                }))).into_response()
            }
            Err(_) => {
                error!("⏰ Admin fee collection timeout for transaction: {}", transaction_id);
                (StatusCode::REQUEST_TIMEOUT, Json(serde_json::json!({
                    "success": false,
                    "message": "Admin fee collection timed out",
                    "code": "TIMEOUT"
                }))).into_response()
            }
        }
    }

    fn build_transaction_filter(&self, params: &TransactionQueryParams) -> Result<mongodb::bson::Document, TransactionsError> {
        let mut filter = doc! {};

        // User ID filter
        if let Some(user_id) = &params.user_id {
            if let Ok(object_id) = ObjectId::parse_str(user_id) {
                filter.insert("user_id", object_id);
            } else {
                return Err(TransactionsError::InvalidInput("Invalid user ID format".to_string()));
            }
        }

        // Blockchain filter
        if let Some(blockchain) = &params.blockchain {
            if blockchain.len() > 20 {
                return Err(TransactionsError::InvalidInput("Blockchain name too long".to_string()));
            }
            filter.insert("blockchain", blockchain);
        }

        // Status filter
        if let Some(status) = &params.status {
            let valid_statuses = ["pending", "completed", "failed", "cancelled"];
            if !valid_statuses.contains(&status.as_str()) {
                return Err(TransactionsError::InvalidInput("Invalid status filter".to_string()));
            }
            filter.insert("status", status);
        }

        // Date range filter
        if let (Some(start_date), Some(end_date)) = (&params.start_date, &params.end_date) {
            if let (Ok(start), Ok(end)) = (start_date.parse::<i64>(), end_date.parse::<i64>()) {
                if start > end {
                    return Err(TransactionsError::InvalidInput("Start date must be before end date".to_string()));
                }
                filter.insert("timestamp", doc! {
                    "$gte": start,
                    "$lte": end
                });
            } else {
                return Err(TransactionsError::InvalidInput("Invalid date format".to_string()));
            }
        }

        // Note: Amount range filtering would require additional fields in TransactionQueryParams

        Ok(filter)
    }

    async fn fetch_user_info_batch(&self, users_collection: &mongodb::Collection<User>, user_ids: &[ObjectId]) -> Result<HashMap<ObjectId, UserInfo>, TransactionsError> {
        let mut user_info_map = HashMap::new();

        if user_ids.is_empty() {
            return Ok(user_info_map);
        }

        let filter = doc! { "_id": { "$in": user_ids } };
        let mut cursor = users_collection.find(filter, None).await
            .map_err(TransactionsError::DatabaseError)?;

        while let Some(user) = cursor.try_next().await.map_err(TransactionsError::DatabaseError)? {
            if let Some(id) = user.id {
                let user_info = UserInfo {
                    username: user.username,
                    first_name: user.first_name,
                    last_name: user.last_name,
                };
                user_info_map.insert(id, user_info);
            }
        }

        Ok(user_info_map)
    }

    /// Get USD value using DexScreener API with CoinGecko fallback
    async fn get_usd_value(&self, native_symbol: &str, amount: f64) -> Option<f64> {
        // Map native symbols to blockchain and token addresses
        let (blockchain, token_address) = match native_symbol {
            "SOL" => (Blockchain::SOL, "So11111111111111111111111111111111111111112"),
            "ETH" => (Blockchain::ETH, "******************************************"),
            "BNB" => (Blockchain::BSC, "******************************************"),
            _ => return None,
        };

        // Try DexScreener API first
        let token_info_service = crate::service::token_info_service::TokenInfoService::new();
        match token_info_service.get_token_price_dexscreener(token_address, &blockchain).await {
            Ok(price) => {
                debug!("DexScreener price for {}: ${:.8}", native_symbol, price);
                return Some(amount * price);
            }
            Err(e) => {
                debug!("DexScreener failed for {}: {}", native_symbol, e);
            }
        }

        // Fallback to CoinGecko API
        let coingecko_id = match native_symbol {
            "SOL" => "solana",
            "ETH" => "ethereum",
            "BNB" => "binancecoin",
            _ => return None,
        };

        // Create HTTP client with timeout
        let client = match reqwest::Client::builder()
            .timeout(std::time::Duration::from_secs(10))
            .build()
        {
            Ok(client) => client,
            Err(_) => return None,
        };

        let url = format!(
            "https://api.coingecko.com/api/v3/simple/price?ids={}&vs_currencies=usd",
            coingecko_id
        );

        match client.get(&url)
            .header("User-Agent", "EasyBot/1.0")
            .send()
            .await
        {
            Ok(response) => {
                if let Ok(data) = response.json::<serde_json::Value>().await {
                    if let Some(price) = data
                        .get(coingecko_id)
                        .and_then(|coin| coin.get("usd"))
                        .and_then(|usd| usd.as_f64())
                    {
                        debug!("CoinGecko fallback price for {}: ${:.8}", native_symbol, price);
                        return Some(amount * price);
                    }
                }
            }
            Err(e) => {
                debug!("Failed to fetch price for {}: {}", native_symbol, e);
            }
        }

        None
    }



    /// Format amounts using proper decimals (Jupiter API for Solana, contract decimals for EVM)
    fn format_amounts_with_decimals(&self, trade: &Trade) -> (Option<f64>, Option<f64>) {
        let native_amount = trade.native_token_amount;
        let token_amount = trade.token_amount;

        match trade.blockchain {
            crate::model::blockchain::Blockchain::SOL => {
                // For Solana, SOL always has 9 decimals
                let formatted_native = native_amount.map(|amount| {
                    // Format SOL to 6 decimal places for display (user preference)
                    self.format_solana_amount(amount, 9)
                });

                let formatted_token = token_amount.map(|amount| {
                    // For Solana tokens, we should ideally get decimals from Jupiter API
                    // For now, format to 6 decimal places as per user preference
                    // TODO: Integrate with Jupiter API to get actual token decimals
                    self.format_solana_amount(amount, 6) // Default to 6 decimals for tokens
                });

                (formatted_native, formatted_token)
            }
            crate::model::blockchain::Blockchain::ETH |
            crate::model::blockchain::Blockchain::BASE => {
                // For Ethereum and Base, native token (ETH) has 18 decimals
                let formatted_native = native_amount.map(|amount| {
                    self.format_evm_amount(amount, 18)
                });

                let formatted_token = token_amount.map(|amount| {
                    // For ERC-20 tokens, we should get decimals from contract
                    // For now, format to 6 decimal places as per user preference
                    // TODO: Integrate with contract to get actual token decimals
                    self.format_evm_amount(amount, 18) // Default to 18 decimals for ERC-20
                });

                (formatted_native, formatted_token)
            }
            crate::model::blockchain::Blockchain::BSC => {
                // For BSC, native token (BNB) has 18 decimals
                let formatted_native = native_amount.map(|amount| {
                    self.format_evm_amount(amount, 18)
                });

                let formatted_token = token_amount.map(|amount| {
                    // For BEP-20 tokens, format to 6 decimal places as per user preference
                    // TODO: Integrate with contract to get actual token decimals
                    self.format_evm_amount(amount, 18) // Default to 18 decimals for BEP-20
                });

                (formatted_native, formatted_token)
            }
        }
    }

    /// Format Solana amounts with proper decimal handling
    fn format_solana_amount(&self, amount: f64, _decimals: u8) -> f64 {
        // prefers 5-6 decimal places for all blockchains without approximation
        // Round to 6 decimal places for precise display
        (amount * 1_000_000.0).round() / 1_000_000.0
    }

    /// Format EVM amounts with proper decimal handling
    fn format_evm_amount(&self, amount: f64, _decimals: u8) -> f64 {
        // prefers 5-6 decimal places for all blockchains without approximation
        // Round to 6 decimal places for precise display
        (amount * 1_000_000.0).round() / 1_000_000.0
    }

    fn trade_to_response_with_user_info(&self, trade: Trade, user_info: Option<UserInfo>) -> TransactionResponse {
        // Determine direction from transaction type
        let direction = match trade.trade_type.to_lowercase().as_str() {
            "buy" => "buy".to_string(),
            "sell" => "sell".to_string(),
            _ => trade.trade_type.to_lowercase(),
        };

        // Use user info from trade if available, otherwise fall back to fetched user_info
        let (username, first_name, last_name) = if trade.user_first_name.is_some() || trade.user_username.is_some() {
            // Use user info stored in the trade
            (trade.user_username.clone(), trade.user_first_name.clone(), None)
        } else if let Some(info) = user_info {
            // Fall back to fetched user info for older trades
            (info.username, Some(info.first_name), info.last_name)
        } else {
            (None, None, None)
        };

        // Determine native token symbol based on blockchain
        let native_symbol = match trade.blockchain {
            crate::model::blockchain::Blockchain::ETH => "ETH",
            crate::model::blockchain::Blockchain::BSC => "BNB",
            crate::model::blockchain::Blockchain::BASE => "ETH",
            crate::model::blockchain::Blockchain::SOL => "SOL",
        };

        // Enhanced amount formatting using Jupiter decimals for Solana
        let (formatted_native_amount, formatted_token_amount) = self.format_amounts_with_decimals(&trade);

        // Intelligent amount display based on transaction direction
        let legacy_amount = if direction == "buy" {
            // For buy: show native token amount spent (SOL, ETH, BNB)
            formatted_native_amount.unwrap_or_else(|| {
                trade.amount_out.unwrap_or(0.0)
            })
        } else {
            // For sell: show token amount sold
            formatted_token_amount.unwrap_or_else(|| {
                trade.amount_out.unwrap_or(0.0)
            })
        };

        TransactionResponse {
            id: trade.id.unwrap_or_else(|| ObjectId::new()).to_hex(),
            user_id: trade.user_id.to_hex(),
            username,
            first_name,
            last_name,
            bot_type: format!("{:?}", trade.blockchain),
            transaction_type: trade.trade_type.clone(),
            direction,

            // Enhanced amount tracking with proper formatting
            native_token_amount: formatted_native_amount,
            token_amount: formatted_token_amount,
            native_token_symbol: trade.native_token_symbol.or_else(|| Some(native_symbol.to_string())),

            // Legacy amount field
            amount: legacy_amount,

            token_symbol: trade.token_symbol.clone(),
            token_address: trade.token_address.clone(),
            status: trade.status.clone().unwrap_or_else(|| "pending".to_string()),
            timestamp: trade.timestamp as u64,
            blockchain: format!("{:?}", trade.blockchain),
            gas_fee: trade.gas_fee,
            success: trade.status.as_deref() == Some("completed"),
            hash: trade.hash,
            block_number: trade.block_number,
            error_message: trade.error_message,

            // Admin fee information
            admin_fee_amount: trade.admin_fee_amount,
            admin_fee_percentage: trade.admin_fee_percentage,
            admin_fee_status: trade.admin_fee_status,
            admin_fee_collection_method: trade.admin_fee_collection_method,
            admin_fee_token_symbol: trade.admin_fee_token_symbol,
            admin_fee_token_address: trade.admin_fee_token_address,
            admin_fee_transaction_id: trade.admin_fee_transaction_id.map(|id| id.to_hex()),
        }
    }

    fn trade_to_response(trade: Trade) -> TransactionResponse {
        // Determine direction from transaction type
        let direction = match trade.trade_type.to_lowercase().as_str() {
            "buy" => "buy".to_string(),
            "sell" => "sell".to_string(),
            _ => trade.trade_type.to_lowercase(),
        };

        // Determine native token symbol based on blockchain
        let native_symbol = match trade.blockchain {
            crate::model::blockchain::Blockchain::ETH => "ETH",
            crate::model::blockchain::Blockchain::BSC => "BNB",
            crate::model::blockchain::Blockchain::BASE => "ETH",
            crate::model::blockchain::Blockchain::SOL => "SOL",
        };

        // For backward compatibility, use the appropriate amount based on transaction type
        let legacy_amount = if direction == "buy" {
            trade.native_token_amount.or(trade.amount_out).unwrap_or(0.0)
        } else {
            trade.token_amount.or(trade.amount_out).unwrap_or(0.0)
        };

        TransactionResponse {
            id: trade.id.unwrap_or_else(|| ObjectId::new()).to_hex(),
            user_id: trade.user_id.to_hex(),
            username: trade.user_username.clone(),
            first_name: trade.user_first_name.clone(),
            last_name: None, // Not stored in trade for now
            bot_type: format!("{:?}", trade.blockchain),
            transaction_type: trade.trade_type.clone(),
            direction,

            // Enhanced amount tracking
            native_token_amount: trade.native_token_amount,
            token_amount: trade.token_amount,
            native_token_symbol: trade.native_token_symbol.or_else(|| Some(native_symbol.to_string())),

            // Legacy amount field
            amount: legacy_amount,

            token_symbol: trade.token_symbol.clone(),
            token_address: trade.token_address.clone(),
            status: trade.status.clone().unwrap_or_else(|| "pending".to_string()),
            timestamp: trade.timestamp as u64,
            blockchain: format!("{:?}", trade.blockchain),
            gas_fee: trade.gas_fee,
            success: trade.status.as_deref() == Some("completed"),
            hash: trade.hash,
            block_number: trade.block_number,
            error_message: trade.error_message,

            // Admin fee information
            admin_fee_amount: trade.admin_fee_amount,
            admin_fee_percentage: trade.admin_fee_percentage,
            admin_fee_status: trade.admin_fee_status,
            admin_fee_collection_method: trade.admin_fee_collection_method,
            admin_fee_token_symbol: trade.admin_fee_token_symbol,
            admin_fee_token_address: trade.admin_fee_token_address,
            admin_fee_transaction_id: trade.admin_fee_transaction_id.map(|id| id.to_hex()),
        }
    }

    async fn get_analytics(
        State(controller): State<Arc<TransactionsController>>,
        headers: HeaderMap,
        Query(params): Query<TransactionQueryParams>,
    ) -> impl IntoResponse {
        // Verify authentication
        let claims = match controller.verify_auth_header(&headers).await {
            Some(claims) => claims,
            None => {
                warn!("Unauthorized transaction analytics access attempt");
                return (StatusCode::UNAUTHORIZED, Json(serde_json::json!({
                    "error": "Unauthorized access",
                    "code": "AUTH_REQUIRED"
                }))).into_response();
            }
        };

        info!("Transaction analytics requested by admin: {}", claims.username);

        let db = DbService::get_db();
        let collection = db.collection::<Trade>("trades");

        // Build filter for analytics
        let mut filter = doc! {};
        if let Some(blockchain) = params.blockchain {
            filter.insert("blockchain", blockchain);
        }
        if let (Some(start_date), Some(end_date)) = (params.start_date, params.end_date) {
            if let (Ok(start), Ok(end)) = (start_date.parse::<i64>(), end_date.parse::<i64>()) {
                filter.insert("timestamp", doc! {
                    "$gte": start,
                    "$lte": end
                });
            }
        }

        // Aggregate analytics data
        let pipeline = vec![
            doc! { "$match": filter },
            doc! {
                "$group": {
                    "_id": null,
                    "total_transactions": { "$sum": 1 },
                    "total_volume": { "$sum": "$amount_out" },
                    "successful_transactions": {
                        "$sum": {
                            "$cond": [
                                { "$eq": ["$status", "completed"] },
                                1,
                                0
                            ]
                        }
                    },
                    "total_gas_fees": { "$sum": "$gas_fee" },
                    "gas_fee_count": {
                        "$sum": {
                            "$cond": [
                                { "$ne": ["$gas_fee", null] },
                                1,
                                0
                            ]
                        }
                    }
                }
            }
        ];

        match collection.aggregate(pipeline, None).await {
            Ok(mut cursor) => {
                if let Ok(Some(result)) = cursor.try_next().await {
                    let total_transactions = result.get_i64("total_transactions").unwrap_or(0);
                    let successful_transactions = result.get_i64("successful_transactions").unwrap_or(0);
                    let total_gas_fees = result.get_f64("total_gas_fees").unwrap_or(0.0);
                    let gas_fee_count = result.get_i64("gas_fee_count").unwrap_or(0);

                    let success_rate = if total_transactions > 0 {
                        (successful_transactions as f64 / total_transactions as f64) * 100.0
                    } else {
                        0.0
                    };

                    let average_gas_fee = if gas_fee_count > 0 {
                        total_gas_fees / gas_fee_count as f64
                    } else {
                        0.0
                    };

                    let analytics = TransactionAnalytics {
                        total_transactions,
                        total_volume: result.get_f64("total_volume").unwrap_or(0.0),
                        success_rate,
                        average_gas_fee,
                        transactions_by_blockchain: HashMap::new(), // Would need additional aggregation
                        transactions_by_type: HashMap::new(), // Would need additional aggregation
                        volume_by_blockchain: HashMap::new(), // Would need additional aggregation
                        hourly_stats: Vec::new(), // Would need additional aggregation
                        top_tokens: Vec::new(), // Would need additional aggregation
                    };

                    (StatusCode::OK, Json(analytics)).into_response()
                } else {
                    (StatusCode::OK, Json(TransactionAnalytics {
                        total_transactions: 0,
                        total_volume: 0.0,
                        success_rate: 0.0,
                        average_gas_fee: 0.0,
                        transactions_by_blockchain: HashMap::new(),
                        transactions_by_type: HashMap::new(),
                        volume_by_blockchain: HashMap::new(),
                        hourly_stats: Vec::new(),
                        top_tokens: Vec::new(),
                    })).into_response()
                }
            }
            Err(_) => {
                let error_analytics = TransactionAnalytics {
                    total_transactions: 0,
                    total_volume: 0.0,
                    success_rate: 0.0,
                    average_gas_fee: 0.0,
                    transactions_by_blockchain: HashMap::new(),
                    transactions_by_type: HashMap::new(),
                    volume_by_blockchain: HashMap::new(),
                    hourly_stats: Vec::new(),
                    top_tokens: Vec::new(),
                };
                (StatusCode::INTERNAL_SERVER_ERROR, Json(error_analytics)).into_response()
            }
        }
    }

    async fn get_fee_statistics(
        State(controller): State<Arc<TransactionsController>>,
        headers: HeaderMap,
    ) -> impl IntoResponse {
        // Verify authentication
        let claims = match controller.verify_auth_header(&headers).await {
            Some(claims) => claims,
            None => {
                warn!("Unauthorized fee statistics access attempt");
                return (StatusCode::UNAUTHORIZED, Json(serde_json::json!({
                    "error": "Unauthorized access",
                    "code": "AUTH_REQUIRED"
                }))).into_response();
            }
        };

        info!("Fee statistics requested by admin: {}", claims.username);

        match controller.calculate_fee_statistics().await {
            Ok(statistics) => (StatusCode::OK, Json(statistics)).into_response(),
            Err(e) => {
                error!("Failed to calculate fee statistics: {:?}", e);
                (StatusCode::INTERNAL_SERVER_ERROR, Json(serde_json::json!({
                    "error": "Failed to calculate fee statistics",
                    "code": "FEE_STATISTICS_ERROR"
                }))).into_response()
            }
        }
    }

    async fn export_transactions(
        State(controller): State<Arc<TransactionsController>>,
        headers: HeaderMap,
        Query(params): Query<TransactionQueryParams>,
    ) -> impl IntoResponse {
        // Verify authentication
        let claims = match controller.verify_auth_header(&headers).await {
            Some(claims) => claims,
            None => {
                warn!("Unauthorized transaction export access attempt");
                return (StatusCode::UNAUTHORIZED, Json(serde_json::json!({
                    "error": "Unauthorized access",
                    "code": "AUTH_REQUIRED"
                }))).into_response();
            }
        };

        info!("Transaction export requested by admin: {}", claims.username);

        // This would implement CSV/JSON export functionality
        // For now, return a simple message
        (StatusCode::OK, Json(serde_json::json!({
            "message": "Export functionality not yet implemented",
            "requested_by": claims.username,
            "params": params
        }))).into_response()
    }

    /// Calculate fee statistics for each blockchain
    async fn calculate_fee_statistics(&self) -> Result<FeeStatisticsResponse, TransactionsError> {
        let db = DbService::get_db();
        let collection = db.collection::<crate::model::AdminFeeTransaction>("admin_fee_transactions");

        let now = chrono::Utc::now();
        let today_start = now.date_naive().and_hms_opt(0, 0, 0).unwrap().and_utc().timestamp();
        let week_start = (now - chrono::Duration::days(7)).timestamp();
        let month_start = (now - chrono::Duration::days(30)).timestamp();

        // Aggregate fee data by blockchain and time periods
        let pipeline = vec![
            doc! {
                "$match": {
                    "status": "completed"
                }
            },
            doc! {
                "$group": {
                    "_id": "$blockchain",
                    "total_fees": { "$sum": "$fee_amount" },
                    "today_fees": {
                        "$sum": {
                            "$cond": [
                                { "$gte": ["$completed_at", today_start] },
                                "$fee_amount",
                                0
                            ]
                        }
                    },
                    "weekly_fees": {
                        "$sum": {
                            "$cond": [
                                { "$gte": ["$completed_at", week_start] },
                                "$fee_amount",
                                0
                            ]
                        }
                    },
                    "monthly_fees": {
                        "$sum": {
                            "$cond": [
                                { "$gte": ["$completed_at", month_start] },
                                "$fee_amount",
                                0
                            ]
                        }
                    },
                    "fee_token_symbol": { "$first": "$fee_token_symbol" }
                }
            }
        ];

        let mut cursor = collection.aggregate(pipeline, None).await
            .map_err(|e| TransactionsError::ValidationError(format!("Failed to aggregate fee data: {}", e)))?;

        let mut fee_statistics = Vec::new();
        let mut total_fees_usd = 0.0;

        while let Ok(Some(doc)) = cursor.try_next().await {
            let blockchain = doc.get_str("_id").unwrap_or("Unknown").to_string();
            let total_fees = doc.get_f64("total_fees").unwrap_or(0.0);
            let today_fees = doc.get_f64("today_fees").unwrap_or(0.0);
            let weekly_fees = doc.get_f64("weekly_fees").unwrap_or(0.0);
            let monthly_fees = doc.get_f64("monthly_fees").unwrap_or(0.0);
            let native_symbol = doc.get_str("fee_token_symbol").unwrap_or("Unknown").to_string();

            // Convert to USD values
            let total_fees_usd_val = self.get_usd_value(&native_symbol, total_fees).await.unwrap_or(0.0);
            let today_fees_usd = self.get_usd_value(&native_symbol, today_fees).await.unwrap_or(0.0);
            let weekly_fees_usd = self.get_usd_value(&native_symbol, weekly_fees).await.unwrap_or(0.0);
            let monthly_fees_usd = self.get_usd_value(&native_symbol, monthly_fees).await.unwrap_or(0.0);

            total_fees_usd += total_fees_usd_val;

            fee_statistics.push(FeeStatistics {
                blockchain,
                native_symbol,
                today_fees_native: today_fees,
                today_fees_usd,
                weekly_fees_native: weekly_fees,
                weekly_fees_usd,
                monthly_fees_native: monthly_fees,
                monthly_fees_usd,
                total_fees_native: total_fees,
                total_fees_usd: total_fees_usd_val,
            });
        }

        Ok(FeeStatisticsResponse {
            fee_statistics,
            total_fees_usd,
        })
    }
}

impl Clone for TransactionsController {
    fn clone(&self) -> Self {
        Self {
            config: self.config.clone(),
            jwt_secret: self.jwt_secret.clone(),
        }
    }
}
