import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { lazy, Suspense } from 'react';
import Login from './pages/Login';
import AdminLayout from './components/admin/AdminLayout';
import LoadingScreen from './components/ui/LoadingScreen';
import LoadingBar from './components/ui/LoadingBar';
import { useAdminStore } from './store/adminStore';
import './App.css';

// Lazy load admin pages
const EasyBoard = lazy(() => import('./pages/admin/EasyBoard'));
const AdminUsers = lazy(() => import('./pages/admin/Users'));
const UserAnalytics = lazy(() => import('./pages/admin/UserAnalytics'));
const AdminTransactions = lazy(() => import('./pages/admin/Transactions'));
const AdminSettings = lazy(() => import('./pages/admin/Settings'));
const AdminManagement = lazy(() => import('./pages/admin/AdminManagement'));


// Auth guard component
const ProtectedRoute = ({ children }: { children: JSX.Element }) => {
  const isAuthenticated = useAdminStore((state) => state.isAuthenticated);

  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }
  return children;
};

// Admin guard component
const AdminRoute = ({ children }: { children: JSX.Element }) => {
  const isAuthenticated = useAdminStore((state) => state.isAuthenticated);
  const currentUser = useAdminStore((state) => state.currentUser);

  if (!isAuthenticated || !currentUser) {
    return <Navigate to="/login" replace />;
  }

  // Check if user has admin privileges
  if (currentUser.role !== 'Admin' && currentUser.role !== 'SuperAdmin') {
    return <Navigate to="/login" replace />;
  }

  return children;
};

function App() {
  return (
    <Router>
      <LoadingBar />
      <Suspense fallback={<LoadingScreen />}>
        <Routes>
          {/* Public routes */}
          <Route path="/" element={<Login />} />
          <Route path="/login" element={<Login />} />

          {/* Admin dashboard routes */}
          <Route
            path="/dashboard"
            element={
              <AdminRoute>
                <AdminLayout />
              </AdminRoute>
            }
          >
            <Route index element={<EasyBoard />} />
            <Route path="transactions" element={<AdminTransactions />} />
            <Route path="users" element={<AdminUsers />} />
            <Route path="settings" element={<AdminSettings />} />
            <Route path="admins" element={<AdminManagement />} />
          </Route>

          {/* Fallback route */}
          <Route path="*" element={<Navigate to="/login" replace />} />
        </Routes>
      </Suspense>
    </Router>
  );
}

export default App;
