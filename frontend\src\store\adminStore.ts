import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';

// Types
export interface AdminUser {
  id: string;
  username: string;
  email: string;
  role: 'Admin' | 'SuperAdmin';
  created_at: number;
  last_login?: number;
}

export interface Bot {
  id: string;
  name: string;
  description: string;
  bot_type: 'BSC' | 'Ethereum' | 'Solana' | 'Base';
  status: 'Active' | 'Inactive' | 'Paused' | 'Error' | 'Maintenance';
  config: BotConfig;
  total_users: number;
  active_users: number;
  total_transactions: number;
  total_volume: number;
  success_rate: number;
  average_response_time: number;
  uptime_percentage: number;
  last_error?: string;
  version: string;
  created_at: number;
  updated_at: number;
  last_restart?: number;
}

export interface BotConfig {
  max_slippage: number;
  gas_price?: number;
  priority_fee?: number;
  auto_approve: boolean;
  max_transaction_amount: number;
  min_liquidity: number;
  honeypot_check: boolean;
  rugpull_check: boolean;
  custom_settings: Record<string, string>;
}

export interface Transaction {
  id: string;
  user_id: string;
  username?: string;
  bot_type: string;
  transaction_type: string;
  amount: number;
  token_symbol: string;
  token_address: string;
  status: string;
  timestamp: number;
  blockchain: string;
  gas_fee?: number;
  success: boolean;
  hash?: string;
  block_number?: number;
  error_message?: string;
}

export interface User {
  id: string;
  chat_id: number;
  first_name: string;
  last_name?: string;
  username?: string;
  current_blockchain: 'bsc' | 'sol' | 'eth' | 'base';
  created_at: number;
  last_seen: number;
}

export interface DashboardAnalytics {
  total_users: number;
  active_users_24h: number;
  total_transactions: number;
  transactions_24h: number;
  total_volume: number;
  volume_24h: number;
  total_fees_collected: number;
  fees_collected_24h: number;
  success_rate: number;
  average_response_time: number;
  blockchain_distribution: Record<string, number>;
  hourly_stats: HourlyStats[];
}

export interface HourlyStats {
  hour: number;
  transactions: number;
  volume: number;
  users: number;
  success_rate: number;
}

export interface SystemHealth {
  uptime_seconds: number;
  cpu_usage: number;
  memory_usage: number;
  disk_usage: number;
  active_connections: number;
  api_requests_per_minute: number;
  database_connections: number;
  blockchain_connections: Record<string, boolean>;
  last_backup?: number;
}

export interface SystemAlert {
  id: string;
  severity: 'Info' | 'Warning' | 'Error' | 'Critical';
  title: string;
  message: string;
  timestamp: number;
  resolved: boolean;
  component: string;
}

// Store interface
interface AdminStore {
  // Auth state
  isAuthenticated: boolean;
  currentUser: AdminUser | null;
  token: string | null;

  // Dashboard data
  analytics: DashboardAnalytics | null;
  systemHealth: SystemHealth | null;
  alerts: SystemAlert[];

  // Bots data
  bots: Bot[];
  selectedBot: Bot | null;
  botsLoading: boolean;

  // Transactions data
  transactions: Transaction[];
  transactionsLoading: boolean;
  transactionsPagination: {
    page: number;
    per_page: number;
    total: number;
  };

  // Users data
  users: User[];
  selectedUser: User | null;
  usersLoading: boolean;
  usersPagination: {
    page: number;
    per_page: number;
    total: number;
  };

  // Admins data
  admins: AdminUser[];
  adminsLoading: boolean;

  // Settings
  settings: Record<string, any>;
  settingsLoading: boolean;

  // Loading states
  dashboardLoading: boolean;

  // Actions
  login: (username: string, password: string) => Promise<boolean>;
  logout: () => void;
  setCurrentUser: (user: AdminUser) => void;
  setToken: (token: string) => void;

  // Dashboard actions
  loadDashboardData: () => Promise<void>;
  setAnalytics: (analytics: DashboardAnalytics) => void;
  setSystemHealth: (health: SystemHealth) => void;
  addAlert: (alert: SystemAlert) => void;
  resolveAlert: (alertId: string) => void;

  // Bots actions
  loadBots: (page?: number, filters?: any) => Promise<void>;
  createBot: (bot: Partial<Bot>) => Promise<boolean>;
  updateBot: (id: string, updates: Partial<Bot>) => Promise<boolean>;
  deleteBot: (id: string) => Promise<boolean>;
  startBot: (id: string) => Promise<boolean>;
  stopBot: (id: string) => Promise<boolean>;
  restartBot: (id: string) => Promise<boolean>;
  setSelectedBot: (bot: Bot | null) => void;

  // Transactions actions
  loadTransactions: (page?: number, filters?: any) => Promise<void>;

  // Users actions
  loadUsers: (page?: number, filters?: any) => Promise<void>;
  setSelectedUser: (user: User | null) => void;

  // Admins actions
  loadAdmins: () => Promise<void>;
  createAdmin: (admin: Partial<AdminUser>) => Promise<boolean>;
  updateAdmin: (id: string, updates: Partial<AdminUser>) => Promise<boolean>;
  deleteAdmin: (id: string) => Promise<boolean>;

  // Settings actions
  loadSettings: () => Promise<void>;
  updateSettings: (settings: Record<string, any>) => Promise<boolean>;
}

// Create the store
export const useAdminStore = create<AdminStore>()(
  devtools(
    persist(
      (set, get) => ({
        // Initial state
        isAuthenticated: false,
        currentUser: null,
        token: null,

        analytics: null,
        systemHealth: null,
        alerts: [],

        bots: [],
        selectedBot: null,
        botsLoading: false,

        transactions: [],
        transactionsLoading: false,
        transactionsPagination: {
          page: 1,
          per_page: 10,
          total: 0,
        },

        users: [],
        selectedUser: null,
        usersLoading: false,
        usersPagination: {
          page: 1,
          per_page: 10,
          total: 0,
        },

        admins: [],
        adminsLoading: false,

        settings: {},
        settingsLoading: false,

        dashboardLoading: false,

        // Auth actions
        login: async (username: string, password: string) => {
          try {
            const { adminApi } = await import('../services/adminApi');
            const response = await adminApi.login(username, password);

            // Store token in localStorage
            localStorage.setItem('admin_token', response.token);

            set({
              isAuthenticated: true,
              currentUser: response.user,
              token: response.token,
            });
            return true;
          } catch (error) {
            console.error('Login error:', error);
            return false;
          }
        },

        logout: () => {
          localStorage.removeItem('admin_token');
          set({
            isAuthenticated: false,
            currentUser: null,
            token: null,
            analytics: null,
            systemHealth: null,
            alerts: [],
            bots: [],
            selectedBot: null,
            transactions: [],
            users: [],
            selectedUser: null,
            admins: [],
            settings: {},
          });
        },

        setCurrentUser: (user: AdminUser) => {
          set({ currentUser: user });
        },

        setToken: (token: string) => {
          set({ token });
        },

        // Dashboard actions
        loadDashboardData: async () => {
          set({ dashboardLoading: true });
          try {
            const { adminApi } = await import('../services/adminApi');

            // Load analytics, system health, and alerts in parallel
            const [analytics, systemHealth, alerts] = await Promise.all([
              adminApi.getDashboardAnalytics(),
              adminApi.getSystemHealth(),
              adminApi.getSystemAlerts(),
            ]);

            set({
              analytics,
              systemHealth,
              alerts,
              dashboardLoading: false
            });
          } catch (error) {
            console.error('Dashboard loading error:', error);
            set({ dashboardLoading: false });
          }
        },

        setAnalytics: (analytics: DashboardAnalytics) => {
          set({ analytics });
        },

        setSystemHealth: (health: SystemHealth) => {
          set({ systemHealth: health });
        },

        addAlert: (alert: SystemAlert) => {
          set((state) => ({
            alerts: [alert, ...state.alerts],
          }));
        },

        resolveAlert: (alertId: string) => {
          set((state) => ({
            alerts: state.alerts.map((alert) =>
              alert.id === alertId ? { ...alert, resolved: true } : alert
            ),
          }));
        },

        // Bots actions
        loadBots: async (page = 1, filters = {}) => {
          set({ botsLoading: true });
          try {
            const { adminApi } = await import('../services/adminApi');
            const response = await adminApi.getBots({ page, per_page: 10, ...filters });
            set({
              bots: response.bots,
              botsLoading: false
            });
          } catch (error) {
            console.error('Bots loading error:', error);
            set({ botsLoading: false });
          }
        },

        createBot: async (bot: Partial<Bot>) => {
          try {
            const { adminApi } = await import('../services/adminApi');
            const newBot = await adminApi.createBot({
              name: bot.name!,
              description: bot.description!,
              bot_type: bot.bot_type!,
              config: bot.config,
            });

            set((state) => ({
              bots: [newBot, ...state.bots],
            }));
            return true;
          } catch (error) {
            console.error('Bot creation error:', error);
            return false;
          }
        },

        updateBot: async (id: string, updates: Partial<Bot>) => {
          try {
            const { adminApi } = await import('../services/adminApi');
            const updatedBot = await adminApi.updateBot(id, updates);

            set((state) => ({
              bots: state.bots.map((bot) =>
                bot.id === id ? updatedBot : bot
              ),
            }));
            return true;
          } catch (error) {
            console.error('Bot update error:', error);
            return false;
          }
        },

        deleteBot: async (id: string) => {
          try {
            const { adminApi } = await import('../services/adminApi');
            await adminApi.deleteBot(id);

            set((state) => ({
              bots: state.bots.filter((bot) => bot.id !== id),
            }));
            return true;
          } catch (error) {
            console.error('Bot deletion error:', error);
            return false;
          }
        },

        startBot: async (id: string) => {
          try {
            const { adminApi } = await import('../services/adminApi');
            await adminApi.startBot(id);

            set((state) => ({
              bots: state.bots.map((bot) =>
                bot.id === id ? { ...bot, status: 'Active' as const } : bot
              ),
            }));
            return true;
          } catch (error) {
            console.error('Bot start error:', error);
            return false;
          }
        },

        stopBot: async (id: string) => {
          try {
            const { adminApi } = await import('../services/adminApi');
            await adminApi.stopBot(id);

            set((state) => ({
              bots: state.bots.map((bot) =>
                bot.id === id ? { ...bot, status: 'Inactive' as const } : bot
              ),
            }));
            return true;
          } catch (error) {
            console.error('Bot stop error:', error);
            return false;
          }
        },

        restartBot: async (id: string) => {
          try {
            const { adminApi } = await import('../services/adminApi');
            await adminApi.restartBot(id);

            set((state) => ({
              bots: state.bots.map((bot) =>
                bot.id === id ? { ...bot, status: 'Active' as const } : bot
              ),
            }));
            return true;
          } catch (error) {
            console.error('Bot restart error:', error);
            return false;
          }
        },

        setSelectedBot: (bot) => set({ selectedBot: bot }),

        // Transactions actions
        loadTransactions: async (page = 1, filters = {}) => {
          set({ transactionsLoading: true });
          try {
            const { adminApi } = await import('../services/adminApi');
            const response = await adminApi.getTransactions({ page, per_page: 20, ...filters });
            set({
              transactions: response.transactions,
              transactionsPagination: {
                page: response.page,
                per_page: response.per_page,
                total: response.total,
              },
              transactionsLoading: false
            });
          } catch (error) {
            console.error('Transactions loading error:', error);
            set({ transactionsLoading: false });
          }
        },

        // Users actions
        loadUsers: async (page = 1, filters = {}) => {
          set({ usersLoading: true });
          try {
            const { adminApi } = await import('../services/adminApi');
            const response = await adminApi.getUsers({ page, per_page: 20, ...filters });
            set({
              users: response.users,
              usersPagination: {
                page: response.page,
                per_page: response.per_page,
                total: response.total,
              },
              usersLoading: false
            });
          } catch (error) {
            console.error('Users loading error:', error);
            set({ usersLoading: false });
          }
        },

        setSelectedUser: (user) => set({ selectedUser: user }),

        // Admins actions
        loadAdmins: async () => {
          set({ adminsLoading: true });
          try {
            const { adminApi } = await import('../services/adminApi');
            const admins = await adminApi.getAdmins();
            set({ admins, adminsLoading: false });
          } catch (error) {
            console.error('Admins loading error:', error);
            set({ adminsLoading: false });
          }
        },

        createAdmin: async (admin: Partial<AdminUser>) => {
          try {
            const { adminApi } = await import('../services/adminApi');
            const newAdmin = await adminApi.createAdmin({
              username: admin.username!,
              email: admin.email!,
              password: 'temp_password', // This should be handled properly
              role: admin.role!,
            });

            set((state) => ({
              admins: [newAdmin, ...state.admins],
            }));
            return true;
          } catch (error) {
            console.error('Admin creation error:', error);
            return false;
          }
        },

        updateAdmin: async (id: string, updates: Partial<AdminUser>) => {
          try {
            const { adminApi } = await import('../services/adminApi');
            const updatedAdmin = await adminApi.updateAdmin(id, updates);

            set((state) => ({
              admins: state.admins.map((admin) =>
                admin.id === id ? updatedAdmin : admin
              ),
            }));
            return true;
          } catch (error) {
            console.error('Admin update error:', error);
            return false;
          }
        },

        deleteAdmin: async (id: string) => {
          try {
            const { adminApi } = await import('../services/adminApi');
            await adminApi.deleteAdmin(id);

            set((state) => ({
              admins: state.admins.filter((admin) => admin.id !== id),
            }));
            return true;
          } catch (error) {
            console.error('Admin deletion error:', error);
            return false;
          }
        },

        // Settings actions
        loadSettings: async () => {
          set({ settingsLoading: true });
          try {
            const { adminApi } = await import('../services/adminApi');
            const settings = await adminApi.getSettings();
            set({ settings, settingsLoading: false });
          } catch (error) {
            console.error('Settings loading error:', error);
            set({ settingsLoading: false });
          }
        },

        updateSettings: async (newSettings: Record<string, any>) => {
          try {
            const { adminApi } = await import('../services/adminApi');
            const updatedSettings = await adminApi.updateSettings(newSettings);
            set({ settings: updatedSettings });
            return true;
          } catch (error) {
            console.error('Settings update error:', error);
            return false;
          }
        },
      }),
      {
        name: 'admin-store',
        partialize: (state) => ({
          isAuthenticated: state.isAuthenticated,
          currentUser: state.currentUser,
          token: state.token,
        }),
      }
    ),
    {
      name: 'admin-store',
    }
  )
);
