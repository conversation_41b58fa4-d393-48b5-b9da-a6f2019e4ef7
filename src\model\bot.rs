use serde::{Deserialize, Serialize};
use mongodb::bson::oid::ObjectId;
use std::time::{SystemTime, UNIX_EPOCH};
use std::collections::HashMap;
use crate::config::defaults;

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum BotType {
    BSC,
    Ethereum,
    Solana,
    Base,
}

impl Default for BotType {
    fn default() -> Self {
        BotType::BSC
    }
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum BotStatus {
    Active,
    Inactive,
    Paused,
    Error,
    Maintenance,
}

impl Default for BotStatus {
    fn default() -> Self {
        BotStatus::Inactive
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BotConfig {
    pub max_slippage: f64,
    pub gas_price: Option<f64>,
    pub priority_fee: Option<f64>,
    pub auto_approve: bool,
    pub max_transaction_amount: f64,
    pub min_liquidity: f64,
    pub honeypot_check: bool,
    pub rugpull_check: bool,
    pub custom_settings: HashMap<String, String>,
}

impl Default for BotConfig {
    fn default() -> Self {
        Self {
            max_slippage: 10.0,
            gas_price: None,
            priority_fee: None,
            auto_approve: false,
            max_transaction_amount: 1000.0,
            min_liquidity: 10000.0,
            honeypot_check: true,
            rugpull_check: true,
            custom_settings: HashMap::new(),
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Bot {
    #[serde(rename = "_id", skip_serializing_if = "Option::is_none")]
    pub id: Option<ObjectId>,
    pub name: String,
    pub description: String,
    pub bot_type: BotType,
    pub status: BotStatus,
    pub config: BotConfig,
    pub total_users: i64,
    pub active_users: i64,
    pub total_transactions: i64,
    pub total_volume: f64,
    pub success_rate: f64,
    pub average_response_time: f64,
    pub uptime_percentage: f64,
    pub last_error: Option<String>,
    pub version: String,
    #[serde(default = "default_timestamp")]
    pub created_at: u64,
    #[serde(default = "default_timestamp")]
    pub updated_at: u64,
    pub last_restart: Option<u64>,
}

impl Bot {
    pub fn new(name: String, description: String, bot_type: BotType) -> Self {
        let now = default_timestamp();
        Self {
            id: None,
            name,
            description,
            bot_type,
            status: BotStatus::Inactive,
            config: BotConfig::default(),
            total_users: 0,
            active_users: 0,
            total_transactions: 0,
            total_volume: 0.0,
            success_rate: 0.0,
            average_response_time: 0.0,
            uptime_percentage: 0.0,
            last_error: None,
            version: "1.0.0".to_string(),
            created_at: now,
            updated_at: now,
            last_restart: None,
        }
    }

    pub fn update_stats(&mut self, transactions: i64, volume: f64, success_rate: f64, response_time: f64) {
        self.total_transactions += transactions;
        self.total_volume += volume;
        self.success_rate = success_rate;
        self.average_response_time = response_time;
        self.updated_at = default_timestamp();
    }

    pub fn set_status(&mut self, status: BotStatus) {
        if status == BotStatus::Active {
            self.last_restart = Some(default_timestamp());
        }
        self.status = status;
        self.updated_at = default_timestamp();
    }

    pub fn set_error(&mut self, error: String) {
        self.status = BotStatus::Error;
        self.last_error = Some(error);
        self.updated_at = default_timestamp();
    }

    pub fn clear_error(&mut self) {
        self.last_error = None;
        self.updated_at = default_timestamp();
    }
}

fn default_timestamp() -> u64 {
    SystemTime::now()
        .duration_since(UNIX_EPOCH)
        .unwrap_or_default()
        .as_secs()
}
