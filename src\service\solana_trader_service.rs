use std::str::FromStr;
use std::sync::Arc;
use std::time::Duration;
use std::collections::HashMap;
use std::num::NonZero;
use std::thread;

use solana_client::nonblocking::rpc_client::RpcClient;
use solana_client::rpc_config::RpcSendTransactionConfig;
use solana_client::rpc_request::TokenAccountsFilter;
use solana_sdk::program_pack::Pack;
use solana_sdk::commitment_config::{CommitmentConfig, CommitmentLevel};
use solana_sdk::pubkey::Pubkey;
use solana_sdk::signature::{Keypair, Signature};
use solana_sdk::signer::Signer;
use solana_sdk::transaction::VersionedTransaction;
use tokio::sync::{Mutex, RwLock};
use reqwest::Client;
use serde::{Deserialize, Serialize};
use serde_json::{json, Value};
use thiserror::Error;
use tokio::process::Command;
use tracing::{info, warn, error};

use crate::model::{BotError, AdminFeeTransaction, FeeTransactionType, Blockchain};
use crate::service::{AdminFeeService, DbService};
use crate::config::defaults;
use mongodb::bson::oid::ObjectId;
use mongodb::bson::Decimal128;
use solana_sdk::system_instruction;
use solana_sdk::transaction::Transaction;
use solana_sdk::message::Message;
use spl_token::instruction as token_instruction;

const ENV_JUPITER_API_BASE_URL: &str = "API_URL_JUPITER_BASE";
const ENV_JUPITER_QUOTE_ENDPOINT: &str = "API_URL_JUPITER_ORDER";
const ENV_JUPITER_EXECUTE_ENDPOINT: &str = "API_URL_JUPITER_EXECUTE";
const ENV_JUPITER_BALANCES_ENDPOINT: &str = "API_URL_JUPITER_BALANCES";

const DEFAULT_JUPITER_API_BASE_URL: &str = "https://lite-api.jup.ag/ultra/v1";
const DEFAULT_JUPITER_QUOTE_ENDPOINT: &str = "/order";
const DEFAULT_JUPITER_EXECUTE_ENDPOINT: &str = "/execute";
const DEFAULT_JUPITER_BALANCES_ENDPOINT: &str = "/balances";

const ENV_SOL_MINT: &str = "CONTRACT_ADDR_SOL";
const DEFAULT_SOL_MINT: &str = "So11111111111111111111111111111111111111112";

const ADMIN_FEE_BPS: u32 = 10;
const ENV_ADMIN_FEE_ACCOUNT: &str = "FEE_RECIPIENT_SOL";
const DEFAULT_ADMIN_FEE_ACCOUNT: &str = "H8symjJ1BGGYeW9teQUWrK5sk4aoWX481YWdk5w34i5a";

const ENV_TOKEN_METADATA_API_URL: &str = "API_URL_TOKEN_METADATA";
const DEFAULT_TOKEN_METADATA_API_URL: &str = "https://token-list-api.solana.com/v1/tokens";
const TOKEN_METADATA_API_URL: &str = DEFAULT_TOKEN_METADATA_API_URL;


fn deserialize_string_or_number<'de, D>(deserializer: D) -> Result<f64, D::Error>
where
    D: serde::Deserializer<'de>,
{
    #[derive(Deserialize)]
    #[serde(untagged)]
    enum StringOrNumber {
        String(String),
        Number(f64),
    }

    match StringOrNumber::deserialize(deserializer)? {
        StringOrNumber::String(s) => {
            // Try to parse the string as a number
            s.parse::<f64>().map_err(serde::de::Error::custom)
        }
        StringOrNumber::Number(n) => Ok(n),
    }
}

#[derive(Debug, Deserialize, Serialize, Clone)]
pub struct JupiterQuoteResponse {
    pub requestId: String,
    pub inputMint: String,
    pub outputMint: String,
    pub inAmount: String,
    pub outAmount: String,
    pub otherAmountThreshold: String,
    pub swapMode: String,
    pub slippageBps: u32,
    #[serde(default)]
    pub priceImpactPct: String,
    pub routePlan: Vec<RoutePlanItem>,
    pub contextSlot: Option<u64>,
    pub timeTaken: Option<f64>,
    pub transaction: String,
    #[serde(default)]
    pub prioritizationType: Option<String>,
    #[serde(default)]
    pub prioritizationFeeLamports: Option<u64>,
    #[serde(default)]
    pub dynamicSlippageReport: Option<serde_json::Value>,
}

#[derive(Debug, Deserialize, Serialize, Clone)]
pub struct RoutePlanItem {
    pub swapInfo: SwapInfo,
    pub percent: u32,
}

#[derive(Debug, Deserialize, Serialize, Clone)]
pub struct SwapInfo {
    pub ammKey: String,
    pub label: String,
    pub inputMint: String,
    pub outputMint: String,
    pub inAmount: String,
    pub outAmount: String,
    pub feeAmount: String,
    pub feeMint: String,
}

#[derive(Debug, Deserialize)]
pub struct JupiterExecuteResponse {
    pub status: String,
    pub signature: String,
    pub slot: String,
    pub code: i32, // Changed from u32 to i32 to handle negative error codes
    pub inputAmountResult: String,
    pub outputAmountResult: String,
    #[serde(default)]
    pub swapEvents: Option<Vec<SwapEvent>>,
    #[serde(default)]
    pub totalInputAmount: Option<String>,
    #[serde(default)]
    pub totalOutputAmount: Option<String>,
}

#[derive(Debug, Deserialize)]
pub struct SwapEvent {
    pub inputMint: String,
    pub inputAmount: String,
    pub outputMint: String,
    pub outputAmount: String,
}

// Jupiter balance response types
#[derive(Debug, Deserialize)]
pub struct JupiterTokenBalance {
    pub amount: String,
    #[serde(rename = "uiAmount")]
    pub ui_amount: f64,
    pub slot: u64,
    #[serde(rename = "isFrozen")]
    pub is_frozen: bool,
}

#[derive(Error, Debug)]
pub enum SolanaTraderError {
    #[error("Jupiter API error: {0}")]
    JupiterApiError(String),

    #[error("Solana RPC error: {0}")]
    SolanaRpcError(String),

    #[error("Transaction error: {0}")]
    TransactionError(String),

    #[error("Serialization error: {0}")]
    SerializationError(String),

    #[error("Timeout error")]
    TimeoutError,

    #[error("Insufficient balance")]
    InsufficientBalance,

    #[error("Token account not found")]
    TokenAccountNotFound,

    #[error("Invalid token address")]
    InvalidTokenAddress,

    #[error("Invalid amount: {0}")]
    InvalidAmount(String),

    #[error("Database error: {0}")]
    DatabaseError(String),

    #[error("Network error: {0}")]
    NetworkError(String),

    #[error("Parse error: {0}")]
    ParseError(String),

    #[error("Validation error: {0}")]
    ValidationError(String),
}

// Convert SolanaTraderError to BotError
impl From<SolanaTraderError> for BotError {
    fn from(error: SolanaTraderError) -> Self {
        BotError::GeneralError(error.to_string())
    }
}



// Token information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TokenInfo {
    pub symbol: String,
    pub name: String,
    pub mint: String,
    pub decimals: u8,
    pub price_usd: Option<f64>,
    pub logo_uri: Option<String>,
}

// Swap result information
#[derive(Debug, Clone)]
pub struct Trade {
    pub signature: String,
    pub blockchain: String,
    pub wallet_address: String,
    pub input_token: String,
    pub input_token_symbol: String,
    pub input_amount: f64,
    pub output_token: String,
    pub output_token_symbol: String,
    pub output_amount: f64,
    pub price_impact: f64,
    pub timestamp: chrono::DateTime<chrono::Utc>,
    pub transaction_url: String,
    pub trade_type: TradeType,
}

#[derive(Debug, Clone, PartialEq)]
pub enum TradeType {
    Buy,
    Sell,
}

impl std::fmt::Display for TradeType {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            TradeType::Buy => write!(f, "buy"),
            TradeType::Sell => write!(f, "sell"),
        }
    }
}

#[derive(Debug, Clone)]
pub struct SwapResult {
    pub signature: Signature,
    pub input_token: TokenInfo,
    pub output_token: TokenInfo,
    pub input_amount: u64,
    pub output_amount: u64,
    pub price_impact: f64,
    pub success: bool,
    pub timestamp: chrono::DateTime<chrono::Utc>,
    pub success_message: Option<String>,
    pub trade: Option<Trade>,
}

// Token metadata structure
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TokenMetadata {
    pub symbol: String,
    pub name: String,
    pub mint: String,
    pub decimals: u8,
    #[serde(default)]
    pub logo_uri: Option<String>,
    #[serde(default)]
    pub price_usd: Option<f64>,
}

// Solana trader service
#[derive(Clone)]
pub struct SolanaTraderService {
    http_client: Client,

    rpc_client: Arc<RpcClient>,

    quote_cache: Arc<RwLock<lru::LruCache<String, JupiterQuoteResponse>>>,

    token_cache: Arc<RwLock<HashMap<String, TokenMetadata>>>,

    tx_queue: Arc<Mutex<Vec<VersionedTransaction>>>,

    worker_threads: usize,

    admin_fee_service: AdminFeeService,
}

impl SolanaTraderService {
    pub fn new(rpc_url: &str, _jito_api_key: Option<String>, worker_threads: usize) -> Self {
        let http_client = Client::builder()
            .timeout(Duration::from_secs(120))
            .danger_accept_invalid_certs(true) // Accept invalid certificates for development
            .build()
            .unwrap_or_default();

        // Use default  
        let reliable_rpc_url = "https://api.mainnet-beta.solana.com".to_string();

        println!("Initializing Solana RPC client with URL: {}", reliable_rpc_url);

        let rpc_client = Arc::new(RpcClient::new_with_commitment(
            reliable_rpc_url,
            CommitmentConfig::confirmed(),
        ));

        let quote_cache = Arc::new(RwLock::new(lru::LruCache::new(NonZero::new(100).unwrap())));

        let token_cache = Arc::new(RwLock::new(HashMap::new()));

        let tx_queue = Arc::new(Mutex::new(Vec::new()));

        Self {
            http_client,
            rpc_client,
            quote_cache,
            token_cache,
            tx_queue,
            worker_threads,
            admin_fee_service: AdminFeeService::new(),
        }
    }

    pub async fn get_swap_quote(
        &self,
        input_mint: &Pubkey,
        output_mint: &Pubkey,
        amount: u64,
        slippage_bps: u32,
        wallet: &Keypair,
    ) -> Result<JupiterQuoteResponse, SolanaTraderError> {
        let cache_key = format!("{}:{}:{}:{}",
            input_mint.to_string(),
            output_mint.to_string(),
            amount,
            slippage_bps
        );

        {
            let cache = self.quote_cache.read().await;
            // LruCache::peek doesn't mutate the cache, so we can use it instead of get
            if let Some(cached_quote) = cache.peek(&cache_key) {
                return Ok(cached_quote.clone());
            }
        }

        println!("Getting swap quote: input_mint={}, output_mint={}, amount={}, slippage_bps={}",
            input_mint.to_string(), output_mint.to_string(), amount, slippage_bps);

        let api_base_url = std::env::var(ENV_JUPITER_API_BASE_URL)
            .unwrap_or_else(|_| DEFAULT_JUPITER_API_BASE_URL.to_string());

        let quote_endpoint = std::env::var(ENV_JUPITER_QUOTE_ENDPOINT)
            .unwrap_or_else(|_| DEFAULT_JUPITER_QUOTE_ENDPOINT.to_string());

        // Get admin fee account from environment variable or use default
        let admin_fee_account = std::env::var(ENV_ADMIN_FEE_ACCOUNT)
            .unwrap_or_else(|_| DEFAULT_ADMIN_FEE_ACCOUNT.to_string());

        // Build quote URL according to the documentation
        // https://dev.jup.ag/docs/ultra-api/get-order
        let quote_url = format!(
            "{}{}?inputMint={}&outputMint={}&amount={}&slippageBps={}&feeBps={}&taker={}&feeAccount={}",
            api_base_url,
            quote_endpoint,
            input_mint.to_string(),
            output_mint.to_string(),
            amount,
            slippage_bps,
            ADMIN_FEE_BPS,
            wallet.pubkey().to_string(),
            admin_fee_account
        );

        println!("Jupiter quote URL: {}", quote_url);

        let response = tokio::time::timeout(
            Duration::from_secs(15),  // Faster quote timeout
            self.http_client.get(&quote_url).send()
        ).await
            .map_err(|_| {
                println!("Jupiter quote API timeout after 15 seconds");
                SolanaTraderError::TimeoutError
            })?
            .map_err(|e| {
                println!("Jupiter quote API error: {}", e);
                SolanaTraderError::JupiterApiError(e.to_string())
            })?;

        // Print response status for debugging
        println!("Jupiter quote response status: {}", response.status());

        if response.status().is_success() {
            let response_text = response.text().await
                .map_err(|e| SolanaTraderError::SerializationError(format!("Failed to get quote response text: {}", e)))?;

            println!("Jupiter quote response: {}", response_text);

            let quote_response: JupiterQuoteResponse = serde_json::from_str(&response_text)
                .map_err(|e| SolanaTraderError::SerializationError(format!("Failed to parse quote response: {}, Response: {}", e, response_text)))?;

            {
                let mut cache = self.quote_cache.write().await;
                cache.put(cache_key, quote_response.clone());
            }

            Ok(quote_response)
        } else {
            let status = response.status();
            let error_text = response.text().await
                .unwrap_or_else(|_| "Unknown error".to_string());

            println!("Jupiter quote error: Status={}, Error={}", status, error_text);

            Err(SolanaTraderError::JupiterApiError(format!("Status: {}, Error: {}", status, error_text)))
        }
    }


    fn calculate_dynamic_slippage(&self, quote: &JupiterQuoteResponse, user_slippage_bps: u32) -> u32 {
        let base_slippage = user_slippage_bps;

        let price_impact = quote.priceImpactPct.parse::<f64>().unwrap_or(0.0);

        // If user specified very high slippage (>10%), respect their choice completely
        // This is likely for sniping or trading volatile tokens where they know the risks
        if base_slippage > 1000 {
            return base_slippage;
        }


        let impact_factor = if price_impact > 10.0 {
            2.0
        } else if price_impact > 5.0 {
            1.7
        } else if price_impact > 2.0 {
            1.4
        } else if price_impact > 1.0 {
            1.25
        } else if price_impact > 0.5 {
            1.15
        } else {
            1.05
        };

        let route_complexity_factor = {
            let route_count = quote.routePlan.len();
            if route_count > 3 {
                1.3 // Very complex route
            } else if route_count > 1 {
                1.15 // Moderately complex route
            } else {
                1.0 // Simple route
            }
        };

        let market_factor = if let (Ok(out_amount), Ok(in_amount)) = (
            quote.outAmount.parse::<f64>(),
            quote.inAmount.parse::<f64>()
        ) {
            let ratio = out_amount / in_amount;
            if ratio < 0.0001 {
                1.5 // Very thin market
            } else if ratio < 0.001 {
                1.3 // Somewhat thin market
            } else {
                1.0 // Normal market
            }
        } else {
            1.0 // Couldn't parse amounts
        };

        let combined_factor = f64::min(impact_factor * route_complexity_factor * market_factor, 3.0);

        let adjusted_slippage = (base_slippage as f64 * combined_factor) as u32;

        if base_slippage < 50 && price_impact > 1.0 {
            adjusted_slippage.max(50)
        } else {
            adjusted_slippage
        }
    }

    pub async fn execute_swap(
        &self,
        wallet: &Keypair,
        quote: &JupiterQuoteResponse,
        _wrap_unwrap_sol: bool,
    ) -> Result<Signature, SolanaTraderError> {
        let dynamic_slippage = self.calculate_dynamic_slippage(quote, quote.slippageBps);

        println!(
            "Slippage adjusted: {} bps → {} bps (price impact: {}%)",
            quote.slippageBps,
            dynamic_slippage,
            quote.priceImpactPct
        );

        println!("Jupiter Quote: input_mint={}, output_mint={}, in_amount={}, out_amount={}, slippage_bps={}",
            quote.inputMint, quote.outputMint, quote.inAmount, quote.outAmount, quote.slippageBps);

        let transaction_data = match base64::decode(&quote.transaction) {
            Ok(data) => data,
            Err(e) => {
                return Err(SolanaTraderError::SerializationError(format!(
                    "Failed to decode transaction: {}", e
                )));
            }
        };

        let mut transaction: VersionedTransaction = bincode::deserialize(&transaction_data)
            .map_err(|e| SolanaTraderError::SerializationError(format!("Failed to deserialize transaction: {}", e)))?;

        // Sign the transaction with the wallet
        // For VersionedTransaction, we need to handle signing differently
        for (i, sig) in transaction.signatures.iter_mut().enumerate() {
            // Only sign if the signature is empty (default)
            if *sig == Signature::default() {
                if i < transaction.message.header().num_required_signatures as usize {
                    let message_data = transaction.message.serialize();
                    *sig = wallet.sign_message(&message_data);
                }
            }
        }

        let signed_transaction = base64::encode(bincode::serialize(&transaction)
            .map_err(|e| SolanaTraderError::SerializationError(format!("Failed to serialize transaction: {}", e)))?);

        // Create execute request according to the documentation
        // https://dev.jup.ag/docs/ultra-api/execute-order
        let execute_request = json!({
            "signedTransaction": signed_transaction,
            "requestId": quote.requestId,
        });

        println!("Jupiter execute request: {}", serde_json::to_string_pretty(&execute_request).unwrap_or_default());

        let api_base_url = std::env::var(ENV_JUPITER_API_BASE_URL)
            .unwrap_or_else(|_| DEFAULT_JUPITER_API_BASE_URL.to_string());

        let execute_endpoint = std::env::var(ENV_JUPITER_EXECUTE_ENDPOINT)
            .unwrap_or_else(|_| DEFAULT_JUPITER_EXECUTE_ENDPOINT.to_string());

        let response = tokio::time::timeout(
            Duration::from_secs(30),  // Reduced timeout for faster execution
            self.http_client.post(format!("{}{}", api_base_url, execute_endpoint))
                .json(&execute_request)
                .send()
        ).await
            .map_err(|_| {
                println!("Jupiter execute API timeout after 30 seconds");
                SolanaTraderError::TimeoutError
            })?
            .map_err(|e| {
                println!("Jupiter execute API error: {}", e);
                SolanaTraderError::JupiterApiError(e.to_string())
            })?;

        if response.status().is_success() {
            println!("Jupiter execute response status: {}", response.status());

            let response_text = response.text().await
                .map_err(|e| SolanaTraderError::SerializationError(format!("Failed to get response text: {}", e)))?;

            println!("Jupiter execute response: {}", response_text);

            let execute_response: JupiterExecuteResponse = match serde_json::from_str(&response_text) {
                Ok(response) => response,
                Err(e) => {
                    return Err(SolanaTraderError::SerializationError(format!(
                        "Failed to parse execute response: {}", e
                    )));
                }
            };

            if execute_response.status != "Success" {
                if execute_response.code < 0 {
                    let error_message = match execute_response.code {
                        -1005 => "Transaction failed due to price movement. Try increasing slippage or wait for better market conditions.".to_string(),
                        -1004 => "Insufficient funds for this transaction. Check your balance and try a smaller amount.".to_string(),
                        -1003 => "Token account not found. The token may not be in your wallet or may need to be created.".to_string(),
                        -1002 => "Program error occurred. The token may have trading restrictions or be paused.".to_string(),
                        -1001 => "RPC connection error. Network congestion detected, please try again in a few moments.".to_string(),
                        -1006 => "Transaction simulation failed. The transaction would likely fail on-chain.".to_string(),
                        -1007 => "Slippage tolerance exceeded. Market moved too much during execution.".to_string(),
                        -1008 => "Liquidity insufficient for this trade size. Try a smaller amount.".to_string(),
                        _ => format!("Transaction execution failed with status: {} and code: {}",
                                    execute_response.status, execute_response.code),
                    };
                    return Err(SolanaTraderError::TransactionError(error_message));
                }

                return Err(SolanaTraderError::TransactionError(format!(
                    "Transaction execution failed with status: {}", execute_response.status
                )));
            }

            let signature = Signature::from_str(&execute_response.signature)
                .map_err(|e| SolanaTraderError::SerializationError(format!("Failed to parse signature: {}", e)))?;

            let input_amount_human = execute_response.inputAmountResult.parse::<f64>().unwrap_or(0.0) / 1_000_000_000.0;
            let output_amount_human = execute_response.outputAmountResult.parse::<f64>().unwrap_or(0.0);

            println!("Transaction successfully executed!");
            println!("Signature: {}", signature);
            println!("Solscan URL: https://solscan.io/tx/{}", execute_response.signature);
            println!("Input: {} SOL", input_amount_human);
            println!("Output: {} tokens", output_amount_human);

            Ok(signature)
        } else {
            let status = response.status();
            let error_text = response.text().await
                .unwrap_or_else(|_| "Unknown error".to_string());

            println!("Jupiter execute error: Status={}, Error={}", status, error_text);

            let user_friendly_error = if let Ok(error_json) = serde_json::from_str::<serde_json::Value>(&error_text) {
                if let Some(error_msg) = error_json.get("error").and_then(|e| e.as_str()) {
                    if error_msg.contains("insufficient funds") || error_msg.contains("0x1") {
                        "Insufficient funds to execute this transaction. Please try a smaller amount."
                    } else if error_msg.contains("slippage") {
                        "Transaction failed due to price movement. Please try again with higher slippage."
                    } else if error_msg.contains("program error") {
                        "Transaction failed due to a program error. The token may have trading restrictions."
                    } else {
                        "Transaction failed. Please try again later."
                    }
                } else {
                    "Transaction failed. Please try again later."
                }
            } else {
                "Transaction failed. Please try again later."
            };

            Err(SolanaTraderError::JupiterApiError(user_friendly_error.to_string()))
        }
    }


    pub async fn buy_token_with_sol(
        &self,
        wallet: &Keypair,
        token_mint: &Pubkey,
        sol_amount: u64,
        slippage_bps: u32,
        chat_id: Option<i64>, //  Accept actual chat_id from UI
    ) -> Result<SwapResult, SolanaTraderError> {
        // Get real user ID from database using actual chat_id
        let user_id = if let Some(chat_id_val) = chat_id {
            match crate::service::DbService::find_user_by_chat_id(chat_id_val).await {
                Ok(Some(user)) => {
                    if let Some(id) = user.id {
                        id
                    } else {
                        println!("❌ CRITICAL: User found but has no ID for chat_id: {}", chat_id_val);
                        return Err(SolanaTraderError::ValidationError("User has no ID in database".to_string()));
                    }
                }
                Ok(None) => {
                    println!("⚠️ No user found for chat_id: {}, calling DB again", chat_id_val);
                    // Call DB again to get fresh user data
                    match crate::service::DbService::find_user_by_chat_id(chat_id_val).await {
                        Ok(Some(user)) => user.id.unwrap_or_else(|| ObjectId::new()),
                        _ => ObjectId::new()
                    }
                }
                Err(e) => {
                    println!("⚠️ Error finding user by chat_id {}: {}, calling DB again", chat_id_val, e);
                    // Call DB again to get fresh user data
                    match crate::service::DbService::find_user_by_chat_id(chat_id_val).await {
                        Ok(Some(user)) => user.id.unwrap_or_else(|| ObjectId::new()),
                        _ => ObjectId::new()
                    }
                }
            }
        } else {
            println!("⚠️ No chat_id provided for buy operation, using fallback");
            ObjectId::new() // Don't block trade, use fallback
        };
        let sol_mint_str = std::env::var(ENV_SOL_MINT)
            .unwrap_or_else(|_| DEFAULT_SOL_MINT.to_string());

        let sol_mint = Pubkey::from_str(&sol_mint_str)
            .map_err(|e| SolanaTraderError::TransactionError(e.to_string()))?;

        // Get admin fee percentage (fast cache lookup)
        let fee_percentage = self.admin_fee_service.get_admin_fee_percentage_for_blockchain(&Blockchain::SOL).await
            .unwrap_or(defaults::DEFAULT_ADMIN_FEE_PERCENTAGE);

        // Calculate fee and actual swap amount
        let fee_amount_lamports = if fee_percentage > 0.0 {
            ((sol_amount as f64) * (fee_percentage / 100.0)) as u64
        } else {
            0
        };
        let actual_swap_amount = sol_amount.saturating_sub(fee_amount_lamports);

        let sol_amount_f64 = sol_amount as f64 / 1_000_000_000.0;
        let fee_amount_f64 = sol_amount_f64 * (fee_percentage / 100.0);
        let total_required_sol = sol_amount_f64 + fee_amount_f64;
        let total_required_lamports = (total_required_sol * 1_000_000_000.0) as u64;

        // Check user's SOL balance
        let user_sol_balance = self.get_token_balance(&wallet.pubkey(), &sol_mint).await?;
        let user_sol_balance_f64 = user_sol_balance as f64 / 1_000_000_000.0;

        if user_sol_balance < total_required_lamports {
            return Err(SolanaTraderError::InsufficientBalance);
        }

        println!("✅ User has sufficient SOL balance:");
        println!("   Available: {} SOL", user_sol_balance_f64);
        println!("   Required for buy: {} SOL", sol_amount_f64);
        println!("   Required for fee: {} SOL", fee_amount_f64);
        println!("   Total required: {} SOL", total_required_sol);

        // PARALLEL EXECUTION: Start swap and fee collection simultaneously
        println!("Starting parallel buy execution: swap + fee collection");

        // Create futures for parallel execution
        let swap_future = async {
            let quote = self.get_swap_quote(&sol_mint, token_mint, actual_swap_amount, slippage_bps, wallet).await?;
            self.execute_swap(wallet, &quote, true).await.map(|sig| (sig, quote))
        };

        let fee_future = async {
            if fee_amount_lamports > 0 {
                // Collect admin fee in parallel with swap execution
                self.collect_buy_admin_fee_parallel(
                    wallet,
                    user_id,
                    fee_amount_lamports,
                    &sol_mint_str,
                    None, // trade_id will be set after trade is saved
                    sol_amount, // original amount user wanted to spend
                ).await
            } else {
                Ok(None)
            }
        };

        // EXECUTE SWAP AND FEE COLLECTION IN PARALLEL WITH HIGH PRIORITY
        println!("Starting parallel execution with maximum resource allocation");
        let start_time = std::time::Instant::now();
        let (swap_result, fee_result) = tokio::try_join!(swap_future, fee_future)?;
        println!("✅ Parallel execution completed in {:?}", start_time.elapsed());
        let (signature, quote) = swap_result;

        // Store fee transaction ID for later linking with trade
        let fee_transaction_id = fee_result;

        let sol_metadata = self.get_token_metadata(&sol_mint.to_string()).await;
        let token_metadata = self.get_token_metadata(&token_mint.to_string()).await;

        let input_token = TokenInfo {
            symbol: sol_metadata.symbol,
            name: sol_metadata.name,
            mint: sol_mint.to_string(),
            decimals: sol_metadata.decimals,
            price_usd: sol_metadata.price_usd,
            logo_uri: sol_metadata.logo_uri,
        };

        let output_token = TokenInfo {
            symbol: token_metadata.symbol,
            name: token_metadata.name,
            mint: token_mint.to_string(),
            decimals: token_metadata.decimals,
            price_usd: token_metadata.price_usd,
            logo_uri: token_metadata.logo_uri,
        };

        let output_amount = quote.outAmount.parse::<u64>().unwrap_or(0);

        let success_message = self.format_success_message(
            &signature,
            &input_token,
            &output_token,
            sol_amount,
            output_amount
        );

        println!("Success message for UI: {}", success_message);

        let mut result = SwapResult {
            signature,
            input_token,
            output_token,
            input_amount: sol_amount,
            output_amount,
            price_impact: quote.priceImpactPct.parse::<f64>().unwrap_or(0.0),
            success: true,
            timestamp: chrono::Utc::now(),
            success_message: Some(success_message),
            trade: None,
        };

        let trade = self.create_trade(wallet, &result, TradeType::Buy);
        println!("Created trade record: {:?}", trade);

        // Use the actual chat_id from UI - CRITICAL: Should always be present at this point
        let actual_chat_id = chat_id.expect("chat_id should be validated earlier");

        // Save the trade and get trade_id
        let trade_id = match self.save_trade(&trade, actual_chat_id).await {
            Ok(trade_id) => {
                println!("Trade saved successfully with ID: {:?}", trade_id);

                // Link fee transaction with trade_id if fee was collected
                if let (Some(fee_tx_id), Some(actual_trade_id)) = (fee_transaction_id, trade_id) {
                    if let Err(e) = self.admin_fee_service.update_fee_transaction_trade_id(fee_tx_id, actual_trade_id).await {
                        println!("⚠️ Failed to link fee transaction with trade: {}", e);
                    } else {
                        println!("✅ Fee transaction {} linked with trade {}", fee_tx_id, actual_trade_id);
                    }
                }

                trade_id
            }
            Err(e) => {
                println!("Warning: Failed to save trade: {}", e);
                None
            }
        };

        println!("✅ Buy operation completed with optimized parallel fee collection");

        result.trade = Some(trade);

        Ok(result)
    }

    pub async fn sell_token_for_sol_with_percentage(
        &self,
        wallet: &Keypair,
        token_mint: &Pubkey,
        percentage: u8,
        slippage_bps: u32,
        chat_id: Option<i64>, // 🚀 NEW: Accept actual chat_id from UI
    ) -> Result<SwapResult, SolanaTraderError> {
        println!("\n=== SELLING TOKEN WITH PERCENTAGE ===");
        println!("Wallet: {}", wallet.pubkey());
        println!("Token: {}", token_mint);
        println!("Percentage: {}%", percentage);
        println!("Slippage: {} bps", slippage_bps);

        // CRITICAL: chat_id is required for proper data linking
        let chat_id_val = if let Some(id) = chat_id {
            id
        } else {
            println!("❌ CRITICAL: No chat_id provided for sell operation");
            return Err(SolanaTraderError::ValidationError("chat_id is required for sell operations".to_string()));
        };

        let token_metadata = self.get_token_metadata(&token_mint.to_string()).await;
        println!("Token metadata: symbol={}, name={}, decimals={}",
            token_metadata.symbol, token_metadata.name, token_metadata.decimals);

        println!("Getting token balances from Jupiter API...");

        let wallet_pubkey = wallet.pubkey();

        let jupiter_result = match self.get_jupiter_token_balances(&wallet_pubkey).await {
            Ok(balances) => Ok(balances),
            Err(e) => {
                println!("Error getting Jupiter balances: {}", e);
                Err(e)
            }
        };

        let token_balance = match jupiter_result {
            Ok(balances) => {
                println!("Jupiter API returned balances for {} tokens", balances.len());

                let token_mint_str = token_mint.to_string();
                if let Some(balance) = balances.get(&token_mint_str) {
                    println!("Found token in Jupiter API: {} (raw: {})",
                        balance.ui_amount, balance.amount);

                    if let Ok(amount) = balance.amount.parse::<u64>() {
                        amount
                    } else {
                        println!("Failed to parse Jupiter balance amount: {}, falling back to RPC method",
                            balance.amount);
                        self.get_token_balance(&wallet.pubkey(), token_mint).await?
                    }
                } else {
                    println!("Token not found in Jupiter API response, falling back to RPC method");
                    self.get_token_balance(&wallet.pubkey(), token_mint).await?
                }
            },
            Err(e) => {
                println!("Error getting balances from Jupiter API: {}, falling back to RPC method", e);
                self.get_token_balance(&wallet.pubkey(), token_mint).await?
            }
        };

        println!(
            "Current token balance: {} {} (raw: {})",
            self.format_token_amount(token_balance, token_metadata.decimals),
            token_metadata.symbol,
            token_balance
        );

        let sol_mint_str = std::env::var(ENV_SOL_MINT)
            .unwrap_or_else(|_| DEFAULT_SOL_MINT.to_string());

        let sol_mint = Pubkey::from_str(&sol_mint_str)
            .map_err(|e| SolanaTraderError::TransactionError(e.to_string()))?;
        let sol_balance_before = self.get_token_balance(&wallet.pubkey(), &sol_mint).await?;
        let sol_metadata = self.get_token_metadata(&sol_mint_str).await;
        println!(
            "Current SOL balance: {} SOL (raw: {})",
            self.format_token_amount(sol_balance_before, sol_metadata.decimals),
            sol_balance_before
        );

        let token_amount = self.calculate_token_amount_from_percentage(
            &wallet.pubkey(), token_mint, percentage, token_balance
        ).await?;

        println!(
            "Selling {}% of token balance: {} {} (raw: {})",
            percentage,
            self.format_token_amount(token_amount, token_metadata.decimals),
            token_metadata.symbol,
            token_amount
        );

        println!("\n=== EXECUTING SELL ORDER ===");
        let result = self.sell_token_for_sol(wallet, token_mint, token_amount, slippage_bps, chat_id).await?;

        let sol_balance_after = self.get_token_balance(&wallet.pubkey(), &sol_mint).await?;
        let sol_gained = sol_balance_after.saturating_sub(sol_balance_before);

        let token_balance_after = self.get_token_balance(&wallet.pubkey(), token_mint).await?;
        let tokens_sold = token_balance.saturating_sub(token_balance_after);

        println!("\n=== SELL ORDER COMPLETED ===");
        println!("Transaction signature: {}", result.signature);
        println!(
            "Sold: {} {} (raw: {}) for {} SOL (raw: {})",
            self.format_token_amount(tokens_sold, token_metadata.decimals),
            token_metadata.symbol,
            tokens_sold,
            self.format_token_amount(sol_gained, sol_metadata.decimals),
            sol_gained
        );
        println!("Price impact: {}%", result.price_impact);
        println!("Transaction URL: https://solscan.io/tx/{}", result.signature);

        println!(
            "New token balance: {} {} (raw: {})",
            self.format_token_amount(token_balance_after, token_metadata.decimals),
            token_metadata.symbol,
            token_balance_after
        );
        println!(
            "New SOL balance: {} SOL (raw: {})",
            self.format_token_amount(sol_balance_after, sol_metadata.decimals),
            sol_balance_after
        );
        println!("=== SELL OPERATION COMPLETE ===\n");

        Ok(result)
    }

    pub async fn sell_token_for_sol(
        &self,
        wallet: &Keypair,
        token_mint: &Pubkey,
        token_amount: u64,
        slippage_bps: u32,
        chat_id: Option<i64>, // 🚀 NEW: Accept actual chat_id from UI
    ) -> Result<SwapResult, SolanaTraderError> {
        println!("\n=== SELLING TOKEN FOR SOL ===");
        println!("Wallet: {}", wallet.pubkey());
        println!("Token: {}", token_mint);
        println!("Amount: {}", token_amount);
        println!("Slippage: {} bps", slippage_bps);

        // CRITICAL: chat_id is required for proper data linking
        let chat_id_val = if let Some(id) = chat_id {
            id
        } else {
            println!("❌ CRITICAL: No chat_id provided for sell operation");
            return Err(SolanaTraderError::ValidationError("chat_id is required for sell operations".to_string()));
        };

        let token_metadata = self.get_token_metadata(&token_mint.to_string()).await;
        println!("Token metadata: symbol={}, name={}, decimals={}",
            token_metadata.symbol, token_metadata.name, token_metadata.decimals);

        let formatted_amount = self.format_token_amount(token_amount, token_metadata.decimals);
        println!("Selling {} {} (raw: {})",
            formatted_amount, token_metadata.symbol, token_amount);

        let sol_mint_str = std::env::var(ENV_SOL_MINT)
            .unwrap_or_else(|_| DEFAULT_SOL_MINT.to_string());

        let sol_mint = Pubkey::from_str(&sol_mint_str)
            .map_err(|e| SolanaTraderError::TransactionError(e.to_string()))?;

        let sol_metadata = self.get_token_metadata(&sol_mint_str).await;

        println!("\nGetting quote from Jupiter...");
        let quote = self.get_swap_quote(token_mint, &sol_mint, token_amount, slippage_bps, wallet).await?;

        let input_amount_raw = quote.inAmount.parse::<u64>().unwrap_or(token_amount);
        let output_amount_raw = quote.outAmount.parse::<u64>().unwrap_or(0);

        let input_formatted = self.format_token_amount(input_amount_raw, token_metadata.decimals);
        let output_formatted = self.format_token_amount(output_amount_raw, sol_metadata.decimals);

        println!("Quote received:");
        println!("  Input: {} {} (raw: {})",
            input_formatted, token_metadata.symbol, input_amount_raw);
        println!("  Output: {} SOL (raw: {})",
            output_formatted, output_amount_raw);
        println!("  Price impact: {}%", quote.priceImpactPct);
        println!("  Slippage: {} bps", quote.slippageBps);

        println!("\nExecuting swap with Jupiter...");
        // Execute swap with Jupiter
        let signature = self.execute_swap(wallet, &quote, true).await?;

        println!("Swap executed successfully!");
        println!("Transaction signature: {}", signature);
        println!("Transaction URL: https://solscan.io/tx/{}", signature);

        let output_amount = quote.outAmount.parse::<u64>().unwrap_or(0);

        // Get token metadata for both tokens first
        let token_metadata = self.get_token_metadata(&token_mint.to_string()).await;
        let sol_metadata = self.get_token_metadata(&sol_mint.to_string()).await;

        // Get real user ID from database using actual chat_id - CRITICAL: Don't use fallback ObjectId::new()
        let user_id = if let Some(chat_id_val) = chat_id {
            match crate::service::DbService::find_user_by_chat_id(chat_id_val).await {
                Ok(Some(user)) => {
                    if let Some(id) = user.id {
                        id
                    } else {
                        println!("❌ CRITICAL: User found but has no ID for chat_id: {}", chat_id_val);
                        return Err(SolanaTraderError::ValidationError("User has no ID in database".to_string()));
                    }
                }
                Ok(None) => {
                    println!("❌ CRITICAL: No user found for chat_id: {}", chat_id_val);
                    return Err(SolanaTraderError::ValidationError(format!("No user found for chat_id: {}", chat_id_val)));
                }
                Err(e) => {
                    println!("❌ CRITICAL: Error finding user by chat_id {}: {}", chat_id_val, e);
                    return Err(SolanaTraderError::DatabaseError(format!("Failed to find user: {}", e)));
                }
            }
        } else {
            println!("❌ CRITICAL: No chat_id provided for sell operation");
            return Err(SolanaTraderError::ValidationError("chat_id is required for sell operations".to_string()));
        };

        // Process admin fee AFTER the swap (for sell operations) - run in background
        // We'll process admin fee after saving the trade to get the trade_id

        let input_token = TokenInfo {
            symbol: token_metadata.symbol,
            name: token_metadata.name,
            mint: token_mint.to_string(),
            decimals: token_metadata.decimals,
            price_usd: token_metadata.price_usd,
            logo_uri: token_metadata.logo_uri,
        };

        let output_token = TokenInfo {
            symbol: sol_metadata.symbol,
            name: sol_metadata.name,
            mint: sol_mint.to_string(),
            decimals: sol_metadata.decimals,
            price_usd: sol_metadata.price_usd,
            logo_uri: sol_metadata.logo_uri,
        };

        // Format success message for UI
        let success_message = self.format_success_message(
            &signature,
            &input_token,
            &output_token,
            token_amount,
            output_amount
        );

        println!("Success message for UI: {}", success_message);

        // Create the swap result
        let mut result = SwapResult {
            signature,
            input_token,
            output_token,
            input_amount: token_amount,
            output_amount,
            price_impact: quote.priceImpactPct.parse::<f64>().unwrap_or(0.0),
            success: true,
            timestamp: chrono::Utc::now(),
            success_message: Some(success_message),
            trade: None,
        };

        // Create and add the trade
        let trade = self.create_trade(wallet, &result, TradeType::Sell);
        println!("Created trade record: {:?}", trade);

        // Use the actual chat_id from UI - CRITICAL: Should always be present at this point
        let actual_chat_id = chat_id.expect("chat_id should be validated earlier");

        // Save the trade to the database and get trade_id
        let trade_id = match self.save_trade(&trade, actual_chat_id).await {
            Ok(trade_id) => {
                println!("Trade saved successfully with ID: {:?}", trade_id);
                trade_id
            }
            Err(e) => {
                println!("Warning: Failed to save trade: {}", e);
                None
            }
        };

        // OPTIMIZED PARALLEL SMART FEE COLLECTION FOR SELL OPERATIONS
        if let Some(trade_id) = trade_id {
            let self_clone = self.clone();
            let wallet_bytes = wallet.to_bytes().to_vec();
            let sol_mint_str_clone = sol_mint_str.clone();
            let token_mint_clone = *token_mint;
            let token_metadata_clone = TokenMetadata {
                name: "Token".to_string(), // Use default name to avoid move issue
                symbol: "TKN".to_string(), // Use default symbol to avoid move issue
                decimals: token_metadata.decimals,
                mint: token_metadata.mint.clone(),
                price_usd: token_metadata.price_usd,
                logo_uri: None, // Use None for clone to avoid move issue
            };

            // Spawn high-priority background task for immediate fee collection
            tokio::spawn(async move {
                if let Ok(wallet_clone) = Keypair::from_bytes(&wallet_bytes) {
                    // Smart fee collection strategy for sell operations
                    match self_clone.process_smart_sell_admin_fee(
                        &wallet_clone,
                        user_id,
                        trade_id,
                        output_amount, // SOL received from sell
                        token_amount,  // Tokens sold
                        &token_metadata_clone,
                        &sol_mint_str_clone,
                        &token_mint_clone,
                    ).await {
                        Ok(Some(fee_tx_id)) => {
                            println!("✅ Smart sell admin fee collected: {}", fee_tx_id);
                        }
                        Ok(None) => {
                            println!("ℹ️ Sell admin fee collection skipped (0% fee or disabled)");
                        }
                        Err(e) => {
                            println!("❌ Smart sell admin fee collection failed: {}", e);
                        }
                    }
                } else {
                    println!("❌ Failed to recreate wallet for smart fee collection");
                }
            });
        }

        result.trade = Some(trade);

        Ok(result)
    }


    pub async fn calculate_token_amount_from_percentage(
        &self,
        wallet: &Pubkey,
        token_mint: &Pubkey,
        percentage: u8,
        provided_token_balance: u64,
    ) -> Result<u64, SolanaTraderError> {
        println!("=== CALCULATING TOKEN AMOUNT FROM PERCENTAGE ===");
        println!("Wallet: {}", wallet);
        println!("Token: {}", token_mint);
        println!("Percentage: {}%", percentage);

        if percentage > 100 {
            println!("Error: Invalid percentage ({}%). Must be between 0 and 100.", percentage);
            return Err(SolanaTraderError::InvalidAmount(format!("Invalid percentage ({}%). Must be between 0 and 100.", percentage)));
        }

        let token_balance = provided_token_balance;
        println!("Using token balance: {} (raw)", token_balance);

        if token_balance == 0 {
            println!("Error: Insufficient balance. Balance is 0.");
            return Err(SolanaTraderError::InsufficientBalance);
        }

        if percentage == 100 {
            // Apply a dynamic buffer based on token value and decimals
            // For tokens with high decimals or large balances, use a smaller percentage buffer
            // For tokens with low decimals or small balances, use a larger fixed buffer

            // Determine the appropriate buffer based on token balance size
            let buffer_percentage = if token_balance > defaults::LARGE_BALANCE_THRESHOLD {
                // For very large balances (>1B), use a smaller percentage
                defaults::LARGE_BALANCE_BUFFER
            } else if token_balance > defaults::MEDIUM_BALANCE_THRESHOLD {
                // For large balances (>1M), use medium buffer
                defaults::MEDIUM_BALANCE_BUFFER
            } else if token_balance > defaults::SMALL_BALANCE_THRESHOLD {
                // For medium balances (>10K), use small buffer
                defaults::SMALL_BALANCE_BUFFER
            } else {
                // For small balances, use tiny buffer
                defaults::TINY_BALANCE_BUFFER
            };

            // Calculate the adjusted balance
            let adjusted_balance = (token_balance as f64 * (1.0 - buffer_percentage)) as u64;

            // Ensure we're not leaving dust amounts (at least 1 token unit)
            let min_buffer = 1;
            let calculated_buffer = token_balance - adjusted_balance;
            let final_buffer = std::cmp::max(calculated_buffer, min_buffer);
            let final_adjusted_balance = token_balance - final_buffer;

            println!("Selling 100% of tokens - using {}% of balance: {} (original balance: {}, buffer: {})",
                     (100.0 * (1.0 - buffer_percentage)) as u64,
                     final_adjusted_balance,
                     token_balance,
                     final_buffer);

            return Ok(final_adjusted_balance);
        }

        let amount = (token_balance as u128 * percentage as u128) / 100;
        println!("Calculated amount: {} (raw) ({}% of {})", amount, percentage, token_balance);

        if amount == 0 {
            println!("Error: Calculated amount is 0. Try a higher percentage.");
            return Err(SolanaTraderError::InvalidAmount("Calculated amount is 0. Try a higher percentage.".to_string()));
        }

        // Check for dust amounts - very small amounts that might cause transaction failures
        // The minimum amount depends on the token's decimals and liquidity
        // For most tokens, at least a few units are needed for a successful swap
        let min_amount_for_swap: u128 = 10; // Minimum amount to avoid dust issues

        if amount < min_amount_for_swap && amount < (token_balance as u128) / 2 {
            println!("Warning: Amount {} is very small and might be considered dust", amount);

            // If the amount is too small but the user has more tokens, suggest a higher percentage
            if (token_balance as u128) > min_amount_for_swap * 2 {
                let suggested_percentage = ((min_amount_for_swap as f64 / token_balance as f64) * 100.0).ceil() as u8;
                println!("Suggesting minimum percentage of {}%", suggested_percentage);
                return Err(SolanaTraderError::InvalidAmount(format!(
                    "Amount too small for a successful swap. Try at least {}% (approximately {} tokens).",
                    suggested_percentage,
                    min_amount_for_swap
                )));
            } else {
                // If the user doesn't have many tokens, suggest selling all
                println!("User has few tokens, suggesting to sell 100%");
                return Err(SolanaTraderError::InvalidAmount(
                    "Amount too small for a successful swap. Try selling 100% of your tokens instead.".to_string()
                ));
            }
        }

        println!("Final amount to sell: {} (raw)", amount);
        println!("=== CALCULATION COMPLETE ===");

        Ok(amount as u64)
    }

    pub async fn save_trade(&self, trade: &Trade, chat_id: i64) -> Result<Option<ObjectId>, SolanaTraderError> {
        println!("Saving Solana trade to database:");
        println!("  Signature: {}", trade.signature);
        println!("  Blockchain: {}", trade.blockchain);
        println!("  Wallet: {}", trade.wallet_address);
        println!("  Type: {}", trade.trade_type);
        println!("  Input: {} {} ({})", trade.input_amount, trade.input_token_symbol, trade.input_token);
        println!("  Output: {} {} ({})", trade.output_amount, trade.output_token_symbol, trade.output_token);
        println!("  Price Impact: {}%", trade.price_impact);
        println!("  Timestamp: {}", trade.timestamp);
        println!("  URL: {}", trade.transaction_url);
        println!("  Chat ID: {}", chat_id);

        use crate::model::{Trade as DbTrade, Blockchain};
        use crate::model::trade::TradeStatus;
        use mongodb::bson::Decimal128;
        use std::str::FromStr;

        // Use the provided chat_id directly (no derivation needed)
        let actual_chat_id = chat_id;

        let contract_address = match trade.trade_type {
            TradeType::Buy => trade.output_token.clone(),
            TradeType::Sell => trade.input_token.clone(),
        };

        let token_name = match trade.trade_type {
            TradeType::Buy => Some(trade.output_token_symbol.clone()),
            TradeType::Sell => Some(trade.input_token_symbol.clone()),
        };

        // Get user information for the trade record
        let (user_id, user_first_name, user_username) = match crate::service::DbService::find_user_by_chat_id(chat_id).await {
            Ok(Some(user)) => {
                if let Some(id) = user.id {
                    (id, Some(user.first_name), user.username)
                } else {
                    println!("❌ CRITICAL: User found but has no ID for chat_id: {}", chat_id);
                    return Err(SolanaTraderError::ValidationError("User has no ID in database".to_string()));
                }
            }
            Ok(None) => {
                println!("❌ CRITICAL: No user found for chat_id: {}", chat_id);
                return Err(SolanaTraderError::ValidationError(format!("No user found for chat_id: {}", chat_id)));
            }
            Err(e) => {
                println!("❌ CRITICAL: Error finding user by chat_id {}: {}", chat_id, e);
                return Err(SolanaTraderError::DatabaseError(format!("Failed to find user: {}", e)));
            }
        };

        // Get admin fee percentage for Solana with intelligent fallback
        // Priority: 1. SOL-specific fee -> 2. Default admin fee -> 3. Hardcoded default (0.5%)
        let admin_fee_percentage = match self.admin_fee_service.get_admin_fee_percentage_for_blockchain(&Blockchain::SOL).await {
            Ok(percentage) => {
                println!("✅ Retrieved admin fee percentage for SOL: {}%", percentage);
                percentage
            }
            Err(e) => {
                println!("⚠️ Failed to get admin fee percentage for SOL, using hardcoded default: {}", e);
                defaults::DEFAULT_ADMIN_FEE_PERCENTAGE
            }
        };

        // Calculate amounts based on trade type
        let (native_token_amount, token_amount) = match trade.trade_type {
            TradeType::Buy => {
                // For buy: native_token_amount = SOL spent, token_amount = tokens received
                (Some(trade.input_amount), Some(trade.output_amount))
            }
            TradeType::Sell => {
                // For sell: token_amount = tokens sold, native_token_amount = SOL received
                (Some(trade.output_amount), Some(trade.input_amount))
            }
        };

        // Calculate admin fee amount
        let admin_fee_amount = if let Some(native_amount) = native_token_amount {
            Some((native_amount * admin_fee_percentage) / 100.0)
        } else {
            None
        };

        let db_trade = DbTrade {
            id: None,
            user_id: user_id, // Use the actual user ObjectId passed to this function
            user_first_name,
            user_username,
            blockchain: Blockchain::SOL,
            token_address: trade.output_token.clone(),
            token_symbol: trade.output_token_symbol.clone(),
            trade_type: format!("{:?}", trade.trade_type),

            // Enhanced amount tracking
            native_token_amount,
            token_amount,
            native_token_symbol: Some("SOL".to_string()),

            // Legacy field for backward compatibility
            amount_out: Some(match trade.trade_type {
                TradeType::Buy => trade.input_amount, // For buy, show SOL spent
                TradeType::Sell => trade.input_amount, // For sell, show tokens sold
            }),

            status: Some("completed".to_string()),
            timestamp: trade.timestamp.timestamp(),
            gas_fee: None, // Gas fee calculation removed as requested
            hash: Some(trade.signature.clone()),
            block_number: None, // Solana uses slots, not block numbers
            error_message: None,

            // Admin fee information
            admin_fee_amount,
            admin_fee_percentage: Some(admin_fee_percentage),
            admin_fee_status: Some("pending".to_string()),
            admin_fee_collection_method: Some("smart_collection".to_string()),
            admin_fee_token_symbol: Some("SOL".to_string()),
            admin_fee_token_address: Some("So11111111111111111111111111111111111111112".to_string()),
            admin_fee_transaction_id: None, // Will be set by admin fee processing
            // Legacy fields for backward compatibility
            contract_address: Some(contract_address),
            token_name,
            amount: Some(Decimal128::from_str(&trade.output_amount.to_string()).unwrap_or_else(|_| Decimal128::from_str("0").unwrap())),
            price: Some(Decimal128::from_str(&trade.input_amount.to_string()).unwrap_or_else(|_| Decimal128::from_str("0").unwrap())),
            transaction_hash: Some(trade.signature.clone()),
            created_at: Some(trade.timestamp),
            updated_at: Some(trade.timestamp),
        };

        match crate::service::DbService::save_trade(&db_trade).await {
            Ok(saved_trade) => {
                let trade_id = saved_trade;
                println!("Solana trade saved to database with ID: {:?}", trade_id);

                if let Ok(Some(user)) = crate::service::DbService::find_user_by_chat_id(actual_chat_id).await {
                    if let Some(user_id) = user.id {
                        if let Ok(Some(mut user_trades)) = crate::service::DbService::find_user_trades(user_id).await {
                            if let Some(trade_id_val) = Some(trade_id) {
                                user_trades.trades.sol.push(trade_id_val);

                                if let Err(e) = crate::service::DbService::save_user_trades(&user_trades).await {
                                    println!("⚠️ Warning: Failed to update user trades: {}", e);
                                    // DON'T BLOCK TRADE - Continue despite database error
                                }
                            }
                        }
                    }
                }

                Ok(Some(trade_id))
            },
            Err(e) => {
                println!("⚠️ Warning: Error saving Solana trade to database: {}", e);
                // DON'T BLOCK TRADE - Return None instead of error
                Ok(None)
            }
        }
    }

    pub fn create_trade(
        &self,
        wallet: &Keypair,
        swap_result: &SwapResult,
        trade_type: TradeType,
    ) -> Trade {
        let input_amount = if swap_result.input_token.decimals > 0 {
            let divisor = 10u64.pow(swap_result.input_token.decimals as u32) as f64;
            swap_result.input_amount as f64 / divisor
        } else {
            swap_result.input_amount as f64
        };

        let output_amount = if swap_result.output_token.decimals > 0 {
            let divisor = 10u64.pow(swap_result.output_token.decimals as u32) as f64;
            swap_result.output_amount as f64 / divisor
        } else {
            swap_result.output_amount as f64
        };

        let transaction_url = format!("https://solscan.io/tx/{}", swap_result.signature.to_string());

        Trade {
            signature: swap_result.signature.to_string(),
            blockchain: "solana".to_string(),
            wallet_address: wallet.pubkey().to_string(),
            input_token: swap_result.input_token.mint.clone(),
            input_token_symbol: swap_result.input_token.symbol.clone(),
            input_amount,
            output_token: swap_result.output_token.mint.clone(),
            output_token_symbol: swap_result.output_token.symbol.clone(),
            output_amount,
            price_impact: swap_result.price_impact,
            timestamp: swap_result.timestamp,
            transaction_url,
            trade_type,
        }
    }

    pub fn format_token_amount(&self, amount: u64, decimals: u8) -> String {
        if decimals > 0 {
            let divisor = 10u64.pow(decimals as u32) as f64;
            let formatted_amount = amount as f64 / divisor;

            if decimals == 9 {
                format!("{:.6}", formatted_amount)
            } else {
                if formatted_amount < 0.001 {
                    format!("{:.6}", formatted_amount)
                } else if formatted_amount < 0.1 {
                    format!("{:.6}", formatted_amount)
                } else if formatted_amount < 10.0 {
                    format!("{:.6}", formatted_amount)
                } else {
                    format!("{:.6}", formatted_amount)
                }
            }
        } else {
            amount.to_string()
        }
    }

    pub fn format_success_message(&self, signature: &Signature, input_token: &TokenInfo, output_token: &TokenInfo, input_amount: u64, output_amount: u64) -> String {
        let input_amount_formatted = self.format_token_amount(input_amount, input_token.decimals);
        let output_amount_formatted = self.format_token_amount(output_amount, output_token.decimals);

        format!(
            "✅ Transaction Successful!\n\n\
            💱 Swapped {} {} for {} {}\n\n\
            🔗 <a href=\"https://solscan.io/tx/{}\">View on Solscan</a>",
            input_amount_formatted, input_token.symbol,
            output_amount_formatted, output_token.symbol,
            signature.to_string()
        )
    }

    pub async fn get_formatted_token_balance(
        &self,
        wallet_pubkey: &Pubkey,
        token_mint: &Pubkey,
    ) -> Result<String, SolanaTraderError> {
        let balance = self.get_token_balance(wallet_pubkey, token_mint).await?;

        let token_metadata = self.get_token_metadata(&token_mint.to_string()).await;

        let formatted_balance = self.format_token_amount(balance, token_metadata.decimals);

        Ok(format!("{} {}", formatted_balance, token_metadata.symbol))
    }

    pub async fn get_jupiter_token_balances(
        &self,
        wallet_pubkey: &Pubkey,
    ) -> Result<HashMap<String, JupiterTokenBalance>, SolanaTraderError> {
        println!("Getting all token balances from Jupiter API for wallet: {}", wallet_pubkey);

        let api_base_url = std::env::var(ENV_JUPITER_API_BASE_URL)
            .unwrap_or_else(|_| DEFAULT_JUPITER_API_BASE_URL.to_string());

        let balances_endpoint = std::env::var(ENV_JUPITER_BALANCES_ENDPOINT)
            .unwrap_or_else(|_| DEFAULT_JUPITER_BALANCES_ENDPOINT.to_string());

        let balances_url = format!(
            "{}{}/{}",
            api_base_url,
            balances_endpoint,
            wallet_pubkey.to_string()
        );

        println!("Jupiter balances URL: {}", balances_url);

        thread::sleep(Duration::from_millis(1000));

        let response = tokio::time::timeout(
            Duration::from_secs(30),
            self.http_client.get(&balances_url).send()
        ).await
            .map_err(|_| SolanaTraderError::TimeoutError)?
            .map_err(|e| SolanaTraderError::JupiterApiError(e.to_string()))?;

        println!("Jupiter balances response status: {}", response.status());

        if response.status().is_success() {
            let response_text = response.text().await
                .map_err(|e| SolanaTraderError::SerializationError(format!("Failed to get balances response text: {}", e)))?;

            println!("Jupiter balances response: {}", response_text);

            let balances: HashMap<String, JupiterTokenBalance> = serde_json::from_str(&response_text)
                .map_err(|e| SolanaTraderError::SerializationError(format!("Failed to parse balances response: {}", e)))?;

            println!("Found {} tokens in wallet", balances.len());

            println!("=== ALL TOKEN BALANCES FROM JUPITER API ===");
            for (mint, balance) in balances.iter() {
                // Special handling for SOL
                if mint == "SOL" {
                    println!("SOL: {} (raw: {})",
                        balance.ui_amount, balance.amount);
                    continue;
                }

                // Try to get token metadata
                let token_metadata = self.get_token_metadata(mint).await;
                println!("{} ({}): {} (raw: {})",
                    token_metadata.symbol, mint,
                    balance.ui_amount, balance.amount);
            }
            println!("=== END OF TOKEN BALANCES ===");

            Ok(balances)
        } else {
            // Get detailed error information
            let status = response.status();
            let error_text = response.text().await
                .unwrap_or_else(|_| "Unknown error".to_string());

            println!("Jupiter balances error: Status={}, Error={}", status, error_text);

            Err(SolanaTraderError::JupiterApiError(format!("Status: {}, Error: {}", status, error_text)))
        }
    }


    /// Get RPC URL with fallback support for Solana (production implementation)
    /// Priority: 1. Admin Settings -> 2. Environment Variables -> 3. Config -> 4. Default URLs
    fn get_rpc_url_with_fallback(&self) -> String {
        // 1. First priority: Try to get RPC URL from admin settings
        if let Ok(Some(admin_rpc_url)) = tokio::task::block_in_place(|| {
            tokio::runtime::Handle::current().block_on(async {
                self.admin_fee_service.get_rpc_url_from_admin_settings(&Blockchain::SOL).await
            })
        }) {
            if !admin_rpc_url.is_empty() && admin_rpc_url.starts_with("http") {
                println!("🔧 Using Solana RPC URL from admin settings: {}", admin_rpc_url);
                return admin_rpc_url;
            }
        }

        let config = crate::config::AppConfig::get();

        // 2. Second priority: Environment variables
        if let Ok(env_rpc_url) = std::env::var("RPC_URL_SOL") {
            if !env_rpc_url.is_empty() && env_rpc_url.starts_with("http") {
                println!("🌍 Using Solana RPC URL from environment: {}", env_rpc_url);
                return env_rpc_url;
            }
        }

        // 3. Third priority: Config file
        if !config.rpc_url_sol.is_empty() && config.rpc_url_sol.starts_with("http") {
            println!("📄 Using Solana RPC URL from config: {}", config.rpc_url_sol);
            return config.rpc_url_sol.clone();
        }

        // 4. Fourth priority: Fallback environment variable
        if let Ok(fallback_rpc_url) = std::env::var("RPC_URL_SOL_FALLBACK") {
            if !fallback_rpc_url.is_empty() && fallback_rpc_url.starts_with("http") {
                println!("🔄 Using Solana fallback RPC URL from environment: {}", fallback_rpc_url);
                return fallback_rpc_url;
            }
        }

        // 5. Fifth priority: Config fallback
        if !config.rpc_url_sol_fallback.is_empty() && config.rpc_url_sol_fallback.starts_with("http") {
            println!("📄 Using Solana fallback RPC URL from config: {}", config.rpc_url_sol_fallback);
            return config.rpc_url_sol_fallback.clone();
        }

        // 6. Final fallback: Default Solana mainnet RPC
        let default_url = "https://api.mainnet-beta.solana.com".to_string();
        println!("⚙️ Using default Solana RPC URL: {}", default_url);
        default_url
    }

    /// Create RPC client with fallback support - prioritize admin settings, then reliable mainnet endpoints
    /// Priority: 1. Admin Settings -> 2. Environment Variables -> 3. Config -> 4. Default URLs
    fn create_fallback_rpc_client(&self) -> Vec<RpcClient> {
        let mut endpoints = Vec::new();

        // Get primary RPC URL using the fallback hierarchy
        let primary_url = self.get_rpc_url_with_fallback();
        endpoints.push(primary_url.clone());

        // Add additional reliable Solana mainnet RPC endpoints as fallbacks
        let fallback_endpoints = vec![
            "https://api.mainnet-beta.solana.com".to_string(),
            "https://solana-api.projectserum.com".to_string(),
            "https://rpc.ankr.com/solana".to_string(),
            "https://solana-mainnet.g.alchemy.com/v2/demo".to_string(),
        ];

        // Add fallback endpoints that aren't already in the list
        for url in fallback_endpoints {
            if !endpoints.contains(&url) {
                endpoints.push(url);
            }
        }

        let mut clients = Vec::new();

        for (i, url) in endpoints.iter().enumerate() {
            if !url.is_empty() && url.starts_with("http") {
                clients.push(RpcClient::new_with_commitment(
                    url.clone(),
                    CommitmentConfig::confirmed(),
                ));
                if i == 0 {
                    println!("Primary Solana RPC: {}", url);
                } else {
                    println!("🔄 Fallback Solana RPC {}: {}", i, url);
                }
            }
        }

        // Ensure we have at least one client
        if clients.is_empty() {
            let default_url = "https://api.mainnet-beta.solana.com".to_string();
            println!("⚙️ Using final default Solana RPC: {}", default_url);
            clients.push(RpcClient::new_with_commitment(
                default_url,
                CommitmentConfig::confirmed(),
            ));
        }

        clients
    }

    /// Create a single RPC client using the primary URL from fallback hierarchy
    fn create_primary_rpc_client(&self) -> RpcClient {
        let primary_url = self.get_rpc_url_with_fallback();
        RpcClient::new_with_commitment(primary_url, CommitmentConfig::confirmed())
    }

    /// Get SOL balance with fallback RPC endpoints using native Rust implementation
    /// Uses multiple RPC endpoints for reliability and accuracy
    async fn get_sol_balance_with_fallback(&self, wallet_pubkey: &Pubkey) -> Result<u64, SolanaTraderError> {
        let clients = self.create_fallback_rpc_client();

        for (i, client) in clients.iter().enumerate() {
            match tokio::time::timeout(Duration::from_secs(10), client.get_balance(wallet_pubkey)).await {
                Ok(Ok(balance)) => {
                    if i > 0 {
                        println!("Successfully got SOL balance from fallback RPC {}: {} lamports", i, balance);
                    } else {
                        println!("Got SOL balance from primary RPC: {} lamports", balance);
                    }
                    return Ok(balance);
                }
                Ok(Err(e)) => {
                    if i == 0 {
                        println!("Primary RPC failed for SOL balance: {}", e);
                    } else {
                        println!("Fallback RPC {} failed for SOL balance: {}", i, e);
                    }
                }
                Err(_) => {
                    println!("RPC {} timeout for SOL balance", i);
                }
            }
        }

        // If all Rust RPC calls fail, try TypeScript service as final fallback
        println!("All Rust RPC endpoints failed for SOL balance, trying TypeScript service fallback");
        self.get_sol_balance_typescript_fallback(wallet_pubkey).await
    }

    /// Call TypeScript token service as fallback for SOL balance when Rust implementation fails
    async fn get_sol_balance_typescript_fallback(&self, wallet_pubkey: &Pubkey) -> Result<u64, SolanaTraderError> {
        println!("🔄 Using TypeScript service as fallback for SOL balance: {}", wallet_pubkey);

        // Get the current working directory and construct path to TypeScript service
        let current_dir = std::env::current_dir()
            .map_err(|e| SolanaTraderError::NetworkError(format!("Failed to get current directory: {}", e)))?;

        let ts_service_path = current_dir.join("externals").join("token-service").join("dist").join("index.js");

        if !ts_service_path.exists() {
            println!("⚠️ TypeScript service not found at: {:?}", ts_service_path);
            return Ok(0);
        }

        println!("📍 Calling TypeScript service at: {:?}", ts_service_path);

        // Execute the TypeScript service
        let output = Command::new("node")
            .arg(ts_service_path)
            .arg("get-wallet-balance")
            .arg("SOL")
            .arg(wallet_pubkey.to_string())
            .output()
            .await
            .map_err(|e| SolanaTraderError::NetworkError(format!("Failed to execute TypeScript service: {}", e)))?;

        if !output.status.success() {
            let stderr = String::from_utf8_lossy(&output.stderr);
            println!("❌ TypeScript service failed with error: {}", stderr);
            return Ok(0);
        }

        let stdout = String::from_utf8_lossy(&output.stdout);
        println!("📤 TypeScript service output: {}", stdout);

        // Parse the JSON response
        match serde_json::from_str::<Value>(&stdout) {
            Ok(json) => {
                if let Some(error) = json.get("error") {
                    if error.as_bool().unwrap_or(false) {
                        let message = json.get("message").and_then(|m| m.as_str()).unwrap_or("Unknown error");
                        println!("❌ TypeScript service returned error: {}", message);
                        return Ok(0);
                    }
                }

                if let Some(balance_str) = json.get("balance").and_then(|b| b.as_str()) {
                    match balance_str.parse::<f64>() {
                        Ok(balance_sol) => {
                            // Convert SOL to lamports
                            let balance_lamports = (balance_sol * 1_000_000_000.0) as u64;
                            println!("✅ TypeScript service returned SOL balance: {} SOL ({} lamports)", balance_sol, balance_lamports);
                            Ok(balance_lamports)
                        }
                        Err(e) => {
                            println!("❌ Failed to parse balance from TypeScript service: {}", e);
                            Ok(0)
                        }
                    }
                } else {
                    println!("❌ No balance field in TypeScript service response");
                    Ok(0)
                }
            }
            Err(e) => {
                println!("❌ Failed to parse JSON from TypeScript service: {}", e);
                Ok(0)
            }
        }
    }



    pub async fn get_token_balance(
        &self,
        wallet_pubkey: &Pubkey,
        token_mint: &Pubkey,
    ) -> Result<u64, SolanaTraderError> {
        if token_mint.to_string() == "So11111111111111111111111111111111111111112" {
            return match self.get_sol_balance_with_fallback(wallet_pubkey).await {
                Ok(balance) => Ok(balance),
                Err(e) => {
                    println!("Error getting Solana balance: {}", e);
                    // Use Rust implementation with additional retry logic
                    println!("Attempting additional balance check with mainnet RPC...");

                    // Try with mainnet RPC as final fallback
                    let _mainnet_client = solana_client::nonblocking::rpc_client::RpcClient::new(
                        "https://api.mainnet-beta.solana.com".to_string()
                    );

                    // Try multiple reliable endpoints for better success rate
                    let fallback_endpoints = vec![
                        "https://api.mainnet-beta.solana.com",
                        "https://solana-api.projectserum.com",
                        "https://rpc.ankr.com/solana",
                    ];

                    for (i, endpoint) in fallback_endpoints.iter().enumerate() {
                        println!("Trying fallback endpoint {} of {}: {}", i + 1, fallback_endpoints.len(), endpoint);
                        let fallback_client = solana_client::nonblocking::rpc_client::RpcClient::new(endpoint.to_string());

                        match tokio::time::timeout(
                            tokio::time::Duration::from_secs(10),
                            fallback_client.get_balance(wallet_pubkey)
                        ).await {
                            Ok(Ok(balance)) => {
                                println!("Successfully retrieved SOL balance using fallback endpoint {}: {} lamports", endpoint, balance);
                                return Ok(balance);
                            }
                            Ok(Err(e)) => {
                                println!("Fallback endpoint {} failed: {}", endpoint, e);
                            }
                            Err(_) => {
                                println!("Timeout with fallback endpoint {}", endpoint);
                            }
                        }
                    }

                    println!("All SOL balance check attempts failed, trying TypeScript service fallback");
                    // Try TypeScript service as final fallback
                    self.get_sol_balance_typescript_fallback(wallet_pubkey).await
                }
            };
        }


        let jupiter_result = match self.get_jupiter_token_balances(wallet_pubkey).await {
            Ok(balances) => Ok(balances),
            Err(e) => {
                println!("Error getting Jupiter balances: {}", e);
                Err(e)
            }
        };

        match jupiter_result {
            Ok(balances) => {
                let token_mint_str = token_mint.to_string();
                if let Some(balance) = balances.get(&token_mint_str) {
                    println!("Found token balance in Jupiter API: {} (raw: {})", balance.ui_amount, balance.amount);

                    if let Ok(amount) = balance.amount.parse::<u64>() {
                        return Ok(amount);
                    } else {
                        println!("Failed to parse Jupiter balance amount: {}", balance.amount);
                    }
                } else {
                    println!("Token not found in Jupiter API response");
                }
            },
            Err(e) => {
                println!("Error getting balances from Jupiter API: {}", e);
            }
        }

        println!("Falling back to RPC method for token balance");

        // Try with fallback RPC clients
        let clients = self.create_fallback_rpc_client();

        for (i, client) in clients.iter().enumerate() {
            match client.get_token_accounts_by_owner(
                wallet_pubkey,
                TokenAccountsFilter::Mint(*token_mint),
            ).await {
            Ok(token_accounts) => {
                if i > 0 {
                    println!("Successfully got token accounts from fallback RPC {}", i);
                }
                println!("Found {} token accounts", token_accounts.len());

                if token_accounts.is_empty() {
                    println!("No token accounts found for this mint, returning 0 balance");
                    return Ok(0);
                }

                let mut total_balance = 0;

                for (j, account) in token_accounts.iter().enumerate() {
                    println!("Processing token account {}: {}", j, account.pubkey);

                    if let Ok(account_pubkey) = Pubkey::from_str(&account.pubkey) {
                        match client.get_account_data(&account_pubkey).await {
                            Ok(account_data) => {
                                match spl_token::state::Account::unpack(&account_data) {
                                    Ok(token_account) => {
                                        println!("Token account balance: {} tokens", token_account.amount);
                                        total_balance += token_account.amount;
                                    },
                                    Err(e) => {
                                        println!("Error unpacking token account: {}", e);
                                    }
                                }
                            },
                            Err(e) => {
                                println!("Error getting account data: {}", e);
                            }
                        }
                    } else {
                        println!("Error parsing account pubkey: {}", account.pubkey);
                    }
                }

                println!("Total token balance: {} tokens", total_balance);
                return Ok(total_balance);
            },
            Err(e) => {
                if i == 0 {
                    println!("Primary RPC failed for token accounts: {}", e);
                } else {
                    println!("Fallback RPC {} failed for token accounts: {}", i, e);
                }
                continue;
            }
        }
        }

        // If all RPC endpoints failed, try TypeScript service as final fallback
        println!("All RPC endpoints failed for token balance, trying TypeScript service fallback");
        self.get_token_balance_typescript_fallback(wallet_pubkey, token_mint).await
    }

    /// Call TypeScript token service as fallback for token balance when Rust implementation fails
    async fn get_token_balance_typescript_fallback(&self, wallet_pubkey: &Pubkey, token_mint: &Pubkey) -> Result<u64, SolanaTraderError> {
        println!("🔄 Using TypeScript service as fallback for token balance: wallet={}, token={}", wallet_pubkey, token_mint);

        // Get the current working directory and construct path to TypeScript service
        let current_dir = std::env::current_dir()
            .map_err(|e| SolanaTraderError::NetworkError(format!("Failed to get current directory: {}", e)))?;

        let ts_service_path = current_dir.join("externals").join("token-service").join("dist").join("index.js");

        if !ts_service_path.exists() {
            println!("⚠️ TypeScript service not found at: {:?}", ts_service_path);
            return Ok(0);
        }

        println!("📍 Calling TypeScript service at: {:?}", ts_service_path);

        // Execute the TypeScript service
        let output = Command::new("node")
            .arg(ts_service_path)
            .arg("get-token-balance")
            .arg("SOL")
            .arg(wallet_pubkey.to_string())
            .arg(token_mint.to_string())
            .output()
            .await
            .map_err(|e| SolanaTraderError::NetworkError(format!("Failed to execute TypeScript service: {}", e)))?;

        if !output.status.success() {
            let stderr = String::from_utf8_lossy(&output.stderr);
            println!("❌ TypeScript service failed with error: {}", stderr);
            return Ok(0);
        }

        let stdout = String::from_utf8_lossy(&output.stdout);
        println!("📤 TypeScript service output: {}", stdout);

        // Parse the JSON response
        match serde_json::from_str::<Value>(&stdout) {
            Ok(json) => {
                if let Some(error) = json.get("error") {
                    if error.as_bool().unwrap_or(false) {
                        let message = json.get("message").and_then(|m| m.as_str()).unwrap_or("Unknown error");
                        println!("❌ TypeScript service returned error: {}", message);
                        return Ok(0);
                    }
                }

                if let Some(balance_str) = json.get("balance").and_then(|b| b.as_str()) {
                    match balance_str.parse::<f64>() {
                        Ok(balance_ui) => {
                            // For SOL native token, convert to lamports
                            if token_mint.to_string() == "So11111111111111111111111111111111111111112" {
                                let balance_lamports = (balance_ui * 1_000_000_000.0) as u64;
                                println!("✅ TypeScript service returned SOL token balance: {} SOL ({} lamports)", balance_ui, balance_lamports);
                                Ok(balance_lamports)
                            } else {
                                // For SPL tokens, we need to get decimals to convert properly
                                // For now, assume the TypeScript service returns the raw amount
                                let balance_raw = (balance_ui * 1_000_000.0) as u64; // Assume 6 decimals as common default
                                println!("✅ TypeScript service returned token balance: {} (raw: {})", balance_ui, balance_raw);
                                Ok(balance_raw)
                            }
                        }
                        Err(e) => {
                            println!("❌ Failed to parse balance from TypeScript service: {}", e);
                            Ok(0)
                        }
                    }
                } else {
                    println!("❌ No balance field in TypeScript service response");
                    Ok(0)
                }
            }
            Err(e) => {
                println!("❌ Failed to parse JSON from TypeScript service: {}", e);
                Ok(0)
            }
        }
    }

    pub async fn get_token_metadata(&self, mint_address: &str) -> TokenMetadata {
        {
            let cache = self.token_cache.read().await;
            if let Some(metadata) = cache.get(mint_address) {
                return metadata.clone();
            }
        }

      
        // First try Jupiter Token API for accurate token information
        let jupiter_token_url = format!("https://lite-api.jup.ag/tokens/v1/token/{}", mint_address);

        println!("🔍 Fetching token metadata from Jupiter API: {}", jupiter_token_url);

        match tokio::time::timeout(Duration::from_secs(10), self.http_client.get(&jupiter_token_url).send()).await {
            Ok(Ok(response)) => {
                if response.status().is_success() {
                    if let Ok(token_data) = response.json::<serde_json::Value>().await {
                        // Parse Jupiter API response
                        if let (Some(symbol), Some(name), Some(decimals)) = (
                            token_data.get("symbol").and_then(|v| v.as_str()),
                            token_data.get("name").and_then(|v| v.as_str()),
                            token_data.get("decimals").and_then(|v| v.as_u64()),
                        ) {
                            let logo_uri = token_data.get("logoURI").and_then(|v| v.as_str()).map(|s| s.to_string());

                            let metadata = TokenMetadata {
                                symbol: symbol.to_string(),
                                name: name.to_string(),
                                mint: mint_address.to_string(),
                                decimals: decimals as u8,
                                logo_uri,
                                price_usd: None,
                            };

                            println!("✅ Retrieved token metadata from Jupiter API: symbol={}, name={}, decimals={}",
                                metadata.symbol, metadata.name, metadata.decimals);

                            // Cache the result
                            {
                                let mut cache = self.token_cache.write().await;
                                cache.insert(mint_address.to_string(), metadata.clone());
                            }

                            return metadata;
                        }
                    }
                }
            }
            Ok(Err(e)) => {
                println!("⚠️ Jupiter API request failed: {}", e);
            }
            Err(_) => {
                println!("⚠️ Jupiter API request timeout");
            }
        }

        println!("🔄 Jupiter API failed, falling back to Solana SDK for token decimals");

        // Always fetch token decimals from blockchain using Solana SDK
        let mut decimals = 0u8; // Will be fetched from blockchain
        let mut symbol = mint_address[0..6].to_string();
        let mut name = format!("Token {}", &mint_address[0..8]);

        // Special handling for SOL native token
        let sol_mint_str = std::env::var(ENV_SOL_MINT)
            .unwrap_or_else(|_| DEFAULT_SOL_MINT.to_string());

        if mint_address == sol_mint_str || mint_address == "SOL" {
            symbol = "SOL".to_string();
            name = "Solana".to_string();
        }

        if let Ok(mint_pubkey) = Pubkey::from_str(mint_address) {
            // Use primary RPC client to get mint account data
            let primary_client = self.create_primary_rpc_client();

            match tokio::time::timeout(Duration::from_secs(10), primary_client.get_account(&mint_pubkey)).await {
                Ok(Ok(account)) => {
                    if let Ok(mint) = spl_token::state::Mint::unpack(&account.data) {
                        decimals = mint.decimals;
                        println!("✅ Retrieved token decimals from blockchain: {} decimals for {} ({})", decimals, symbol, mint_address);
                    } else {
                        println!("⚠️ Failed to unpack mint data for {}", mint_address);
                        // For SOL, use known decimals as fallback
                        if mint_address == sol_mint_str || mint_address == "SOL" {
                            decimals = 9;
                        } else {
                            decimals = 6; // Common default for SPL tokens
                        }
                    }
                }
                Ok(Err(e)) => {
                    println!("⚠️ RPC failed to get mint account for {}: {}", mint_address, e);
                    // For SOL, use known decimals as fallback
                    if mint_address == sol_mint_str || mint_address == "SOL" {
                        decimals = 9;
                    } else {
                        decimals = 6; // Common default for SPL tokens
                    }
                }
                Err(_) => {
                    println!("⚠️ RPC timeout getting mint account for {}", mint_address);
                    // For SOL, use known decimals as fallback
                    if mint_address == sol_mint_str || mint_address == "SOL" {
                        decimals = 9;
                    } else {
                        decimals = 6; // Common default for SPL tokens
                    }
                }
            }
        } else {
            println!("⚠️ Invalid mint address format: {}", mint_address);
            decimals = 6; // Default for invalid addresses
        }

        let metadata = TokenMetadata {
            symbol,
            name,
            mint: mint_address.to_string(),
            decimals,
            logo_uri: if mint_address == sol_mint_str || mint_address == "SOL" {
                Some("https://raw.githubusercontent.com/solana-labs/token-list/main/assets/mainnet/So11111111111111111111111111111111111111112/logo.png".to_string())
            } else {
                None
            },
            price_usd: None,
        };

        println!("🔧 Created metadata for {}: symbol={}, decimals={}", mint_address, metadata.symbol, metadata.decimals);

        // Cache the metadata
        {
            let mut cache = self.token_cache.write().await;
            cache.insert(mint_address.to_string(), metadata.clone());
        }

        metadata
    }

    pub async fn initialize(&self) {
        for i in 0..self.worker_threads {
            let tx_queue = self.tx_queue.clone();
            let rpc_client = self.rpc_client.clone();
            let http_client = self.http_client.clone();

            tokio::spawn(async move {
                println!("Starting worker thread {}", i);
                Self::transaction_worker(tx_queue, rpc_client, http_client).await;
            });
        }

        let sol_mint_str = std::env::var(ENV_SOL_MINT)
            .unwrap_or_else(|_| DEFAULT_SOL_MINT.to_string());

        self.get_token_metadata(&sol_mint_str).await;
    }

    async fn transaction_worker(
        tx_queue: Arc<Mutex<Vec<VersionedTransaction>>>,
        rpc_client: Arc<RpcClient>,
        _http_client: Client,
    ) {
        let mut recent_blockhash = None;
        let mut last_blockhash_update = std::time::Instant::now();

        loop {
            Self::update_blockhash(&rpc_client, &mut recent_blockhash, &mut last_blockhash_update).await;

            let txs = match Self::get_transactions_from_queue(&tx_queue).await {
                Some(txs) => txs,
                None => continue,
            };

            for tx in txs {
                let rpc_client = rpc_client.clone();

                tokio::spawn(async move {
                    Self::process_single_transaction(tx, rpc_client).await;
                });
            }

            tokio::time::sleep(Duration::from_millis(5)).await;
        }
    }

    async fn update_blockhash(
        rpc_client: &Arc<RpcClient>,
        recent_blockhash: &mut Option<solana_sdk::hash::Hash>,
        last_blockhash_update: &mut std::time::Instant,
    ) {
        if recent_blockhash.is_none() || last_blockhash_update.elapsed() > std::time::Duration::from_secs(15) {
            if let Ok(blockhash) = rpc_client.get_latest_blockhash().await {
                *recent_blockhash = Some(blockhash);
                *last_blockhash_update = std::time::Instant::now();
            }
        }
    }

    async fn get_transactions_from_queue(
        tx_queue: &Arc<Mutex<Vec<VersionedTransaction>>>,
    ) -> Option<Vec<VersionedTransaction>> {
        let mut queue = tx_queue.lock().await;
        if queue.is_empty() {
            tokio::time::sleep(Duration::from_millis(50)).await; // Reduced sleep time
            return None;
        }

        let mut txs = Vec::with_capacity(5);
        for _ in 0..5 {
            if let Some(tx) = queue.pop() {
                txs.push(tx);
            } else {
                break;
            }
        }
        Some(txs)
    }

    async fn process_single_transaction(
        tx: VersionedTransaction,
        rpc_client: Arc<RpcClient>,
    ) {
        let signature = tx.signatures[0];

        println!("Sending transaction via RPC: {}", signature);

        let tx_bytes = {
            match bincode::serialize(&tx) {
                Ok(bytes) => bytes,
                Err(e) => {
                    println!("Failed to serialize transaction: {}", e);
                    return;
                }
            }
        }; // tx is no longer needed after this point and will be dropped

        drop(tx);

        let regular_tx = {
            match bincode::deserialize::<solana_sdk::transaction::Transaction>(&tx_bytes) {
                Ok(tx) => tx,
                Err(e) => {
                    println!("Failed to deserialize transaction: {}", e);
                    return;
                }
            }
        };

        drop(tx_bytes);

        Self::send_transaction(&rpc_client, &regular_tx, signature).await;
    }

    async fn send_transaction(
        rpc_client: &Arc<RpcClient>,
        tx: &solana_sdk::transaction::Transaction,
        _signature: Signature,
    ) {
        let config = RpcSendTransactionConfig {
            skip_preflight: true,
            preflight_commitment: Some(CommitmentLevel::Confirmed),
            encoding: None,
            max_retries: Some(5),
            min_context_slot: None,
        };

        match rpc_client.send_transaction_with_config(tx, config).await {
            Ok(sig) => println!("Transaction sent successfully via RPC: {}", sig),
            Err(e) => {
                let user_friendly_error = Self::process_transaction_error(&e);
                println!("RPC submission failed: {}", user_friendly_error);
            }
        }
    }

    fn process_transaction_error(error: &solana_client::client_error::ClientError) -> &'static str {
        let error_str = error.to_string();

        if error_str.contains("0x1") || error_str.contains("insufficient funds") {
            "Insufficient funds to execute this transaction. Please try a smaller amount."
        } else if error_str.contains("custom program error") {
            "Transaction failed due to a program error. The token may have trading restrictions."
        } else {
            "Failed to send transaction. Please try again with a higher slippage."
        }
    }

    /// Get user ID from wallet address by looking up in the database using wallet address
    /// This is the proper way to find users by wallet address without chat_id derivation
    pub async fn get_user_id_from_wallet(&self, wallet_pubkey: &Pubkey) -> Result<ObjectId, SolanaTraderError> {
        let wallet_address = wallet_pubkey.to_string();

        // Find user by wallet address in user_wallets collection
        match DbService::find_user_by_wallet_address(&wallet_address).await {
            Ok(Some(user)) => {
                if let Some(user_id) = user.id {
                    println!("✅ Found user ID {} for wallet {}", user_id, wallet_address);
                    Ok(user_id)
                } else {
                    println!("❌ User found but has no ID for wallet {}", wallet_address);
                    Err(SolanaTraderError::ValidationError("User has no ID in database".to_string()))
                }
            }
            Ok(None) => {
                println!("❌ No user found for wallet {}", wallet_address);
                Err(SolanaTraderError::ValidationError(format!("No user found for wallet: {}", wallet_address)))
            }
            Err(e) => {
                println!("❌ Database error looking up user for wallet {}: {}", wallet_address, e);
                Err(SolanaTraderError::DatabaseError(format!("Failed to find user: {}", e)))
            }
        }
    }

    /// Transfer SOL to admin address for fee collection with smart $0.25 threshold
    pub async fn transfer_sol_admin_fee(
        &self,
        wallet: &Keypair,
        admin_address: &Pubkey,
        amount_lamports: u64,
    ) -> Result<Signature, SolanaTraderError> {
        let fee_amount_sol = amount_lamports as f64 / 1_000_000_000.0;
        println!("🔍 Checking SOL admin fee threshold: {} lamports ({} SOL)", amount_lamports, fee_amount_sol);

        // Check if fee meets minimum and adjust ONLY if below $0.25 equivalent
        let price_service = crate::service::price_service::PriceService::new();

        let final_fee_sol = match price_service.get_adjusted_fee_amount(&crate::model::Blockchain::SOL, fee_amount_sol).await {
            Ok(adjusted_amount) => adjusted_amount,
            Err(e) => {
                println!("⚠️ Failed to get adjusted fee amount, using original: {}", e);
                fee_amount_sol
            }
        };

        let final_amount_lamports = (final_fee_sol * 1_000_000_000.0) as u64;

        if final_amount_lamports != amount_lamports {
            println!("💰 SOL admin fee adjusted from {} lamports to {} lamports (minimum $0.25 equivalent)",
                     amount_lamports, final_amount_lamports);
        } else {
            println!("✅ SOL admin fee using calculated amount: {} lamports", final_amount_lamports);
        }

        // Create transfer instruction with adjusted amount
        let transfer_instruction = system_instruction::transfer(
            &wallet.pubkey(),
            admin_address,
            final_amount_lamports,
        );

        // Get recent blockhash
        let recent_blockhash = self.rpc_client
            .get_latest_blockhash()
            .await
            .map_err(|e| SolanaTraderError::SolanaRpcError(format!("Failed to get recent blockhash: {}", e)))?;

        // Create and sign transaction
        let transaction = Transaction::new_signed_with_payer(
            &[transfer_instruction],
            Some(&wallet.pubkey()),
            &[wallet],
            recent_blockhash,
        );

        // Send transaction
        let signature = self.rpc_client
            .send_and_confirm_transaction(&transaction)
            .await
            .map_err(|e| SolanaTraderError::TransactionError(format!("Failed to send SOL transfer: {}", e)))?;

        println!("SOL admin fee transfer successful: {}", signature);
        Ok(signature)
    }

    /// Transfer SPL token to admin address for fee collection
    pub async fn transfer_token_admin_fee(
        &self,
        wallet: &Keypair,
        token_mint: &Pubkey,
        admin_address: &Pubkey,
        amount: u64,
        _decimals: u8,
    ) -> Result<Signature, SolanaTraderError> {
        println!("Transferring {} token admin fee to {}", amount, admin_address);

        // Get user's token account
        // Calculate associated token accounts manually
        let user_token_account = Pubkey::find_program_address(
            &[
                &wallet.pubkey().to_bytes(),
                &spl_token::id().to_bytes(),
                &token_mint.to_bytes(),
            ],
            &spl_token::id(),
        ).0;

        let admin_token_account = Pubkey::find_program_address(
            &[
                &admin_address.to_bytes(),
                &spl_token::id().to_bytes(),
                &token_mint.to_bytes(),
            ],
            &spl_token::id(),
        ).0;

        let mut instructions = Vec::new();

        // Check if admin token account exists, create if not
        let admin_account_info = self.rpc_client.get_account(&admin_token_account).await;
        if admin_account_info.is_err() {
            println!("Creating admin token account for {}", token_mint);
            // Use system instruction to create account (simplified approach)
            let create_account_instruction = system_instruction::create_account(
                &wallet.pubkey(),
                &admin_token_account,
                self.rpc_client.get_minimum_balance_for_rent_exemption(spl_token::state::Account::LEN).await
                    .unwrap_or(defaults::SOL_RENT_EXEMPTION_DEFAULT), // Default rent exemption for token account
                spl_token::state::Account::LEN as u64,
                &spl_token::id(),
            );
            instructions.push(create_account_instruction);
        }

        // Create transfer instruction
        let transfer_instruction = token_instruction::transfer(
            &spl_token::id(),
            &user_token_account,
            &admin_token_account,
            &wallet.pubkey(),
            &[],
            amount,
        ).map_err(|e| SolanaTraderError::TransactionError(format!("Failed to create transfer instruction: {}", e)))?;
        instructions.push(transfer_instruction);

        // Get recent blockhash
        let recent_blockhash = self.rpc_client
            .get_latest_blockhash()
            .await
            .map_err(|e| SolanaTraderError::SolanaRpcError(format!("Failed to get recent blockhash: {}", e)))?;

        // Create and sign transaction
        let transaction = Transaction::new_signed_with_payer(
            &instructions,
            Some(&wallet.pubkey()),
            &[wallet],
            recent_blockhash,
        );

        // Send transaction
        let signature = self.rpc_client
            .send_and_confirm_transaction(&transaction)
            .await
            .map_err(|e| SolanaTraderError::TransactionError(format!("Failed to send token transfer: {}", e)))?;

        println!("Token admin fee transfer successful: {}", signature);
        Ok(signature)
    }

    /// Check if user has sufficient SOL balance for trade + admin fee + rent
    pub async fn check_sufficient_balance_for_buy(
        &self,
        wallet: &Keypair,
        amount_lamports: u64,
    ) -> Result<bool, SolanaTraderError> {
        // Get admin fee percentage for Solana
        let fee_percentage = match self.admin_fee_service.get_admin_fee_percentage_for_blockchain(&Blockchain::SOL).await {
            Ok(percentage) => percentage,
            Err(_) => defaults::DEFAULT_ADMIN_FEE_PERCENTAGE, // Default fallback
        };

        // Calculate admin fee (fee is taken from received tokens, but we need buffer for slippage)
        let buffer_percentage = defaults::SLIPPAGE_BUFFER_PERCENTAGE; // Buffer for slippage and transaction fees
        let total_percentage = fee_percentage + buffer_percentage;
        let buffer_amount = (amount_lamports as f64 * total_percentage / 100.0) as u64;

        // Account for minimum rent and transaction fees
        let minimum_rent = 890880; // Minimum rent for account
        let transaction_fee_buffer = 10000; // Buffer for transaction fees
        let required_balance = amount_lamports + buffer_amount + minimum_rent + transaction_fee_buffer;

        // Get user's current SOL balance
        match self.rpc_client.get_balance(&wallet.pubkey()).await {
            Ok(balance) => {
                let has_sufficient = balance >= required_balance;

                if !has_sufficient {
                    let balance_sol = balance as f64 / solana_sdk::native_token::LAMPORTS_PER_SOL as f64;
                    let required_sol = required_balance as f64 / solana_sdk::native_token::LAMPORTS_PER_SOL as f64;
                    let trade_sol = amount_lamports as f64 / solana_sdk::native_token::LAMPORTS_PER_SOL as f64;
                    let buffer_sol = (buffer_amount + minimum_rent + transaction_fee_buffer) as f64 / solana_sdk::native_token::LAMPORTS_PER_SOL as f64;

                    println!("Insufficient SOL balance for buy: has {:.6} SOL, needs {:.6} SOL (trade: {:.6} + fee/buffer/rent: {:.6})",
                             balance_sol, required_sol, trade_sol, buffer_sol);
                }

                Ok(has_sufficient)
            }
            Err(e) => {
                println!("Failed to get SOL balance: {}", e);
                Err(SolanaTraderError::NetworkError(format!("Failed to get balance: {}", e)))
            }
        }
    }

    /// Check if user has sufficient token balance for sell using proper solana-client methods
    pub async fn check_sufficient_balance_for_sell(
        &self,
        wallet: &Keypair,
        token_mint: &Pubkey,
        sell_amount: u64,
        decimals: u8,
    ) -> Result<bool, SolanaTraderError> {
        // Get associated token account address
        let associated_token_address = spl_associated_token_account::get_associated_token_address(
            &wallet.pubkey(),
            token_mint,
        );

        // Use the proper RPC method to get token account balance
        match self.rpc_client.get_token_account_balance(&associated_token_address).await {
            Ok(token_balance) => {
                // Parse the balance amount (already in token units)
                let balance_amount = token_balance.amount.parse::<u64>()
                    .map_err(|e| SolanaTraderError::ParseError(format!("Failed to parse balance: {}", e)))?;

                let has_sufficient = balance_amount >= sell_amount;

                if !has_sufficient {
                    let balance_tokens = balance_amount as f64 / 10_u64.pow(decimals as u32) as f64;
                    let required_tokens = sell_amount as f64 / 10_u64.pow(decimals as u32) as f64;

                    println!("Insufficient token balance for sell: has {:.6} tokens, needs {:.6} tokens",
                             balance_tokens, required_tokens);
                }

                Ok(has_sufficient)
            }
            Err(e) => {
                // Check if it's because the account doesn't exist
                if e.to_string().contains("could not find account") {
                    println!("Token account doesn't exist for mint {}", token_mint);
                    Ok(false)
                } else {
                    println!("Failed to get token account balance: {}", e);
                    Err(SolanaTraderError::NetworkError(format!("Failed to get token balance: {}", e)))
                }
            }
        }
    }

    /// Get token decimals for Solana SPL token using proper solana-client methods
    pub async fn get_token_decimals(&self, token_mint: &Pubkey) -> Result<u8, SolanaTraderError> {
        // Get mint account info
        match self.rpc_client.get_account(token_mint).await {
            Ok(account) => {
                // Check if this is a valid SPL token mint account
                if account.owner != spl_token::id() {
                    return Err(SolanaTraderError::ValidationError(
                        "Account is not owned by SPL Token program".to_string()
                    ));
                }

                // Parse mint account data
                use spl_token::state::Mint;
                match Mint::unpack(&account.data) {
                    Ok(mint) => {
                        Ok(mint.decimals)
                    }
                    Err(e) => {
                        println!("Failed to parse mint account for {}: {}", token_mint, e);
                        Err(SolanaTraderError::ParseError(format!("Failed to parse mint: {}", e)))
                    }
                }
            }
            Err(e) => {
                println!("Failed to get mint account for {}: {}", token_mint, e);
                Err(SolanaTraderError::NetworkError(format!("Failed to get mint account: {}", e)))
            }
        }
    }



    /// Process admin fee for buy operations (deduct fee before swap)
    pub async fn process_buy_admin_fee(
        &self,
        wallet: &Keypair,
        user_id: ObjectId,
        original_amount: u64,
        token_symbol: &str,
        token_address: &str,
    ) -> Result<(u64, Option<ObjectId>), SolanaTraderError> {
        self.process_buy_admin_fee_with_trade_id(
            wallet,
            user_id,
            None, // No trade_id for backward compatibility
            original_amount,
            token_symbol,
            token_address,
        ).await
    }

    /// Process admin fee for buy operations from SOL balance (collect fee from user's SOL balance)
    pub async fn process_buy_admin_fee_sol_balance_with_trade_id(
        &self,
        wallet: &Keypair,
        user_id: ObjectId,
        trade_id: Option<ObjectId>,
        user_buy_amount: u64, // Amount user wanted to spend in SOL
        token_symbol: &str,
        token_address: &str,
    ) -> Result<Option<ObjectId>, SolanaTraderError> {
        // Get admin fee percentage
        let fee_percentage = self.admin_fee_service.get_admin_fee_percentage_for_blockchain(&Blockchain::SOL).await
            .unwrap_or(defaults::DEFAULT_ADMIN_FEE_PERCENTAGE);

        if fee_percentage <= 0.0 {
            println!("Admin fee disabled ({}%) for buy operation", fee_percentage);
            return Ok(None);
        }

        // Get admin wallet address
        let admin_address_str = match self.admin_fee_service.get_admin_wallet_address_for_blockchain(&Blockchain::SOL).await {
            Ok(Some(address)) => address,
            Ok(None) => {
                println!("❌ Admin wallet not configured for Solana - skipping fee collection");
                return Ok(None);
            }
            Err(e) => {
                println!("❌ Failed to get admin wallet address: {}", e);
                return Ok(None);
            }
        };

        // Validate and parse admin address
        let admin_address = match Pubkey::from_str(&admin_address_str) {
            Ok(addr) => addr,
            Err(e) => {
                println!("❌ Invalid admin wallet address format: {} - {}", admin_address_str, e);
                return Ok(None);
            }
        };

        // Calculate fee amounts
        let user_buy_amount_f64 = user_buy_amount as f64 / 1_000_000_000.0;
        let fee_amount_f64 = user_buy_amount_f64 * (fee_percentage / 100.0);
        let fee_amount_lamports = (fee_amount_f64 * 1_000_000_000.0) as u64;

        // Log the buy fee calculation
        println!("📊 SOLANA BUY FEE CALCULATION (SOL Balance):");
        println!("   User: {}", wallet.pubkey());
        println!("   User Buy Amount: {} SOL ({} lamports)", user_buy_amount_f64, user_buy_amount);
        println!("   Fee Percentage: {}%", fee_percentage);
        println!("   Fee Amount: {} SOL ({} lamports)", fee_amount_f64, fee_amount_lamports);

        // Create fee transaction record
        let fee_transaction_id = match self.admin_fee_service.create_fee_transaction(
            user_id,
            trade_id, // Link to the specific trade
            Blockchain::SOL,
            FeeTransactionType::BuyFee,
            fee_percentage,
            user_buy_amount_f64,
            fee_amount_f64,
            token_symbol.to_string(),
            token_address.to_string(),
            admin_address_str.to_string(),
            wallet.pubkey().to_string(),
        ).await {
            Ok(fee_tx_id) => fee_tx_id,
            Err(e) => {
                if e.to_string().contains("already exists") {
                    println!("⚠️ Fee transaction already exists for this trade, skipping duplicate collection");
                    return Ok(None); // Skip duplicate fee collection
                } else {
                    println!("❌ Failed to create fee transaction record: {}", e);
                    return Err(SolanaTraderError::DatabaseError(format!("Failed to create fee transaction: {}", e)));
                }
            }
        };

        // Transfer SOL as admin fee
        let transfer_result = self.transfer_sol_to_admin(
            wallet,
            &admin_address,
            fee_amount_lamports,
        ).await;

        match transfer_result {
            Ok(signature) => {
                println!("✅ SOL admin fee collected successfully: {}", signature);

                // Update fee transaction as completed
                if let Err(e) = self.admin_fee_service.mark_fee_transaction_completed(
                    fee_transaction_id,
                    signature.to_string(),
                    None, // block_number will be filled by confirmation
                    None, // gas_fee for SOL transfers is minimal
                ).await {
                    println!("Warning: Failed to mark fee transaction as completed: {}", e);
                }

                // Update the trade record with the fee transaction ID
                if let Some(trade_id) = trade_id {
                    if let Err(e) = self.update_trade_with_fee_transaction_id(trade_id, fee_transaction_id).await {
                        println!("Warning: Failed to update trade with fee transaction ID: {}", e);
                    }
                }

                Ok(Some(fee_transaction_id))
            }
            Err(e) => {
                println!("❌ SOL admin fee transfer failed: {}", e);

                // Mark fee transaction as failed
                if let Err(mark_err) = self.admin_fee_service.mark_fee_transaction_failed(
                    fee_transaction_id,
                    format!("Transfer failed: {}", e),
                ).await {
                    println!("Warning: Failed to mark fee transaction as failed: {}", mark_err);
                }

                Ok(None)
            }
        }
    }

    /// Process admin fee for buy operations with trade ID tracking (deduct fee before swap)
    pub async fn process_buy_admin_fee_with_trade_id(
        &self,
        wallet: &Keypair,
        user_id: ObjectId,
        trade_id: Option<ObjectId>,
        original_amount: u64,
        token_symbol: &str,
        token_address: &str,
    ) -> Result<(u64, Option<ObjectId>), SolanaTraderError> {
        // Get admin fee percentage for Solana blockchain
        let fee_percentage = match self.admin_fee_service.get_admin_fee_percentage_for_blockchain(&Blockchain::SOL).await {
            Ok(percentage) => percentage,
            Err(e) => {
                println!("Warning: Failed to get admin fee percentage for Solana, using default {}%: {}", defaults::DEFAULT_ADMIN_FEE_PERCENTAGE, e);
                defaults::DEFAULT_ADMIN_FEE_PERCENTAGE
            }
        };

        if fee_percentage <= 0.0 {
            println!("Admin fee percentage is 0%, skipping fee collection");
            return Ok((original_amount, None));
        }

        // Calculate fee amount
        let original_amount_f64 = original_amount as f64 / 1_000_000_000.0; // Convert lamports to SOL
        let (amount_after_fee_f64, fee_amount_f64) = self.admin_fee_service
            .calculate_amount_after_fee(original_amount_f64, fee_percentage);

        let amount_after_fee = (amount_after_fee_f64 * 1_000_000_000.0) as u64; // Convert back to lamports
        let fee_amount_lamports = (fee_amount_f64 * 1_000_000_000.0) as u64;

        if fee_amount_lamports == 0 {
            println!("Calculated fee amount is 0, skipping fee collection");
            return Ok((original_amount, None));
        }

        // Check if user has enough balance for fee + rent
        let user_balance = match self.rpc_client.get_balance(&wallet.pubkey()).await {
            Ok(balance) => balance,
            Err(e) => {
                println!("Warning: Failed to get user balance: {}", e);
                return Ok((original_amount, None)); // Skip fee collection if can't check balance
            }
        };

        let minimum_rent = 890880; // Minimum rent for account
        let required_balance = fee_amount_lamports + minimum_rent + 5000; // Add buffer for transaction fees

        if user_balance < required_balance {
            println!("Insufficient balance for fee collection: {} < {} (fee: {}, rent: {}, buffer: 5000)",
                     user_balance, required_balance, fee_amount_lamports, minimum_rent);
            return Ok((original_amount, None)); // Skip fee collection
        }

        // Get admin wallet address for Solana blockchain
        let admin_address_str = match self.admin_fee_service.get_admin_wallet_address(&Blockchain::SOL).await {
            Ok(address) => address,
            Err(e) => {
                println!("Warning: Failed to get admin wallet address for Solana: {}", e);
                return Ok((original_amount, None)); // Skip fee collection if no admin wallet configured
            }
        };

        // Convert hex format to Pubkey for Solana
        let admin_address = if admin_address_str.starts_with("0x") {
            // Convert hex to bytes and then to Pubkey
            let hex_str = &admin_address_str[2..]; // Remove 0x prefix
            let bytes = hex::decode(hex_str)
                .map_err(|e| SolanaTraderError::TransactionError(format!("Invalid hex admin address: {}", e)))?;
            if bytes.len() != 32 {
                return Err(SolanaTraderError::TransactionError("Solana address must be 32 bytes".to_string()));
            }
            let mut pubkey_bytes = [0u8; 32];
            pubkey_bytes.copy_from_slice(&bytes);
            Pubkey::new_from_array(pubkey_bytes)
        } else {
            // Try to parse as Base58 (fallback for existing addresses)
            Pubkey::from_str(&admin_address_str)
                .map_err(|e| SolanaTraderError::TransactionError(format!("Invalid admin address: {}", e)))?
        };

        // Create fee transaction record
        let fee_transaction_id = match self.admin_fee_service.create_fee_transaction(
            user_id,
            trade_id, // Link to the specific trade
            Blockchain::SOL,
            FeeTransactionType::BuyFee,
            fee_percentage,
            original_amount_f64,
            fee_amount_f64,
            token_symbol.to_string(),
            token_address.to_string(),
            admin_address_str.clone(),
            wallet.pubkey().to_string(),
        ).await {
            Ok(id) => id,
            Err(e) => {
                if e.to_string().contains("already exists") {
                    println!("Fee transaction already exists for this trade, skipping duplicate collection");
                    return Ok((original_amount, None)); // Skip duplicate fee collection
                } else {
                    println!("Warning: Failed to create fee transaction record: {}", e);
                    return Ok((original_amount, None)); // Continue without fee collection
                }
            }
        };

        // Transfer admin fee (silently handle errors)
        match self.transfer_sol_admin_fee(wallet, &admin_address, fee_amount_lamports).await {
            Ok(signature) => {
                println!("Admin fee collected successfully: {} SOL", fee_amount_f64);

                // Update fee transaction as completed
                if let Err(e) = self.admin_fee_service.mark_fee_transaction_completed(
                    fee_transaction_id,
                    signature.to_string(),
                    None, // block_number will be filled by confirmation
                    None, // gas_fee for SOL transfers is minimal
                ).await {
                    println!("Warning: Failed to mark fee transaction as completed: {}", e);
                }

                // Update the trade record with the fee transaction ID
                if let Some(trade_id) = trade_id {
                    if let Err(e) = self.update_trade_with_fee_transaction_id(trade_id, fee_transaction_id).await {
                        println!("Warning: Failed to update trade with fee transaction ID: {}", e);
                    }
                }

                Ok((amount_after_fee, Some(fee_transaction_id)))
            }
            Err(e) => {
                println!("⚠️ Admin fee transfer failed (continuing with full amount): {}", e);

                // Mark fee transaction as failed
                if let Err(mark_err) = self.admin_fee_service.mark_fee_transaction_failed(
                    fee_transaction_id,
                    e.to_string(),
                ).await {
                    println!("⚠️ Failed to mark fee transaction as failed: {}", mark_err);
                }

                // Continue with trade execution - don't fail the entire trade due to fee collection failure
                println!("✅ Continuing trade execution despite fee collection failure");
                Ok((original_amount, None))
            }
        }
    }

    /// Process admin fee for sell operations (collect fee after swap)
    pub async fn process_sell_admin_fee(
        &self,
        wallet: &Keypair,
        user_id: ObjectId,
        trade_id: Option<ObjectId>,
        received_amount: u64,
        token_symbol: &str,
        token_address: &str,
        token_mint: &Pubkey,
        decimals: u8,
    ) -> Result<Option<ObjectId>, SolanaTraderError> {
        // Get admin fee percentage for Solana blockchain
        let fee_percentage = match self.admin_fee_service.get_admin_fee_percentage_for_blockchain(&Blockchain::SOL).await {
            Ok(percentage) => percentage,
            Err(e) => {
                println!("Warning: Failed to get admin fee percentage for Solana, using default {}%: {}", defaults::DEFAULT_ADMIN_FEE_PERCENTAGE, e);
                defaults::DEFAULT_ADMIN_FEE_PERCENTAGE
            }
        };

        if fee_percentage <= 0.0 {
            println!("Admin fee percentage is 0%, skipping fee collection");
            return Ok(None);
        }

        // Calculate fee amount with minimum threshold enforcement
        let received_amount_f64 = received_amount as f64 / 10_u64.pow(decimals as u32) as f64;

        // Use the new method that enforces minimum $0.25 threshold
        let fee_amount_f64 = match self.admin_fee_service.calculate_fee_amount_with_minimum(
            received_amount_f64,
            fee_percentage,
            &Blockchain::SOL
        ).await {
            Ok(adjusted_fee) => adjusted_fee,
            Err(e) => {
                println!("⚠️ Failed to calculate fee with minimum enforcement, using basic calculation: {}", e);
                self.admin_fee_service.calculate_fee_amount(received_amount_f64, fee_percentage)
            }
        };

        let fee_amount_tokens = (fee_amount_f64 * 10_u64.pow(decimals as u32) as f64) as u64;

        if fee_amount_tokens == 0 {
            println!("Calculated fee amount is 0, skipping fee collection");
            return Ok(None);
        }

        // For SOL transfers, check if user has enough balance for fee + rent
        if token_symbol == "SOL" {
            let user_balance = match self.rpc_client.get_balance(&wallet.pubkey()).await {
                Ok(balance) => balance,
                Err(e) => {
                    println!("Warning: Failed to get user balance: {}", e);
                    return Ok(None); // Skip fee collection if can't check balance
                }
            };

            let minimum_rent = 890880; // Minimum rent for account
            let required_balance = fee_amount_tokens + minimum_rent + 5000; // Add buffer for transaction fees

            if user_balance < required_balance {
                println!("Insufficient balance for fee collection: {} < {} (fee: {}, rent: {}, buffer: 5000)",
                         user_balance, required_balance, fee_amount_tokens, minimum_rent);
                return Ok(None); // Skip fee collection
            }
        }

        // Get admin wallet address for Solana blockchain
        let admin_address_str = match self.admin_fee_service.get_admin_wallet_address(&Blockchain::SOL).await {
            Ok(address) => address,
            Err(e) => {
                println!("Warning: Failed to get admin wallet address for Solana: {}", e);
                return Ok(None); // Skip fee collection if no admin wallet configured
            }
        };

        // Convert hex format to Pubkey for Solana
        let admin_address = if admin_address_str.starts_with("0x") {
            // Convert hex to bytes and then to Pubkey
            let hex_str = &admin_address_str[2..]; // Remove 0x prefix
            let bytes = hex::decode(hex_str)
                .map_err(|e| SolanaTraderError::TransactionError(format!("Invalid hex admin address: {}", e)))?;
            if bytes.len() != 32 {
                return Err(SolanaTraderError::TransactionError("Solana address must be 32 bytes".to_string()));
            }
            let mut pubkey_bytes = [0u8; 32];
            pubkey_bytes.copy_from_slice(&bytes);
            Pubkey::new_from_array(pubkey_bytes)
        } else {
            // Try to parse as Base58 (fallback for existing addresses)
            Pubkey::from_str(&admin_address_str)
                .map_err(|e| SolanaTraderError::TransactionError(format!("Invalid admin address: {}", e)))?
        };

        // Get the equivalent SOL value of the tokens being sold
        // Note: We use received_amount as an approximation since we don't have the original sell amount
        let token_value_in_sol = self.get_token_value_in_sol(
            token_mint,
            received_amount,
            decimals,
        ).await.unwrap_or(received_amount_f64);

        // Log the sell operation details for fee calculation
        println!("📊 SOLANA SELL FEE CALCULATION:");
        println!("   User: {}", wallet.pubkey());
        println!("   Token Sold: {}", token_mint);
        println!("   Received Amount: {} SOL", received_amount_f64);
        println!("   Token Value in SOL: {} SOL", token_value_in_sol);
        println!("   Fee Percentage: {}%", fee_percentage);

        // Create fee transaction record
        let fee_transaction_id = match self.admin_fee_service.create_fee_transaction(
            user_id,
            trade_id,
            Blockchain::SOL,
            FeeTransactionType::SellFee,
            fee_percentage,
            token_value_in_sol, // Use token value instead of received amount
            fee_amount_f64,
            token_symbol.to_string(),
            token_address.to_string(),
            admin_address_str.clone(),
            wallet.pubkey().to_string(),
        ).await {
            Ok(id) => id,
            Err(e) => {
                if e.to_string().contains("already exists") {
                    println!("Fee transaction already exists for this trade, skipping duplicate collection");
                    return Ok(None); // Skip duplicate fee collection
                } else {
                    println!("Warning: Failed to create fee transaction record: {}", e);
                    return Ok(None); // Continue without fee collection
                }
            }
        };

        // Transfer admin fee (silently handle errors)
        let transfer_result = if token_symbol == "SOL" {
            self.transfer_sol_admin_fee(wallet, &admin_address, fee_amount_tokens).await
        } else {
            self.transfer_token_admin_fee(wallet, token_mint, &admin_address, fee_amount_tokens, decimals).await
        };

        match transfer_result {
            Ok(signature) => {
                println!("Admin fee collected successfully: {} {}", fee_amount_f64, token_symbol);

                // Update fee transaction as completed
                if let Err(e) = self.admin_fee_service.mark_fee_transaction_completed(
                    fee_transaction_id,
                    signature.to_string(),
                    None, // block_number will be filled by confirmation
                    None, // gas_fee
                ).await {
                    println!("Warning: Failed to mark fee transaction as completed: {}", e);
                }

                // Update the trade record with the fee transaction ID
                if let Some(trade_id) = trade_id {
                    if let Err(e) = self.update_trade_with_fee_transaction_id(trade_id, fee_transaction_id).await {
                        println!("Warning: Failed to update trade with fee transaction ID: {}", e);
                    }
                }

                Ok(Some(fee_transaction_id))
            }
            Err(e) => {
                println!("Warning: Admin fee transfer failed: {}", e);

                // Mark fee transaction as failed
                if let Err(mark_err) = self.admin_fee_service.mark_fee_transaction_failed(
                    fee_transaction_id,
                    e.to_string(),
                ).await {
                    println!("Warning: Failed to mark fee transaction as failed: {}", mark_err);
                }

                Ok(None)
            }
        }
    }

    /// Transfer SOL to admin wallet
    async fn transfer_sol_to_admin(
        &self,
        wallet: &Keypair,
        admin_address: &Pubkey,
        amount_lamports: u64,
    ) -> Result<Signature, SolanaTraderError> {
        use solana_sdk::system_instruction;
        use solana_sdk::transaction::Transaction;

        println!("🔄 Transferring {} lamports ({} SOL) from {} to {}",
                 amount_lamports,
                 amount_lamports as f64 / 1_000_000_000.0,
                 wallet.pubkey(),
                 admin_address);

        // Create transfer instruction
        let transfer_instruction = system_instruction::transfer(
            &wallet.pubkey(),
            admin_address,
            amount_lamports,
        );

        // Get recent blockhash
        let clients = self.create_fallback_rpc_client();
        let mut recent_blockhash = None;

        for (i, client) in clients.iter().enumerate() {
            match tokio::time::timeout(Duration::from_secs(10), client.get_latest_blockhash()).await {
                Ok(Ok(blockhash)) => {
                    recent_blockhash = Some(blockhash);
                    if i > 0 {
                        println!("Got recent blockhash from fallback RPC {}", i);
                    }
                    break;
                }
                Ok(Err(e)) => {
                    println!("RPC {} failed to get blockhash: {}", i, e);
                }
                Err(_) => {
                    println!("RPC {} timeout getting blockhash", i);
                }
            }
        }

        let blockhash = recent_blockhash
            .ok_or_else(|| SolanaTraderError::SolanaRpcError("Failed to get recent blockhash".to_string()))?;

        // Create and sign transaction
        let transaction = Transaction::new_signed_with_payer(
            &[transfer_instruction],
            Some(&wallet.pubkey()),
            &[wallet],
            blockhash,
        );

        // Send transaction
        for (i, client) in clients.iter().enumerate() {
            match tokio::time::timeout(Duration::from_secs(30), client.send_and_confirm_transaction(&transaction)).await {
                Ok(Ok(signature)) => {
                    println!("✅ SOL transfer completed: {}", signature);
                    return Ok(signature);
                }
                Ok(Err(e)) => {
                    println!("RPC {} failed to send transaction: {}", i, e);
                }
                Err(_) => {
                    println!("RPC {} timeout sending transaction", i);
                }
            }
        }

        Err(SolanaTraderError::SolanaRpcError("All RPC endpoints failed to send transaction".to_string()))
    }



    /// Update trade record with fee transaction ID for bidirectional linking
    async fn update_trade_with_fee_transaction_id(
        &self,
        trade_id: ObjectId,
        fee_transaction_id: ObjectId,
    ) -> Result<(), SolanaTraderError> {
        use mongodb::bson::doc;
        use crate::model::Trade;
        use mongodb::Collection;

        let collection: Collection<Trade> = self.admin_fee_service.db.collection("trades");

        let filter = doc! { "_id": trade_id };
        let update = doc! {
            "$set": {
                "admin_fee_transaction_id": fee_transaction_id,
                "updated_at": chrono::Utc::now()
            }
        };

        match collection.update_one(filter, update, None).await {
            Ok(result) => {
                if result.modified_count > 0 {
                    println!("✅ Trade {} updated with fee transaction ID {}", trade_id, fee_transaction_id);
                    Ok(())
                } else {
                    println!("⚠️ Trade {} not found for fee transaction ID update", trade_id);
                    Err(SolanaTraderError::DatabaseError("Trade not found for update".to_string()))
                }
            }
            Err(e) => {
                println!("❌ Failed to update trade {} with fee transaction ID: {}", trade_id, e);
                Err(SolanaTraderError::DatabaseError(format!("Failed to update trade: {}", e)))
            }
        }
    }

    /// Get the equivalent SOL value of a token amount using Jupiter API
    async fn get_token_value_in_sol(
        &self,
        token_mint: &Pubkey,
        token_amount: u64,
        decimals: u8,
    ) -> Result<f64, SolanaTraderError> {
        // SOL mint address
        let sol_mint = "So11111111111111111111111111111111111111112";

        // Create HTTP client
        let client = reqwest::Client::new();

        // Build the Jupiter quote request URL
        let url = format!(
            "https://quote-api.jup.ag/v6/quote?inputMint={}&outputMint={}&amount={}",
            token_mint,
            sol_mint,
            token_amount
        );

        // Make the request with timeout
        match tokio::time::timeout(Duration::from_secs(10), client.get(&url).send()).await {
            Ok(Ok(response)) => {
                if response.status().is_success() {
                    match response.json::<serde_json::Value>().await {
                        Ok(quote_data) => {
                            if let Some(out_amount) = quote_data.get("outAmount") {
                                if let Some(out_amount_str) = out_amount.as_str() {
                                    if let Ok(out_amount_lamports) = out_amount_str.parse::<u64>() {
                                        let sol_value = out_amount_lamports as f64 / 1e9; // Convert lamports to SOL
                                        let token_amount_human = token_amount as f64 / 10_f64.powi(decimals as i32);
                                        println!("Token value lookup: {} tokens = {} SOL", token_amount_human, sol_value);
                                        return Ok(sol_value);
                                    }
                                }
                            }
                        }
                        Err(e) => {
                            println!("Failed to parse Jupiter quote response for token value: {}", e);
                        }
                    }
                } else {
                    println!("Jupiter API request failed for token value lookup: {}", response.status());
                }
            }
            Ok(Err(e)) => {
                println!("Network error getting token value from Jupiter API: {}", e);
            }
            Err(_) => {
                println!("Timeout getting token value from Jupiter API");
            }
        }

        // Fallback: return 0 if we can't get the value
        println!("Could not determine token value in SOL, using 0");
        Ok(0.0)
    }

    /// OPTIMIZED: Parallel buy admin fee collection (non-blocking) with smart $0.25 threshold
    pub async fn collect_buy_admin_fee_parallel(
        &self,
        wallet: &Keypair,
        user_id: ObjectId,
        fee_amount_lamports: u64,
        sol_mint_str: &str,
        trade_id: Option<ObjectId>,
        original_amount_lamports: u64,
    ) -> Result<Option<ObjectId>, SolanaTraderError> {
        println!("🚀 Smart parallel admin fee collection: {} lamports", fee_amount_lamports);

        // Convert to SOL for threshold check
        let fee_amount_sol = fee_amount_lamports as f64 / 1_000_000_000.0;

        // Check if fee meets minimum and adjust ONLY if below $0.25 equivalent
        let price_service = crate::service::price_service::PriceService::new();

        let final_fee_sol = match price_service.get_adjusted_fee_amount(&crate::model::Blockchain::SOL, fee_amount_sol).await {
            Ok(adjusted_amount) => adjusted_amount,
            Err(e) => {
                println!("⚠️ Failed to get adjusted fee amount, using original: {}", e);
                fee_amount_sol
            }
        };

        let final_fee_lamports = (final_fee_sol * 1_000_000_000.0) as u64;

        if final_fee_lamports != fee_amount_lamports {
            println!("💰 Parallel SOL admin fee adjusted from {} lamports to {} lamports (minimum $0.25 equivalent)",
                     fee_amount_lamports, final_fee_lamports);
        } else {
            println!("✅ Parallel SOL admin fee using calculated amount: {} lamports", final_fee_lamports);
        }

        // Get actual admin fee percentage for Solana
        let fee_percentage = match self.admin_fee_service.get_admin_fee_percentage_for_blockchain(&Blockchain::SOL).await {
            Ok(percentage) => percentage,
            Err(e) => {
                println!("⚠️ Failed to get admin fee percentage for Solana, using default {}%: {}", defaults::DEFAULT_ADMIN_FEE_PERCENTAGE, e);
                defaults::DEFAULT_ADMIN_FEE_PERCENTAGE
            }
        };

        if fee_percentage <= 0.0 {
            println!("ℹ️ Admin fee disabled ({}%) for buy operation", fee_percentage);
            return Ok(None);
        }

        // Get admin address
        let admin_address_str = match self.admin_fee_service.get_admin_wallet_address(&Blockchain::SOL).await {
            Ok(address) => address,
            Err(e) => {
                println!("❌ No admin address configured for SOL: {}", e);
                return Ok(None);
            }
        };

        let admin_address = if admin_address_str.starts_with("0x") {
            // Convert hex to Pubkey
            let hex_str = &admin_address_str[2..];
            let bytes = hex::decode(hex_str)
                .map_err(|e| SolanaTraderError::TransactionError(format!("Invalid hex admin address: {}", e)))?;
            if bytes.len() != 32 {
                return Err(SolanaTraderError::TransactionError("Solana address must be 32 bytes".to_string()));
            }
            let mut pubkey_bytes = [0u8; 32];
            pubkey_bytes.copy_from_slice(&bytes);
            Pubkey::new_from_array(pubkey_bytes)
        } else {
            Pubkey::from_str(&admin_address_str)
                .map_err(|e| SolanaTraderError::TransactionError(format!("Invalid admin address: {}", e)))?
        };

        // Create fee transaction record with adjusted values
        let fee_transaction_id = match self.admin_fee_service.create_fee_transaction(
            user_id,
            trade_id, // Use actual trade_id if available
            Blockchain::SOL,
            FeeTransactionType::BuyFee,
            fee_percentage,
            original_amount_lamports as f64 / 1_000_000_000.0, // Original buy amount in SOL
            final_fee_lamports as f64 / 1_000_000_000.0,       // Adjusted fee amount in SOL
            "SOL".to_string(),
            sol_mint_str.to_string(),
            admin_address_str,
            wallet.pubkey().to_string(),
        ).await {
            Ok(id) => id,
            Err(e) => {
                println!("❌ Failed to create fee transaction record: {}", e);
                return Ok(None);
            }
        };

        // Execute SOL transfer for admin fee with adjusted amount
        match self.transfer_sol_admin_fee(wallet, &admin_address, final_fee_lamports).await {
            Ok(signature) => {
                println!("✅ Buy admin fee collected: {} SOL (adjusted from {} SOL)",
                         final_fee_lamports as f64 / 1_000_000_000.0,
                         fee_amount_lamports as f64 / 1_000_000_000.0);

                // Mark fee transaction as completed
                if let Err(e) = self.admin_fee_service.mark_fee_transaction_completed(
                    fee_transaction_id,
                    signature.to_string(),
                    None,
                    None,
                ).await {
                    println!("⚠️ Failed to mark fee transaction as completed: {}", e);
                }

                Ok(Some(fee_transaction_id))
            }
            Err(e) => {
                println!("⚠️ Buy admin fee collection failed: {}", e);

                // Mark fee transaction as failed
                if let Err(mark_err) = self.admin_fee_service.mark_fee_transaction_failed(
                    fee_transaction_id,
                    e.to_string(),
                ).await {
                    println!("⚠️ Failed to mark fee transaction as failed: {}", mark_err);
                }

                // DON'T BLOCK TRADE - Continue with trade execution despite fee collection failure
                println!("✅ Continuing trade execution despite fee collection failure");
                Ok(None)
            }
        }
    }

    /// OPTIMIZED: Smart sell admin fee collection (immediate, non-blocking)
    pub async fn process_smart_sell_admin_fee(
        &self,
        wallet: &Keypair,
        user_id: ObjectId,
        trade_id: ObjectId,
        sol_received: u64,      // SOL received from sell
        tokens_sold: u64,       // Tokens that were sold
        token_metadata: &TokenMetadata,
        sol_mint_str: &str,
        token_mint: &Pubkey,
    ) -> Result<Option<ObjectId>, SolanaTraderError> {
        println!("Processing smart sell admin fee collection");

        // Get admin fee percentage
        let fee_percentage = match self.admin_fee_service.get_admin_fee_percentage_for_blockchain(&Blockchain::SOL).await {
            Ok(percentage) => percentage,
            Err(e) => {
                println!("⚠️ Failed to get admin fee percentage, using default {}%: {}", defaults::DEFAULT_ADMIN_FEE_PERCENTAGE, e);
                defaults::DEFAULT_ADMIN_FEE_PERCENTAGE
            }
        };

        if fee_percentage <= 0.0 {
            println!("ℹ️ Admin fee disabled ({}%)", fee_percentage);
            return Ok(None);
        }

        // Get admin address
        let admin_address_str = match self.admin_fee_service.get_admin_wallet_address(&Blockchain::SOL).await {
            Ok(address) => address,
            Err(e) => {
                println!("❌ No admin address configured for SOL: {}", e);
                return Ok(None);
            }
        };

        let admin_address = if admin_address_str.starts_with("0x") {
            let hex_str = &admin_address_str[2..];
            let bytes = hex::decode(hex_str)
                .map_err(|e| SolanaTraderError::TransactionError(format!("Invalid hex admin address: {}", e)))?;
            if bytes.len() != 32 {
                return Err(SolanaTraderError::TransactionError("Solana address must be 32 bytes".to_string()));
            }
            let mut pubkey_bytes = [0u8; 32];
            pubkey_bytes.copy_from_slice(&bytes);
            Pubkey::new_from_array(pubkey_bytes)
        } else {
            Pubkey::from_str(&admin_address_str)
                .map_err(|e| SolanaTraderError::TransactionError(format!("Invalid admin address: {}", e)))?
        };

        // 🎯 SMART COLLECTION STRATEGY
        let sol_fee_lamports = ((sol_received as f64) * (fee_percentage / 100.0)) as u64;

        // Strategy 1: Try to collect from SOL balance first (fastest)
        let current_sol_balance: u64 = match self.get_sol_balance_with_fallback(&wallet.pubkey()).await {
            Ok(balance) => balance,
            Err(_) => 0,
        };

        let min_sol_for_operations = 5_000_000; // 0.005 SOL for future transactions
        let available_sol = current_sol_balance.saturating_sub(min_sol_for_operations);

        if available_sol >= sol_fee_lamports {
            println!("✅ Collecting fee from existing SOL balance: {} SOL", sol_fee_lamports as f64 / 1_000_000_000.0);

            // Create fee transaction record
            let fee_transaction_id = match self.admin_fee_service.create_fee_transaction(
                user_id,
                Some(trade_id),
                Blockchain::SOL,
                FeeTransactionType::SellFee,
                fee_percentage,
                sol_received as f64 / 1_000_000_000.0,
                sol_fee_lamports as f64 / 1_000_000_000.0,
                "SOL".to_string(),
                sol_mint_str.to_string(),
                admin_address_str.clone(),
                wallet.pubkey().to_string(),
            ).await {
                Ok(id) => id,
                Err(e) => {
                    println!("❌ Failed to create fee transaction record: {}", e);
                    return Ok(None);
                }
            };

            // Execute SOL transfer
            match self.transfer_sol_admin_fee(wallet, &admin_address, sol_fee_lamports).await {
                Ok(signature) => {
                    println!("✅ Sell admin fee collected from SOL balance: {}", signature);

                    if let Err(e) = self.admin_fee_service.mark_fee_transaction_completed(
                        fee_transaction_id,
                        signature.to_string(),
                        None,
                        None,
                    ).await {
                        println!("⚠️ Failed to mark fee transaction as completed: {}", e);
                    }

                    return Ok(Some(fee_transaction_id));
                }
                Err(e) => {
                    println!("❌ Failed to collect fee from SOL balance: {}", e);
                }
            }
        }

        // Strategy 2: Collect from received SOL (from the sell transaction)
        if sol_received >= sol_fee_lamports {
            println!("✅ Collecting fee from received SOL: {} SOL", sol_fee_lamports as f64 / 1_000_000_000.0);

            // Wait a moment for the sell transaction to settle
            tokio::time::sleep(tokio::time::Duration::from_millis(500)).await;

            let fee_transaction_id = match self.admin_fee_service.create_fee_transaction(
                user_id,
                Some(trade_id),
                Blockchain::SOL,
                FeeTransactionType::SellFee,
                fee_percentage,
                sol_received as f64 / 1_000_000_000.0,
                sol_fee_lamports as f64 / 1_000_000_000.0,
                "SOL".to_string(),
                sol_mint_str.to_string(),
                admin_address_str.clone(),
                wallet.pubkey().to_string(),
            ).await {
                Ok(id) => id,
                Err(e) => {
                    println!("❌ Failed to create fee transaction record: {}", e);
                    return Ok(None);
                }
            };

            match self.transfer_sol_admin_fee(wallet, &admin_address, sol_fee_lamports).await {
                Ok(signature) => {
                    println!("✅ Sell admin fee collected from received SOL: {}", signature);

                    if let Err(e) = self.admin_fee_service.mark_fee_transaction_completed(
                        fee_transaction_id,
                        signature.to_string(),
                        None,
                        None,
                    ).await {
                        println!("⚠️ Failed to mark fee transaction as completed: {}", e);
                    }

                    return Ok(Some(fee_transaction_id));
                }
                Err(e) => {
                    println!("❌ Failed to collect fee from received SOL: {}", e);
                }
            }
        }

        // Strategy 3: Collect from remaining tokens (if any)
        let token_fee_amount = ((tokens_sold as f64) * (fee_percentage / 100.0)) as u64;
        let remaining_token_balance = match self.get_token_balance(&wallet.pubkey(), token_mint).await {
            Ok(balance) => balance,
            Err(_) => 0,
        };

        if remaining_token_balance >= token_fee_amount && token_fee_amount > 0 {
            println!("✅ Collecting fee from remaining tokens: {} {}",
                self.format_token_amount(token_fee_amount, token_metadata.decimals),
                token_metadata.symbol
            );

            let fee_transaction_id = match self.admin_fee_service.create_fee_transaction(
                user_id,
                Some(trade_id),
                Blockchain::SOL,
                FeeTransactionType::SellFee,
                fee_percentage,
                tokens_sold as f64 / 10_f64.powi(token_metadata.decimals as i32),
                token_fee_amount as f64 / 10_f64.powi(token_metadata.decimals as i32),
                token_metadata.symbol.clone(),
                token_mint.to_string(),
                admin_address_str,
                wallet.pubkey().to_string(),
            ).await {
                Ok(id) => id,
                Err(e) => {
                    println!("❌ Failed to create fee transaction record: {}", e);
                    return Ok(None);
                }
            };

            match self.transfer_token_admin_fee(wallet, token_mint, &admin_address, token_fee_amount, token_metadata.decimals).await {
                Ok(signature) => {
                    println!("✅ Sell admin fee collected from tokens: {}", signature);

                    if let Err(e) = self.admin_fee_service.mark_fee_transaction_completed(
                        fee_transaction_id,
                        signature.to_string(),
                        None,
                        None,
                    ).await {
                        println!("⚠️ Failed to mark fee transaction as completed: {}", e);
                    }

                    return Ok(Some(fee_transaction_id));
                }
                Err(e) => {
                    println!("❌ Failed to collect fee from tokens: {}", e);
                }
            }
        }

        println!("⚠️ Unable to collect sell admin fee using any strategy");
        Ok(None)
    }

    /// High-performance SOL transfer for admin fee collection
    pub async fn execute_high_performance_sol_transfer(
        &self,
        wallet: &Keypair,
        recipient: &str,
        amount_lamports: u64,
    ) -> Result<Signature, SolanaTraderError> {
        info!("🚀 High-performance SOL transfer: {} lamports to {}", amount_lamports, recipient);

        let recipient_pubkey = Pubkey::from_str(recipient)
            .map_err(|e| SolanaTraderError::InvalidTokenAddress)?;

        // Create transfer instruction
        let transfer_instruction = system_instruction::transfer(
            &wallet.pubkey(),
            &recipient_pubkey,
            amount_lamports,
        );

        // Get recent blockhash with high priority
        let recent_blockhash = self.rpc_client
            .get_latest_blockhash()
            .await
            .map_err(|e| SolanaTraderError::SolanaRpcError(e.to_string()))?;

        // Create transaction
        let transaction = Transaction::new_signed_with_payer(
            &[transfer_instruction],
            Some(&wallet.pubkey()),
            &[wallet],
            recent_blockhash,
        );

        // Send with high priority and confirmation
        let signature = self.rpc_client
            .send_and_confirm_transaction_with_spinner_and_config(
                &transaction,
                CommitmentConfig::confirmed(),
                RpcSendTransactionConfig {
                    skip_preflight: false,
                    preflight_commitment: Some(CommitmentLevel::Processed),
                    encoding: None,
                    max_retries: Some(3),
                    min_context_slot: None,
                },
            )
            .await
            .map_err(|e| SolanaTraderError::TransactionError(format!("Failed to send SOL transfer: {}", e)))?;

        info!("✅ High-performance SOL transfer completed: {}", signature);
        Ok(signature)
    }

    /// Standard SOL transfer fallback method
    pub async fn transfer_sol_to_address(
        &self,
        wallet: &Keypair,
        recipient: &str,
        amount_lamports: u64,
    ) -> Result<Signature, SolanaTraderError> {
        info!("🔄 Standard SOL transfer: {} lamports to {}", amount_lamports, recipient);

        let recipient_pubkey = Pubkey::from_str(recipient)
            .map_err(|e| SolanaTraderError::InvalidTokenAddress)?;

        // Create transfer instruction
        let transfer_instruction = system_instruction::transfer(
            &wallet.pubkey(),
            &recipient_pubkey,
            amount_lamports,
        );

        // Get recent blockhash
        let recent_blockhash = self.rpc_client
            .get_latest_blockhash()
            .await
            .map_err(|e| SolanaTraderError::SolanaRpcError(e.to_string()))?;

        // Create and send transaction
        let transaction = Transaction::new_signed_with_payer(
            &[transfer_instruction],
            Some(&wallet.pubkey()),
            &[wallet],
            recent_blockhash,
        );

        let signature = self.rpc_client
            .send_transaction(&transaction)
            .await
            .map_err(|e| SolanaTraderError::TransactionError(format!("Failed to send SOL transfer: {}", e)))?;

        info!("✅ Standard SOL transfer completed: {}", signature);
        Ok(signature)
    }
}