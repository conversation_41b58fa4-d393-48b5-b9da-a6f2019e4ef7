import React, { useState, useEffect } from 'react';
import { Card } from './ui/Card';
import { adminApi } from '../services/adminApi';
import type { FeeStatistics as FeeStatsType } from '../services/adminApi';
import { formatCurrency } from '../utils/formatters';

interface FeeStatisticsProps {
  className?: string;
}

export function FeeStatistics({ className = '' }: FeeStatisticsProps) {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [feeStats, setFeeStats] = useState<FeeStatsType[]>([]);
  const [totalFeesUsd, setTotalFeesUsd] = useState(0);

  useEffect(() => {
    fetchFeeStatistics();
  }, []);

  const fetchFeeStatistics = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await adminApi.getFeeStatistics();
      setFeeStats(response.fee_statistics);
      setTotalFeesUsd(response.total_fees_usd);
    } catch (err: any) {
      console.error('Failed to fetch fee statistics:', err);
      setError(err.response?.data?.error || 'Failed to load fee statistics');
    } finally {
      setLoading(false);
    }
  };

  const getBlockchainIcon = (blockchain: string) => {
    switch (blockchain.toUpperCase()) {
      case 'SOL':
        return '◎';
      case 'ETH':
        return 'Ξ';
      case 'BSC':
        return '🔶';
      case 'BASE':
        return '🔵';
      default:
        return '⚡';
    }
  };

  const getBlockchainColor = (blockchain: string) => {
    switch (blockchain.toUpperCase()) {
      case 'SOL':
        return 'from-purple-500 to-purple-600';
      case 'ETH':
        return 'from-blue-500 to-blue-600';
      case 'BSC':
        return 'from-yellow-500 to-yellow-600';
      case 'BASE':
        return 'from-blue-400 to-blue-500';
      default:
        return 'from-gray-500 to-gray-600';
    }
  };

  if (loading) {
    return (
      <div className={`space-y-6 ${className}`}>
        <div className="flex items-center justify-between">
          <h2 className="text-xl font-semibold text-white">Fee Statistics</h2>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[1, 2, 3].map((i) => (
            <Card key={i} className="p-6 animate-pulse">
              <div className="h-4 bg-gray-700 rounded mb-4"></div>
              <div className="space-y-3">
                <div className="h-3 bg-gray-700 rounded"></div>
                <div className="h-3 bg-gray-700 rounded"></div>
                <div className="h-3 bg-gray-700 rounded"></div>
              </div>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`space-y-6 ${className}`}>
        <div className="flex items-center justify-between">
          <h2 className="text-xl font-semibold text-white">Fee Statistics</h2>
        </div>
        <Card className="p-6">
          <div className="text-center">
            <div className="text-red-400 mb-2">⚠️</div>
            <h3 className="text-lg font-medium text-white mb-2">Failed to Load Fee Statistics</h3>
            <p className="text-gray-400 mb-4">{error}</p>
            <button
              onClick={fetchFeeStatistics}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              Try Again
            </button>
          </div>
        </Card>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold text-white">Fee Statistics</h2>
        <div className="text-sm text-gray-400">
          Total Collected: <span className="text-green-400 font-semibold">{formatCurrency(totalFeesUsd)}</span>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {feeStats.map((stats) => (
          <Card key={stats.blockchain} className="p-6 relative overflow-hidden">
            {/* Background gradient */}
            <div className={`absolute inset-0 bg-gradient-to-br ${getBlockchainColor(stats.blockchain)} opacity-5`}></div>
            
            <div className="relative">
              {/* Header */}
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-2">
                  <span className="text-2xl">{getBlockchainIcon(stats.blockchain)}</span>
                  <div>
                    <h3 className="text-lg font-semibold text-white">{stats.blockchain}</h3>
                    <p className="text-sm text-gray-400">{stats.native_symbol}</p>
                  </div>
                </div>
              </div>

              {/* Fee Statistics */}
              <div className="space-y-4">
                {/* Today's Fees */}
                <div className="bg-gray-800/50 rounded-lg p-3">
                  <div className="flex justify-between items-center mb-1">
                    <span className="text-sm text-gray-400">Today's Fees</span>
                    <span className="text-xs text-gray-500">Last 24 hours</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-lg font-semibold text-white">
                      {formatCurrency(stats.today_fees_usd)}
                    </span>
                    <span className="text-sm text-gray-400">
                      {stats.today_fees_native.toFixed(6)} {stats.native_symbol}
                    </span>
                  </div>
                </div>

                {/* Weekly Fees */}
                <div className="bg-gray-800/50 rounded-lg p-3">
                  <div className="flex justify-between items-center mb-1">
                    <span className="text-sm text-gray-400">Weekly Fees</span>
                    <span className="text-xs text-gray-500">Last 7 days</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-lg font-semibold text-white">
                      {formatCurrency(stats.weekly_fees_usd)}
                    </span>
                    <span className="text-sm text-gray-400">
                      {stats.weekly_fees_native.toFixed(6)} {stats.native_symbol}
                    </span>
                  </div>
                </div>

                {/* Monthly Fees */}
                <div className="bg-gray-800/50 rounded-lg p-3">
                  <div className="flex justify-between items-center mb-1">
                    <span className="text-sm text-gray-400">Monthly Fees</span>
                    <span className="text-xs text-gray-500">Last 30 days</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-lg font-semibold text-white">
                      {formatCurrency(stats.monthly_fees_usd)}
                    </span>
                    <span className="text-sm text-gray-400">
                      {stats.monthly_fees_native.toFixed(6)} {stats.native_symbol}
                    </span>
                  </div>
                </div>

                {/* Total Fees */}
                <div className="bg-gradient-to-r from-green-900/20 to-green-800/20 rounded-lg p-3 border border-green-800/30">
                  <div className="flex justify-between items-center mb-1">
                    <span className="text-sm text-green-400">Total Collected</span>
                    <span className="text-xs text-green-500">All time</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-lg font-semibold text-green-400">
                      {formatCurrency(stats.total_fees_usd)}
                    </span>
                    <span className="text-sm text-green-500">
                      {stats.total_fees_native.toFixed(6)} {stats.native_symbol}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </Card>
        ))}
      </div>

      {feeStats.length === 0 && (
        <Card className="p-8">
          <div className="text-center">
            <div className="text-gray-400 mb-2">📊</div>
            <h3 className="text-lg font-medium text-white mb-2">No Fee Data Available</h3>
            <p className="text-gray-400">Fee statistics will appear here once transactions are processed.</p>
          </div>
        </Card>
      )}
    </div>
  );
}
