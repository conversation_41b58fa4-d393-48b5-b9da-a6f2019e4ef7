# EasyBot Scripts

This directory contains utility scripts for the EasyBot project.

## Available Scripts

### test-api-routes.sh

A comprehensive test script for validating API routes configuration.

**Usage:**
```bash
# Run all API route tests
./scripts/test-api-routes.sh

# Show current route configuration
./scripts/test-api-routes.sh config

# Show help
./scripts/test-api-routes.sh help
```

**Features:**
- Tests all configurable API endpoints
- Validates environment variable configuration
- Attempts authentication and tests protected routes
- Color-coded output for easy reading
- Loads configuration from `.env` file automatically

**Environment Variables:**
- `BASE_URL` - API base URL (default: http://localhost:3000)
- `ADMIN_USERNAME` - Admin username for testing (default: admin)
- `ADMIN_PASSWORD` - Admin password for testing (default: admin123)

**Example Output:**
```
[INFO] Testing: POST /api/auth/login - Admin Login
[SUCCESS] /api/auth/login responded with 200

[INFO] Testing: GET /api/bots - List Bots
[WARNING] /api/bots requires authentication (401) - Expected

[INFO] Testing: GET /api/admin/dashboard - Dashboard Data
[SUCCESS] /api/admin/dashboard responded with 200
```

## Running Scripts

Make sure scripts are executable:
```bash
chmod +x scripts/*.sh
```

Run from the project root directory:
```bash
./scripts/test-api-routes.sh
```

## Development

When adding new scripts:
1. Make them executable: `chmod +x script-name.sh`
2. Add proper error handling with `set -e`
3. Use colored output for better UX
4. Document usage in this README
5. Test on different environments

## Troubleshooting

**Permission Denied:**
```bash
chmod +x scripts/test-api-routes.sh
```

**Script Not Found:**
Make sure you're running from the project root directory.

**API Tests Failing:**
1. Ensure the backend server is running
2. Check the `.env` file for correct configuration
3. Verify the base URL is correct
4. Check if authentication is working

**Environment Variables Not Loading:**
Make sure your `.env` file is in the project root and properly formatted.
