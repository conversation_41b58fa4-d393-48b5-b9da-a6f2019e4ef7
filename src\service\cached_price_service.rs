use std::sync::Arc;
use std::time::Duration;
use mongodb::{bson::doc, Collection, Database};
use tokio::time::timeout;
use futures::stream::TryStreamExt;
use tracing::{info, warn, error, debug};
use anyhow::{Result, Context};

use crate::model::{CachedPrice, Blockchain, BotError};
use crate::service::{db_service::DbService, price_service::PriceService};

const DB_OPERATION_TIMEOUT: Duration = Duration::from_secs(10);
const PRICE_CACHE_TTL: u64 = 3600; // 1 hour in seconds
const BACKGROUND_UPDATE_INTERVAL: u64 = 3600; // 1 hour in seconds

#[derive(Debug, Clone)]
pub struct CachedPriceService {
    pub db: Database,
    pub price_service: Arc<PriceService>,
}

impl CachedPriceService {
    pub fn new() -> Self {
        Self {
            db: DbService::get_db(),
            price_service: Arc::new(PriceService::new()),
        }
    }

    /// Get USD value for a blockchain's native token amount using cached prices
    pub async fn get_usd_value(&self, blockchain: &Blockchain, amount: f64) -> Result<f64, BotError> {
        let native_symbol = self.get_native_symbol(blockchain);
        let price_usd = self.get_cached_price(blockchain).await?;
        Ok(amount * price_usd)
    }

    /// Get cached price for a blockchain, fetch if not available or stale
    pub async fn get_cached_price(&self, blockchain: &Blockchain) -> Result<f64, BotError> {
        let collection: Collection<CachedPrice> = self.db.collection("cached_prices");
        let native_symbol = self.get_native_symbol(blockchain);

        // Try to get cached price first
        let filter = doc! {
            "blockchain": format!("{:?}", blockchain),
            "native_symbol": &native_symbol
        };

        let cached_price_result = timeout(
            DB_OPERATION_TIMEOUT,
            collection.find_one(filter.clone(), None)
        ).await;

        match cached_price_result {
            Ok(Ok(Some(cached_price))) => {
                // Check if price is still fresh (less than 1 hour old)
                if !cached_price.is_stale(PRICE_CACHE_TTL) {
                    debug!("Using cached price for {:?}: ${:.4} (age: {}s)", 
                           blockchain, cached_price.price_usd, cached_price.age_seconds());
                    return Ok(cached_price.price_usd);
                } else {
                    info!("Cached price for {:?} is stale (age: {}s), fetching fresh price", 
                          blockchain, cached_price.age_seconds());
                }
            }
            Ok(Ok(None)) => {
                info!("No cached price found for {:?}, fetching fresh price", blockchain);
            }
            Ok(Err(e)) => {
                warn!("Database error getting cached price for {:?}: {}", blockchain, e);
            }
            Err(_) => {
                warn!("Timeout getting cached price for {:?}", blockchain);
            }
        }

        // Fetch fresh price and update cache
        self.fetch_and_cache_price(blockchain).await
    }

    /// Fetch fresh price from API and update cache
    async fn fetch_and_cache_price(&self, blockchain: &Blockchain) -> Result<f64, BotError> {
        let native_symbol = self.get_native_symbol(blockchain);
        
        // Fetch fresh price using the existing price service
        let fresh_price = self.price_service.get_native_token_price(blockchain).await
            .map_err(|e| BotError::external_api_error(format!("Failed to fetch price for {:?}: {}", blockchain, e)))?;

        info!("Fetched fresh price for {:?}: ${:.4}", blockchain, fresh_price);

        // Update cache in database
        if let Err(e) = self.update_cached_price(blockchain, &native_symbol, fresh_price).await {
            warn!("Failed to update cached price for {:?}: {}", blockchain, e);
            // Don't fail the request if cache update fails, just return the fresh price
        }

        Ok(fresh_price)
    }

    /// Update cached price in database
    async fn update_cached_price(&self, blockchain: &Blockchain, native_symbol: &str, price_usd: f64) -> Result<(), BotError> {
        let collection: Collection<CachedPrice> = self.db.collection("cached_prices");
        
        let filter = doc! {
            "blockchain": format!("{:?}", blockchain),
            "native_symbol": native_symbol
        };

        let now = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs();

        let update_doc = doc! {
            "$set": {
                "price_usd": price_usd,
                "last_updated": now as i64
            },
            "$setOnInsert": {
                "blockchain": format!("{:?}", blockchain),
                "native_symbol": native_symbol,
                "created_at": now as i64
            }
        };

        let options = mongodb::options::UpdateOptions::builder()
            .upsert(true)
            .build();

        timeout(
            DB_OPERATION_TIMEOUT,
            collection.update_one(filter, update_doc, options)
        ).await
        .map_err(|_| BotError::database_error("Timeout updating cached price".to_string()))?
        .map_err(|e| BotError::database_error(format!("Failed to update cached price: {}", e)))?;

        debug!("Updated cached price for {:?}: ${:.4}", blockchain, price_usd);
        Ok(())
    }

    /// Initialize cache with current prices for all supported blockchains
    pub async fn initialize_cache(&self) -> Result<(), BotError> {
        info!("Initializing price cache for all supported blockchains...");

        let blockchains = vec![
            Blockchain::SOL,
            Blockchain::ETH,
            Blockchain::BSC,
            Blockchain::BASE,
        ];

        for blockchain in blockchains {
            match self.fetch_and_cache_price(&blockchain).await {
                Ok(price) => {
                    info!("✅ Initialized cache for {:?}: ${:.4}", blockchain, price);
                }
                Err(e) => {
                    error!("❌ Failed to initialize cache for {:?}: {}", blockchain, e);
                    // Continue with other blockchains even if one fails
                }
            }
        }

        info!("Price cache initialization completed");
        Ok(())
    }

    /// Background task to update all cached prices every hour
    pub async fn start_background_price_updater(&self) {
        info!("Starting background price updater (interval: {}s)", BACKGROUND_UPDATE_INTERVAL);
        
        let mut interval = tokio::time::interval(Duration::from_secs(BACKGROUND_UPDATE_INTERVAL));
        let service = self.clone();

        tokio::spawn(async move {
            loop {
                interval.tick().await;
                
                info!("🔄 Background price update started");
                
                let blockchains = vec![
                    Blockchain::SOL,
                    Blockchain::ETH,
                    Blockchain::BSC,
                    Blockchain::BASE,
                ];

                for blockchain in blockchains {
                    match service.fetch_and_cache_price(&blockchain).await {
                        Ok(price) => {
                            debug!("✅ Updated background cache for {:?}: ${:.4}", blockchain, price);
                        }
                        Err(e) => {
                            warn!("⚠️ Failed to update background cache for {:?}: {}", blockchain, e);
                        }
                    }
                    
                    // Small delay between API calls to avoid rate limiting
                    tokio::time::sleep(Duration::from_millis(500)).await;
                }
                
                info!("🔄 Background price update completed");
            }
        });
    }

    /// Get all cached prices for debugging/monitoring
    pub async fn get_all_cached_prices(&self) -> Result<Vec<CachedPrice>, BotError> {
        let collection: Collection<CachedPrice> = self.db.collection("cached_prices");
        
        let cursor_result = timeout(
            DB_OPERATION_TIMEOUT,
            collection.find(doc! {}, None)
        ).await;

        match cursor_result {
            Ok(Ok(mut cursor)) => {
                let mut prices = Vec::new();
                while let Ok(Some(price)) = cursor.try_next().await {
                    prices.push(price);
                }
                Ok(prices)
            }
            Ok(Err(e)) => Err(BotError::database_error(format!("Failed to get cached prices: {}", e))),
            Err(_) => Err(BotError::database_error("Timeout getting cached prices".to_string())),
        }
    }

    /// Helper to get native symbol for blockchain
    fn get_native_symbol(&self, blockchain: &Blockchain) -> String {
        match blockchain {
            Blockchain::SOL => "SOL".to_string(),
            Blockchain::ETH => "ETH".to_string(),
            Blockchain::BSC => "BNB".to_string(),
            Blockchain::BASE => "ETH".to_string(),
        }
    }
}
