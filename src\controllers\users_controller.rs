use axum::{
    routing::{get, post, put, delete},
    Router,
    extract::{Json, Path, Query, State},
    http::{StatusCode, HeaderMap},
    response::IntoResponse,
};
use serde::{Deserialize, Serialize};
use mongodb::{
    bson::{doc, oid::ObjectId},
    options::{FindOptions, AggregateOptions},
    error::Error as MongoError,
};
use futures_util::TryStreamExt;
use std::{
    sync::Arc,
    collections::HashMap,
    time::{SystemTime, UNIX_EPOCH, Duration},
};
use tokio::time::timeout;
use tracing::{error, warn, info, debug};
use anyhow::{Result, Context, anyhow};
use crate::model::{User, BotError};
use crate::service::db_service::DbService;
use crate::config::AppConfig;
use crate::controllers::auth_controller::Claims;
use jsonwebtoken::{decode, DecodingKey, Validation, Algorithm};

// Production constants
const DB_OPERATION_TIMEOUT: Duration = Duration::from_secs(30);
const MAX_USERS_PER_PAGE: i64 = 100;
const DEFAULT_USERS_PER_PAGE: i64 = 20;
const MAX_SEARCH_LENGTH: usize = 100;

#[derive(Debug, thiserror::Error)]
pub enum UsersError {
    #[error("Database operation failed: {0}")]
    DatabaseError(#[from] MongoError),
    #[error("User not found: {0}")]
    UserNotFound(String),
    #[error("Invalid input: {0}")]
    InvalidInput(String),
    #[error("Authorization failed: {0}")]
    AuthorizationFailed(String),
    #[error("Operation timeout")]
    Timeout,
    #[error("Validation failed: {0}")]
    ValidationError(String),
}

#[derive(Debug, Serialize)]
pub struct UserResponse {
    pub id: String,
    pub chat_id: i64,
    pub first_name: String,
    pub last_name: Option<String>,
    pub username: Option<String>,
    pub current_blockchain: String,
    pub created_at: u64,
    pub last_seen: u64,
    pub total_transactions: i64,
    pub total_volume: f64,
    pub is_active: bool,
}

#[derive(Debug, Serialize)]
pub struct UserListResponse {
    pub users: Vec<UserResponse>,
    pub total: i64,
    pub page: i64,
    pub per_page: i64,
}

#[derive(Debug, Deserialize)]
pub struct UserQueryParams {
    pub page: Option<i64>,
    pub per_page: Option<i64>,
    pub search: Option<String>,
    pub blockchain: Option<String>,
    pub status: Option<String>,
    pub sort_by: Option<String>,
    pub sort_order: Option<String>,
}

#[derive(Debug, Deserialize)]
pub struct UpdateUserRequest {
    pub first_name: Option<String>,
    pub last_name: Option<String>,
    pub username: Option<String>,
    pub current_blockchain: Option<String>,
}

#[derive(Debug, Serialize)]
pub struct UserAnalytics {
    pub total_users: i64,
    pub active_users: i64,
    pub new_users_today: i64,
    pub new_users_week: i64,
    pub users_by_blockchain: HashMap<String, i64>,
    pub activity_stats: UserActivityStats,
}

#[derive(Debug, Serialize)]
pub struct UserActivityStats {
    pub daily_active_users: i64,
    pub weekly_active_users: i64,
    pub monthly_active_users: i64,
    pub average_session_time: f64,
}

pub struct UsersController {
    config: Arc<AppConfig>,
    jwt_secret: String,
}

impl UsersController {
    pub fn new(config: Arc<AppConfig>) -> Self {
        let jwt_secret = std::env::var("JWT_SECRET")
            .context("JWT_SECRET environment variable is required")
            .expect("Critical configuration missing");

        Self { config, jwt_secret }
    }

    /// Verify authentication header and extract claims
    async fn verify_auth_header(&self, headers: &HeaderMap) -> Option<Claims> {
        if let Some(auth_header) = headers.get("authorization") {
            if let Ok(auth_str) = auth_header.to_str() {
                if let Some(token) = auth_str.strip_prefix("Bearer ") {
                    let validation = Validation::new(Algorithm::HS256);
                    if let Ok(token_data) = decode::<Claims>(
                        token,
                        &DecodingKey::from_secret(self.jwt_secret.as_ref()),
                        &validation,
                    ) {
                        return Some(token_data.claims);
                    }
                }
            }
        }
        None
    }

    /// Validate query parameters
    fn validate_query_params(&self, params: &UserQueryParams) -> Result<(), UsersError> {
        if let Some(per_page) = params.per_page {
            if per_page <= 0 || per_page > MAX_USERS_PER_PAGE {
                return Err(UsersError::InvalidInput(
                    format!("per_page must be between 1 and {}", MAX_USERS_PER_PAGE)
                ));
            }
        }

        if let Some(page) = params.page {
            if page <= 0 {
                return Err(UsersError::InvalidInput("page must be greater than 0".to_string()));
            }
        }

        if let Some(search) = &params.search {
            if search.len() > MAX_SEARCH_LENGTH {
                return Err(UsersError::InvalidInput(
                    format!("search query too long (max {} characters)", MAX_SEARCH_LENGTH)
                ));
            }
        }

        Ok(())
    }

    pub fn create_router(&self) -> Router {
        let controller = Arc::new(self.clone());
        let routes = &self.config.api_routes;

        Router::new()
            .route(&routes.admin_users, get(Self::get_users))
            .route(&format!("{}/:id", routes.admin_users), get(Self::get_user))
            .route(&format!("{}/:id", routes.admin_users), put(Self::update_user))
            .route(&format!("{}/:id", routes.admin_users), delete(Self::delete_user))
            .route(&format!("{}/:id/analytics", routes.admin_users), get(Self::get_user_analytics))
            .route(&format!("{}/analytics", routes.admin_users), get(Self::get_users_analytics))
            .with_state(controller)
    }

    async fn get_users(
        State(controller): State<Arc<UsersController>>,
        headers: HeaderMap,
        Query(params): Query<UserQueryParams>,
    ) -> impl IntoResponse {
        let start_time = SystemTime::now();

        // Verify authentication
        let claims = match controller.verify_auth_header(&headers).await {
            Some(claims) => claims,
            None => {
                warn!("Unauthorized users list access attempt");
                return (StatusCode::UNAUTHORIZED, Json(serde_json::json!({
                    "error": "Unauthorized access",
                    "code": "AUTH_REQUIRED"
                }))).into_response();
            }
        };

        info!("Users list requested by admin: {}", claims.username);

        // Validate query parameters
        if let Err(validation_error) = controller.validate_query_params(&params) {
            warn!("Invalid query parameters: {:?}", validation_error);
            return (StatusCode::BAD_REQUEST, Json(serde_json::json!({
                "error": validation_error.to_string(),
                "code": "INVALID_PARAMS"
            }))).into_response();
        }

        let page = params.page.unwrap_or(1);
        let per_page = params.per_page.unwrap_or(DEFAULT_USERS_PER_PAGE).min(MAX_USERS_PER_PAGE);
        let skip = (page - 1) * per_page;

        // Build filter with input sanitization
        let filter_result = controller.build_user_filter(&params).await;
        let filter = match filter_result {
            Ok(filter) => filter,
            Err(e) => {
                error!("Failed to build user filter: {:?}", e);
                return (StatusCode::BAD_REQUEST, Json(serde_json::json!({
                    "error": e.to_string(),
                    "code": "FILTER_ERROR"
                }))).into_response();
            }
        };

        // Execute database operations with timeout
        let db_result = timeout(
            DB_OPERATION_TIMEOUT,
            controller.fetch_users_with_stats(filter, page, per_page, skip, &params)
        ).await;

        match db_result {
            Ok(Ok(response)) => {
                let elapsed = start_time.elapsed().unwrap_or_default();
                info!("Users list generated for {} in {:?}", claims.username, elapsed);
                (StatusCode::OK, Json(response)).into_response()
            }
            Ok(Err(e)) => {
                error!("Database error while fetching users: {:?}", e);
                (StatusCode::INTERNAL_SERVER_ERROR, Json(serde_json::json!({
                    "error": "Failed to fetch users",
                    "code": "DATABASE_ERROR"
                }))).into_response()
            }
            Err(_) => {
                error!("Timeout while fetching users");
                (StatusCode::REQUEST_TIMEOUT, Json(serde_json::json!({
                    "error": "Request timeout",
                    "code": "TIMEOUT"
                }))).into_response()
            }
        }
    }

    async fn build_user_filter(&self, params: &UserQueryParams) -> Result<mongodb::bson::Document, UsersError> {
        let mut filter = doc! {};

        // Blockchain filter
        if let Some(blockchain) = &params.blockchain {
            if blockchain.len() > 50 { // Reasonable limit
                return Err(UsersError::InvalidInput("Blockchain name too long".to_string()));
            }
            filter.insert("current_blockchain", blockchain);
        }

        // Search filter with sanitization
        if let Some(search) = &params.search {
            let sanitized_search = search.trim();
            if !sanitized_search.is_empty() {
                // Escape regex special characters
                let escaped_search = regex::escape(sanitized_search);
                filter.insert("$or", vec![
                    doc! { "first_name": { "$regex": escaped_search.clone(), "$options": "i" } },
                    doc! { "last_name": { "$regex": escaped_search.clone(), "$options": "i" } },
                    doc! { "username": { "$regex": escaped_search, "$options": "i" } }
                ]);
            }
        }

        // Status filter
        if let Some(status) = &params.status {
            let now = SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .map_err(|_| UsersError::InvalidInput("System time error".to_string()))?
                .as_secs();
            let thirty_days_ago = now - (30 * 24 * 60 * 60);

            match status.as_str() {
                "active" => {
                    filter.insert("last_seen", doc! { "$gte": thirty_days_ago as i64 });
                }
                "inactive" => {
                    filter.insert("last_seen", doc! { "$lt": thirty_days_ago as i64 });
                }
                _ => {
                    return Err(UsersError::InvalidInput("Invalid status filter".to_string()));
                }
            }
        }

        Ok(filter)
    }

    async fn fetch_users_with_stats(
        &self,
        filter: mongodb::bson::Document,
        page: i64,
        per_page: i64,
        skip: i64,
        params: &UserQueryParams,
    ) -> Result<UserListResponse, UsersError> {
        let db = DbService::get_db();
        let collection = db.collection::<User>("users");

        // Get total count and users concurrently
        let count_future = collection.count_documents(filter.clone(), None);

        // Build sort options with validation
        let sort_field = params.sort_by.as_deref().unwrap_or("created_at");
        let valid_sort_fields = ["created_at", "last_seen", "first_name", "username"];
        if !valid_sort_fields.contains(&sort_field) {
            return Err(UsersError::InvalidInput("Invalid sort field".to_string()));
        }

        let sort_order = if params.sort_order.as_deref() == Some("asc") { 1 } else { -1 };

        let find_options = FindOptions::builder()
            .skip(skip as u64)
            .limit(per_page)
            .sort(doc! { sort_field: sort_order })
            .build();

        let users_future = collection.find(filter, find_options);

        let (total_result, cursor_result) = tokio::try_join!(count_future, users_future)?;
        let mut cursor = cursor_result;
        let mut users = Vec::new();

        while let Some(user) = cursor.try_next().await? {
            let user_response = self.user_to_response_with_stats(user).await?;
            users.push(user_response);
        }

        Ok(UserListResponse {
            users,
            total: total_result as i64,
            page,
            per_page,
        })
    }

    async fn get_user(
        State(controller): State<Arc<UsersController>>,
        headers: HeaderMap,
        Path(id): Path<String>,
    ) -> impl IntoResponse {
        // Verify authentication
        let claims = match controller.verify_auth_header(&headers).await {
            Some(claims) => claims,
            None => {
                warn!("Unauthorized user access attempt for ID: {}", id);
                return (StatusCode::UNAUTHORIZED, Json(serde_json::json!({
                    "error": "Unauthorized access",
                    "code": "AUTH_REQUIRED"
                }))).into_response();
            }
        };

        info!("User details requested for ID: {} by admin: {}", id, claims.username);

        if let Ok(object_id) = ObjectId::parse_str(&id) {
            let db = DbService::get_db();
            let collection = db.collection::<User>("users");

            match collection.find_one(doc! { "_id": object_id }, None).await {
                Ok(Some(user)) => {
                    info!("User found for ID: {} by admin: {}", id, claims.username);
                    (StatusCode::OK, Json(controller.user_to_response_with_stats(user).await.unwrap_or_else(|_| UserResponse {
                        id: "".to_string(),
                        chat_id: 0,
                        first_name: "Error".to_string(),
                        last_name: None,
                        username: None,
                        current_blockchain: "".to_string(),
                        created_at: 0,
                        last_seen: 0,
                        total_transactions: 0,
                        total_volume: 0.0,
                        is_active: false,
                    }))).into_response()
                }
                Ok(None) => {
                    warn!("User not found for ID: {}", id);
                    let error_user = UserResponse {
                        id: "".to_string(),
                        chat_id: 0,
                        first_name: "".to_string(),
                        last_name: None,
                        username: None,
                        current_blockchain: "".to_string(),
                        created_at: 0,
                        last_seen: 0,
                        total_transactions: 0,
                        total_volume: 0.0,
                        is_active: false,
                    };
                    (StatusCode::NOT_FOUND, Json(error_user)).into_response()
                },
                Err(_) => {
                    let error_user = UserResponse {
                        id: "".to_string(),
                        chat_id: 0,
                        first_name: "".to_string(),
                        last_name: None,
                        username: None,
                        current_blockchain: "".to_string(),
                        created_at: 0,
                        last_seen: 0,
                        total_transactions: 0,
                        total_volume: 0.0,
                        is_active: false,
                    };
                    (StatusCode::INTERNAL_SERVER_ERROR, Json(error_user)).into_response()
                }
            }
        } else {
            let error_user = UserResponse {
                id: "".to_string(),
                chat_id: 0,
                first_name: "".to_string(),
                last_name: None,
                username: None,
                current_blockchain: "".to_string(),
                created_at: 0,
                last_seen: 0,
                total_transactions: 0,
                total_volume: 0.0,
                is_active: false,
            };
            (StatusCode::BAD_REQUEST, Json(error_user)).into_response()
        }
    }

    async fn update_user(
        State(controller): State<Arc<UsersController>>,
        headers: HeaderMap,
        Path(id): Path<String>,
        Json(update_req): Json<UpdateUserRequest>,
    ) -> impl IntoResponse {
        // Verify authentication
        let claims = match controller.verify_auth_header(&headers).await {
            Some(claims) => claims,
            None => {
                warn!("Unauthorized user update attempt for ID: {}", id);
                return (StatusCode::UNAUTHORIZED, Json(serde_json::json!({
                    "error": "Unauthorized access",
                    "code": "AUTH_REQUIRED"
                }))).into_response();
            }
        };

        info!("User update requested for ID: {} by admin: {}", id, claims.username);

        if let Ok(object_id) = ObjectId::parse_str(&id) {
            let db = DbService::get_db();
            let collection = db.collection::<User>("users");

            let mut update_doc = doc! {};

            if let Some(first_name) = update_req.first_name {
                update_doc.insert("first_name", first_name);
            }
            if let Some(last_name) = update_req.last_name {
                update_doc.insert("last_name", last_name);
            }
            if let Some(username) = update_req.username {
                update_doc.insert("username", username);
            }
            if let Some(blockchain) = update_req.current_blockchain {
                update_doc.insert("current_blockchain", blockchain);
            }

            if update_doc.is_empty() {
                return (StatusCode::BAD_REQUEST, Json(serde_json::json!({
                    "error": "No fields to update"
                }))).into_response();
            }

            match collection.update_one(
                doc! { "_id": object_id },
                doc! { "$set": update_doc },
                None,
            ).await {
                Ok(result) => {
                    if result.matched_count > 0 {
                        // Fetch and return updated user
                        if let Ok(Some(user)) = collection.find_one(doc! { "_id": object_id }, None).await {
                            (StatusCode::OK, Json(Self::user_to_response(user))).into_response()
                        } else {
                            (StatusCode::INTERNAL_SERVER_ERROR, Json(serde_json::json!({
                                "error": "Failed to fetch updated user"
                            }))).into_response()
                        }
                    } else {
                        (StatusCode::NOT_FOUND, Json(serde_json::json!({
                            "error": "User not found"
                        }))).into_response()
                    }
                }
                Err(_) => (StatusCode::INTERNAL_SERVER_ERROR, Json(serde_json::json!({
                    "error": "Failed to update user"
                }))).into_response()
            }
        } else {
            (StatusCode::BAD_REQUEST, Json(serde_json::json!({
                "error": "Invalid user ID"
            }))).into_response()
        }
    }

    async fn delete_user(
        State(controller): State<Arc<UsersController>>,
        headers: HeaderMap,
        Path(id): Path<String>,
    ) -> impl IntoResponse {
        // Verify authentication
        let claims = match controller.verify_auth_header(&headers).await {
            Some(claims) => claims,
            None => {
                warn!("Unauthorized user deletion attempt for ID: {}", id);
                return (StatusCode::UNAUTHORIZED, Json(serde_json::json!({
                    "error": "Unauthorized access",
                    "code": "AUTH_REQUIRED"
                }))).into_response();
            }
        };

        // Only SuperAdmins can delete users
        if claims.role != "SuperAdmin" {
            warn!("Non-SuperAdmin attempted user deletion: {} for user: {}", claims.username, id);
            return (StatusCode::FORBIDDEN, Json(serde_json::json!({
                "error": "Only SuperAdmins can delete users",
                "code": "INSUFFICIENT_PERMISSIONS"
            }))).into_response();
        }

        info!("User deletion requested for ID: {} by SuperAdmin: {}", id, claims.username);

        if let Ok(object_id) = ObjectId::parse_str(&id) {
            let db = DbService::get_db();
            let collection = db.collection::<User>("users");

            match collection.delete_one(doc! { "_id": object_id }, None).await {
                Ok(result) => {
                    if result.deleted_count > 0 {
                        info!("User {} deleted successfully by SuperAdmin: {}", id, claims.username);
                        (StatusCode::OK, Json(serde_json::json!({
                            "message": "User deleted successfully"
                        }))).into_response()
                    } else {
                        warn!("User not found for deletion: {}", id);
                        (StatusCode::NOT_FOUND, Json(serde_json::json!({
                            "error": "User not found"
                        }))).into_response()
                    }
                }
                Err(e) => {
                    error!("Failed to delete user {}: {:?}", id, e);
                    (StatusCode::INTERNAL_SERVER_ERROR, Json(serde_json::json!({
                        "error": "Failed to delete user"
                    }))).into_response()
                }
            }
        } else {
            warn!("Invalid user ID format for deletion: {}", id);
            (StatusCode::BAD_REQUEST, Json(serde_json::json!({
                "error": "Invalid user ID"
            }))).into_response()
        }
    }

    fn user_to_response(user: User) -> UserResponse {
        UserResponse {
            id: user.id.unwrap_or_else(|| ObjectId::new()).to_hex(),
            chat_id: user.chat_id,
            first_name: user.first_name,
            last_name: user.last_name,
            username: user.username,
            current_blockchain: format!("{:?}", user.current_blockchain),
            created_at: user.created_at,
            last_seen: user.last_seen,
            total_transactions: 0, // Basic response without stats
            total_volume: 0.0,
            is_active: false, // Would need to calculate based on last_seen
        }
    }

    async fn user_to_response_with_stats(&self, user: User) -> Result<UserResponse, UsersError> {
        let db = DbService::get_db();
        let transactions_collection = db.collection::<mongodb::bson::Document>("transactions");

        let user_id = user.id.unwrap_or_else(|| ObjectId::new());
        let thirty_days_ago = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .map_err(|_| UsersError::ValidationError("System time error".to_string()))?
            .as_secs() - (30 * 24 * 60 * 60);

        // Get user transaction statistics
        let stats_pipeline = vec![
            doc! {
                "$match": {
                    "user_id": user_id
                }
            },
            doc! {
                "$group": {
                    "_id": null,
                    "total_transactions": { "$sum": 1 },
                    "total_volume": { "$sum": "$amount" },
                    "successful_transactions": {
                        "$sum": {
                            "$cond": [
                                { "$eq": ["$status", "completed"] },
                                1,
                                0
                            ]
                        }
                    }
                }
            }
        ];

        let mut cursor = transactions_collection.aggregate(stats_pipeline, None).await?;
        let (total_transactions, total_volume) = if let Some(result) = cursor.try_next().await? {
            (
                result.get_i64("total_transactions").unwrap_or(0),
                result.get_f64("total_volume").unwrap_or(0.0)
            )
        } else {
            (0, 0.0)
        };

        Ok(UserResponse {
            id: user_id.to_hex(),
            chat_id: user.chat_id,
            first_name: user.first_name,
            last_name: user.last_name,
            username: user.username,
            current_blockchain: format!("{:?}", user.current_blockchain),
            created_at: user.created_at,
            last_seen: user.last_seen,
            total_transactions,
            total_volume,
            is_active: user.last_seen > thirty_days_ago,
        })
    }

    async fn get_user_analytics(
        State(controller): State<Arc<UsersController>>,
        headers: HeaderMap,
        Path(id): Path<String>,
    ) -> impl IntoResponse {
        let start_time = SystemTime::now();

        // Verify authentication
        let claims = match controller.verify_auth_header(&headers).await {
            Some(claims) => claims,
            None => {
                warn!("Unauthorized user analytics access attempt for user: {}", id);
                return (StatusCode::UNAUTHORIZED, Json(serde_json::json!({
                    "error": "Unauthorized access",
                    "code": "AUTH_REQUIRED"
                }))).into_response();
            }
        };

        info!("User analytics requested for user: {} by admin: {}", id, claims.username);

        let object_id = match ObjectId::parse_str(&id) {
            Ok(oid) => oid,
            Err(_) => {
                warn!("Invalid user ID format: {}", id);
                return (StatusCode::BAD_REQUEST, Json(serde_json::json!({
                    "error": "Invalid user ID format",
                    "code": "INVALID_ID"
                }))).into_response();
            }
        };

        // Execute analytics generation with timeout
        let analytics_result = timeout(
            DB_OPERATION_TIMEOUT,
            controller.generate_user_analytics(object_id)
        ).await;

        match analytics_result {
            Ok(Ok(analytics)) => {
                let elapsed = start_time.elapsed().unwrap_or_default();
                info!("User analytics generated for {} in {:?}", id, elapsed);
                (StatusCode::OK, Json(analytics)).into_response()
            }
            Ok(Err(UsersError::UserNotFound(_))) => {
                warn!("User not found for analytics: {}", id);
                (StatusCode::NOT_FOUND, Json(serde_json::json!({
                    "error": "User not found",
                    "code": "USER_NOT_FOUND"
                }))).into_response()
            }
            Ok(Err(e)) => {
                error!("Error generating user analytics for {}: {:?}", id, e);
                (StatusCode::INTERNAL_SERVER_ERROR, Json(serde_json::json!({
                    "error": "Failed to generate analytics",
                    "code": "ANALYTICS_ERROR"
                }))).into_response()
            }
            Err(_) => {
                error!("Timeout generating user analytics for: {}", id);
                (StatusCode::REQUEST_TIMEOUT, Json(serde_json::json!({
                    "error": "Request timeout",
                    "code": "TIMEOUT"
                }))).into_response()
            }
        }
    }

    async fn generate_user_analytics(&self, user_id: ObjectId) -> Result<serde_json::Value, UsersError> {
        let db = DbService::get_db();
        let users_collection = db.collection::<User>("users");
        let transactions_collection = db.collection::<mongodb::bson::Document>("transactions");

        // Verify user exists
        let user = users_collection
            .find_one(doc! { "_id": user_id }, None)
            .await?
            .ok_or_else(|| UsersError::UserNotFound(user_id.to_hex()))?;

        let now = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .map_err(|_| UsersError::ValidationError("System time error".to_string()))?
            .as_secs();

        // Generate comprehensive analytics
        let analytics_future = self.get_user_transaction_analytics(user_id);
        let blockchain_future = self.get_user_blockchain_preferences(user_id);
        let activity_future = self.get_user_activity_patterns(user_id, now);
        let performance_future = self.get_user_performance_metrics(user_id);

        let (transaction_analytics, blockchain_prefs, activity_patterns, performance_metrics) =
            tokio::try_join!(analytics_future, blockchain_future, activity_future, performance_future)?;

        Ok(serde_json::json!({
            "user_id": user_id.to_hex(),
            "user_info": {
                "chat_id": user.chat_id,
                "first_name": user.first_name,
                "last_name": user.last_name,
                "username": user.username,
                "join_date": user.created_at,
                "last_activity": user.last_seen,
                "current_blockchain": format!("{:?}", user.current_blockchain)
            },
            "transaction_analytics": transaction_analytics,
            "blockchain_preferences": blockchain_prefs,
            "activity_patterns": activity_patterns,
            "performance_metrics": performance_metrics,
            "generated_at": now
        }))
    }

    async fn get_user_transaction_analytics(&self, user_id: ObjectId) -> Result<serde_json::Value, UsersError> {
        let db = DbService::get_db();
        let transactions_collection = db.collection::<mongodb::bson::Document>("transactions");

        let pipeline = vec![
            doc! { "$match": { "user_id": user_id } },
            doc! {
                "$group": {
                    "_id": null,
                    "total_transactions": { "$sum": 1 },
                    "total_volume": { "$sum": "$amount" },
                    "successful_transactions": {
                        "$sum": { "$cond": [{ "$eq": ["$status", "completed"] }, 1, 0] }
                    },
                    "failed_transactions": {
                        "$sum": { "$cond": [{ "$ne": ["$status", "completed"] }, 1, 0] }
                    },
                    "avg_transaction_amount": { "$avg": "$amount" },
                    "max_transaction_amount": { "$max": "$amount" },
                    "min_transaction_amount": { "$min": "$amount" }
                }
            }
        ];

        let mut cursor = transactions_collection.aggregate(pipeline, None).await?;
        if let Some(result) = cursor.try_next().await? {
            Ok(serde_json::json!({
                "total_transactions": result.get_i64("total_transactions").unwrap_or(0),
                "total_volume": result.get_f64("total_volume").unwrap_or(0.0),
                "successful_transactions": result.get_i64("successful_transactions").unwrap_or(0),
                "failed_transactions": result.get_i64("failed_transactions").unwrap_or(0),
                "avg_transaction_amount": result.get_f64("avg_transaction_amount").unwrap_or(0.0),
                "max_transaction_amount": result.get_f64("max_transaction_amount").unwrap_or(0.0),
                "min_transaction_amount": result.get_f64("min_transaction_amount").unwrap_or(0.0)
            }))
        } else {
            Ok(serde_json::json!({
                "total_transactions": 0,
                "total_volume": 0.0,
                "successful_transactions": 0,
                "failed_transactions": 0,
                "avg_transaction_amount": 0.0,
                "max_transaction_amount": 0.0,
                "min_transaction_amount": 0.0
            }))
        }
    }

    async fn get_user_blockchain_preferences(&self, user_id: ObjectId) -> Result<serde_json::Value, UsersError> {
        let db = DbService::get_db();
        let transactions_collection = db.collection::<mongodb::bson::Document>("transactions");

        let pipeline = vec![
            doc! { "$match": { "user_id": user_id } },
            doc! {
                "$group": {
                    "_id": "$blockchain",
                    "transaction_count": { "$sum": 1 },
                    "total_volume": { "$sum": "$amount" }
                }
            },
            doc! { "$sort": { "transaction_count": -1 } }
        ];

        let mut cursor = transactions_collection.aggregate(pipeline, None).await?;
        let mut preferences = Vec::new();

        while let Some(result) = cursor.try_next().await? {
            if let Ok(blockchain) = result.get_str("_id") {
                preferences.push(serde_json::json!({
                    "blockchain": blockchain,
                    "transaction_count": result.get_i64("transaction_count").unwrap_or(0),
                    "total_volume": result.get_f64("total_volume").unwrap_or(0.0)
                }));
            }
        }

        Ok(serde_json::json!({
            "blockchain_usage": preferences,
            "favorite_blockchain": preferences.first()
                .and_then(|p| p.get("blockchain"))
                .unwrap_or(&serde_json::Value::String("None".to_string()))
        }))
    }

    async fn get_user_activity_patterns(&self, user_id: ObjectId, now: u64) -> Result<serde_json::Value, UsersError> {
        let db = DbService::get_db();
        let transactions_collection = db.collection::<mongodb::bson::Document>("transactions");

        let seven_days_ago = now - (7 * 24 * 60 * 60);
        let thirty_days_ago = now - (30 * 24 * 60 * 60);

        let pipeline = vec![
            doc! { "$match": { "user_id": user_id } },
            doc! {
                "$facet": {
                    "last_7_days": [
                        { "$match": { "timestamp": { "$gte": seven_days_ago as i64 } } },
                        { "$count": "count" }
                    ],
                    "last_30_days": [
                        { "$match": { "timestamp": { "$gte": thirty_days_ago as i64 } } },
                        { "$count": "count" }
                    ],
                    "hourly_pattern": [
                        {
                            "$addFields": {
                                "hour": { "$hour": { "$toDate": { "$multiply": ["$timestamp", 1000] } } }
                            }
                        },
                        { "$group": { "_id": "$hour", "count": { "$sum": 1 } } },
                        { "$sort": { "_id": 1 } }
                    ]
                }
            }
        ];

        let mut cursor = transactions_collection.aggregate(pipeline, None).await?;
        if let Some(result) = cursor.try_next().await? {
            let last_7_days = result.get_array("last_7_days")
                .ok()
                .and_then(|arr| arr.first())
                .and_then(|doc| doc.as_document())
                .and_then(|doc| doc.get_i64("count").ok())
                .unwrap_or(0);

            let last_30_days = result.get_array("last_30_days")
                .ok()
                .and_then(|arr| arr.first())
                .and_then(|doc| doc.as_document())
                .and_then(|doc| doc.get_i64("count").ok())
                .unwrap_or(0);

            let mut hourly_pattern = Vec::new();
            if let Ok(hourly_array) = result.get_array("hourly_pattern") {
                for item in hourly_array {
                    if let Some(doc) = item.as_document() {
                        if let (Ok(hour), Ok(count)) = (doc.get_i32("_id"), doc.get_i64("count")) {
                            hourly_pattern.push(serde_json::json!({
                                "hour": hour,
                                "transaction_count": count
                            }));
                        }
                    }
                }
            }

            Ok(serde_json::json!({
                "transactions_last_7_days": last_7_days,
                "transactions_last_30_days": last_30_days,
                "hourly_activity_pattern": hourly_pattern,
                "avg_daily_transactions": if last_30_days > 0 { last_30_days as f64 / 30.0 } else { 0.0 }
            }))
        } else {
            Ok(serde_json::json!({
                "transactions_last_7_days": 0,
                "transactions_last_30_days": 0,
                "hourly_activity_pattern": [],
                "avg_daily_transactions": 0.0
            }))
        }
    }

    async fn get_user_performance_metrics(&self, user_id: ObjectId) -> Result<serde_json::Value, UsersError> {
        let db = DbService::get_db();
        let transactions_collection = db.collection::<mongodb::bson::Document>("transactions");

        let pipeline = vec![
            doc! { "$match": { "user_id": user_id } },
            doc! {
                "$group": {
                    "_id": null,
                    "total_transactions": { "$sum": 1 },
                    "successful_transactions": {
                        "$sum": { "$cond": [{ "$eq": ["$status", "completed"] }, 1, 0] }
                    },
                    "avg_response_time": { "$avg": "$response_time_ms" },
                    "total_fees_paid": { "$sum": "$fee" }
                }
            }
        ];

        let mut cursor = transactions_collection.aggregate(pipeline, None).await?;
        if let Some(result) = cursor.try_next().await? {
            let total = result.get_i64("total_transactions").unwrap_or(0);
            let successful = result.get_i64("successful_transactions").unwrap_or(0);
            let success_rate = if total > 0 { (successful as f64 / total as f64) * 100.0 } else { 0.0 };

            Ok(serde_json::json!({
                "success_rate": success_rate,
                "avg_response_time_ms": result.get_f64("avg_response_time").unwrap_or(0.0),
                "total_fees_paid": result.get_f64("total_fees_paid").unwrap_or(0.0),
                "reliability_score": if success_rate >= 95.0 { "Excellent" }
                                   else if success_rate >= 85.0 { "Good" }
                                   else if success_rate >= 70.0 { "Fair" }
                                   else { "Poor" }
            }))
        } else {
            Ok(serde_json::json!({
                "success_rate": 0.0,
                "avg_response_time_ms": 0.0,
                "total_fees_paid": 0.0,
                "reliability_score": "No Data"
            }))
        }
    }

    async fn get_users_analytics(
        State(controller): State<Arc<UsersController>>,
        headers: HeaderMap,
    ) -> impl IntoResponse {
        // Verify authentication
        let claims = match controller.verify_auth_header(&headers).await {
            Some(claims) => claims,
            None => {
                warn!("Unauthorized users analytics access attempt");
                return (StatusCode::UNAUTHORIZED, Json(serde_json::json!({
                    "error": "Unauthorized access",
                    "code": "AUTH_REQUIRED"
                }))).into_response();
            }
        };

        info!("Users analytics requested by admin: {}", claims.username);

        let db = DbService::get_db();
        let collection = db.collection::<User>("users");

        let now = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap()
            .as_secs();

        let one_day_ago = now - (24 * 60 * 60);
        let one_week_ago = now - (7 * 24 * 60 * 60);
        let thirty_days_ago = now - (30 * 24 * 60 * 60);

        // Aggregate user analytics
        let pipeline = vec![
            doc! {
                "$facet": {
                    "total_users": [
                        { "$count": "count" }
                    ],
                    "active_users": [
                        { "$match": { "last_seen": { "$gte": thirty_days_ago as i64 } } },
                        { "$count": "count" }
                    ],
                    "new_users_today": [
                        { "$match": { "created_at": { "$gte": one_day_ago as i64 } } },
                        { "$count": "count" }
                    ],
                    "new_users_week": [
                        { "$match": { "created_at": { "$gte": one_week_ago as i64 } } },
                        { "$count": "count" }
                    ],
                    "users_by_blockchain": [
                        { "$group": { "_id": "$current_blockchain", "count": { "$sum": 1 } } }
                    ],
                    "daily_active": [
                        { "$match": { "last_seen": { "$gte": one_day_ago as i64 } } },
                        { "$count": "count" }
                    ],
                    "weekly_active": [
                        { "$match": { "last_seen": { "$gte": one_week_ago as i64 } } },
                        { "$count": "count" }
                    ]
                }
            }
        ];

        match collection.aggregate(pipeline, None).await {
            Ok(mut cursor) => {
                if let Ok(Some(result)) = cursor.try_next().await {
                    let total_users = result.get_array("total_users")
                        .ok()
                        .and_then(|arr| arr.first())
                        .and_then(|doc| doc.as_document())
                        .and_then(|doc| doc.get_i64("count").ok())
                        .unwrap_or(0);

                    let active_users = result.get_array("active_users")
                        .ok()
                        .and_then(|arr| arr.first())
                        .and_then(|doc| doc.as_document())
                        .and_then(|doc| doc.get_i64("count").ok())
                        .unwrap_or(0);

                    let new_users_today = result.get_array("new_users_today")
                        .ok()
                        .and_then(|arr| arr.first())
                        .and_then(|doc| doc.as_document())
                        .and_then(|doc| doc.get_i64("count").ok())
                        .unwrap_or(0);

                    let new_users_week = result.get_array("new_users_week")
                        .ok()
                        .and_then(|arr| arr.first())
                        .and_then(|doc| doc.as_document())
                        .and_then(|doc| doc.get_i64("count").ok())
                        .unwrap_or(0);

                    let daily_active = result.get_array("daily_active")
                        .ok()
                        .and_then(|arr| arr.first())
                        .and_then(|doc| doc.as_document())
                        .and_then(|doc| doc.get_i64("count").ok())
                        .unwrap_or(0);

                    let weekly_active = result.get_array("weekly_active")
                        .ok()
                        .and_then(|arr| arr.first())
                        .and_then(|doc| doc.as_document())
                        .and_then(|doc| doc.get_i64("count").ok())
                        .unwrap_or(0);

                    // Process blockchain distribution
                    let mut users_by_blockchain = HashMap::new();
                    if let Ok(blockchain_array) = result.get_array("users_by_blockchain") {
                        for item in blockchain_array {
                            if let Some(doc) = item.as_document() {
                                if let (Ok(blockchain), Ok(count)) = (doc.get_str("_id"), doc.get_i64("count")) {
                                    users_by_blockchain.insert(blockchain.to_string(), count);
                                }
                            }
                        }
                    }

                    let analytics = UserAnalytics {
                        total_users,
                        active_users,
                        new_users_today,
                        new_users_week,
                        users_by_blockchain,
                        activity_stats: UserActivityStats {
                            daily_active_users: daily_active,
                            weekly_active_users: weekly_active,
                            monthly_active_users: active_users,
                            average_session_time: 0.0, // Would need session tracking
                        },
                    };

                    (StatusCode::OK, Json(analytics)).into_response()
                } else {
                    (StatusCode::OK, Json(UserAnalytics {
                        total_users: 0,
                        active_users: 0,
                        new_users_today: 0,
                        new_users_week: 0,
                        users_by_blockchain: HashMap::new(),
                        activity_stats: UserActivityStats {
                            daily_active_users: 0,
                            weekly_active_users: 0,
                            monthly_active_users: 0,
                            average_session_time: 0.0,
                        },
                    })).into_response()
                }
            }
            Err(_) => {
                let error_analytics = UserAnalytics {
                    total_users: 0,
                    active_users: 0,
                    new_users_today: 0,
                    new_users_week: 0,
                    users_by_blockchain: HashMap::new(),
                    activity_stats: UserActivityStats {
                        daily_active_users: 0,
                        weekly_active_users: 0,
                        monthly_active_users: 0,
                        average_session_time: 0.0,
                    },
                };
                (StatusCode::INTERNAL_SERVER_ERROR, Json(error_analytics)).into_response()
            }
        }
    }
}

impl Clone for UsersController {
    fn clone(&self) -> Self {
        Self {
            config: self.config.clone(),
            jwt_secret: self.jwt_secret.clone(),
        }
    }
}
