use serde::{Deserialize, Serialize};
use crate::model::blockchain::Blockchain;

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct Dashboard {
    pub message_id: Option<i32>,
    pub content: Option<String>,
    pub markup: Option<serde_json::Value>,
}

impl Dashboard {
    pub fn new() -> Self {
        Self {
            message_id: None,
            content: None,
            markup: None,
        }
    }
    
    pub fn update(&mut self, message_id: i32, content: String, markup: serde_json::Value) {
        self.message_id = Some(message_id);
        self.content = Some(content);
        self.markup = Some(markup);
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DashboardManager {
    pub bsc_dashboard: Dashboard,
    pub sol_dashboard: Dashboard,
    pub eth_dashboard: Dashboard,
    pub base_dashboard: Dashboard,
}

impl DashboardManager {
    pub fn new() -> Self {
        Self {
            bsc_dashboard: Dashboard::new(),
            sol_dashboard: Dashboard::new(),
            eth_dashboard: Dashboard::new(),
            base_dashboard: Dashboard::new(),
        }
    }
    
    pub fn get_dashboard(&self, blockchain: Blockchain) -> &Dashboard {
        match blockchain {
            Blockchain::BSC => &self.bsc_dashboard,
            Blockchain::SOL => &self.sol_dashboard,
            Blockchain::ETH => &self.eth_dashboard,
            Blockchain::BASE => &self.base_dashboard,
        }
    }
    
    pub fn get_dashboard_mut(&mut self, blockchain: Blockchain) -> &mut Dashboard {
        match blockchain {
            Blockchain::BSC => &mut self.bsc_dashboard,
            Blockchain::SOL => &mut self.sol_dashboard,
            Blockchain::ETH => &mut self.eth_dashboard,
            Blockchain::BASE => &mut self.base_dashboard,
        }
    }
}
