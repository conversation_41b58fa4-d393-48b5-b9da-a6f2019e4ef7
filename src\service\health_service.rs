use std::time::Instant;
use std::sync::Arc;
use tokio::sync::Mutex;
use axum::{
    routing::get,
    Router,
    extract::Path,
    response::{IntoResponse, Json},
    http::StatusCode,
};
use serde_json::{json, Value};
use std::net::SocketAddr;

pub struct HealthService {
    start_time: Arc<Mutex<Instant>>,
}

impl HealthService {
    pub fn new() -> Self {
        Self {
            start_time: Arc::new(Mutex::new(Instant::now())),
        }
    }

    pub async fn start(&self, port: u16) {
        let app = self.create_router();
        self.start_with_router(port, app).await;
    }

    pub async fn start_with_router(&self, port: u16, router: Router) {
        log::info!("Starting server on port {}", port);
        log::info!("Current working directory: {:?}", std::env::current_dir().unwrap_or_default());

        // Create address to bind to
        let addr = SocketAddr::from(([0, 0, 0, 0], port));
        log::info!("Trying to bind to {}", addr);

        // Start the server
        match axum::Server::try_bind(&addr) {
            Ok(server) => {
                log::info!("Server successfully started on port {}", port);

                // Spawn the server to run in the background
                tokio::spawn(async move {
                    log::info!("Server is now running on port {}", port);
                    if let Err(e) = server.serve(router.into_make_service()).await {
                        log::error!("Server error: {}", e);
                    }
                });
            },
            Err(e) => {
                log::error!("Failed to start server on port {}: {}", port, e);

                // Try alternative ports if the main one fails
                self.try_alternative_ports_with_router(router).await;
            }
        }
    }

    // Create a router with health check endpoints
    pub fn create_router(&self) -> Router {
        let root_start_time = self.start_time.clone();
        let health_start_time = self.start_time.clone();
        let render_start_time = self.start_time.clone();

        Router::new()
            // Root route
            .route("/", get(move || {
                let start_time_clone = root_start_time.clone();
                async move {
                    health_handler(start_time_clone).await
                }
            }))
            // Health route
            .route("/health", get(move || {
                let start_time_clone = health_start_time.clone();
                async move {
                    health_handler(start_time_clone).await
                }
            }))
            // Special route for Render's health check (just in case)
            .route("/healthz", get(move || {
                let start_time_clone = render_start_time.clone();
                async move {
                    health_handler(start_time_clone).await
                }
            }))
    }

    // Try binding to alternative ports with default router
    async fn try_alternative_ports(&self) {
        let router = self.create_router();
        self.try_alternative_ports_with_router(router).await;
    }

    // Try binding to alternative ports with custom router
    async fn try_alternative_ports_with_router(&self, router: Router) {
        // List of alternative ports to try
        let alt_ports = [8080, 3000, 5000, 8888];

        for alt_port in alt_ports {
            log::info!("Trying alternative port {}", alt_port);

            let alt_addr = SocketAddr::from(([0, 0, 0, 0], alt_port));
            let alt_router = router.clone();

            match axum::Server::try_bind(&alt_addr) {
                Ok(server) => {
                    log::info!("Server successfully started on alternative port {}", alt_port);

                    tokio::spawn(async move {
                        log::info!("Server is now running on alternative port {}", alt_port);
                        if let Err(e) = server.serve(alt_router.into_make_service()).await {
                            log::error!("Server error on alternative port {}: {}", alt_port, e);
                        }
                    });

                    // Successfully bound to an alternative port, so return
                    return;
                },
                Err(alt_e) => {
                    log::error!("Failed to start server on alternative port {}: {}", alt_port, alt_e);
                    // Continue to the next port
                }
            }
        }

        log::error!("Failed to bind to any alternative port. Server will not be available.");
    }
}

// Health check handler
async fn health_handler(start_time: Arc<Mutex<Instant>>) -> impl IntoResponse {
    let uptime = {
        let guard = start_time.lock().await;
        guard.elapsed().as_secs()
    };

    log::info!("Responding to health check with uptime: {}", uptime);

    // Return JSON response with additional information
    Json(json!({
        "status": "ok",
        "uptime": uptime,
        "version": env!("CARGO_PKG_VERSION"),
        "timestamp": chrono::Utc::now().to_rfc3339()
    }))
}
