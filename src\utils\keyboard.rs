use teloxide::types::{InlineKeyboardButton, InlineKeyboardMarkup};

/// Creates a keyboard with the given buttons
pub fn create_keyboard(buttons: Vec<Vec<(String, String)>>) -> InlineKeyboardMarkup {
    let keyboard: Vec<Vec<InlineKeyboardButton>> = buttons
        .into_iter()
        .map(|row| {
            row.into_iter()
                .map(|(text, callback_data)| {
                    InlineKeyboardButton::callback(text, callback_data)
                })
                .collect()
        })
        .collect();
    
    InlineKeyboardMarkup::new(keyboard)
}
