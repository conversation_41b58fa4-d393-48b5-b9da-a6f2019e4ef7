<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Test</title>
</head>
<body>
    <h1>EasyBot API Test</h1>
    
    <div>
        <h2>1. Test Login</h2>
        <button onclick="testLogin()">Test Login</button>
        <div id="loginResult"></div>
    </div>
    
    <div>
        <h2>2. Test Dashboard (requires login)</h2>
        <button onclick="testDashboard()">Test Dashboard</button>
        <div id="dashboardResult"></div>
    </div>
    
    <div>
        <h2>3. Test Health Check</h2>
        <button onclick="testHealth()">Test Health Check</button>
        <div id="healthResult"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8000/api';
        let authToken = localStorage.getItem('admin_token');

        async function testLogin() {
            const resultDiv = document.getElementById('loginResult');
            resultDiv.innerHTML = 'Testing login...';
            
            try {
                const response = await fetch(`${API_BASE}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: 'Olajosh',
                        password: 'EasyBot@2024'
                    })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    authToken = data.token;
                    localStorage.setItem('admin_token', authToken);
                    resultDiv.innerHTML = `<div style="color: green;">Login successful! Token: ${authToken.substring(0, 20)}...</div>`;
                } else {
                    resultDiv.innerHTML = `<div style="color: red;">Login failed: ${JSON.stringify(data)}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div style="color: red;">Error: ${error.message}</div>`;
            }
        }

        async function testDashboard() {
            const resultDiv = document.getElementById('dashboardResult');
            resultDiv.innerHTML = 'Testing dashboard...';
            
            if (!authToken) {
                resultDiv.innerHTML = '<div style="color: red;">Please login first</div>';
                return;
            }
            
            try {
                const response = await fetch(`${API_BASE}/admin/dashboard`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                        'Content-Type': 'application/json',
                    }
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.innerHTML = `<div style="color: green;">Dashboard data received: <pre>${JSON.stringify(data, null, 2)}</pre></div>`;
                } else {
                    resultDiv.innerHTML = `<div style="color: red;">Dashboard failed: ${JSON.stringify(data)}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div style="color: red;">Error: ${error.message}</div>`;
            }
        }

        async function testHealth() {
            const resultDiv = document.getElementById('healthResult');
            resultDiv.innerHTML = 'Testing health check...';
            
            try {
                const response = await fetch('http://localhost:8000/health', {
                    method: 'GET',
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.innerHTML = `<div style="color: green;">Health check successful: <pre>${JSON.stringify(data, null, 2)}</pre></div>`;
                } else {
                    resultDiv.innerHTML = `<div style="color: red;">Health check failed: ${JSON.stringify(data)}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div style="color: red;">Error: ${error.message}</div>`;
            }
        }

        // Load token on page load
        if (authToken) {
            document.body.insertAdjacentHTML('afterbegin', `<div style="background: #e8f5e8; padding: 10px; margin-bottom: 20px;">Token found: ${authToken.substring(0, 20)}...</div>`);
        }
    </script>
</body>
</html>
