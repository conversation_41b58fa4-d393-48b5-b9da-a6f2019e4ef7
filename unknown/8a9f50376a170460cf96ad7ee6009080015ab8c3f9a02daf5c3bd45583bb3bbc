"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getTokenBalance = getTokenBalance;
const ethers_1 = require("ethers");
const web3_js_1 = require("@solana/web3.js");
const spl_token_1 = require("@solana/spl-token");
// RPC endpoints
const RPC_ENDPOINTS = {
    ETH: process.env.ETH_RPC_URL || 'https://eth-mainnet.g.alchemy.com/v2/demo',
    BSC: process.env.BSC_RPC_URL || 'https://bsc-dataseed.binance.org',
    SOL: process.env.SOL_RPC_URL || 'https://api.mainnet-beta.solana.com',
    BASE: process.env.BASE_RPC_URL || 'https://mainnet.base.org'
};
// ERC20 ABI for token balance
const ERC20_ABI = [
    'function balanceOf(address owner) view returns (uint256)',
    'function decimals() view returns (uint8)',
    'function symbol() view returns (string)'
];
/**
 * Get the token balance for a specific token in a wallet
 * @param blockchain The blockchain to query (ETH, BSC, SOL, BASE)
 * @param walletAddress The wallet address to check
 * @param tokenAddress The token contract address
 * @returns The token balance as a string
 */
async function getTokenBalance(blockchain, walletAddress, tokenAddress) {
    try {
        switch (blockchain) {
            case 'ETH':
            case 'BSC':
            case 'BASE': {
                // Create provider based on blockchain
                const provider = new ethers_1.ethers.JsonRpcProvider(RPC_ENDPOINTS[blockchain]);
                // Create contract instance
                const tokenContract = new ethers_1.ethers.Contract(tokenAddress, ERC20_ABI, provider);
                // Get token decimals
                const decimals = await tokenContract.decimals();
                // Get balance in smallest units
                const balance = await tokenContract.balanceOf(walletAddress);
                // Convert to token units
                return ethers_1.ethers.formatUnits(balance, decimals);
            }
            case 'SOL': {
                // Create Solana connection
                const connection = new web3_js_1.Connection(RPC_ENDPOINTS.SOL);
                // Special case for SOL native token
                if (tokenAddress === 'So11111111111111111111111111111111111111112') {
                    const balanceLamports = await connection.getBalance(new web3_js_1.PublicKey(walletAddress));
                    return (balanceLamports / **********).toString();
                }
                // For SPL tokens, get all token accounts owned by the wallet
                const tokenAccounts = await connection.getParsedTokenAccountsByOwner(new web3_js_1.PublicKey(walletAddress), {
                    programId: spl_token_1.TOKEN_PROGRAM_ID
                });
                // Find the account for the specified token
                const tokenAccount = tokenAccounts.value.find((account) => account.account.data.parsed.info.mint === tokenAddress);
                if (!tokenAccount) {
                    return '0';
                }
                // Get the token amount
                const tokenAmount = tokenAccount.account.data.parsed.info.tokenAmount;
                // Return the UI amount (already formatted with decimals)
                return tokenAmount.uiAmountString || '0';
            }
            default:
                throw new Error(`Unsupported blockchain: ${blockchain}`);
        }
    }
    catch (error) {
        console.error(`Error getting token balance for ${blockchain}:`, error);
        throw error;
    }
}
