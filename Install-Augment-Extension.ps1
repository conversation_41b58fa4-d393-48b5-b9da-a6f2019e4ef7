<#
.SYNOPSIS
Attempts to install older versions of 'augment.vscode-augment' starting from 0.1.0.
Logs results and stops at the first successful install.
#>

$LogFile = "Augment_Install_Log.txt"
$ExtensionId = "augment.vscode-augment"
$VersionPrefix = "0.200."  # Starts from 0.1.0
$MaxMinorVersion = 10000   # Stop after 50 attempts (0.1.0 to 0.1.49)

# Initialize log
"=== Augment Extension Installation Log ===`n$(Get-Date)`n" | Out-File -FilePath $LogFile

for ($minor = 0; $minor -lt $MaxMinorVersion; $minor++) {
    $Version = "$VersionPrefix$minor"  # e.g., 0.1.0, 0.1.1, etc.
    $InstallCommand = "code --install-extension ${ExtensionId}@${Version}"
    
    # Log attempt
    "Attempting to install ${ExtensionId}@${Version}..." | Tee-Object -FilePath $LogFile -Append
    
    # Run install
    $Result = Invoke-Expression $InstallCommand 2>&1
    
    # Check success
    if ($LASTEXITCODE -eq 0) {
        "SUCCESS: Installed ${ExtensionId}@${Version}`n" | Tee-Object -FilePath $LogFile -Append
        break
    } else {
        "FAILED: $($Result -join "`n")`n" | Out-File -FilePath $LogFile -Append
    }
    
    Start-Sleep -Milliseconds 500  # Brief pause between attempts
}

"=== Script finished ===" | Out-File -FilePath $LogFile -Append