{"name": "utf-8-validate", "version": "5.0.10", "description": "Check if a buffer contains valid UTF-8", "main": "index.js", "engines": {"node": ">=6.14.2"}, "scripts": {"install": "node-gyp-build", "prebuild": "prebuildify --napi --strip --target=14.0.0", "prebuild-darwin-x64+arm64": "prebuildify --arch x64+arm64 --napi --strip --target=14.0.0", "test": "mocha"}, "repository": {"type": "git", "url": "https://github.com/websockets/utf-8-validate"}, "keywords": ["utf-8-validate"], "author": "<PERSON><PERSON> <PERSON> <<EMAIL>> (http://2x.io)", "license": "MIT", "bugs": {"url": "https://github.com/websockets/utf-8-validate/issues"}, "homepage": "https://github.com/websockets/utf-8-validate", "dependencies": {"node-gyp-build": "^4.3.0"}, "devDependencies": {"mocha": "^10.0.0", "node-gyp": "^9.1.0", "prebuildify": "^5.0.0"}}