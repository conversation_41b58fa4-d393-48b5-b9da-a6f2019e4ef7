# Test script for health check endpoint
Write-Host "Testing health check endpoint..."

# Test root endpoint
Write-Host "Testing root endpoint (/)..."
try {
    $response = Invoke-WebRequest -Uri "http://localhost:8000/" -Method GET -TimeoutSec 5
    Write-Host "Response status code: $($response.StatusCode)"
    Write-Host "Response body: $($response.Content)"
} catch {
    Write-Host "Error accessing root endpoint: $_"
}

# Test health endpoint
Write-Host "`nTesting health endpoint (/health)..."
try {
    $response = Invoke-WebRequest -Uri "http://localhost:8000/health" -Method GET -TimeoutSec 5
    Write-Host "Response status code: $($response.StatusCode)"
    Write-Host "Response body: $($response.Content)"
} catch {
    Write-Host "Error accessing health endpoint: $_"
}

Write-Host "`nHealth check test completed."
