[2025-06-10T20:14:19Z INFO  Easybot::utils::logging] Logger initialized with level: INFO
[2025-06-10T20:14:19Z INFO  Easybot] Starting EasyBot...
[2025-06-10T20:14:19Z INFO  Easybot] Environment variables for port configuration:
[2025-06-10T20:14:19Z INFO  Easybot]   PORT=8000
[2025-06-10T20:14:19Z INFO  Easybot]   SOCKET_PORT=3000
[2025-06-10T20:14:19Z INFO  Easybot] Using port 8000 from config for health check service
[2025-06-10T20:14:19Z INFO  Easybot] Using port 3000 from config for socket service
[2025-06-10T20:14:19Z INFO  Easybot] Starting health check service on port 8000...
[2025-06-10T20:14:19Z INFO  Easybot] Initializing admin service and controllers...
[2025-06-10T20:14:19Z INFO  Easybot::service::health_service] Starting server on port 8000
[2025-06-10T20:14:19Z INFO  Easybot::service::health_service] Current working directory: "D:\\workspace\\.rust\\Easybot"
[2025-06-10T20:14:19Z INFO  Easybot::service::health_service] Trying to bind to 0.0.0.0:8000
[2025-06-10T20:14:19Z INFO  Easybot::service::health_service] Server successfully started on port 8000
[2025-06-10T20:14:19Z INFO  Easybot::service::health_service] Server is now running on port 8000
[2025-06-10T20:14:19Z INFO  Easybot] Health check and admin services initialization completed
[2025-06-10T20:14:19Z INFO  Easybot] Initializing database connection...
[2025-06-10T20:14:56Z INFO  Easybot] Database connection established
[2025-06-10T20:14:56Z INFO  Easybot] Checking and initializing default super admin...
[2025-06-10T20:14:57Z INFO  Easybot::service::admin_init_service] SuperAdmin already exists. Skipping default admin creation.
[2025-06-10T20:14:57Z WARN  Easybot::service::admin_init_service] ⚠️  SECURITY WARNING: Default admin is still using default password!
[2025-06-10T20:14:57Z WARN  Easybot::service::admin_init_service] ⚠️  Please change the password immediately for security!
[2025-06-10T20:14:57Z WARN  Easybot::service::admin_init_service] ⚠️  Username: Olajosh
[2025-06-10T20:14:57Z WARN  Easybot::service::admin_init_service] ⚠️  Email: <EMAIL>
[2025-06-10T20:14:57Z INFO  Easybot] Initializing cached price service...
[2025-06-10T20:14:57Z INFO  Easybot::service::cached_price_service] Initializing price cache for all supported blockchains...
[2025-06-10T20:14:58Z INFO  Easybot::service::price_service] DexScreener price for SOL: $163.12000000
[2025-06-10T20:14:58Z INFO  Easybot::service::cached_price_service] Fetched fresh price for SOL: $163.1200
[2025-06-10T20:14:58Z INFO  Easybot::service::cached_price_service] ✅ Initialized cache for SOL: $163.1200
[2025-06-10T20:14:59Z INFO  Easybot::service::price_service] DexScreener price for ETH: $2758.11000000
[2025-06-10T20:14:59Z INFO  Easybot::service::cached_price_service] Fetched fresh price for ETH: $2758.1100
[2025-06-10T20:15:00Z INFO  Easybot::service::cached_price_service] ✅ Initialized cache for ETH: $2758.1100
[2025-06-10T20:15:01Z INFO  Easybot::service::price_service] DexScreener price for BSC: $668.69000000
[2025-06-10T20:15:01Z INFO  Easybot::service::cached_price_service] Fetched fresh price for BSC: $668.6900
[2025-06-10T20:15:01Z INFO  Easybot::service::cached_price_service] ✅ Initialized cache for BSC: $668.6900
[2025-06-10T20:15:02Z INFO  Easybot::service::price_service] DexScreener price for BASE: $2760.67000000
[2025-06-10T20:15:02Z INFO  Easybot::service::cached_price_service] Fetched fresh price for BASE: $2760.6700
[2025-06-10T20:15:02Z INFO  Easybot::service::cached_price_service] ✅ Initialized cache for BASE: $2760.6700
[2025-06-10T20:15:02Z INFO  Easybot::service::cached_price_service] Price cache initialization completed
[2025-06-10T20:15:02Z INFO  Easybot] Price cache initialized successfully
[2025-06-10T20:15:02Z INFO  Easybot] Starting background price updater...
[2025-06-10T20:15:02Z INFO  Easybot::service::cached_price_service] Starting background price updater (interval: 3600s)
[2025-06-10T20:15:02Z INFO  Easybot] Background price updater started
[2025-06-10T20:15:02Z INFO  Easybot::service::cached_price_service] 🔄 Background price update started
[2025-06-10T20:15:02Z INFO  Easybot::service::price_service] Using cached native price for SOL: $163.1200
[2025-06-10T20:15:02Z INFO  Easybot::service::cached_price_service] Fetched fresh price for SOL: $163.1200
[2025-06-10T20:15:02Z INFO  Easybot] Initializing Solana trader service...
[2025-06-10T20:15:02Z INFO  Easybot] Solana trader service initialized
[2025-06-10T20:15:02Z INFO  Easybot] Initializing snipe worker...
[2025-06-10T20:15:02Z INFO  Easybot] Snipe worker initialized and started
[2025-06-10T20:15:02Z INFO  Easybot] Starting background fee collection service...
[2025-06-10T20:15:02Z INFO  Easybot] Background fee collection service started
[2025-06-10T20:15:02Z INFO  Easybot] Starting bot dispatcher...
[2025-06-10T20:15:03Z INFO  Easybot::service::price_service] Using cached native price for ETH: $2758.1100
[2025-06-10T20:15:03Z INFO  Easybot::service::cached_price_service] Fetched fresh price for ETH: $2758.1100
[2025-06-10T20:15:04Z INFO  Easybot::service::price_service] Using cached native price for BSC: $668.6900
[2025-06-10T20:15:04Z INFO  Easybot::service::cached_price_service] Fetched fresh price for BSC: $668.6900
[2025-06-10T20:15:04Z INFO  Easybot::service::price_service] Using cached native price for BASE: $2760.6700
[2025-06-10T20:15:04Z INFO  Easybot::service::cached_price_service] Fetched fresh price for BASE: $2760.6700
[2025-06-10T20:15:05Z INFO  Easybot::service::cached_price_service] 🔄 Background price update completed
[2025-06-10T20:15:07Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T20:15:10Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T20:15:11Z INFO  Easybot::controllers::transactions_controller] Transactions list requested by admin: Dev
[2025-06-10T20:15:12Z ERROR Easybot::controllers::transactions_controller] Database error while fetching transactions: DatabaseError(Error { kind: BsonDeserialization(DeserializationError { message: "invalid type: map, expected an RFC 3339 formatted date and time string" }), labels: {}, wire_version: None, source: None })
[2025-06-10T20:15:13Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T20:15:17Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T20:15:20Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T20:15:23Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T20:15:27Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T20:15:30Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T20:15:34Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T20:15:37Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T20:15:40Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T20:15:44Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T20:15:47Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T20:15:50Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
