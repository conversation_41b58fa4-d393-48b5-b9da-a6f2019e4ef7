[2025-06-10T20:30:01Z INFO  Easybot::utils::logging] Logger initialized with level: INFO
[2025-06-10T20:30:01Z INFO  Easybot] Starting EasyBot...
[2025-06-10T20:30:01Z INFO  Easybot] Environment variables for port configuration:
[2025-06-10T20:30:01Z INFO  Easybot]   PORT=8000
[2025-06-10T20:30:01Z INFO  Easybot]   SOCKET_PORT=3000
[2025-06-10T20:30:01Z INFO  Easybot] Using port 8000 from config for health check service
[2025-06-10T20:30:01Z INFO  Easybot] Using port 3000 from config for socket service
[2025-06-10T20:30:01Z INFO  Easybot] Starting health check service on port 8000...
[2025-06-10T20:30:01Z INFO  Easybot] Initializing admin service and controllers...
[2025-06-10T20:30:01Z INFO  Easybot::service::health_service] Starting server on port 8000
[2025-06-10T20:30:01Z INFO  Easybot::service::health_service] Current working directory: "D:\\workspace\\.rust\\Easybot"
[2025-06-10T20:30:01Z INFO  Easybot::service::health_service] Trying to bind to 0.0.0.0:8000
[2025-06-10T20:30:01Z INFO  Easybot::service::health_service] Server successfully started on port 8000
[2025-06-10T20:30:01Z INFO  Easybot::service::health_service] Server is now running on port 8000
[2025-06-10T20:30:01Z INFO  Easybot] Health check and admin services initialization completed
[2025-06-10T20:30:01Z INFO  Easybot] Initializing database connection...
[2025-06-10T20:30:24Z INFO  Easybot] Database connection established
[2025-06-10T20:30:24Z INFO  Easybot] Checking and initializing default super admin...
[2025-06-10T20:30:24Z INFO  Easybot::service::admin_init_service] SuperAdmin already exists. Skipping default admin creation.
[2025-06-10T20:30:25Z WARN  Easybot::service::admin_init_service] ⚠️  SECURITY WARNING: Default admin is still using default password!
[2025-06-10T20:30:25Z WARN  Easybot::service::admin_init_service] ⚠️  Please change the password immediately for security!
[2025-06-10T20:30:25Z WARN  Easybot::service::admin_init_service] ⚠️  Username: Olajosh
[2025-06-10T20:30:25Z WARN  Easybot::service::admin_init_service] ⚠️  Email: <EMAIL>
[2025-06-10T20:30:25Z INFO  Easybot] Initializing cached price service...
[2025-06-10T20:30:25Z INFO  Easybot::service::cached_price_service] Initializing price cache for all supported blockchains...
[2025-06-10T20:30:28Z INFO  Easybot::service::price_service] DexScreener price for SOL: $163.68000000
[2025-06-10T20:30:28Z INFO  Easybot::service::cached_price_service] Fetched fresh price for SOL: $163.6800
[2025-06-10T20:30:28Z INFO  Easybot::service::cached_price_service] ✅ Initialized cache for SOL: $163.6800
[2025-06-10T20:30:29Z INFO  Easybot::service::price_service] DexScreener price for ETH: $2759.54000000
[2025-06-10T20:30:29Z INFO  Easybot::service::cached_price_service] Fetched fresh price for ETH: $2759.5400
[2025-06-10T20:30:29Z INFO  Easybot::service::cached_price_service] ✅ Initialized cache for ETH: $2759.5400
[2025-06-10T20:30:30Z INFO  Easybot::service::price_service] DexScreener price for BSC: $668.72000000
[2025-06-10T20:30:30Z INFO  Easybot::service::cached_price_service] Fetched fresh price for BSC: $668.7200
[2025-06-10T20:30:30Z INFO  Easybot::service::cached_price_service] ✅ Initialized cache for BSC: $668.7200
[2025-06-10T20:30:31Z INFO  Easybot::service::price_service] DexScreener price for BASE: $2760.81000000
[2025-06-10T20:30:31Z INFO  Easybot::service::cached_price_service] Fetched fresh price for BASE: $2760.8100
[2025-06-10T20:30:31Z INFO  Easybot::service::cached_price_service] ✅ Initialized cache for BASE: $2760.8100
[2025-06-10T20:30:31Z INFO  Easybot::service::cached_price_service] Price cache initialization completed
[2025-06-10T20:30:31Z INFO  Easybot] Price cache initialized successfully
[2025-06-10T20:30:31Z INFO  Easybot] Starting background price updater...
[2025-06-10T20:30:31Z INFO  Easybot::service::cached_price_service] Starting background price updater (interval: 3600s)
[2025-06-10T20:30:31Z INFO  Easybot] Background price updater started
[2025-06-10T20:30:31Z INFO  Easybot::service::cached_price_service] 🔄 Background price update started
[2025-06-10T20:30:31Z INFO  Easybot::service::price_service] Using cached native price for SOL: $163.6800
[2025-06-10T20:30:31Z INFO  Easybot::service::cached_price_service] Fetched fresh price for SOL: $163.6800
[2025-06-10T20:30:31Z INFO  Easybot] Initializing Solana trader service...
[2025-06-10T20:30:32Z INFO  Easybot] Solana trader service initialized
[2025-06-10T20:30:32Z INFO  Easybot] Initializing snipe worker...
[2025-06-10T20:30:32Z INFO  Easybot] Snipe worker initialized and started
[2025-06-10T20:30:32Z INFO  Easybot] Starting background fee collection service...
[2025-06-10T20:30:32Z INFO  Easybot] Background fee collection service started
[2025-06-10T20:30:32Z INFO  Easybot] Starting bot dispatcher...
[2025-06-10T20:30:32Z INFO  Easybot::service::price_service] Using cached native price for ETH: $2759.5400
[2025-06-10T20:30:32Z INFO  Easybot::service::cached_price_service] Fetched fresh price for ETH: $2759.5400
[2025-06-10T20:30:33Z INFO  Easybot::service::price_service] Using cached native price for BSC: $668.7200
[2025-06-10T20:30:33Z INFO  Easybot::service::cached_price_service] Fetched fresh price for BSC: $668.7200
[2025-06-10T20:30:34Z INFO  Easybot::service::price_service] Using cached native price for BASE: $2760.8100
[2025-06-10T20:30:34Z INFO  Easybot::service::cached_price_service] Fetched fresh price for BASE: $2760.8100
[2025-06-10T20:30:34Z INFO  Easybot::service::cached_price_service] 🔄 Background price update completed
[2025-06-10T20:30:35Z INFO  Easybot::controllers::transactions_controller] Transactions list requested by admin: Dev
[2025-06-10T20:30:35Z ERROR Easybot::controllers::transactions_controller] Database error while fetching transactions: DatabaseError(Error { kind: BsonDeserialization(DeserializationError { message: "expecting DateTime" }), labels: {}, wire_version: None, source: None })
[2025-06-10T20:30:36Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T20:30:39Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T20:30:43Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T20:30:46Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T20:30:49Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T20:30:53Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T20:30:56Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T20:31:00Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T20:31:03Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T20:31:07Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T20:31:10Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T20:31:13Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T20:31:16Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T20:31:20Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T20:31:23Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T20:31:26Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T20:31:30Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T20:31:33Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T20:31:36Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T20:31:40Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T20:31:43Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T20:31:46Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T20:31:50Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T20:31:53Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T20:31:56Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T20:31:58Z INFO  teloxide::dispatching::dispatcher] ^C received, trying to shutdown the dispatcher...
[2025-06-10T20:31:58Z INFO  teloxide::utils::shutdown_token] Trying to shutdown the dispatcher...
