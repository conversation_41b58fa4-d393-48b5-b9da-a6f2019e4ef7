[2025-06-11T13:33:03Z INFO  Easybot::utils::logging] Logger initialized with level: INFO
[2025-06-11T13:33:03Z INFO  Easybot] Starting EasyBot...
[2025-06-11T13:33:03Z INFO  Easybot] Environment variables for port configuration:
[2025-06-11T13:33:03Z INFO  Easybot]   PORT=8000
[2025-06-11T13:33:03Z INFO  Easybot]   SOCKET_PORT=3000
[2025-06-11T13:33:03Z INFO  Easybot] Using port 8000 from config for health check service
[2025-06-11T13:33:03Z INFO  Easybot] Using port 3000 from config for socket service
[2025-06-11T13:33:03Z INFO  Easybot] Starting health check service on port 8000...
[2025-06-11T13:33:03Z INFO  Easybot] Initializing admin service and controllers...
[2025-06-11T13:33:03Z INFO  Easybot::service::health_service] Starting server on port 8000
[2025-06-11T13:33:03Z INFO  Easybot::service::health_service] Current working directory: "D:\\workspace\\.rust\\Easybot"
[2025-06-11T13:33:03Z INFO  Easybot::service::health_service] Trying to bind to 0.0.0.0:8000
[2025-06-11T13:33:03Z INFO  Easybot::service::health_service] Server successfully started on port 8000
[2025-06-11T13:33:03Z INFO  Easybot::service::health_service] Server is now running on port 8000
[2025-06-11T13:33:04Z INFO  Easybot] Health check and admin services initialization completed
[2025-06-11T13:33:04Z INFO  Easybot] Initializing database connection...
[2025-06-11T13:33:24Z INFO  Easybot] Database connection established
[2025-06-11T13:33:24Z INFO  Easybot] Checking and initializing default super admin...
[2025-06-11T13:33:24Z INFO  Easybot::service::admin_init_service] SuperAdmin already exists. Skipping default admin creation.
[2025-06-11T13:33:26Z WARN  Easybot::service::admin_init_service] ⚠️  SECURITY WARNING: Default admin is still using default password!
[2025-06-11T13:33:26Z WARN  Easybot::service::admin_init_service] ⚠️  Please change the password immediately for security!
[2025-06-11T13:33:26Z WARN  Easybot::service::admin_init_service] ⚠️  Username: Olajosh
[2025-06-11T13:33:26Z WARN  Easybot::service::admin_init_service] ⚠️  Email: <EMAIL>
[2025-06-11T13:33:26Z INFO  Easybot] Initializing cached price service...
[2025-06-11T13:33:26Z INFO  Easybot::service::cached_price_service] Initializing price cache for all supported blockchains...
[2025-06-11T13:33:27Z INFO  Easybot::service::price_service] DexScreener price for SOL: $166.32000000
[2025-06-11T13:33:27Z INFO  Easybot::service::cached_price_service] Fetched fresh price for SOL: $166.3200
[2025-06-11T13:33:27Z INFO  Easybot::service::cached_price_service] ✅ Initialized cache for SOL: $166.3200
[2025-06-11T13:33:28Z INFO  Easybot::service::price_service] DexScreener price for ETH: $2799.46000000
[2025-06-11T13:33:28Z INFO  Easybot::service::cached_price_service] Fetched fresh price for ETH: $2799.4600
[2025-06-11T13:33:28Z INFO  Easybot::service::cached_price_service] ✅ Initialized cache for ETH: $2799.4600
[2025-06-11T13:33:29Z INFO  Easybot::service::price_service] DexScreener price for BSC: $668.11000000
[2025-06-11T13:33:29Z INFO  Easybot::service::cached_price_service] Fetched fresh price for BSC: $668.1100
[2025-06-11T13:33:30Z INFO  Easybot::service::cached_price_service] ✅ Initialized cache for BSC: $668.1100
[2025-06-11T13:33:32Z INFO  Easybot::service::price_service] DexScreener price for BASE: $2794.90000000
[2025-06-11T13:33:32Z INFO  Easybot::service::cached_price_service] Fetched fresh price for BASE: $2794.9000
[2025-06-11T13:33:32Z INFO  Easybot::service::cached_price_service] ✅ Initialized cache for BASE: $2794.9000
[2025-06-11T13:33:32Z INFO  Easybot::service::cached_price_service] Price cache initialization completed
[2025-06-11T13:33:32Z INFO  Easybot] Price cache initialized successfully
[2025-06-11T13:33:32Z INFO  Easybot] Starting background price updater...
[2025-06-11T13:33:32Z INFO  Easybot::service::cached_price_service] Starting background price updater (interval: 3600s)
[2025-06-11T13:33:32Z INFO  Easybot] Background price updater started
[2025-06-11T13:33:32Z INFO  Easybot::service::cached_price_service] 🔄 Background price update started
[2025-06-11T13:33:32Z INFO  Easybot::service::price_service] Using cached native price for SOL: $166.3200
[2025-06-11T13:33:32Z INFO  Easybot::service::cached_price_service] Fetched fresh price for SOL: $166.3200
[2025-06-11T13:33:32Z INFO  Easybot] Initializing Solana trader service...
[2025-06-11T13:33:32Z INFO  Easybot] Solana trader service initialized
[2025-06-11T13:33:32Z INFO  Easybot] Initializing snipe worker...
[2025-06-11T13:33:32Z INFO  Easybot] Snipe worker initialized and started
[2025-06-11T13:33:32Z INFO  Easybot] Starting background fee collection service...
[2025-06-11T13:33:32Z INFO  Easybot] Background fee collection service started
[2025-06-11T13:33:32Z INFO  Easybot] Starting bot dispatcher...
[2025-06-11T13:33:33Z INFO  Easybot::service::price_service] Using cached native price for ETH: $2799.4600
[2025-06-11T13:33:33Z INFO  Easybot::service::cached_price_service] Fetched fresh price for ETH: $2799.4600
[2025-06-11T13:33:34Z INFO  Easybot::service::price_service] Using cached native price for BSC: $668.1100
[2025-06-11T13:33:34Z INFO  Easybot::service::cached_price_service] Fetched fresh price for BSC: $668.1100
[2025-06-11T13:33:34Z INFO  Easybot::service::price_service] Using cached native price for BASE: $2794.9000
[2025-06-11T13:33:34Z INFO  Easybot::service::cached_price_service] Fetched fresh price for BASE: $2794.9000
[2025-06-11T13:33:35Z INFO  Easybot::service::cached_price_service] 🔄 Background price update completed
[2025-06-11T13:33:37Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-11T13:33:41Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-11T13:33:44Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-11T13:33:47Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-11T13:33:51Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-11T13:33:54Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-11T13:33:58Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-11T13:34:01Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-11T13:34:05Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-11T13:34:08Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-11T13:34:12Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-11T13:34:15Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-11T13:34:19Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-11T13:34:22Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-11T13:34:26Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-11T13:34:29Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-11T13:34:33Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-11T13:34:36Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-11T13:34:40Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-11T13:34:40Z INFO  Easybot::controllers::transactions_controller] Transactions list requested by admin: Dev
[2025-06-11T13:34:41Z INFO  Easybot::controllers::transactions_controller] Transactions list generated for Dev in 521.1614ms
[2025-06-11T13:34:41Z INFO  Easybot::controllers::transactions_controller] Fee statistics requested by admin: Dev
[2025-06-11T13:34:43Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-11T13:34:47Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-11T13:34:50Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-11T13:34:54Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-11T13:34:57Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-11T13:35:00Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-11T13:35:04Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-11T13:35:07Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-11T13:35:08Z INFO  Easybot::controllers::transactions_controller] Fee statistics requested by admin: Dev
[2025-06-11T13:35:10Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-11T13:35:14Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-11T13:35:17Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-11T13:35:21Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-11T13:35:24Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-11T13:35:27Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-11T13:35:31Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-11T13:35:34Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-11T13:35:37Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-11T13:35:39Z INFO  teloxide::dispatching::dispatcher] ^C received, trying to shutdown the dispatcher...
[2025-06-11T13:35:39Z INFO  teloxide::utils::shutdown_token] Trying to shutdown the dispatcher...
