[2025-06-10T22:49:08Z INFO  Easybot::utils::logging] Logger initialized with level: INFO
[2025-06-10T22:49:08Z INFO  Easybot] Starting EasyBot...
[2025-06-10T22:49:08Z INFO  Easybot] Environment variables for port configuration:
[2025-06-10T22:49:08Z INFO  Easybot]   PORT=8000
[2025-06-10T22:49:08Z INFO  Easybot]   SOCKET_PORT=3000
[2025-06-10T22:49:08Z INFO  Easybot] Using port 8000 from config for health check service
[2025-06-10T22:49:08Z INFO  Easybot] Using port 3000 from config for socket service
[2025-06-10T22:49:08Z INFO  Easybot] Starting health check service on port 8000...
[2025-06-10T22:49:08Z INFO  Easybot] Initializing admin service and controllers...
[2025-06-10T22:49:08Z INFO  Easybot::service::health_service] Starting server on port 8000
[2025-06-10T22:49:08Z INFO  Easybot::service::health_service] Current working directory: "D:\\workspace\\.rust\\Easybot"
[2025-06-10T22:49:08Z INFO  Easybot::service::health_service] Trying to bind to 0.0.0.0:8000
[2025-06-10T22:49:08Z INFO  Easybot::service::health_service] Server successfully started on port 8000
[2025-06-10T22:49:08Z INFO  Easybot::service::health_service] Server is now running on port 8000
[2025-06-10T22:49:08Z INFO  Easybot] Health check and admin services initialization completed
[2025-06-10T22:49:08Z INFO  Easybot] Initializing database connection...
[2025-06-10T22:49:43Z INFO  Easybot] Database connection established
[2025-06-10T22:49:43Z INFO  Easybot] Checking and initializing default super admin...
[2025-06-10T22:49:44Z INFO  Easybot::service::admin_init_service] SuperAdmin already exists. Skipping default admin creation.
[2025-06-10T22:49:48Z WARN  Easybot::service::admin_init_service] ⚠️  SECURITY WARNING: Default admin is still using default password!
[2025-06-10T22:49:48Z WARN  Easybot::service::admin_init_service] ⚠️  Please change the password immediately for security!
[2025-06-10T22:49:48Z WARN  Easybot::service::admin_init_service] ⚠️  Username: Olajosh
[2025-06-10T22:49:48Z WARN  Easybot::service::admin_init_service] ⚠️  Email: <EMAIL>
[2025-06-10T22:49:48Z INFO  Easybot] Initializing cached price service...
[2025-06-10T22:49:48Z INFO  Easybot::service::cached_price_service] Initializing price cache for all supported blockchains...
[2025-06-10T22:49:51Z INFO  Easybot::service::price_service] DexScreener price for SOL: $164.43000000
[2025-06-10T22:49:51Z INFO  Easybot::service::cached_price_service] Fetched fresh price for SOL: $164.4300
[2025-06-10T22:49:51Z INFO  Easybot::service::cached_price_service] ✅ Initialized cache for SOL: $164.4300
[2025-06-10T22:49:53Z INFO  Easybot::service::price_service] DexScreener price for ETH: $2803.94000000
[2025-06-10T22:49:53Z INFO  Easybot::service::cached_price_service] Fetched fresh price for ETH: $2803.9400
[2025-06-10T22:49:54Z INFO  Easybot::service::cached_price_service] ✅ Initialized cache for ETH: $2803.9400
[2025-06-10T22:49:55Z INFO  Easybot::service::price_service] DexScreener price for BSC: $670.21000000
[2025-06-10T22:49:55Z INFO  Easybot::service::cached_price_service] Fetched fresh price for BSC: $670.2100
[2025-06-10T22:49:56Z INFO  Easybot::service::cached_price_service] ✅ Initialized cache for BSC: $670.2100
[2025-06-10T22:49:58Z INFO  Easybot::service::price_service] DexScreener price for BASE: $2804.36000000
[2025-06-10T22:49:58Z INFO  Easybot::service::cached_price_service] Fetched fresh price for BASE: $2804.3600
[2025-06-10T22:50:00Z INFO  Easybot::service::cached_price_service] ✅ Initialized cache for BASE: $2804.3600
[2025-06-10T22:50:00Z INFO  Easybot::service::cached_price_service] Price cache initialization completed
[2025-06-10T22:50:00Z INFO  Easybot] Price cache initialized successfully
[2025-06-10T22:50:00Z INFO  Easybot] Starting background price updater...
[2025-06-10T22:50:00Z INFO  Easybot::service::cached_price_service] Starting background price updater (interval: 3600s)
[2025-06-10T22:50:00Z INFO  Easybot] Background price updater started
[2025-06-10T22:50:00Z INFO  Easybot::service::cached_price_service] 🔄 Background price update started
[2025-06-10T22:50:00Z INFO  Easybot::service::price_service] Using cached native price for SOL: $164.4300
[2025-06-10T22:50:00Z INFO  Easybot::service::cached_price_service] Fetched fresh price for SOL: $164.4300
[2025-06-10T22:50:00Z INFO  Easybot] Initializing Solana trader service...
[2025-06-10T22:50:01Z INFO  Easybot::service::price_service] Using cached native price for ETH: $2803.9400
[2025-06-10T22:50:01Z INFO  Easybot::service::cached_price_service] Fetched fresh price for ETH: $2803.9400
[2025-06-10T22:50:02Z INFO  Easybot::service::price_service] Using cached native price for BSC: $670.2100
[2025-06-10T22:50:02Z INFO  Easybot::service::cached_price_service] Fetched fresh price for BSC: $670.2100
[2025-06-10T22:50:03Z INFO  Easybot::service::price_service] Using cached native price for BASE: $2804.3600
[2025-06-10T22:50:03Z INFO  Easybot::service::cached_price_service] Fetched fresh price for BASE: $2804.3600
[2025-06-10T22:50:03Z INFO  Easybot] Solana trader service initialized
[2025-06-10T22:50:03Z INFO  Easybot] Initializing snipe worker...
[2025-06-10T22:50:03Z INFO  Easybot] Snipe worker initialized and started
[2025-06-10T22:50:03Z INFO  Easybot] Starting background fee collection service...
[2025-06-10T22:50:03Z INFO  Easybot] Background fee collection service started
[2025-06-10T22:50:03Z INFO  Easybot] Starting bot dispatcher...
[2025-06-10T22:50:04Z INFO  Easybot::service::cached_price_service] 🔄 Background price update completed
[2025-06-10T22:50:10Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:50:15Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:50:16Z INFO  Easybot::controllers::auth_controller] Login attempt for user: Dev
[2025-06-10T22:50:19Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:50:20Z INFO  Easybot::controllers::auth_controller] Successful login for user: Dev in 3.3274152s
[2025-06-10T22:50:20Z INFO  Easybot::controllers::analytics_controller] Dashboard data requested by admin: 683a0b0944ad9dc5fa324858
[2025-06-10T22:50:20Z INFO  Easybot::controllers::analytics_controller] Calculating dashboard analytics for period: 1749509420 to 1749595820
[2025-06-10T22:50:22Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:50:23Z INFO  Easybot::controllers::analytics_controller] Dashboard data generated in 2.7471876s
[2025-06-10T22:50:25Z INFO  Easybot::controllers::transactions_controller] Transactions list requested by admin: Dev
[2025-06-10T22:50:26Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:50:26Z INFO  Easybot::controllers::transactions_controller] Transactions list generated for Dev in 1.0720334s
[2025-06-10T22:50:26Z INFO  Easybot::controllers::transactions_controller] Fee statistics requested by admin: Dev
[2025-06-10T22:50:29Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:50:33Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:50:37Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:50:41Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:50:44Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:50:48Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:50:52Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:50:55Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:50:59Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:51:02Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:51:06Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:51:10Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:51:13Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:51:17Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:51:20Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:51:24Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:51:27Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:51:31Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:51:35Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:51:38Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:51:42Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:51:45Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:51:49Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:51:53Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:51:57Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:52:01Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:52:04Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:52:08Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:52:11Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:52:15Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:52:19Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:52:22Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:52:26Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:52:29Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:52:33Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:52:37Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:52:40Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:52:44Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:52:48Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:52:52Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:52:55Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:52:56Z INFO  Easybot] Received callback query with data: {"command":"view_sol"}
[2025-06-10T22:52:59Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:53:04Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:53:07Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:53:11Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:53:15Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:53:19Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:53:22Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:53:26Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:53:29Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:53:33Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:53:36Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:53:40Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:53:44Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:53:47Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:53:51Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:53:52Z INFO  Easybot] Received callback query with data: {"command":"scan_contract_sol"}
[2025-06-10T22:53:54Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:53:58Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:54:02Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:54:05Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:54:09Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:54:12Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:54:16Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:54:18Z INFO  Easybot] Received callback query with data: spend_token:sol:7hB4AgnZp7RiThvtFTGMedkrU3G8X34fwHzy1vQjpump
[2025-06-10T22:54:19Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:54:23Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:54:27Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:54:31Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:54:34Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:54:38Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:54:41Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:54:45Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:54:48Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:54:52Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:54:56Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:54:59Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:55:03Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:55:06Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:55:10Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:55:13Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:55:17Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:55:21Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:55:24Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:55:28Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:55:32Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:55:36Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:55:36Z INFO  Easybot::service::price_service] DexScreener price for SOL: $164.27000000
[2025-06-10T22:55:36Z INFO  Easybot::service::price_service] Minimum fee for SOL: 0.00152188 tokens ($0.25 USD at $164.2700/token)
[2025-06-10T22:55:36Z INFO  Easybot::service::price_service] 💰 Fee amount 0.00000005 below minimum 0.00152188 for SOL, adjusting to minimum $0.25 equivalent
[2025-06-10T22:55:39Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:55:39Z INFO  Easybot::service::price_service] DexScreener price for SOL: $164.27000000
[2025-06-10T22:55:39Z INFO  Easybot::service::price_service] Minimum fee for SOL: 0.00152188 tokens ($0.25 USD at $164.2700/token)
[2025-06-10T22:55:39Z INFO  Easybot::service::price_service] 💰 Fee amount 0.00000005 below minimum 0.00152188 for SOL, adjusting to minimum $0.25 equivalent
[2025-06-10T22:55:39Z INFO  Easybot::service::price_service] Using cached native price for SOL: $164.2700
[2025-06-10T22:55:39Z INFO  Easybot::service::price_service] 🔥 Admin fee calculation: base=0.00001000, percentage=0.50%, calculated=0.00000005, final=0.00152188 SOL ($0.2500)
[2025-06-10T22:55:40Z INFO  Easybot::service::admin_fee_service] Created admin fee transaction: id=6848b7ec6a19e3593cf5e2d7, user_id=683cab67f5800b0c46d3ba1b, blockchain=SOL, type=BuyFee, fee_amount=0.0015218847020149753
[2025-06-10T22:55:42Z INFO  Easybot::service::price_service] DexScreener price for SOL: $164.27000000
[2025-06-10T22:55:42Z INFO  Easybot::service::price_service] Minimum fee for SOL: 0.00152188 tokens ($0.25 USD at $164.2700/token)
[2025-06-10T22:55:42Z INFO  Easybot::service::price_service] 💰 Fee amount 0.00152188 below minimum 0.00152188 for SOL, adjusting to minimum $0.25 equivalent
[2025-06-10T22:55:43Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:55:46Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:55:50Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:55:50Z INFO  Easybot::service::admin_fee_service] ✅ Fee transaction completed with adjusted amount: id=6848b7ec6a19e3593cf5e2d7, hash=4riFHFoXEQLdka1r5Fgqr6qDntvJG2LYeEA7RS4Ws41Fj2QCjnikcYDtxF5JF5TkitsSmKzfamUMrEhdWc4LaCv1, actual_fee=0.001521884
[2025-06-10T22:55:54Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:55:54Z INFO  Easybot::service::price_service] DexScreener price for SOL: $164.27000000
[2025-06-10T22:55:54Z INFO  Easybot::service::price_service] Minimum fee for SOL: 0.00152188 tokens ($0.25 USD at $164.2700/token)
[2025-06-10T22:55:54Z INFO  Easybot::service::price_service] 💰 Fee amount 0.00000005 below minimum 0.00152188 for SOL, adjusting to minimum $0.25 equivalent
[2025-06-10T22:55:54Z INFO  Easybot::service::price_service] Using cached native price for SOL: $164.2700
[2025-06-10T22:55:54Z INFO  Easybot::service::price_service] 🔥 Admin fee calculation: base=0.00001000, percentage=0.50%, calculated=0.00000005, final=0.00152188 SOL ($0.2500)
[2025-06-10T22:55:57Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:56:01Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:56:04Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:56:08Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:56:11Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:56:15Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:56:18Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:56:22Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:56:25Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:56:29Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:56:33Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:56:36Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:56:40Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:56:43Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:56:47Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:56:50Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:56:54Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:56:57Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:57:01Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:57:04Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:57:08Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:57:11Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:57:15Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:57:18Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:57:22Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:57:26Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:57:29Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:57:33Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:57:36Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:57:40Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:57:44Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:57:47Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:57:51Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:57:54Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:57:58Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:58:01Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:58:05Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:58:08Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:58:12Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:58:15Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:58:19Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:58:22Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:58:26Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:58:29Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:58:33Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:58:37Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:58:41Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:58:44Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:58:48Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:58:51Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:58:55Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:58:59Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:59:03Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:59:06Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:59:10Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:59:13Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:59:17Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:59:21Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T22:59:24Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
