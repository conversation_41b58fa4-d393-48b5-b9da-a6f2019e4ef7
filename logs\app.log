[2025-06-10T16:28:53Z INFO  Easybot::utils::logging] Logger initialized with level: INFO
[2025-06-10T16:28:53Z INFO  Easybot] Starting EasyBot...
[2025-06-10T16:28:53Z INFO  Easybot] Environment variables for port configuration:
[2025-06-10T16:28:53Z INFO  Easybot]   PORT=8000
[2025-06-10T16:28:53Z INFO  Easybot]   SOCKET_PORT=3000
[2025-06-10T16:28:53Z INFO  Easybot] Using port 8000 from config for health check service
[2025-06-10T16:28:53Z INFO  Easybot] Using port 3000 from config for socket service
[2025-06-10T16:28:53Z INFO  Easybot] Starting health check service on port 8000...
[2025-06-10T16:28:53Z INFO  Easybot] Initializing admin service and controllers...
[2025-06-10T16:28:53Z INFO  Easybot::service::health_service] Starting server on port 8000
[2025-06-10T16:28:53Z INFO  Easybot::service::health_service] Current working directory: "D:\\workspace\\.rust\\Easybot"
[2025-06-10T16:28:53Z INFO  Easybot::service::health_service] Trying to bind to 0.0.0.0:8000
[2025-06-10T16:28:53Z INFO  Easybot::service::health_service] Server successfully started on port 8000
[2025-06-10T16:28:53Z INFO  Easybot::service::health_service] Server is now running on port 8000
[2025-06-10T16:28:54Z INFO  Easybot] Health check and admin services initialization completed
[2025-06-10T16:28:54Z INFO  Easybot] Initializing database connection...
[2025-06-10T16:29:20Z INFO  Easybot] Database connection established
[2025-06-10T16:29:20Z INFO  Easybot] Checking and initializing default super admin...
[2025-06-10T16:29:20Z INFO  Easybot::service::admin_init_service] SuperAdmin already exists. Skipping default admin creation.
[2025-06-10T16:29:23Z WARN  Easybot::service::admin_init_service] ⚠️  SECURITY WARNING: Default admin is still using default password!
[2025-06-10T16:29:23Z WARN  Easybot::service::admin_init_service] ⚠️  Please change the password immediately for security!
[2025-06-10T16:29:23Z WARN  Easybot::service::admin_init_service] ⚠️  Username: Olajosh
[2025-06-10T16:29:23Z WARN  Easybot::service::admin_init_service] ⚠️  Email: <EMAIL>
[2025-06-10T16:29:23Z INFO  Easybot] Initializing Solana trader service...
[2025-06-10T16:29:23Z INFO  Easybot] Solana trader service initialized
[2025-06-10T16:29:23Z INFO  Easybot] Initializing snipe worker...
[2025-06-10T16:29:23Z INFO  Easybot] Snipe worker initialized and started
[2025-06-10T16:29:23Z INFO  Easybot] Starting background fee collection service...
[2025-06-10T16:29:23Z INFO  Easybot] Background fee collection service started
[2025-06-10T16:29:23Z INFO  Easybot] Starting bot dispatcher...
[2025-06-10T16:29:29Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T16:29:32Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T16:29:36Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T16:29:39Z ERROR teloxide::error_handlers] An error from the update listener: Api(TerminatedByOtherGetUpdates)
[2025-06-10T16:29:46Z INFO  Easybot] Received callback query with data: back_to_last_token
[2025-06-10T16:30:07Z INFO  Easybot] Received callback query with data: {"command":"scan_contract_base"}
[2025-06-10T16:30:23Z INFO  Easybot] Received callback query with data: {"command":"view_sol"}
[2025-06-10T16:30:28Z INFO  Easybot] Received callback query with data: {"command":"scan_contract_sol"}
[2025-06-10T16:30:42Z INFO  Easybot] Received callback query with data: sell_token:sol:7hB4AgnZp7RiThvtFTGMedkrU3G8X34fwHzy1vQjpump
[2025-06-10T16:30:58Z INFO  Easybot::service::admin_fee_service] Created admin fee transaction: id=68485dc229d1868e96407033, user_id=683cab67f5800b0c46d3ba1b, blockchain=SOL, type=SellFee, fee_amount=0.000000024
[2025-06-10T16:30:59Z INFO  Easybot::service::price_service] DexScreener price for SOL: $158.20000000
[2025-06-10T16:30:59Z INFO  Easybot::service::price_service] Minimum fee for SOL: 0.00158028 tokens ($0.25 USD at $158.2000/token)
[2025-06-10T16:30:59Z INFO  Easybot::service::price_service] 💰 Fee amount 0.00000002 below minimum 0.00158028 for SOL, adjusting to minimum $0.25 equivalent
[2025-06-10T16:31:02Z INFO  Easybot::service::price_service] DexScreener price for SOL: $158.20000000
[2025-06-10T16:31:02Z INFO  Easybot::service::price_service] Minimum fee for SOL: 0.00158028 tokens ($0.25 USD at $158.2000/token)
[2025-06-10T16:31:02Z INFO  Easybot::service::price_service] 💰 Fee amount 0.00000002 below minimum 0.00158028 for SOL, adjusting to minimum $0.25 equivalent
[2025-06-10T16:31:55Z INFO  Easybot] Received callback query with data: back_to_last_token
