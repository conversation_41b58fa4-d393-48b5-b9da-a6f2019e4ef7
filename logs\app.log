[2025-06-10T17:48:16Z INFO  Easybot::utils::logging] Logger initialized with level: INFO
[2025-06-10T17:48:16Z INFO  Easybot] Starting EasyBot...
[2025-06-10T17:48:16Z INFO  Easybot] Environment variables for port configuration:
[2025-06-10T17:48:16Z INFO  Easybot]   PORT=8000
[2025-06-10T17:48:16Z INFO  Easybot]   SOCKET_PORT=3000
[2025-06-10T17:48:16Z INFO  Easybot] Using port 8000 from config for health check service
[2025-06-10T17:48:16Z INFO  Easybot] Using port 3000 from config for socket service
[2025-06-10T17:48:16Z INFO  Easybot] Starting health check service on port 8000...
[2025-06-10T17:48:16Z INFO  Easybot] Initializing admin service and controllers...
[2025-06-10T17:48:16Z INFO  Easybot::service::health_service] Starting server on port 8000
[2025-06-10T17:48:16Z INFO  Easybot::service::health_service] Current working directory: "D:\\workspace\\.rust\\Easybot"
[2025-06-10T17:48:16Z INFO  Easybot::service::health_service] Trying to bind to 0.0.0.0:8000
[2025-06-10T17:48:16Z INFO  Easybot::service::health_service] Server successfully started on port 8000
[2025-06-10T17:48:16Z INFO  Easybot::service::health_service] Server is now running on port 8000
[2025-06-10T17:48:16Z INFO  Easybot] Health check and admin services initialization completed
[2025-06-10T17:48:16Z INFO  Easybot] Initializing database connection...
[2025-06-10T17:48:42Z INFO  Easybot] Database connection established
[2025-06-10T17:48:42Z INFO  Easybot] Checking and initializing default super admin...
[2025-06-10T17:48:42Z INFO  Easybot::service::admin_init_service] SuperAdmin already exists. Skipping default admin creation.
[2025-06-10T17:48:45Z WARN  Easybot::service::admin_init_service] ⚠️  SECURITY WARNING: Default admin is still using default password!
[2025-06-10T17:48:45Z WARN  Easybot::service::admin_init_service] ⚠️  Please change the password immediately for security!
[2025-06-10T17:48:45Z WARN  Easybot::service::admin_init_service] ⚠️  Username: Olajosh
[2025-06-10T17:48:45Z WARN  Easybot::service::admin_init_service] ⚠️  Email: <EMAIL>
[2025-06-10T17:48:45Z INFO  Easybot] Initializing Solana trader service...
[2025-06-10T17:48:46Z INFO  Easybot] Solana trader service initialized
[2025-06-10T17:48:46Z INFO  Easybot] Initializing snipe worker...
[2025-06-10T17:48:46Z INFO  Easybot] Snipe worker initialized and started
[2025-06-10T17:48:46Z INFO  Easybot] Starting background fee collection service...
[2025-06-10T17:48:46Z INFO  Easybot] Background fee collection service started
[2025-06-10T17:48:46Z INFO  Easybot] Starting bot dispatcher...
[2025-06-10T17:49:02Z INFO  Easybot::controllers::transactions_controller] Transactions list requested by admin: Dev
[2025-06-10T17:49:04Z INFO  Easybot::controllers::transactions_controller] Transactions list generated for Dev in 2.617136s
[2025-06-10T17:49:04Z INFO  Easybot::controllers::transactions_controller] Fee statistics requested by admin: Dev
[2025-06-10T17:49:30Z INFO  Easybot::controllers::transactions_controller] Transactions list requested by admin: Dev
[2025-06-10T17:49:32Z INFO  Easybot::controllers::transactions_controller] Transactions list generated for Dev in 2.0214992s
[2025-06-10T17:49:32Z INFO  Easybot::controllers::transactions_controller] Fee statistics requested by admin: Dev
[2025-06-10T17:49:39Z INFO  Easybot::controllers::transactions_controller] Transactions list requested by admin: Dev
[2025-06-10T17:49:41Z INFO  Easybot::controllers::transactions_controller] Transactions list generated for Dev in 2.519727s
[2025-06-10T17:49:41Z INFO  Easybot::controllers::transactions_controller] Fee statistics requested by admin: Dev
[2025-06-10T17:49:46Z INFO  Easybot] Received callback query with data: back_to_last_token
[2025-06-10T17:49:59Z INFO  Easybot] Received callback query with data: sell_token:sol:7hB4AgnZp7RiThvtFTGMedkrU3G8X34fwHzy1vQjpump
[2025-06-10T17:50:20Z INFO  Easybot::service::price_service] DexScreener price for SOL: $158.23000000
[2025-06-10T17:50:20Z INFO  Easybot::service::price_service] Minimum fee for SOL: 0.00157998 tokens ($0.25 USD at $158.2300/token)
[2025-06-10T17:50:20Z INFO  Easybot::service::price_service] 💰 Fee amount 0.00000084 below minimum 0.00157998 for SOL, adjusting to minimum $0.25 equivalent
[2025-06-10T17:50:26Z INFO  Easybot::service::admin_fee_service] Created admin fee transaction: id=684870618e96b56aa3776701, user_id=683cab67f5800b0c46d3ba1b, blockchain=SOL, type=SellFee, fee_amount=0.0015799785122922328
[2025-06-10T17:50:27Z INFO  Easybot::service::price_service] DexScreener price for SOL: $158.23000000
[2025-06-10T17:50:27Z INFO  Easybot::service::price_service] Minimum fee for SOL: 0.00157998 tokens ($0.25 USD at $158.2300/token)
[2025-06-10T17:50:27Z INFO  Easybot::service::price_service] 💰 Fee amount 0.00157998 below minimum 0.00157998 for SOL, adjusting to minimum $0.25 equivalent
[2025-06-10T17:50:30Z INFO  Easybot::service::admin_fee_service] ✅ Fee transaction completed with adjusted amount: id=684870618e96b56aa3776701, hash=2LkBNj5VtUZqMwJ1aQbAMPyYvfo9EXvqsRQNrd5AptzihoDMcJhLhyKumNNUbbruPSiyEKiM9a2Y9yLR3TDLGNRM, actual_fee=0.0015799785122922328
[2025-06-10T17:50:43Z INFO  Easybot::controllers::transactions_controller] Transactions list requested by admin: Dev
[2025-06-10T17:50:47Z ERROR Easybot::controllers::transactions_controller] Database error while fetching transactions: DatabaseError(Error { kind: BsonDeserialization(DeserializationError { message: "invalid type: map, expected an RFC 3339 formatted date and time string" }), labels: {}, wire_version: None, source: None })
[2025-06-10T17:50:54Z INFO  Easybot::controllers::transactions_controller] Transactions list requested by admin: Dev
[2025-06-10T17:50:54Z ERROR Easybot::controllers::transactions_controller] Database error while fetching transactions: DatabaseError(Error { kind: BsonDeserialization(DeserializationError { message: "invalid type: map, expected an RFC 3339 formatted date and time string" }), labels: {}, wire_version: None, source: None })
[2025-06-10T17:51:01Z INFO  Easybot::controllers::transactions_controller] Transactions list requested by admin: Dev
[2025-06-10T17:51:02Z ERROR Easybot::controllers::transactions_controller] Database error while fetching transactions: DatabaseError(Error { kind: BsonDeserialization(DeserializationError { message: "invalid type: map, expected an RFC 3339 formatted date and time string" }), labels: {}, wire_version: None, source: None })
[2025-06-10T17:51:04Z INFO  Easybot::controllers::transactions_controller] Transactions list requested by admin: Dev
[2025-06-10T17:51:04Z ERROR Easybot::controllers::transactions_controller] Database error while fetching transactions: DatabaseError(Error { kind: BsonDeserialization(DeserializationError { message: "invalid type: map, expected an RFC 3339 formatted date and time string" }), labels: {}, wire_version: None, source: None })
[2025-06-10T17:51:06Z INFO  Easybot::controllers::analytics_controller] Dashboard data requested by admin: 683a0b0944ad9dc5fa324858
[2025-06-10T17:51:06Z INFO  Easybot::controllers::analytics_controller] Calculating dashboard analytics for period: 1749491466 to 1749577866
[2025-06-10T17:51:10Z INFO  Easybot::controllers::analytics_controller] Dashboard data generated in 4.250444s
[2025-06-10T17:51:13Z INFO  Easybot::controllers::transactions_controller] Transactions list requested by admin: Dev
[2025-06-10T17:51:14Z ERROR Easybot::controllers::transactions_controller] Database error while fetching transactions: DatabaseError(Error { kind: BsonDeserialization(DeserializationError { message: "invalid type: map, expected an RFC 3339 formatted date and time string" }), labels: {}, wire_version: None, source: None })
[2025-06-10T17:51:38Z INFO  Easybot::controllers::transactions_controller] Transactions list requested by admin: Dev
[2025-06-10T17:51:38Z ERROR Easybot::controllers::transactions_controller] Database error while fetching transactions: DatabaseError(Error { kind: BsonDeserialization(DeserializationError { message: "invalid type: map, expected an RFC 3339 formatted date and time string" }), labels: {}, wire_version: None, source: None })
[2025-06-10T17:52:02Z INFO  Easybot::controllers::transactions_controller] Transactions list requested by admin: Dev
[2025-06-10T17:52:02Z ERROR Easybot::controllers::transactions_controller] Database error while fetching transactions: DatabaseError(Error { kind: BsonDeserialization(DeserializationError { message: "invalid type: map, expected an RFC 3339 formatted date and time string" }), labels: {}, wire_version: None, source: None })
[2025-06-10T17:52:22Z INFO  Easybot::controllers::transactions_controller] Transactions list requested by admin: Dev
[2025-06-10T17:52:23Z ERROR Easybot::controllers::transactions_controller] Database error while fetching transactions: DatabaseError(Error { kind: BsonDeserialization(DeserializationError { message: "invalid type: map, expected an RFC 3339 formatted date and time string" }), labels: {}, wire_version: None, source: None })
[2025-06-10T17:52:29Z INFO  Easybot::controllers::users_controller] Users list requested by admin: Dev
[2025-06-10T17:52:32Z INFO  Easybot::controllers::users_controller] Users list generated for Dev in 3.2310435s
[2025-06-10T17:52:40Z INFO  Easybot::controllers::transactions_controller] Transactions list requested by admin: Dev
[2025-06-10T17:52:40Z ERROR Easybot::controllers::transactions_controller] Database error while fetching transactions: DatabaseError(Error { kind: BsonDeserialization(DeserializationError { message: "invalid type: map, expected an RFC 3339 formatted date and time string" }), labels: {}, wire_version: None, source: None })
[2025-06-10T17:52:58Z INFO  Easybot::controllers::transactions_controller] Transactions list requested by admin: Dev
[2025-06-10T17:52:59Z ERROR Easybot::controllers::transactions_controller] Database error while fetching transactions: DatabaseError(Error { kind: BsonDeserialization(DeserializationError { message: "invalid type: map, expected an RFC 3339 formatted date and time string" }), labels: {}, wire_version: None, source: None })
[2025-06-10T17:53:23Z INFO  Easybot::controllers::analytics_controller] Dashboard data requested by admin: 683a0b0944ad9dc5fa324858
[2025-06-10T17:57:09Z INFO  Easybot::controllers::analytics_controller] Dashboard data requested by admin: 683a0b0944ad9dc5fa324858
[2025-06-10T17:57:09Z INFO  Easybot::controllers::analytics_controller] Calculating dashboard analytics for period: 1749491829 to 1749578229
[2025-06-10T17:57:13Z INFO  Easybot::controllers::analytics_controller] Dashboard data generated in 3.5800919s
[2025-06-10T17:57:26Z INFO  Easybot::controllers::analytics_controller] Dashboard data requested by admin: 683a0b0944ad9dc5fa324858
[2025-06-10T18:03:01Z INFO  teloxide::dispatching::dispatcher] ^C received, trying to shutdown the dispatcher...
[2025-06-10T18:03:01Z INFO  teloxide::utils::shutdown_token] Trying to shutdown the dispatcher...
