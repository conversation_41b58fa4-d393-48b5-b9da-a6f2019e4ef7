[2025-06-10T16:41:15Z INFO  Easybot::utils::logging] Logger initialized with level: INFO
[2025-06-10T16:41:15Z INFO  Easybot] Starting EasyBot...
[2025-06-10T16:41:15Z INFO  Easybot] Environment variables for port configuration:
[2025-06-10T16:41:15Z INFO  Easybot]   PORT=8000
[2025-06-10T16:41:15Z INFO  Easybot]   SOCKET_PORT=3000
[2025-06-10T16:41:15Z INFO  Easybot] Using port 8000 from config for health check service
[2025-06-10T16:41:15Z INFO  Easybot] Using port 3000 from config for socket service
[2025-06-10T16:41:15Z INFO  Easybot] Starting health check service on port 8000...
[2025-06-10T16:41:15Z INFO  Easybot] Initializing admin service and controllers...
[2025-06-10T16:41:15Z INFO  Easybot::service::health_service] Starting server on port 8000
[2025-06-10T16:41:15Z INFO  Easybot::service::health_service] Current working directory: "D:\\workspace\\.rust\\Easybot"
[2025-06-10T16:41:15Z INFO  Easybot::service::health_service] Trying to bind to 0.0.0.0:8000
[2025-06-10T16:41:15Z INFO  Easybot::service::health_service] Server successfully started on port 8000
[2025-06-10T16:41:15Z INFO  Easybot::service::health_service] Server is now running on port 8000
[2025-06-10T16:41:16Z INFO  Easybot] Health check and admin services initialization completed
[2025-06-10T16:41:16Z INFO  Easybot] Initializing database connection...
[2025-06-10T16:41:45Z INFO  Easybot] Database connection established
[2025-06-10T16:41:45Z INFO  Easybot] Checking and initializing default super admin...
[2025-06-10T16:41:45Z INFO  Easybot::service::admin_init_service] SuperAdmin already exists. Skipping default admin creation.
[2025-06-10T16:41:48Z WARN  Easybot::service::admin_init_service] ⚠️  SECURITY WARNING: Default admin is still using default password!
[2025-06-10T16:41:48Z WARN  Easybot::service::admin_init_service] ⚠️  Please change the password immediately for security!
[2025-06-10T16:41:48Z WARN  Easybot::service::admin_init_service] ⚠️  Username: Olajosh
[2025-06-10T16:41:48Z WARN  Easybot::service::admin_init_service] ⚠️  Email: <EMAIL>
[2025-06-10T16:41:48Z INFO  Easybot] Initializing Solana trader service...
[2025-06-10T16:41:50Z INFO  Easybot] Solana trader service initialized
[2025-06-10T16:41:50Z INFO  Easybot] Initializing snipe worker...
[2025-06-10T16:41:50Z INFO  Easybot] Snipe worker initialized and started
[2025-06-10T16:41:50Z INFO  Easybot] Starting background fee collection service...
[2025-06-10T16:41:50Z INFO  Easybot] Background fee collection service started
[2025-06-10T16:41:50Z INFO  Easybot] Starting bot dispatcher...
[2025-06-10T16:43:23Z INFO  Easybot] Received callback query with data: spend_token:sol:7hB4AgnZp7RiThvtFTGMedkrU3G8X34fwHzy1vQjpump
[2025-06-10T16:43:54Z INFO  Easybot] Received callback query with data: spend_token:sol:7hB4AgnZp7RiThvtFTGMedkrU3G8X34fwHzy1vQjpump
[2025-06-10T16:44:30Z INFO  Easybot::service::price_service] DexScreener price for SOL: $157.75000000
[2025-06-10T16:44:30Z INFO  Easybot::service::price_service] Minimum fee for SOL: 0.00158479 tokens ($0.25 USD at $157.7500/token)
[2025-06-10T16:44:30Z INFO  Easybot::service::price_service] 💰 Fee amount 0.00000050 below minimum 0.00158479 for SOL, adjusting to minimum $0.25 equivalent
[2025-06-10T16:44:33Z INFO  Easybot::service::admin_fee_service] Created admin fee transaction: id=684860f1aaa8707eadd0de4e, user_id=683cab67f5800b0c46d3ba1b, blockchain=SOL, type=BuyFee, fee_amount=0.001584786
[2025-06-10T16:44:34Z INFO  Easybot::service::price_service] DexScreener price for SOL: $157.75000000
[2025-06-10T16:44:34Z INFO  Easybot::service::price_service] Minimum fee for SOL: 0.00158479 tokens ($0.25 USD at $157.7500/token)
[2025-06-10T16:44:34Z INFO  Easybot::service::price_service] 💰 Fee amount 0.00158479 below minimum 0.00158479 for SOL, adjusting to minimum $0.25 equivalent
[2025-06-10T16:44:39Z INFO  Easybot::service::admin_fee_service] Marked fee transaction as completed: id=684860f1aaa8707eadd0de4e, hash=5Zn4fm6Xq9rJX5ACjFoT55fRRBozZshVfbcgyyB5qvjfMPbDPNCU4EcxG2S8GweVtipvMhBSdcyZxPj7XxuE36Xo
[2025-06-10T16:45:01Z INFO  Easybot::controllers::transactions_controller] Transactions list requested by admin: Dev
[2025-06-10T16:45:03Z INFO  Easybot::controllers::transactions_controller] Transactions list generated for Dev in 2.6088081s
[2025-06-10T16:50:42Z INFO  Easybot::controllers::transactions_controller] Transactions list requested by admin: Dev
[2025-06-10T16:50:46Z INFO  Easybot::controllers::transactions_controller] Transactions list generated for Dev in 3.195045s
[2025-06-10T16:55:09Z INFO  teloxide::dispatching::dispatcher] ^C received, trying to shutdown the dispatcher...
[2025-06-10T16:55:09Z INFO  teloxide::utils::shutdown_token] Trying to shutdown the dispatcher...
[2025-06-10T16:55:13Z INFO  teloxide::utils::shutdown_token] Dispatching has been shut down.
[2025-06-10T16:55:13Z INFO  teloxide::dispatching::dispatcher] dispatcher is shutdown...
[2025-06-10T16:55:13Z INFO  Easybot] Bot stopped
