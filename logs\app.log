[2025-06-09T17:23:41Z INFO  Easybot::utils::logging] Logger initialized with level: INFO
[2025-06-09T17:23:41Z INFO  Easybot] Starting EasyBot...
[2025-06-09T17:23:41Z INFO  Easybot] Environment variables for port configuration:
[2025-06-09T17:23:41Z INFO  Easybot]   PORT=8000
[2025-06-09T17:23:41Z INFO  Easybot]   SOCKET_PORT=3000
[2025-06-09T17:23:41Z INFO  Easybot] Using port 8000 from config for health check service
[2025-06-09T17:23:41Z INFO  Easybot] Using port 3000 from config for socket service
[2025-06-09T17:23:41Z INFO  Easybot] Starting health check service on port 8000...
[2025-06-09T17:23:41Z INFO  Easybot] Initializing admin service and controllers...
[2025-06-09T17:23:41Z INFO  Easybot::service::health_service] Starting server on port 8000
[2025-06-09T17:23:41Z INFO  Easybot::service::health_service] Current working directory: "D:\\workspace\\.rust\\Easybot"
[2025-06-09T17:23:41Z INFO  Easybot::service::health_service] Trying to bind to 0.0.0.0:8000
[2025-06-09T17:23:41Z INFO  Easybot::service::health_service] Server successfully started on port 8000
[2025-06-09T17:23:41Z INFO  Easybot::service::health_service] Server is now running on port 8000
[2025-06-09T17:23:41Z INFO  Easybot] Health check and admin services initialization completed
[2025-06-09T17:23:41Z INFO  Easybot] Initializing database connection...
[2025-06-09T17:24:04Z INFO  Easybot] Database connection established
[2025-06-09T17:24:04Z INFO  Easybot] Checking and initializing default super admin...
[2025-06-09T17:24:04Z INFO  Easybot::service::admin_init_service] SuperAdmin already exists. Skipping default admin creation.
[2025-06-09T17:24:05Z WARN  Easybot::service::admin_init_service] ⚠️  SECURITY WARNING: Default admin is still using default password!
[2025-06-09T17:24:05Z WARN  Easybot::service::admin_init_service] ⚠️  Please change the password immediately for security!
[2025-06-09T17:24:05Z WARN  Easybot::service::admin_init_service] ⚠️  Username: Olajosh
[2025-06-09T17:24:05Z WARN  Easybot::service::admin_init_service] ⚠️  Email: <EMAIL>
[2025-06-09T17:24:05Z INFO  Easybot] Initializing Solana trader service...
[2025-06-09T17:24:06Z INFO  Easybot] Solana trader service initialized
[2025-06-09T17:24:06Z INFO  Easybot] Initializing snipe worker...
[2025-06-09T17:24:06Z INFO  Easybot] Snipe worker initialized and started
[2025-06-09T17:24:06Z INFO  Easybot] Starting background fee collection service...
[2025-06-09T17:24:06Z INFO  Easybot] Background fee collection service started
[2025-06-09T17:24:06Z INFO  Easybot] Starting bot dispatcher...
[2025-06-09T17:24:06Z INFO  Easybot::service::admin_fee_service] Retrying failed fee transaction: id=6847160c3bd505748bcce476, blockchain=SOL, retry_count=2
[2025-06-09T17:24:06Z INFO  Easybot::service::price_service] Fetching fresh price for solana from CoinGecko
[2025-06-09T17:24:07Z INFO  Easybot::service::price_service] Fresh price for solana: $156.3700
[2025-06-09T17:24:07Z INFO  Easybot::service::price_service] Minimum fee for SOL: 0.00159877 tokens ($0.25 USD at $156.3700/token)
[2025-06-09T17:24:07Z INFO  Easybot::service::price_service] Fee amount 0.00000005 below minimum 0.00159877 for SOL
[2025-06-09T17:24:07Z ERROR Easybot::service::admin_fee_service] Fee collection retry failed: Blockchain error: Transaction error: Fee amount below minimum threshold
[2025-06-09T17:24:07Z WARN  Easybot::service::admin_fee_service] Marked fee transaction as failed: id=6847160c3bd505748bcce476, error=Retry attempt 3: Blockchain error: Transaction error: Fee amount below minimum threshold, retry_count=3
[2025-06-09T17:24:07Z INFO  Easybot::service::admin_fee_service] Background fee collection processed 1 failed transactions
[2025-06-09T17:26:08Z INFO  Easybot] Received callback query with data: back_to_last_token
[2025-06-09T17:26:29Z INFO  Easybot] Received callback query with data: sell_token:sol:7hB4AgnZp7RiThvtFTGMedkrU3G8X34fwHzy1vQjpump
[2025-06-09T17:26:51Z INFO  Easybot::service::admin_fee_service] Created admin fee transaction: id=6847195bdeab0a26e6ee732d, user_id=683cab67f5800b0c46d3ba1b, blockchain=SOL, type=SellFee, fee_amount=0.000000047
[2025-06-09T17:26:51Z INFO  Easybot::service::price_service] Fetching fresh price for solana from CoinGecko
[2025-06-09T17:26:52Z INFO  Easybot::service::price_service] Fresh price for solana: $156.2700
[2025-06-09T17:26:52Z INFO  Easybot::service::price_service] Minimum fee for SOL: 0.00159980 tokens ($0.25 USD at $156.2700/token)
[2025-06-09T17:26:52Z INFO  Easybot::service::price_service] Fee amount 0.00000005 below minimum 0.00159980 for SOL
[2025-06-09T17:26:53Z INFO  Easybot::service::price_service] Fetching fresh price for solana from CoinGecko
[2025-06-09T17:26:54Z INFO  Easybot::service::price_service] Fresh price for solana: $156.2700
[2025-06-09T17:26:54Z INFO  Easybot::service::price_service] Minimum fee for SOL: 0.00159980 tokens ($0.25 USD at $156.2700/token)
[2025-06-09T17:26:54Z INFO  Easybot::service::price_service] Fee amount 0.00000005 below minimum 0.00159980 for SOL
[2025-06-09T17:33:01Z ERROR teloxide::error_handlers] An error from the update listener: Network(reqwest::Error { kind: Request, url: Url { scheme: "https", cannot_be_a_base: false, username: "", password: None, host: Some(Domain("api.telegram.org")), port: None, path: "/token:redacted/GetUpdates", query: None, fragment: None }, source: TimedOut })
[2025-06-09T17:43:05Z INFO  Easybot] Received callback query with data: back_to_last_token
[2025-06-09T17:43:13Z INFO  Easybot] Received callback query with data: spend_token:sol:7hB4AgnZp7RiThvtFTGMedkrU3G8X34fwHzy1vQjpump
[2025-06-09T17:43:28Z INFO  Easybot::service::price_service] Fetching fresh price for solana from CoinGecko
[2025-06-09T17:43:29Z INFO  Easybot::service::price_service] Fresh price for solana: $156.0700
[2025-06-09T17:43:29Z INFO  Easybot::service::price_service] Minimum fee for SOL: 0.00160185 tokens ($0.25 USD at $156.0700/token)
[2025-06-09T17:43:29Z INFO  Easybot::service::price_service] Fee amount 0.00000005 below minimum 0.00160185 for SOL
[2025-06-09T17:47:17Z ERROR teloxide::error_handlers] An error from the update listener: Network(reqwest::Error { kind: Request, url: Url { scheme: "https", cannot_be_a_base: false, username: "", password: None, host: Some(Domain("api.telegram.org")), port: None, path: "/token:redacted/GetUpdates", query: None, fragment: None }, source: TimedOut })
