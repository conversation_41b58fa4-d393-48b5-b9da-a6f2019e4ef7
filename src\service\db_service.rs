use mongodb::{Client, Database, Collection, options::ClientOptions};
use mongodb::bson::{doc, oid::ObjectId};
use futures_util::stream::{TryStreamExt, StreamExt};
use std::sync::OnceLock;
use std::error::Error;
use std::time::Duration;
use crate::config::AppConfig;
use crate::model::{
    User, UserWallets, UserConfig, UserSession, UserDashboard, UserTrades, UserData,
    Trade, Blockchain, BotError, Snipe, UserSnipes
};

// Database client singleton
static DB_CLIENT: OnceLock<Database> = OnceLock::new();

pub struct DbService;

impl DbService {
    pub async fn init() -> Result<(), Box<dyn Error>> {
        let config = AppConfig::get();

        println!("Initializing database connection...");
        let start_time = std::time::Instant::now();

        // Initialize the database connection with highly optimized options
        let mut client_options = ClientOptions::parse(&config.mongodb_uri).await?;

        // Set connection pool options for maximum performance
        client_options.max_pool_size = Some(20);  // Increased pool size
        client_options.min_pool_size = Some(5);   // Increased min pool size
        client_options.connect_timeout = Some(Duration::from_secs(3)); // Reduced timeout
        client_options.max_idle_time = Some(Duration::from_secs(60));  // Increased idle time
        client_options.server_selection_timeout = Some(Duration::from_secs(3)); // Faster server selection

        // Set write concern for faster writes
        let write_concern = mongodb::options::WriteConcern::builder()
            .w(mongodb::options::Acknowledgment::Majority)
            .build();
        client_options.write_concern = Some(write_concern);

        // Set read concern for faster reads
        let read_concern = mongodb::options::ReadConcern::local();
        client_options.read_concern = Some(read_concern);

        let client = Client::with_options(client_options)?;
        let db = client.database("EasyBotTS");

        // Create indexes for better performance
        println!("Creating indexes...");
        let index_start = std::time::Instant::now();

        // Create index on chat_id field for faster lookups in users collection
        let users_collection = db.collection::<User>("users");
        let index_options = mongodb::options::IndexOptions::builder()
            .unique(true)
            .build();
        let index_model = mongodb::IndexModel::builder()
            .keys(doc! { "chat_id": 1 })
            .options(index_options)
            .build();

        // Create the index (ignore errors if it already exists)
        match users_collection.create_index(index_model, None).await {
            Ok(_) => println!("Created index on users.chat_id field"),
            Err(e) => println!("Error creating index (may already exist): {}", e),
        }

        // Create indexes for user_id field in all related collections
        // Create index for user_wallets collection
        let user_wallets_collection = db.collection::<UserWallets>("user_wallets");
        let wallets_index_options = mongodb::options::IndexOptions::builder()
            .unique(true)
            .build();
        let wallets_index_model = mongodb::IndexModel::builder()
            .keys(doc! { "user_id": 1 })
            .options(wallets_index_options)
            .build();

        match user_wallets_collection.create_index(wallets_index_model, None).await {
            Ok(_) => println!("Created index on user_wallets.user_id field"),
            Err(e) => println!("Error creating index on user_wallets (may already exist): {}", e),
        }

        // Create index for user_config collection
        let user_config_collection = db.collection::<UserConfig>("user_config");
        let config_index_options = mongodb::options::IndexOptions::builder()
            .unique(true)
            .build();
        let config_index_model = mongodb::IndexModel::builder()
            .keys(doc! { "user_id": 1 })
            .options(config_index_options)
            .build();

        match user_config_collection.create_index(config_index_model, None).await {
            Ok(_) => println!("Created index on user_config.user_id field"),
            Err(e) => println!("Error creating index on user_config (may already exist): {}", e),
        }

        // Create index for user_session collection
        let user_session_collection = db.collection::<UserSession>("user_session");
        let session_index_options = mongodb::options::IndexOptions::builder()
            .unique(true)
            .build();
        let session_index_model = mongodb::IndexModel::builder()
            .keys(doc! { "user_id": 1 })
            .options(session_index_options)
            .build();

        match user_session_collection.create_index(session_index_model, None).await {
            Ok(_) => println!("Created index on user_session.user_id field"),
            Err(e) => println!("Error creating index on user_session (may already exist): {}", e),
        }

        // Create index for user_dashboard collection
        let user_dashboard_collection = db.collection::<UserDashboard>("user_dashboard");
        let dashboard_index_options = mongodb::options::IndexOptions::builder()
            .unique(true)
            .build();
        let dashboard_index_model = mongodb::IndexModel::builder()
            .keys(doc! { "user_id": 1 })
            .options(dashboard_index_options)
            .build();

        match user_dashboard_collection.create_index(dashboard_index_model, None).await {
            Ok(_) => println!("Created index on user_dashboard.user_id field"),
            Err(e) => println!("Error creating index on user_dashboard (may already exist): {}", e),
        }

        // Create index for user_trades collection
        let user_trades_collection = db.collection::<UserTrades>("user_trades");
        let trades_index_options = mongodb::options::IndexOptions::builder()
            .unique(true)
            .build();
        let trades_index_model = mongodb::IndexModel::builder()
            .keys(doc! { "user_id": 1 })
            .options(trades_index_options)
            .build();

        match user_trades_collection.create_index(trades_index_model, None).await {
            Ok(_) => println!("Created index on user_trades.user_id field"),
            Err(e) => println!("Error creating index on user_trades (may already exist): {}", e),
        }

        // Create index for user_snipes collection
        let user_snipes_collection = db.collection::<UserSnipes>("user_snipes");
        let snipes_index_options = mongodb::options::IndexOptions::builder()
            .unique(true)
            .build();
        let snipes_index_model = mongodb::IndexModel::builder()
            .keys(doc! { "user_id": 1 })
            .options(snipes_index_options)
            .build();

        match user_snipes_collection.create_index(snipes_index_model, None).await {
            Ok(_) => println!("Created index on user_snipes.user_id field"),
            Err(e) => println!("Error creating index on user_snipes (may already exist): {}", e),
        }

        // Create index for snipes collection
        let snipes_collection = db.collection::<Snipe>("snipes");
        let snipes_index = mongodb::IndexModel::builder()
            .keys(doc! { "user_id": 1, "blockchain": 1, "contract_address": 1 })
            .build();

        match snipes_collection.create_index(snipes_index, None).await {
            Ok(_) => println!("Created index on snipes.user_id, snipes.blockchain, and snipes.contract_address fields"),
            Err(e) => println!("Error creating index on snipes (may already exist): {}", e),
        }

        // Create index for trades collection
        let trades_collection = db.collection::<Trade>("trades");
        let trades_index = mongodb::IndexModel::builder()
            .keys(doc! { "user_id": 1, "blockchain": 1 })
            .build();

        match trades_collection.create_index(trades_index, None).await {
            Ok(_) => println!("Created index on trades.user_id and trades.blockchain fields"),
            Err(e) => println!("Error creating index on trades (may already exist): {}", e),
        }



        println!("Index creation took: {:?}", index_start.elapsed());

        DB_CLIENT.set(db).unwrap();

        println!("Database initialization complete in: {:?}", start_time.elapsed());
        Ok(())
    }

    pub fn get_db() -> &'static Database {
        DB_CLIENT.get().expect("Database not initialized")
    }

    fn users_collection() -> Collection<User> {
        Self::get_db().collection("users")
    }

    pub fn user_wallets_collection() -> Collection<UserWallets> {
        Self::get_db().collection("user_wallets")
    }

    fn user_config_collection() -> Collection<UserConfig> {
        Self::get_db().collection("user_config")
    }

    fn user_session_collection() -> Collection<UserSession> {
        Self::get_db().collection("user_session")
    }

    fn user_dashboard_collection() -> Collection<UserDashboard> {
        Self::get_db().collection("user_dashboard")
    }

    pub fn user_trades_collection() -> Collection<UserTrades> {
        Self::get_db().collection("user_trades")
    }

    fn trades_collection() -> Collection<Trade> {
        Self::get_db().collection("trades")
    }

    pub fn get_trades_collection() -> Collection<Trade> {
        Self::trades_collection()
    }

    fn snipes_collection() -> Collection<Snipe> {
        Self::get_db().collection("snipes")
    }

    pub fn get_snipes_collection() -> Collection<Snipe> {
        Self::snipes_collection()
    }

    fn user_snipes_collection() -> Collection<UserSnipes> {
        Self::get_db().collection("user_snipes")
    }



    pub async fn find_user_by_chat_id(chat_id: i64) -> Result<Option<User>, BotError> {
        println!("=== DB LOOKUP: find_user_by_chat_id({}) ===", chat_id);
        let start_time = std::time::Instant::now();

        // Optimized database query with projection and hints
        let filter = doc! { "chat_id": chat_id };
        println!("Created filter: {:?}", filter);

        // Create options without a hint (MongoDB will use the default index)
        let options = mongodb::options::FindOneOptions::default();
        println!("Created options");

        // Execute the query with optimized options
        println!("Executing MongoDB query...");
        let query_start = std::time::Instant::now();
        let result = Self::users_collection()
            .find_one(filter, Some(options))
            .await
            .map_err(|e| BotError::GeneralError(format!("Database error: {}", e)))?;
        println!("MongoDB query took: {:?}", query_start.elapsed());

        // Log the result
        match &result {
            Some(user) => println!("User found: {} ({})", user.display_name(), user.chat_id),
            None => println!("No user found with chat_id: {}", chat_id),
        }

        println!("Total find_user_by_chat_id time: {:?}", start_time.elapsed());
        Ok(result)
    }

    /// Find user by wallet address (for cases where chat_id is missing)
    pub async fn find_user_by_wallet_address(wallet_address: &str) -> Result<Option<User>, BotError> {
        println!("=== DB LOOKUP: find_user_by_wallet_address({}) ===", wallet_address);
        let start_time = std::time::Instant::now();

        // Check in user_wallets collection first
        let user_wallets_collection: mongodb::Collection<crate::model::UserWallets> = Self::user_wallets_collection();

        let filter = doc! {
            "$or": [
                { "sol_wallet.address": wallet_address },
                { "eth_wallet.address": wallet_address },
                { "bsc_wallet.address": wallet_address },
                { "base_wallet.address": wallet_address }
            ]
        };

        let user_wallets = user_wallets_collection
            .find_one(filter, None)
            .await
            .map_err(|e| BotError::GeneralError(format!("Database error: {}", e)))?;

        if let Some(wallets) = user_wallets {
            // Found user wallets, now get the user
            let user_result = Self::find_user_by_id(wallets.user_id).await?;
            println!("Total find_user_by_wallet_address time: {:?}", start_time.elapsed());
            return Ok(user_result);
        }

        println!("No user found with wallet address: {}", wallet_address);
        println!("Total find_user_by_wallet_address time: {:?}", start_time.elapsed());
        Ok(None)
    }

    /// Find user by ObjectId
    pub async fn find_user_by_id(user_id: ObjectId) -> Result<Option<User>, BotError> {
        let filter = doc! { "_id": user_id };
        let result = Self::users_collection()
            .find_one(filter, None)
            .await
            .map_err(|e| BotError::GeneralError(format!("Database error: {}", e)))?;
        Ok(result)
    }

    /// Get or create a complete user with all related models
    pub async fn get_or_create_complete_user(
        chat_id: i64,
        tg_user: &teloxide::types::User
    ) -> Result<(User, UserWallets, UserConfig, UserSession, UserDashboard, UserTrades), BotError> {
        println!("=== Getting or creating complete user for chat_id: {} ===", chat_id);
        let start_time = std::time::Instant::now();

        // Try to find the user first
        let user_opt = Self::find_user_by_chat_id(chat_id).await?;

        // Get or create the user
        let user = match user_opt {
            Some(existing_user) => {
                println!("Found existing user: {}", existing_user.display_name());
                // Update last seen
                let mut user = existing_user;
                user.update_last_seen();
                Self::save_user(&user).await?;
                user
            },
            None => {
                println!("Creating new user for chat_id: {}", chat_id);
                let mut new_user = User::from_telegram_user(tg_user);
                new_user.chat_id = chat_id;
                Self::save_user(&new_user).await?;

                // Reload to get the ID
                let saved_user = Self::find_user_by_chat_id(chat_id).await?
                    .ok_or_else(|| BotError::GeneralError("Failed to retrieve newly created user".to_string()))?;
                saved_user
            }
        };

        // Ensure user has an ID
        let user_id = user.id.ok_or_else(|| BotError::GeneralError("User has no ID".to_string()))?;

        // Get or create user wallets
        let wallets = match Self::find_user_wallets(user_id).await? {
            Some(existing_wallets) => {
                println!("Found existing wallets");
                existing_wallets
            },
            None => {
                println!("Creating new wallets");
                let new_wallets = UserWallets::new(user_id);
                Self::save_user_wallets(&new_wallets).await?;

                // Reload to get the ID
                Self::find_user_wallets(user_id).await?
                    .ok_or_else(|| BotError::GeneralError("Failed to retrieve newly created wallets".to_string()))?
            }
        };

        // Get or create user config
        let config = match Self::find_user_config(user_id).await? {
            Some(existing_config) => {
                println!("Found existing config");
                existing_config
            },
            None => {
                println!("Creating new config");
                let new_config = UserConfig::new(user_id);
                Self::save_user_config(&new_config).await?;

                // Reload to get the ID
                Self::find_user_config(user_id).await?
                    .ok_or_else(|| BotError::GeneralError("Failed to retrieve newly created config".to_string()))?
            }
        };

        // Get or create user session
        let session = match Self::find_user_session(user_id).await? {
            Some(existing_session) => {
                println!("Found existing session");
                existing_session
            },
            None => {
                println!("Creating new session");
                let new_session = UserSession::new(user_id);
                Self::save_user_session(&new_session).await?;

                // Reload to get the ID
                Self::find_user_session(user_id).await?
                    .ok_or_else(|| BotError::GeneralError("Failed to retrieve newly created session".to_string()))?
            }
        };

        // Get or create user dashboard
        let dashboard = match Self::find_user_dashboard(user_id).await? {
            Some(existing_dashboard) => {
                println!("Found existing dashboard");
                existing_dashboard
            },
            None => {
                println!("Creating new dashboard");
                let new_dashboard = UserDashboard::new(user_id);
                Self::save_user_dashboard(&new_dashboard).await?;

                // Reload to get the ID
                Self::find_user_dashboard(user_id).await?
                    .ok_or_else(|| BotError::GeneralError("Failed to retrieve newly created dashboard".to_string()))?
            }
        };

        // Get or create user trades
        let trades = match Self::find_user_trades(user_id).await? {
            Some(existing_trades) => {
                println!("Found existing trades");
                existing_trades
            },
            None => {
                println!("Creating new trades");
                let new_trades = UserTrades::new(user_id);
                Self::save_user_trades(&new_trades).await?;

                // Reload to get the ID
                Self::find_user_trades(user_id).await?
                    .ok_or_else(|| BotError::GeneralError("Failed to retrieve newly created trades".to_string()))?
            }
        };

        println!("Total get_or_create_complete_user time: {:?}", start_time.elapsed());
        Ok((user, wallets, config, session, dashboard, trades))
    }

    /// Get or create a UserData object
    pub async fn get_or_create_user_data(
        chat_id: i64,
        tg_user: &teloxide::types::User
    ) -> Result<UserData, BotError> {
        println!("=== Getting or creating UserData for chat_id: {} ===", chat_id);
        let start_time = std::time::Instant::now();

        let (user, wallets, config, session, dashboard, trades) =
            Self::get_or_create_complete_user(chat_id, tg_user).await?;

        let user_data = UserData::new(user, wallets, config, session, dashboard, trades);

        println!("Total get_or_create_user_data time: {:?}", start_time.elapsed());
        Ok(user_data)
    }

    /// Save all user data
    pub async fn save_user_data(data: &UserData) -> Result<(), BotError> {
        println!("=== Saving UserData for chat_id: {} ===", data.chat_id());
        let start_time = std::time::Instant::now();

        // Save all components
        Self::save_user(&data.user).await?;
        Self::save_user_wallets(&data.wallets).await?;
        Self::save_user_config(&data.config).await?;
        Self::save_user_session(&data.session).await?;
        Self::save_user_dashboard(&data.dashboard).await?;
        Self::save_user_trades(&data.trades).await?;

        println!("Total save_user_data time: {:?}", start_time.elapsed());
        Ok(())
    }

    pub async fn save_user(user: &User) -> Result<(), BotError> {
        println!("=== DB SAVE: save_user({}) ===", user.chat_id);
        let start_time = std::time::Instant::now();

        // Optimized database save with upsert and write concern
        if let Some(id) = user.id {
            println!("Updating existing user with ID: {:?}", id);

            // Update existing user with upsert option
            let filter = doc! { "_id": id };

            // Create options with upsert and write concern
            let mut options = mongodb::options::ReplaceOptions::default();
            options.upsert = Some(true);

            // Execute the update with optimized options
            println!("Executing MongoDB replace_one...");
            let update_start = std::time::Instant::now();
            Self::users_collection()
                .replace_one(filter, user, options)
                .await
                .map_err(|e| BotError::GeneralError(format!("Database error: {}", e)))?;
            println!("MongoDB replace_one took: {:?}", update_start.elapsed());
        } else {
            println!("Inserting new user");

            // Insert new user with write concern
            let options = mongodb::options::InsertOneOptions::default();

            // Execute the insert with optimized options
            println!("Executing MongoDB insert_one...");
            let insert_start = std::time::Instant::now();
            Self::users_collection()
                .insert_one(user, options)
                .await
                .map_err(|e| BotError::GeneralError(format!("Database error: {}", e)))?;
            println!("MongoDB insert_one took: {:?}", insert_start.elapsed());
        }

        println!("Total save_user time: {:?}", start_time.elapsed());
        Ok(())
    }

    pub async fn find_trades_by_user_and_blockchain(
        user_id: i64,
        blockchain: Blockchain,
    ) -> Result<Vec<Trade>, BotError> {
        // Convert user_id to ObjectId by finding the user first
        let user_opt = Self::find_user_by_chat_id(user_id).await?;
        let user = match user_opt {
            Some(u) => u,
            None => return Ok(Vec::new()), // No user found, return empty trades
        };

        let user_object_id = match user.id {
            Some(id) => id,
            None => return Ok(Vec::new()), // User has no ID, return empty trades
        };

        let filter = doc! {
            "user_id": user_object_id,
            "blockchain": blockchain.to_string(),
        };

        let cursor = Self::trades_collection()
            .find(filter, None)
            .await
            .map_err(|e| BotError::GeneralError(format!("Database error: {}", e)))?;

        let trades = cursor
            .try_collect()
            .await
            .map_err(|e| BotError::GeneralError(format!("Database error: {}", e)))?;

        Ok(trades)
    }

    pub async fn save_trade(trade: &Trade) -> Result<ObjectId, BotError> {
        if let Some(id) = trade.id {
            let filter = doc! { "_id": id };
            Self::trades_collection()
                .replace_one(filter, trade, None)
                .await
                .map_err(|e| BotError::GeneralError(format!("Database error: {}", e)))?;

            Ok(id)
        } else {
            let result = Self::trades_collection()
                .insert_one(trade, None)
                .await
                .map_err(|e| BotError::GeneralError(format!("Database error: {}", e)))?;

            Ok(result.inserted_id.as_object_id().unwrap())
        }
    }

    // Snipe-related methods
    pub async fn find_snipes_by_user_and_blockchain(
        user_id: i64,
        blockchain: Blockchain,
    ) -> Result<Vec<Snipe>, BotError> {
        // Convert user_id to ObjectId by finding the user first
        let user_opt = Self::find_user_by_chat_id(user_id).await?;
        let user = match user_opt {
            Some(u) => u,
            None => return Ok(Vec::new()), // No user found, return empty snipes
        };

        let user_object_id = match user.id {
            Some(id) => id,
            None => return Ok(Vec::new()), // User has no ID, return empty snipes
        };

        let filter = doc! {
            "user_id": user_object_id,
            "blockchain": blockchain.to_string(),
        };

        let cursor = Self::snipes_collection()
            .find(filter, None)
            .await
            .map_err(|e| BotError::GeneralError(format!("Database error: {}", e)))?;

        let snipes = cursor
            .try_collect()
            .await
            .map_err(|e| BotError::GeneralError(format!("Database error: {}", e)))?;

        Ok(snipes)
    }

    pub async fn find_snipes_by_user_blockchain_and_token(
        user_id: i64,
        blockchain: Blockchain,
        token_address: &str,
    ) -> Result<Vec<Snipe>, BotError> {
        // Convert user_id to ObjectId by finding the user first
        let user_opt = Self::find_user_by_chat_id(user_id).await?;
        let user = match user_opt {
            Some(u) => u,
            None => return Ok(Vec::new()), // No user found, return empty snipes
        };

        let user_object_id = match user.id {
            Some(id) => id,
            None => return Ok(Vec::new()), // User has no ID, return empty snipes
        };

        let filter = doc! {
            "user_id": user_object_id,
            "blockchain": blockchain.to_string(),
            "contract_address": token_address,
        };

        let options = mongodb::options::FindOptions::builder().sort(doc! { "created_at": -1 }).build();

        let collection = Self::get_snipes_collection();
        let cursor = collection.find(filter, options).await.map_err(|e| {
            BotError::GeneralError(format!("Failed to find snipes: {}", e))
        })?;

        let snipes = cursor.try_collect().await.map_err(|e| {
            BotError::GeneralError(format!("Failed to collect snipes: {}", e))
        })?;

        Ok(snipes)
    }

    pub async fn get_all_pending_snipes(
        blockchain: &Blockchain,
    ) -> Result<Vec<Snipe>, BotError> {
        let filter = doc! {
            "blockchain": blockchain.to_string(),
            "status": "pending",
        };

        let options = mongodb::options::FindOptions::builder().sort(doc! { "created_at": -1 }).build();

        let collection = Self::get_snipes_collection();
        let cursor = collection.find(filter, options).await.map_err(|e| {
            BotError::GeneralError(format!("Failed to find pending snipes: {}", e))
        })?;

        // Safely collect the snipes, handling any potential errors with individual documents
        let mut snipes = Vec::new();
        let mut cursor = cursor;

        while let Some(result) = cursor.next().await {
            match result {
                Ok(snipe) => snipes.push(snipe),
                Err(e) => {
                    println!("Warning: Error parsing snipe document: {}", e);
                    // Continue processing other documents
                }
            }
        }

        Ok(snipes)
    }

    pub async fn find_snipe_by_id(id: ObjectId) -> Result<Option<Snipe>, BotError> {
        let filter = doc! { "_id": id };
        let result = Self::snipes_collection()
            .find_one(filter, None)
            .await
            .map_err(|e| BotError::GeneralError(format!("Database error: {}", e)))?;

        Ok(result)
    }

    pub async fn save_snipe(snipe: &mut Snipe) -> Result<ObjectId, BotError> {
        println!("=== DB SAVE: save_snipe ===");
        println!("Snipe details: User ID: {}, Blockchain: {}, Contract: {}", snipe.user_id, snipe.blockchain.as_str(), &snipe.contract_address);

        if let Some(id) = snipe.id {
            println!("Updating existing snipe with ID: {}", id);
            let filter = doc! { "_id": id };
            match Self::snipes_collection()
                .replace_one(filter, snipe, None)
                .await {
                    Ok(result) => {
                        println!("Snipe updated successfully. Matched count: {}, Modified count: {}",
                            result.matched_count, result.modified_count);
                        Ok(id)
                    },
                    Err(e) => {
                        println!("Error updating snipe: {}", e);
                        Err(BotError::GeneralError(format!("Database error: {}", e)))
                    }
                }
        } else {
            println!("Inserting new snipe");
            match Self::snipes_collection()
                .insert_one(snipe, None)
                .await {
                    Ok(result) => {
                        let id = result.inserted_id.as_object_id().unwrap();
                        println!("Snipe inserted successfully with ID: {}", id);
                        Ok(id)
                    },
                    Err(e) => {
                        println!("Error inserting snipe: {}", e);
                        Err(BotError::GeneralError(format!("Database error: {}", e)))
                    }
                }
        }
    }

    pub async fn delete_snipe(id: ObjectId) -> Result<bool, BotError> {
        let filter = doc! { "_id": id };
        let result = Self::snipes_collection()
            .delete_one(filter, None)
            .await
            .map_err(|e| BotError::GeneralError(format!("Database error: {}", e)))?;

        Ok(result.deleted_count > 0)
    }

    pub async fn find_user_snipes(user_id: i64) -> Result<Option<UserSnipes>, BotError> {
        println!("=== DB LOOKUP: find_user_snipes ===");
        println!("User ID: {}", user_id);

        // Convert user_id to ObjectId by finding the user first
        let user_opt = Self::find_user_by_chat_id(user_id).await?;
        let user = match user_opt {
            Some(u) => u,
            None => return Ok(None), // No user found, return None
        };

        let user_object_id = match user.id {
            Some(id) => id,
            None => return Ok(None), // User has no ID, return None
        };

        let filter = doc! { "user_id": user_object_id };
        println!("Filter: {:?}", filter);

        match Self::user_snipes_collection()
            .find_one(filter, None)
            .await {
                Ok(result) => {
                    match &result {
                        Some(snipes) => {
                            println!("Found user snipes with ID: {:?}", snipes.id);
                            println!("BSC snipes: {}, SOL snipes: {}, ETH snipes: {}, BASE snipes: {}",
                                snipes.snipes.bsc.len(),
                                snipes.snipes.sol.len(),
                                snipes.snipes.eth.len(),
                                snipes.snipes.base.len());
                        },
                        None => println!("No user snipes found for user ID: {}", user_id)
                    }
                    Ok(result)
                },
                Err(e) => {
                    println!("Error finding user snipes: {}", e);
                    Err(BotError::GeneralError(format!("Database error: {}", e)))
                }
            }
    }

    pub async fn save_user_snipes(user_snipes: &UserSnipes) -> Result<(), BotError> {
        println!("=== DB SAVE: save_user_snipes ===");
        println!("User ID: {}", user_snipes.user_id);
        println!("BSC snipes: {}, SOL snipes: {}, ETH snipes: {}, BASE snipes: {}",
            user_snipes.snipes.bsc.len(),
            user_snipes.snipes.sol.len(),
            user_snipes.snipes.eth.len(),
            user_snipes.snipes.base.len());

        if let Some(id) = user_snipes.id {
            println!("Updating existing user snipes with ID: {}", id);
            let filter = doc! { "_id": id };
            match Self::user_snipes_collection()
                .replace_one(filter, user_snipes, None)
                .await {
                    Ok(result) => {
                        println!("User snipes updated successfully. Matched count: {}, Modified count: {}",
                            result.matched_count, result.modified_count);
                        Ok(())
                    },
                    Err(e) => {
                        println!("Error updating user snipes: {}", e);
                        Err(BotError::GeneralError(format!("Database error: {}", e)))
                    }
                }
        } else {
            println!("Inserting new user snipes");
            match Self::user_snipes_collection()
                .insert_one(user_snipes, None)
                .await {
                    Ok(result) => {
                        println!("User snipes inserted successfully with ID: {}",
                            result.inserted_id.as_object_id().unwrap());
                        Ok(())
                    },
                    Err(e) => {
                        println!("Error inserting user snipes: {}", e);
                        Err(BotError::GeneralError(format!("Database error: {}", e)))
                    }
                }
        }
    }

    pub async fn get_or_create_user_snipes(user_id: i64) -> Result<UserSnipes, BotError> {
        println!("=== DB LOOKUP: get_or_create_user_snipes ===");
        println!("User ID: {}", user_id);

        // Convert user_id to ObjectId by finding the user first
        let user_opt = Self::find_user_by_chat_id(user_id).await?;
        let user = match user_opt {
            Some(u) => u,
            None => return Err(BotError::GeneralError("User not found".to_string())),
        };

        let user_object_id = match user.id {
            Some(id) => id,
            None => return Err(BotError::GeneralError("User has no ID".to_string())),
        };

        if let Some(user_snipes) = Self::find_user_snipes(user_id).await? {
            println!("Found existing user snipes");
            println!("BSC snipes: {}, SOL snipes: {}, ETH snipes: {}, BASE snipes: {}",
                user_snipes.snipes.bsc.len(),
                user_snipes.snipes.sol.len(),
                user_snipes.snipes.eth.len(),
                user_snipes.snipes.base.len());
            Ok(user_snipes)
        } else {
            println!("No user snipes found, creating new one");
            let new_user_snipes = UserSnipes::new(user_object_id);
            Self::save_user_snipes(&new_user_snipes).await?;

            println!("Retrieving newly created user snipes");
            match Self::find_user_snipes(user_id).await? {
                Some(snipes) => {
                    println!("Successfully retrieved newly created user snipes");
                    Ok(snipes)
                },
                None => {
                    println!("ERROR: Failed to retrieve newly created user snipes");
                    Err(BotError::GeneralError("Failed to retrieve newly created user snipes".to_string()))
                }
            }
        }
    }

    pub async fn add_snipe_to_user(
        user_id: i64,
        blockchain: &Blockchain,
        snipe: &mut Snipe,
    ) -> Result<(), BotError> {
        println!("=== DB SAVE: add_snipe_to_user ===");
        println!("CRITICAL: Adding snipe to user. User ID: {}, Blockchain: {}, Contract: {}",
            user_id, blockchain.as_str(), &snipe.contract_address);

        // Check if a snipe with the same contract address already exists
        println!("Checking for existing snipes with the same contract address...");
        let existing_snipes = Self::find_snipes_by_user_and_blockchain(user_id, blockchain.clone()).await?;

        for existing_snipe in &existing_snipes {
            if existing_snipe.contract_address.to_lowercase() == snipe.contract_address.to_lowercase() {
                println!("CRITICAL: Found existing snipe with the same contract address: {}", existing_snipe.contract_address);
                println!("Using existing snipe ID: {:?}", existing_snipe.id);

                // If the snipe already exists, just return success
                if let Some(_id) = existing_snipe.id {
                    return Ok(());
                }
            }
        }

        // Convert user_id to ObjectId by finding the user first
        println!("CRITICAL: Converting user_id to ObjectId...");
        let user_opt = Self::find_user_by_chat_id(user_id).await?;
        let user = match user_opt {
            Some(u) => u,
            None => return Err(BotError::GeneralError("User not found".to_string())),
        };

        let _user_object_id = match user.id {
            Some(id) => id,
            None => return Err(BotError::GeneralError("User has no ID".to_string())),
        };

        // The snipe already has a user_id field that's an ObjectId created from the chat_id
        // We don't need to update it, as it will be used to link back to the user
        // This approach allows us to store the chat_id directly in the snipe

        // Save the snipe first to get an ID
        println!("CRITICAL: Saving new snipe to database...");
        let snipe_id = match Self::save_snipe(snipe).await {
            Ok(id) => {
                println!("CRITICAL: Snipe saved successfully with ID: {}", id);
                id
            },
            Err(e) => {
                println!("CRITICAL ERROR: Error saving snipe: {}", e);
                return Err(e);
            }
        };

        // Get or create user snipes
        println!("CRITICAL: Getting or creating user snipes...");
        let mut user_snipes = match Self::get_or_create_user_snipes(user_id).await {
            Ok(snipes) => {
                println!("CRITICAL: User snipes retrieved/created successfully");
                snipes
            },
            Err(e) => {
                println!("CRITICAL ERROR: Error getting/creating user snipes: {}", e);
                return Err(e);
            }
        };

        // Add the snipe to the user's snipes
        println!("CRITICAL: Adding snipe ID {} to user's {} snipes", snipe_id, blockchain.as_str());
        user_snipes.add_snipe(blockchain, snipe_id);

        // Save the updated user snipes
        println!("CRITICAL: Saving updated user snipes...");
        match Self::save_user_snipes(&user_snipes).await {
            Ok(_) => println!("CRITICAL: User snipes saved successfully"),
            Err(e) => {
                println!("CRITICAL ERROR: Error saving user snipes: {}", e);
                return Err(e);
            }
        }

        println!("CRITICAL: Snipe added to user successfully");
        Ok(())
    }

    pub async fn remove_snipe_from_user(
        user_id: i64,
        blockchain: &Blockchain,
        snipe_id: ObjectId,
    ) -> Result<(), BotError> {
        // Get user snipes
        if let Some(mut user_snipes) = Self::find_user_snipes(user_id).await? {
            // Remove the snipe from the user's snipes
            user_snipes.remove_snipe(blockchain, snipe_id);

            // Save the updated user snipes
            Self::save_user_snipes(&user_snipes).await?;

            // Delete the snipe
            Self::delete_snipe(snipe_id).await?;
        }

        Ok(())
    }

    pub async fn get_user_snipes_by_blockchain(
        user_id: i64,
        blockchain: &Blockchain,
    ) -> Result<Vec<Snipe>, BotError> {
        // Get user snipes
        if let Some(user_snipes) = Self::find_user_snipes(user_id).await? {
            // Get the snipe IDs for the blockchain
            let snipe_ids = user_snipes.get_snipes(blockchain);

            // Fetch each snipe
            let mut snipes = Vec::new();
            for snipe_id in snipe_ids {
                if let Some(snipe) = Self::find_snipe_by_id(*snipe_id).await? {
                    snipes.push(snipe);
                }
            }

            Ok(snipes)
        } else {
            Ok(Vec::new())
        }
    }

    // Methods for UserWallets
    pub async fn find_user_wallets(user_id: ObjectId) -> Result<Option<UserWallets>, BotError> {
        println!("=== DB LOOKUP: find_user_wallets({}) ===", user_id);
        let start_time = std::time::Instant::now();

        let filter = doc! { "user_id": user_id };
        let result = Self::user_wallets_collection()
            .find_one(filter, None)
            .await
            .map_err(|e| BotError::GeneralError(format!("Database error: {}", e)))?;

        println!("Total find_user_wallets time: {:?}", start_time.elapsed());
        Ok(result)
    }

    pub async fn save_user_wallets(wallets: &UserWallets) -> Result<(), BotError> {
        println!("=== DB SAVE: save_user_wallets ===");
        let start_time = std::time::Instant::now();

        if let Some(id) = wallets.id {
            let filter = doc! { "_id": id };
            Self::user_wallets_collection()
                .replace_one(filter, wallets, None)
                .await
                .map_err(|e| BotError::GeneralError(format!("Database error: {}", e)))?;
        } else {
            Self::user_wallets_collection()
                .insert_one(wallets, None)
                .await
                .map_err(|e| BotError::GeneralError(format!("Database error: {}", e)))?;
        }

        println!("Total save_user_wallets time: {:?}", start_time.elapsed());
        Ok(())
    }

    /// Get user wallets by user ObjectId
    pub async fn get_user_wallets(user_id: ObjectId) -> Result<Option<UserWallets>, BotError> {
        Self::find_user_wallets(user_id).await
    }

    // Methods for UserConfig
    pub async fn find_user_config(user_id: ObjectId) -> Result<Option<UserConfig>, BotError> {
        println!("=== DB LOOKUP: find_user_config({}) ===", user_id);
        let start_time = std::time::Instant::now();

        let filter = doc! { "user_id": user_id };
        let result = Self::user_config_collection()
            .find_one(filter, None)
            .await
            .map_err(|e| BotError::GeneralError(format!("Database error: {}", e)))?;

        println!("Total find_user_config time: {:?}", start_time.elapsed());
        Ok(result)
    }

    pub async fn save_user_config(config: &UserConfig) -> Result<(), BotError> {
        println!("=== DB SAVE: save_user_config ===");
        let start_time = std::time::Instant::now();

        if let Some(id) = config.id {
            let filter = doc! { "_id": id };
            Self::user_config_collection()
                .replace_one(filter, config, None)
                .await
                .map_err(|e| BotError::GeneralError(format!("Database error: {}", e)))?;
        } else {
            Self::user_config_collection()
                .insert_one(config, None)
                .await
                .map_err(|e| BotError::GeneralError(format!("Database error: {}", e)))?;
        }

        println!("Total save_user_config time: {:?}", start_time.elapsed());
        Ok(())
    }

    // Methods for UserSession
    pub async fn find_user_session(user_id: ObjectId) -> Result<Option<UserSession>, BotError> {
        println!("=== DB LOOKUP: find_user_session({}) ===", user_id);
        let start_time = std::time::Instant::now();

        let filter = doc! { "user_id": user_id };
        let result = Self::user_session_collection()
            .find_one(filter, None)
            .await
            .map_err(|e| BotError::GeneralError(format!("Database error: {}", e)))?;

        println!("Total find_user_session time: {:?}", start_time.elapsed());
        Ok(result)
    }

    pub async fn save_user_session(session: &UserSession) -> Result<(), BotError> {
        println!("=== DB SAVE: save_user_session ===");
        let start_time = std::time::Instant::now();

        // Create optimized write options with faster write concern
        let write_concern = mongodb::options::WriteConcern::builder()
            .w(mongodb::options::Acknowledgment::Majority)
            .build();

        if let Some(id) = session.id {
            let filter = doc! { "_id": id };

            // Create options with write concern
            let mut options = mongodb::options::ReplaceOptions::default();
            options.write_concern = Some(write_concern.clone());

            Self::user_session_collection()
                .replace_one(filter, session, options)
                .await
                .map_err(|e| BotError::GeneralError(format!("Database error: {}", e)))?;
        } else {
            // Create options with write concern
            let mut options = mongodb::options::InsertOneOptions::default();
            options.write_concern = Some(write_concern);

            Self::user_session_collection()
                .insert_one(session, options)
                .await
                .map_err(|e| BotError::GeneralError(format!("Database error: {}", e)))?;
        }

        println!("Total save_user_session time: {:?}", start_time.elapsed());
        Ok(())
    }

    // Methods for UserDashboard
    pub async fn find_user_dashboard(user_id: ObjectId) -> Result<Option<UserDashboard>, BotError> {
        println!("=== DB LOOKUP: find_user_dashboard({}) ===", user_id);
        let start_time = std::time::Instant::now();

        let filter = doc! { "user_id": user_id };
        let result = Self::user_dashboard_collection()
            .find_one(filter, None)
            .await
            .map_err(|e| BotError::GeneralError(format!("Database error: {}", e)))?;

        println!("Total find_user_dashboard time: {:?}", start_time.elapsed());
        Ok(result)
    }

    pub async fn save_user_dashboard(dashboard: &UserDashboard) -> Result<(), BotError> {
        println!("=== DB SAVE: save_user_dashboard ===");
        let start_time = std::time::Instant::now();

        if let Some(id) = dashboard.id {
            let filter = doc! { "_id": id };
            Self::user_dashboard_collection()
                .replace_one(filter, dashboard, None)
                .await
                .map_err(|e| BotError::GeneralError(format!("Database error: {}", e)))?;
        } else {
            Self::user_dashboard_collection()
                .insert_one(dashboard, None)
                .await
                .map_err(|e| BotError::GeneralError(format!("Database error: {}", e)))?;
        }

        println!("Total save_user_dashboard time: {:?}", start_time.elapsed());
        Ok(())
    }

    // Methods for UserTrades
    pub async fn find_user_trades(user_id: ObjectId) -> Result<Option<UserTrades>, BotError> {
        println!("=== DB LOOKUP: find_user_trades({}) ===", user_id);
        let start_time = std::time::Instant::now();

        // Validate the user_id
        if user_id == ObjectId::default() {
            println!("ERROR: Invalid user_id parameter (default ObjectId)");
            return Err(BotError::GeneralError("Invalid user_id parameter (default ObjectId)".to_string()));
        }

        let filter = doc! { "user_id": user_id };

        // Add retry logic for better reliability
        let mut retry_count = 0;
        let max_retries = 3;

        loop {
            match Self::user_trades_collection()
                .find_one(filter.clone(), None)
                .await {
                    Ok(result) => {
                        match &result {
                            Some(trades) => {
                                println!("Found user_trades with ID: {:?}", trades.id);
                                println!("User trades contains: BSC trades={}, ETH trades={}, BASE trades={}, SOL trades={}",
                                    trades.trades.bsc.len(),
                                    trades.trades.eth.len(),
                                    trades.trades.base.len(),
                                    trades.trades.sol.len());
                            },
                            None => {
                                println!("No user_trades found for user_id: {}", user_id);
                            }
                        }

                        println!("Total find_user_trades time: {:?}", start_time.elapsed());
                        return Ok(result);
                    },
                    Err(e) => {
                        retry_count += 1;
                        if retry_count >= max_retries {
                            println!("ERROR: Failed to find user_trades after {} retries: {}", max_retries, e);
                            return Err(BotError::GeneralError(format!("Database error after {} retries: {}", max_retries, e)));
                        }

                        println!("Error finding user_trades (retry {}/{}): {}", retry_count, max_retries, e);
                        tokio::time::sleep(tokio::time::Duration::from_millis(500 * retry_count)).await;
                    }
                }
        }
    }

    pub async fn save_user_trades(trades: &UserTrades) -> Result<(), BotError> {
        println!("=== DB SAVE: save_user_trades ===");
        let start_time = std::time::Instant::now();

        // Validate the user_trades object
        if trades.user_id == ObjectId::default() {
            println!("ERROR: Invalid user_id in user_trades (default ObjectId)");
            return Err(BotError::GeneralError("Invalid user_id in user_trades (default ObjectId)".to_string()));
        }

        println!("User trades: user_id={:?}, id={:?}, trades: BSC={}, ETH={}, BASE={}, SOL={}",
            trades.user_id, trades.id,
            trades.trades.bsc.len(), trades.trades.eth.len(),
            trades.trades.base.len(), trades.trades.sol.len());



        // Create options with upsert for better reliability
        let mut options = mongodb::options::ReplaceOptions::default();
        options.upsert = Some(true);

        if let Some(id) = trades.id {
            println!("Updating existing user_trades with ID: {:?}", id);
            let filter = doc! { "_id": id };

            println!("Executing MongoDB replace_one for user_trades...");
            let update_start = std::time::Instant::now();

            // Add retry logic for better reliability
            let mut retry_count = 0;
            let max_retries = 3;

            loop {
                match Self::user_trades_collection()
                    .replace_one(filter.clone(), trades, options.clone())
                    .await {
                        Ok(result) => {
                            println!("MongoDB replace_one for user_trades took: {:?}, matched: {}, modified: {}, upserted: {:?}",
                                update_start.elapsed(), result.matched_count, result.modified_count, result.upserted_id);

                            if result.matched_count == 0 && result.upserted_id.is_none() {
                                println!("WARNING: Document not found and not upserted");
                            }

                            break;
                        },
                        Err(e) => {
                            retry_count += 1;
                            if retry_count >= max_retries {
                                println!("ERROR: Failed to update user_trades after {} retries: {}", max_retries, e);
                                return Err(BotError::GeneralError(format!("Database error after {} retries: {}", max_retries, e)));
                            }

                            println!("Error updating user_trades (retry {}/{}): {}", retry_count, max_retries, e);
                            tokio::time::sleep(tokio::time::Duration::from_millis(500 * retry_count)).await;
                        }
                    }
            }
        } else {
            println!("Inserting new user_trades for user_id: {:?}", trades.user_id);

            println!("Executing MongoDB insert_one for user_trades...");
            let insert_start = std::time::Instant::now();

            // Add retry logic for better reliability
            let mut retry_count = 0;
            let max_retries = 3;

            loop {
                match Self::user_trades_collection()
                    .insert_one(trades, None)
                    .await {
                        Ok(result) => {
                            println!("MongoDB insert_one for user_trades took: {:?}, inserted ID: {:?}",
                                insert_start.elapsed(), result.inserted_id);

                            // Verify the insert was successful
                            if let Some(oid) = result.inserted_id.as_object_id() {
                                println!("Successfully inserted user_trades with ID: {}", oid);

                                // Verify we can retrieve the document
                                match Self::user_trades_collection()
                                    .find_one(doc! { "_id": oid }, None)
                                    .await {
                                        Ok(Some(_)) => {
                                            println!("Successfully verified user_trades document exists");
                                        },
                                        Ok(None) => {
                                            println!("WARNING: Could not verify user_trades document exists after insert");
                                        },
                                        Err(e) => {
                                            println!("WARNING: Error verifying user_trades document: {}", e);
                                        }
                                    }
                            } else {
                                println!("WARNING: Inserted ID is not an ObjectId");
                            }

                            break;
                        },
                        Err(e) => {
                            retry_count += 1;
                            if retry_count >= max_retries {
                                println!("ERROR: Failed to insert user_trades after {} retries: {}", max_retries, e);

                                // Try one last attempt with upsert
                                println!("Attempting final upsert by user_id as fallback");
                                let filter = doc! { "user_id": trades.user_id };

                                match Self::user_trades_collection()
                                    .replace_one(filter, trades, options.clone())
                                    .await {
                                        Ok(result) => {
                                            println!("Fallback upsert succeeded: matched={}, modified={}, upserted={:?}",
                                                result.matched_count, result.modified_count, result.upserted_id);
                                            break;
                                        },
                                        Err(e2) => {
                                            println!("ERROR: Fallback upsert also failed: {}", e2);
                                            return Err(BotError::GeneralError(format!("Database error after fallback: {}", e2)));
                                        }
                                    }
                            }

                            println!("Error inserting user_trades (retry {}/{}): {}", retry_count, max_retries, e);
                            tokio::time::sleep(tokio::time::Duration::from_millis(500 * retry_count)).await;
                        }
                    }
            }
        }

        println!("Total save_user_trades time: {:?}", start_time.elapsed());
        Ok(())
    }
}
