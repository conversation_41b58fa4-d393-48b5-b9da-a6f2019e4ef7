import{r,j as e,c as Q,B as u,a as _}from"./index-dCUkEeO4.js";import{C as f}from"./Card-CLlE15Sf.js";import{adminApi as X}from"./adminApi-BFZ8qr13.js";import{F as P}from"./CheckCircleIcon-CWCxsrwa.js";import{F as z}from"./ClockIcon-BpyFwEBR.js";import{F as Y}from"./ChevronDownIcon-CFiNowBg.js";import{F as V}from"./ArrowUpIcon-D56z6YfI.js";import{F as ee}from"./ArrowPathIcon-CSc3-Uxt.js";import{F as se}from"./XCircleIcon-CX-NVp4V.js";function te({title:c,titleId:n,...x},h){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:h,"aria-labelledby":n},x),c?r.createElement("title",{id:n},c):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M19.5 13.5 12 21m0 0-7.5-7.5M12 21V3"}))}const W=r.forwardRef(te);function le({title:c,titleId:n,...x},h){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:h,"aria-labelledby":n},x),c?r.createElement("title",{id:n},c):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M7.5 21 3 16.5m0 0L7.5 12M3 16.5h13.5m0-13.5L21 7.5m0 0L16.5 12M21 7.5H7.5"}))}const C=r.forwardRef(le);function re({title:c,titleId:n,...x},h){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:h,"aria-labelledby":n},x),c?r.createElement("title",{id:n},c):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.25 18.75a60.07 60.07 0 0 1 15.797 2.101c.727.198 1.453-.342 1.453-1.096V18.75M3.75 4.5v.75A.75.75 0 0 1 3 6h-.75m0 0v-.375c0-.621.504-1.125 1.125-1.125H20.25M2.25 6v9m18-10.5v.75c0 .414.336.75.75.75h.75m-1.5-1.5h.375c.621 0 1.125.504 1.125 1.125v9.75c0 .621-.504 1.125-1.125 1.125h-.375m1.5-1.5H21a.75.75 0 0 0-.75.75v.75m0 0H3.75m0 0h-.375a1.125 1.125 0 0 1-1.125-1.125V15m1.5 1.5v-.75A.75.75 0 0 0 3 15h-.75M15 10.5a3 3 0 1 1-6 0 3 3 0 0 1 6 0Zm3 0h.008v.008H18V10.5Zm-12 0h.008v.008H6V10.5Z"}))}const ae=r.forwardRef(re);function ie({title:c,titleId:n,...x},h){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:h,"aria-labelledby":n},x),c?r.createElement("title",{id:n},c):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 3c2.755 0 5.455.232 8.083.678.533.09.917.556.917 1.096v1.044a2.25 2.25 0 0 1-.659 1.591l-5.432 5.432a2.25 2.25 0 0 0-.659 1.591v2.927a2.25 2.25 0 0 1-1.244 2.013L9.75 21v-6.568a2.25 2.25 0 0 0-.659-1.591L3.659 7.409A2.25 2.25 0 0 1 3 5.818V4.774c0-.54.384-1.006.917-1.096A48.32 48.32 0 0 1 12 3Z"}))}const ne=r.forwardRef(ie);function we(){const[c,n]=r.useState(!0),[x,h]=r.useState(null),[j,N]=r.useState("all"),[g,k]=r.useState("24h"),[S,T]=r.useState(null),[o,Z]=r.useState([]),[I,q]=r.useState(0),[$,de]=r.useState(1),[G]=r.useState(20),[b,J]=r.useState({totalFees:0,todayFees:0,weeklyFees:0,monthlyFees:0}),L=async()=>{var s,t;try{n(!0),h(null);const l={page:$,per_page:G};if(j!=="all"&&(l.status=j),g!=="all"){const i=new Date;let m;switch(g){case"24h":m=new Date(i.getTime()-24*60*60*1e3);break;case"7d":m=new Date(i.getTime()-7*24*60*60*1e3);break;case"30d":m=new Date(i.getTime()-30*24*60*60*1e3);break;default:m=new Date(i.getTime()-24*60*60*1e3)}l.start_date=Math.floor(m.getTime()/1e3).toString(),l.end_date=Math.floor(i.getTime()/1e3).toString()}const a=await X.getTransactions(l);Z(a.transactions),q(a.total),K(a.transactions)}catch(l){console.error("Failed to fetch transactions:",l),h(((t=(s=l.response)==null?void 0:s.data)==null?void 0:t.error)||"Failed to load transactions")}finally{n(!1)}},K=s=>{const t=new Date,l=new Date(t.getTime()-24*60*60*1e3),a=new Date(t.getTime()-7*24*60*60*1e3),i=new Date(t.getTime()-30*24*60*60*1e3);let m=0,M=0,U=0,O=0;s.forEach(H=>{const F=new Date(H.timestamp*1e3),v=H.gas_fee||0;m+=v,F>=l&&(M+=v),F>=a&&(U+=v),F>=i&&(O+=v)}),J({totalFees:m,todayFees:M,weeklyFees:U,monthlyFees:O})};r.useEffect(()=>{L()},[$,j,g]);function p(...s){return s.filter(Boolean).join(" ")}const w=s=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD",minimumFractionDigits:2,maximumFractionDigits:6}).format(s),y=(s,t,l)=>{let a=18;t==="SOL"&&(a=9);const i=s/Math.pow(10,a);return i<1e-6?`${i.toFixed(8)} ${l}`:i<.001?`${i.toFixed(6)} ${l}`:i<1?`${i.toFixed(4)} ${l}`:i<1e3?`${i.toFixed(2)} ${l}`:`${i.toFixed(1)} ${l}`},A=s=>{switch(s.toUpperCase()){case"ETH":return"ETH";case"BSC":return"BNB";case"BASE":return"ETH";case"SOL":return"SOL";default:return s}},d=s=>s.direction?s.direction.toLowerCase():s.transaction_type.toLowerCase(),E=(s,t)=>{switch(s.toUpperCase()){case"ETH":return`https://etherscan.io/tx/${t}`;case"BSC":return`https://bscscan.com/tx/${t}`;case"BASE":return`https://basescan.org/tx/${t}`;case"SOL":return`https://solscan.io/tx/${t}`;default:return`https://etherscan.io/tx/${t}`}},R=s=>{switch(s.toLowerCase()){case"completed":case"success":return e.jsx(P,{className:"h-5 w-5 text-green-400"});case"pending":return e.jsx(z,{className:"h-5 w-5 text-yellow-400"});case"failed":case"error":return e.jsx(se,{className:"h-5 w-5 text-red-400"});default:return e.jsx(ee,{className:"h-5 w-5 text-gray-400"})}},B=s=>{switch(s.toLowerCase()){case"completed":case"success":return"bg-green-900/30 text-green-400 border border-green-500/30";case"pending":return"bg-yellow-900/30 text-yellow-400 border border-yellow-500/30";case"failed":case"error":return"bg-red-900/30 text-red-400 border border-red-500/30";default:return"bg-gray-900/30 text-gray-400 border border-gray-500/30"}},D=(()=>{const s=o.filter(a=>a.success||a.status.toLowerCase()==="completed").length,t=o.filter(a=>a.status.toLowerCase()==="pending").length,l=o.filter(a=>!a.success&&a.status.toLowerCase()==="failed").length;return{completed:s,pending:t,failed:l}})();return c?e.jsx("div",{className:"flex items-center justify-center h-64",children:e.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"})}):x?e.jsxs("div",{className:"space-y-8",children:[e.jsx("div",{className:"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4",children:e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold text-white",children:"Transactions"}),e.jsx("p",{className:"mt-1 text-sm text-gray-400",children:"Monitor bot trading transactions and fees"})]})}),e.jsx(f,{className:"p-6",children:e.jsx("div",{className:"flex items-center justify-center h-64",children:e.jsxs("div",{className:"text-center",children:[e.jsx(Q,{className:"h-12 w-12 text-red-400 mx-auto mb-4"}),e.jsx("h3",{className:"text-lg font-medium text-white mb-2",children:"Failed to Load Transactions"}),e.jsx("p",{className:"text-gray-400 mb-4",children:x}),e.jsx(u,{onClick:L,variant:"glass",children:"Try Again"})]})})})]}):e.jsxs("div",{className:"space-y-8",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold text-white",children:"Transactions"}),e.jsx("p",{className:"mt-1 text-sm text-gray-400",children:"Monitor bot trading transactions and fees"})]}),e.jsxs("div",{className:"flex space-x-2",children:[e.jsx(u,{variant:g==="24h"?"glass":"outline",size:"sm",onClick:()=>k("24h"),children:"24h"}),e.jsx(u,{variant:g==="7d"?"glass":"outline",size:"sm",onClick:()=>k("7d"),children:"7d"}),e.jsx(u,{variant:g==="30d"?"glass":"outline",size:"sm",onClick:()=>k("30d"),children:"30d"})]})]}),e.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6",children:[e.jsx(f,{className:"p-4 md:p-6 relative overflow-hidden",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"flex-shrink-0",children:e.jsx("div",{className:"h-10 w-10 md:h-12 md:w-12 rounded-full bg-indigo-800/30 flex items-center justify-center",children:e.jsx(C,{className:"h-5 w-5 md:h-6 md:w-6 text-indigo-400","aria-hidden":"true"})})}),e.jsx("div",{className:"ml-3 md:ml-5 w-0 flex-1",children:e.jsxs("dl",{children:[e.jsx("dt",{className:"text-xs md:text-sm font-medium text-gray-300 truncate",children:"Total Transactions"}),e.jsx("dd",{children:e.jsx("div",{className:"text-base md:text-lg font-semibold text-white",children:I.toLocaleString()})})]})})]})}),e.jsx(f,{className:"p-4 md:p-6 relative overflow-hidden",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"flex-shrink-0",children:e.jsx("div",{className:"h-10 w-10 md:h-12 md:w-12 rounded-full bg-green-800/30 flex items-center justify-center",children:e.jsx(P,{className:"h-5 w-5 md:h-6 md:w-6 text-green-400","aria-hidden":"true"})})}),e.jsx("div",{className:"ml-3 md:ml-5 w-0 flex-1",children:e.jsxs("dl",{children:[e.jsx("dt",{className:"text-xs md:text-sm font-medium text-gray-300 truncate",children:"Completed"}),e.jsx("dd",{children:e.jsx("div",{className:"text-base md:text-lg font-semibold text-white",children:D.completed.toLocaleString()})})]})})]})}),e.jsx(f,{className:"p-4 md:p-6 relative overflow-hidden",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"flex-shrink-0",children:e.jsx("div",{className:"h-10 w-10 md:h-12 md:w-12 rounded-full bg-yellow-800/30 flex items-center justify-center",children:e.jsx(z,{className:"h-5 w-5 md:h-6 md:w-6 text-yellow-400","aria-hidden":"true"})})}),e.jsx("div",{className:"ml-3 md:ml-5 w-0 flex-1",children:e.jsxs("dl",{children:[e.jsx("dt",{className:"text-xs md:text-sm font-medium text-gray-300 truncate",children:"Pending"}),e.jsx("dd",{children:e.jsx("div",{className:"text-base md:text-lg font-semibold text-white",children:D.pending.toLocaleString()})})]})})]})}),e.jsx(f,{className:"p-4 md:p-6 relative overflow-hidden",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"flex-shrink-0",children:e.jsx("div",{className:"h-10 w-10 md:h-12 md:w-12 rounded-full bg-purple-800/30 flex items-center justify-center",children:e.jsx(ae,{className:"h-5 w-5 md:h-6 md:w-6 text-purple-400","aria-hidden":"true"})})}),e.jsx("div",{className:"ml-3 md:ml-5 w-0 flex-1",children:e.jsxs("dl",{children:[e.jsx("dt",{className:"text-xs md:text-sm font-medium text-gray-300 truncate",children:"Total Fees"}),e.jsx("dd",{children:e.jsx("div",{className:"text-base md:text-lg font-semibold text-white",children:w(b.totalFees)})})]})})]})})]}),e.jsx("div",{className:"grid grid-cols-1 gap-6",children:e.jsxs(f,{className:"p-6 relative overflow-hidden",children:[e.jsxs("div",{className:"flex justify-between items-center mb-6",children:[e.jsx("h3",{className:"text-base font-semibold leading-6 text-white",children:"Recent Transactions"}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsxs("div",{className:"relative",children:[e.jsxs(u,{variant:"outline",size:"sm",className:"flex items-center",children:[e.jsx(ne,{className:"h-4 w-4 mr-1"}),"Filter: ",j.charAt(0).toUpperCase()+j.slice(1),e.jsx(Y,{className:"h-4 w-4 ml-1"})]}),e.jsx("div",{className:"absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-gray-800 ring-1 ring-black ring-opacity-5 z-10 hidden",children:e.jsxs("div",{className:"py-1",role:"menu","aria-orientation":"vertical",children:[e.jsx("button",{className:"block px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 w-full text-left",onClick:()=>N("all"),children:"All"}),e.jsx("button",{className:"block px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 w-full text-left",onClick:()=>N("completed"),children:"Completed"}),e.jsx("button",{className:"block px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 w-full text-left",onClick:()=>N("pending"),children:"Pending"}),e.jsx("button",{className:"block px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 w-full text-left",onClick:()=>N("failed"),children:"Failed"})]})})]}),e.jsx(u,{variant:"outline",size:"sm",children:"Export"})]})]}),e.jsxs("div",{className:"overflow-hidden rounded-xl border border-white/10 bg-white/5 backdrop-blur-sm",children:[e.jsx("div",{className:"block md:hidden",children:e.jsx("div",{className:"space-y-4 p-4",children:o.length===0?e.jsxs("div",{className:"text-center py-8",children:[e.jsx(C,{className:"h-12 w-12 mx-auto mb-4 text-gray-500"}),e.jsx("h3",{className:"text-lg font-medium text-white mb-2",children:"No transactions found"}),e.jsx("p",{className:"text-gray-400",children:"No transactions match your current filters."})]}):o.map(s=>e.jsxs("div",{className:p(S===s.id?"bg-indigo-900/20":"","bg-white/5 rounded-lg p-4 border border-white/10 hover:bg-indigo-900/10 cursor-pointer"),onClick:()=>T(s.id),children:[e.jsxs("div",{className:"flex justify-between items-start mb-2",children:[e.jsxs("div",{className:"flex-1",children:[e.jsx("p",{className:"text-sm font-medium text-white truncate",children:s.id}),e.jsx("p",{className:"text-xs text-gray-400",children:s.first_name?s.last_name?`${s.first_name} ${s.last_name}`:s.first_name:s.username||`User ${s.user_id}`})]}),e.jsxs("span",{className:p(B(s.status),"inline-flex items-center rounded-full px-2 py-1 text-xs font-medium"),children:[e.jsx("span",{className:"mr-1",children:R(s.status)}),s.status.charAt(0).toUpperCase()+s.status.slice(1)]})]}),e.jsxs("div",{className:"flex justify-between items-center mb-2",children:[e.jsxs("span",{className:p(d(s)==="buy"?"bg-green-900/30 text-green-400 border border-green-500/30":"bg-red-900/30 text-red-400 border border-red-500/30","inline-flex items-center rounded-full px-2 py-1 text-xs font-medium"),children:[d(s)==="buy"?e.jsx(W,{className:"-ml-0.5 mr-1 h-3 w-3 text-green-400"}):e.jsx(V,{className:"-ml-0.5 mr-1 h-3 w-3 text-red-400"}),d(s).charAt(0).toUpperCase()+d(s).slice(1)]}),e.jsx("span",{className:"text-xs text-gray-400 bg-gray-800/50 px-2 py-1 rounded",children:s.blockchain.toUpperCase()})]}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-white",children:(()=>{if(d(s)==="buy"){const t=A(s.blockchain);return y(s.amount,s.blockchain,t)}else return y(s.amount,s.blockchain,s.token_symbol)})()}),e.jsx("p",{className:"text-xs text-gray-400",children:d(s)==="buy"?"Amount Spent":"Amount Sold"})]}),e.jsx(u,{variant:"outline",size:"sm",onClick:t=>{t.stopPropagation(),s.hash&&window.open(E(s.blockchain,s.hash),"_blank")},children:"View"})]})]},s.id))})}),e.jsxs("table",{className:"hidden md:table min-w-full divide-y divide-white/10",children:[e.jsx("thead",{children:e.jsxs("tr",{children:[e.jsx("th",{scope:"col",className:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-white sm:pl-6",children:"Transaction ID"}),e.jsx("th",{scope:"col",className:"px-3 py-3.5 text-left text-sm font-semibold text-white",children:"User"}),e.jsx("th",{scope:"col",className:"px-3 py-3.5 text-left text-sm font-semibold text-white",children:"Bot"}),e.jsx("th",{scope:"col",className:"px-3 py-3.5 text-left text-sm font-semibold text-white",children:"Type"}),e.jsx("th",{scope:"col",className:"px-3 py-3.5 text-left text-sm font-semibold text-white",children:"Amount"}),e.jsx("th",{scope:"col",className:"px-3 py-3.5 text-left text-sm font-semibold text-white",children:"Fee"}),e.jsx("th",{scope:"col",className:"px-3 py-3.5 text-left text-sm font-semibold text-white",children:"Status"}),e.jsx("th",{scope:"col",className:"px-3 py-3.5 text-left text-sm font-semibold text-white",children:"Blockchain"}),e.jsx("th",{scope:"col",className:"relative py-3.5 pl-3 pr-4 sm:pr-6",children:e.jsx("span",{className:"sr-only",children:"Actions"})})]})}),e.jsx("tbody",{className:"divide-y divide-white/10",children:o.length===0?e.jsx("tr",{children:e.jsx("td",{colSpan:9,className:"px-6 py-12 text-center",children:e.jsxs("div",{className:"text-gray-400",children:[e.jsx(C,{className:"h-12 w-12 mx-auto mb-4 text-gray-500"}),e.jsx("h3",{className:"text-lg font-medium text-white mb-2",children:"No transactions found"}),e.jsx("p",{children:"No transactions match your current filters."})]})})}):o.map(s=>e.jsxs("tr",{className:p(S===s.id?"bg-indigo-900/20":"","hover:bg-indigo-900/10 cursor-pointer"),onClick:()=>T(s.id),children:[e.jsx("td",{className:"whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-white sm:pl-6",children:s.id}),e.jsx("td",{className:"whitespace-nowrap px-3 py-4 text-sm text-gray-300",children:s.first_name?s.last_name?`${s.first_name} ${s.last_name}`:s.first_name:s.username||`User ${s.user_id}`}),e.jsxs("td",{className:"whitespace-nowrap px-3 py-4 text-sm text-gray-300",children:[s.bot_type," Bot"]}),e.jsx("td",{className:"whitespace-nowrap px-3 py-4 text-sm",children:e.jsxs("span",{className:p(d(s)==="buy"?"bg-green-900/30 text-green-400 border border-green-500/30":"bg-red-900/30 text-red-400 border border-red-500/30","inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium"),children:[d(s)==="buy"?e.jsx(W,{className:"-ml-0.5 mr-1.5 h-3 w-3 text-green-400"}):e.jsx(V,{className:"-ml-0.5 mr-1.5 h-3 w-3 text-red-400"}),d(s).charAt(0).toUpperCase()+d(s).slice(1)]})}),e.jsxs("td",{className:"whitespace-nowrap px-3 py-4 text-sm text-gray-300",children:[e.jsx("div",{children:(()=>{if(d(s)==="buy"){const t=A(s.blockchain);return y(s.amount,s.blockchain,t)}else return y(s.amount,s.blockchain,s.token_symbol)})()}),e.jsx("div",{className:"text-xs text-gray-400",children:d(s)==="buy"?"Amount Spent":"Amount Sold"})]}),e.jsx("td",{className:"whitespace-nowrap px-3 py-4 text-sm text-gray-300",children:e.jsxs("div",{className:"space-y-1",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("span",{children:s.admin_fee_amount?w(s.admin_fee_amount):"N/A"}),s.admin_fee_status&&e.jsxs("span",{className:p(s.admin_fee_status==="completed"?"bg-green-900/30 text-green-400":s.admin_fee_status==="pending"?"bg-yellow-900/30 text-yellow-400":s.admin_fee_status==="retrying"?"bg-blue-900/30 text-blue-400":"bg-red-900/30 text-red-400","inline-flex items-center rounded-full px-1.5 py-0.5 text-xs font-medium"),children:[s.admin_fee_status==="completed"&&"✓",s.admin_fee_status==="pending"&&"⏳",s.admin_fee_status==="retrying"&&"🔄",s.admin_fee_status==="failed"&&"✗"]})]}),s.admin_fee_percentage&&e.jsxs("div",{className:"text-xs text-gray-400",children:[s.admin_fee_percentage,"% • ",s.admin_fee_collection_method||"smart"]})]})}),e.jsx("td",{className:"whitespace-nowrap px-3 py-4 text-sm",children:e.jsxs("span",{className:p(B(s.status),"inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium"),children:[e.jsx("span",{className:"mr-1.5",children:R(s.status)}),s.status.charAt(0).toUpperCase()+s.status.slice(1)]})}),e.jsx("td",{className:"whitespace-nowrap px-3 py-4 text-sm",children:e.jsx("span",{className:"text-gray-300",children:s.blockchain.toUpperCase()})}),e.jsx("td",{className:"relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-6",children:e.jsx(u,{variant:"outline",size:"sm",onClick:t=>{t.stopPropagation(),s.hash&&window.open(E(s.blockchain,s.hash),"_blank")},children:"View"})})]},s.id))})]})]})]})}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs(f,{className:"p-6 relative overflow-hidden",children:[e.jsx("h3",{className:"text-base font-semibold leading-6 text-white mb-4",children:"Fee Statistics"}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"h-10 w-10 flex-shrink-0 rounded-full bg-indigo-800/30 flex items-center justify-center",children:e.jsx(_,{className:"h-5 w-5 text-indigo-400","aria-hidden":"true"})}),e.jsxs("div",{className:"ml-4",children:[e.jsx("p",{className:"text-sm font-medium text-white",children:"Today's Fees"}),e.jsx("p",{className:"text-xs text-gray-400",children:"Last 24 hours"})]})]}),e.jsx("p",{className:"text-lg font-semibold text-white",children:w(b.todayFees)})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"h-10 w-10 flex-shrink-0 rounded-full bg-indigo-800/30 flex items-center justify-center",children:e.jsx(_,{className:"h-5 w-5 text-indigo-400","aria-hidden":"true"})}),e.jsxs("div",{className:"ml-4",children:[e.jsx("p",{className:"text-sm font-medium text-white",children:"Weekly Fees"}),e.jsx("p",{className:"text-xs text-gray-400",children:"Last 7 days"})]})]}),e.jsx("p",{className:"text-lg font-semibold text-white",children:w(b.weeklyFees)})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"h-10 w-10 flex-shrink-0 rounded-full bg-indigo-800/30 flex items-center justify-center",children:e.jsx(_,{className:"h-5 w-5 text-indigo-400","aria-hidden":"true"})}),e.jsxs("div",{className:"ml-4",children:[e.jsx("p",{className:"text-sm font-medium text-white",children:"Monthly Fees"}),e.jsx("p",{className:"text-xs text-gray-400",children:"Last 30 days"})]})]}),e.jsx("p",{className:"text-lg font-semibold text-white",children:w(b.monthlyFees)})]})]})]}),e.jsxs(f,{className:"p-6 relative overflow-hidden",children:[e.jsx("h3",{className:"text-base font-semibold leading-6 text-white mb-4",children:"Transaction Distribution"}),e.jsxs("div",{className:"space-y-4",children:[(()=>{const s=o.reduce((l,a)=>(l[a.bot_type]=(l[a.bot_type]||0)+1,l),{}),t=o.length;return Object.entries(s).map(([l,a])=>{const i=t>0?Math.round(a/t*100):0;return e.jsxs("div",{children:[e.jsxs("div",{className:"flex justify-between mb-1",children:[e.jsxs("span",{className:"text-sm text-gray-300",children:[l," Bot"]}),e.jsxs("span",{className:"text-sm text-gray-300",children:[i,"%"]})]}),e.jsx("div",{className:"w-full bg-gray-700 rounded-full h-2",children:e.jsx("div",{className:"bg-indigo-500 h-2 rounded-full",style:{width:`${i}%`}})})]},l)})})(),e.jsx("div",{className:"pt-4 border-t border-white/10 mt-4",children:(()=>{const s=o.filter(m=>d(m)==="buy").length,t=o.filter(m=>d(m)==="sell").length,l=s+t,a=l>0?Math.round(s/l*100):0,i=l>0?Math.round(t/l*100):0;return e.jsxs(e.Fragment,{children:[e.jsxs("div",{children:[e.jsxs("div",{className:"flex justify-between mb-1",children:[e.jsx("span",{className:"text-sm text-gray-300",children:"Buy Orders"}),e.jsxs("span",{className:"text-sm text-gray-300",children:[a,"%"]})]}),e.jsx("div",{className:"w-full bg-gray-700 rounded-full h-2",children:e.jsx("div",{className:"bg-green-500 h-2 rounded-full",style:{width:`${a}%`}})})]}),e.jsxs("div",{children:[e.jsxs("div",{className:"flex justify-between mb-1",children:[e.jsx("span",{className:"text-sm text-gray-300",children:"Sell Orders"}),e.jsxs("span",{className:"text-sm text-gray-300",children:[i,"%"]})]}),e.jsx("div",{className:"w-full bg-gray-700 rounded-full h-2",children:e.jsx("div",{className:"bg-red-500 h-2 rounded-full",style:{width:`${i}%`}})})]})]})})()})]})]})]})]})}export{we as default};
