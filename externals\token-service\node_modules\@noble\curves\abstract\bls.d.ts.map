{"version": 3, "file": "bls.d.ts", "sourceRoot": "", "sources": ["../src/abstract/bls.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;;IAgBI;AACJ,sEAAsE;AAEtE,OAAO,EAEL,KAAK,YAAY,EACjB,KAAK,IAAI,IAAI,OAAO,EACpB,KAAK,UAAU,EACf,YAAY,EACb,MAAM,oBAAoB,CAAC;AAC5B,OAAO,EAAE,KAAK,MAAM,EAAoC,MAAM,cAAc,CAAC;AAC7E,OAAO,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,YAAY,CAAC;AAClE,OAAO,EAAE,KAAK,KAAK,EAAE,KAAK,GAAG,EAAE,KAAK,OAAO,EAAyB,MAAM,YAAY,CAAC;AACvF,OAAO,EACL,KAAK,cAAc,EACnB,KAAK,eAAe,EACpB,KAAK,aAAa,EAEnB,MAAM,kBAAkB,CAAC;AAE1B,KAAK,EAAE,GAAG,MAAM,CAAC;AAKjB,MAAM,MAAM,SAAS,GAAG,gBAAgB,GAAG,UAAU,CAAC;AAEtD,MAAM,MAAM,mBAAmB,CAAC,EAAE,IAAI;IACpC,OAAO,CAAC,GAAG,EAAE,GAAG,GAAG,aAAa,CAAC,EAAE,CAAC,CAAC;IACrC,UAAU,CAAC,KAAK,EAAE,aAAa,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC;IACjD,KAAK,CAAC,KAAK,EAAE,aAAa,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC;CACzC,CAAC;AAEF,MAAM,MAAM,cAAc,CAAC,EAAE,IAAI;IAC/B,OAAO,CAAC,GAAG,EAAE,GAAG,GAAG,aAAa,CAAC,EAAE,CAAC,CAAC;IACrC,UAAU,CAAC,KAAK,EAAE,aAAa,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC;IACjD,KAAK,CAAC,KAAK,EAAE,aAAa,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC;CACzC,CAAC;AAEF,MAAM,MAAM,wBAAwB,GAAG,CACrC,EAAE,EAAE,GAAG,EACP,EAAE,EAAE,GAAG,EACP,EAAE,EAAE,GAAG,EACP,EAAE,EAAE,GAAG,EACP,EAAE,EAAE,GAAG,KACJ;IAAE,EAAE,EAAE,GAAG,CAAC;IAAC,EAAE,EAAE,GAAG,CAAC;IAAC,EAAE,EAAE,GAAG,CAAA;CAAE,CAAC;AACnC,MAAM,MAAM,gBAAgB,GAAG,CAC7B,EAAE,EAAE,GAAG,EACP,EAAE,EAAE,GAAG,EACP,EAAE,EAAE,GAAG,EACP,EAAE,EAAE,GAAG,EACP,EAAE,EAAE,GAAG,EACP,QAAQ,EAAE,wBAAwB,KAC/B,IAAI,CAAC;AACV,MAAM,MAAM,SAAS,GAAG;IACtB,EAAE,EAAE,IAAI,CAAC,eAAe,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,GAAG;QACnC,cAAc,EAAE,cAAc,CAAC,EAAE,CAAC,CAAC;QACnC,UAAU,EAAE,UAAU,CAAC,EAAE,CAAC,CAAC;QAC3B,WAAW,EAAE,OAAO,CAAC;KACtB,CAAC;IACF,EAAE,EAAE,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,GAAG;QACpC,SAAS,EAAE,cAAc,CAAC,GAAG,CAAC,CAAC;QAC/B,UAAU,EAAE,UAAU,CAAC,GAAG,CAAC,CAAC;QAC5B,WAAW,EAAE,OAAO,CAAC;KACtB,CAAC;IACF,MAAM,EAAE;QACN,EAAE,EAAE,MAAM,CAAC,EAAE,CAAC,CAAC;QACf,EAAE,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;QACnB,GAAG,EAAE,MAAM,CAAC;QACZ,GAAG,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC;QACjB,IAAI,EAAE,OAAO,CAAC;KACf,CAAC;IACF,MAAM,EAAE;QAIN,WAAW,EAAE,MAAM,CAAC;QACpB,SAAS,EAAE,OAAO,CAAC;QACnB,CAAC,EAAE,MAAM,CAAC;QACV,SAAS,EAAE,SAAS,CAAC;KACtB,CAAC;IACF,WAAW,EAAE,OAAO,CAAC;IACrB,IAAI,EAAE,KAAK,CAAC;IACZ,WAAW,EAAE,CAAC,WAAW,CAAC,EAAE,MAAM,KAAK,UAAU,CAAC;IAElD,cAAc,CAAC,EAAE,gBAAgB,CAAC;CACnC,CAAC;AAEF,KAAK,gBAAgB,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC;AAC1C,KAAK,UAAU,GAAG,gBAAgB,EAAE,CAAC;AAErC,MAAM,MAAM,OAAO,GAAG;IACpB,YAAY,EAAE,CAAC,UAAU,EAAE,OAAO,KAAK,UAAU,CAAC;IAClD,8BAA8B,EAAE,CAAC,UAAU,EAAE,OAAO,KAAK,UAAU,CAAC;IACpE,IAAI,EAAE;QACJ,CAAC,OAAO,EAAE,GAAG,EAAE,UAAU,EAAE,OAAO,EAAE,OAAO,CAAC,EAAE,YAAY,GAAG,UAAU,CAAC;QACxE,CAAC,OAAO,EAAE,aAAa,CAAC,GAAG,CAAC,EAAE,UAAU,EAAE,OAAO,EAAE,OAAO,CAAC,EAAE,YAAY,GAAG,aAAa,CAAC,GAAG,CAAC,CAAC;KAChG,CAAC;IACF,kBAAkB,EAAE;QAClB,CAAC,OAAO,EAAE,GAAG,EAAE,UAAU,EAAE,OAAO,EAAE,OAAO,CAAC,EAAE,YAAY,GAAG,UAAU,CAAC;QACxE,CAAC,OAAO,EAAE,aAAa,CAAC,EAAE,CAAC,EAAE,UAAU,EAAE,OAAO,EAAE,OAAO,CAAC,EAAE,YAAY,GAAG,aAAa,CAAC,EAAE,CAAC,CAAC;KAC9F,CAAC;IACF,MAAM,EAAE,CACN,SAAS,EAAE,GAAG,GAAG,aAAa,CAAC,GAAG,CAAC,EACnC,OAAO,EAAE,GAAG,GAAG,aAAa,CAAC,GAAG,CAAC,EACjC,SAAS,EAAE,GAAG,GAAG,aAAa,CAAC,EAAE,CAAC,EAClC,OAAO,CAAC,EAAE,YAAY,KACnB,OAAO,CAAC;IACb,oBAAoB,EAAE,CACpB,SAAS,EAAE,GAAG,GAAG,aAAa,CAAC,EAAE,CAAC,EAClC,OAAO,EAAE,GAAG,GAAG,aAAa,CAAC,EAAE,CAAC,EAChC,SAAS,EAAE,GAAG,GAAG,aAAa,CAAC,GAAG,CAAC,EACnC,OAAO,CAAC,EAAE,YAAY,KACnB,OAAO,CAAC;IACb,WAAW,EAAE,CACX,SAAS,EAAE,GAAG,GAAG,aAAa,CAAC,GAAG,CAAC,EACnC,QAAQ,EAAE,CAAC,GAAG,GAAG,aAAa,CAAC,GAAG,CAAC,CAAC,EAAE,EACtC,UAAU,EAAE,CAAC,GAAG,GAAG,aAAa,CAAC,EAAE,CAAC,CAAC,EAAE,EACvC,OAAO,CAAC,EAAE,YAAY,KACnB,OAAO,CAAC;IACb,mBAAmB,EAAE;QACnB,CAAC,UAAU,EAAE,GAAG,EAAE,GAAG,UAAU,CAAC;QAChC,CAAC,UAAU,EAAE,aAAa,CAAC,EAAE,CAAC,EAAE,GAAG,aAAa,CAAC,EAAE,CAAC,CAAC;KACtD,CAAC;IACF,mBAAmB,EAAE;QACnB,CAAC,UAAU,EAAE,GAAG,EAAE,GAAG,UAAU,CAAC;QAChC,CAAC,UAAU,EAAE,aAAa,CAAC,GAAG,CAAC,EAAE,GAAG,aAAa,CAAC,GAAG,CAAC,CAAC;KACxD,CAAC;IACF,wBAAwB,EAAE;QACxB,CAAC,UAAU,EAAE,GAAG,EAAE,GAAG,UAAU,CAAC;QAChC,CAAC,UAAU,EAAE,aAAa,CAAC,EAAE,CAAC,EAAE,GAAG,aAAa,CAAC,EAAE,CAAC,CAAC;KACtD,CAAC;IACF,eAAe,EAAE,CAAC,KAAK,EAAE,CAAC,UAAU,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC;IACzD,OAAO,EAAE,CAAC,CAAC,EAAE,aAAa,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,aAAa,CAAC,GAAG,CAAC,EAAE,iBAAiB,CAAC,EAAE,OAAO,KAAK,IAAI,CAAC;IAC5F,YAAY,EAAE,CACZ,KAAK,EAAE;QAAE,EAAE,EAAE,aAAa,CAAC,EAAE,CAAC,CAAC;QAAC,EAAE,EAAE,aAAa,CAAC,GAAG,CAAC,CAAA;KAAE,EAAE,EAC1D,iBAAiB,CAAC,EAAE,OAAO,KACxB,IAAI,CAAC;IACV,EAAE,EAAE,cAAc,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,OAAO,YAAY,CAAC,EAAE,CAAC,CAAC,CAAC;IAC7D,EAAE,EAAE,cAAc,CAAC,GAAG,CAAC,GAAG,UAAU,CAAC,OAAO,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC;IAC/D,SAAS,EAAE,cAAc,CAAC,GAAG,CAAC,CAAC;IAC/B,cAAc,EAAE,mBAAmB,CAAC,EAAE,CAAC,CAAC;IACxC,MAAM,EAAE;QACN,WAAW,EAAE,MAAM,CAAC;QACpB,CAAC,EAAE,MAAM,CAAC;QACV,GAAG,EAAE,MAAM,CAAC;QACZ,GAAG,EAAE,GAAG,CAAC;KACV,CAAC;IACF,MAAM,EAAE;QACN,EAAE,EAAE,MAAM,CAAC,EAAE,CAAC,CAAC;QACf,GAAG,EAAE,MAAM,CAAC;QACZ,GAAG,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC;QACjB,IAAI,EAAE,OAAO,CAAC;QACd,EAAE,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;KACpB,CAAC;IACF,KAAK,EAAE;QACL,gBAAgB,EAAE,MAAM,UAAU,CAAC;QACnC,sBAAsB,EAAE,CAAC,CAAC,EAAE,aAAa,CAAC,GAAG,CAAC,KAAK,UAAU,CAAC;KAC/D,CAAC;CACH,CAAC;AAgBF,wBAAgB,GAAG,CAAC,KAAK,EAAE,SAAS,GAAG,OAAO,CA0X7C"}