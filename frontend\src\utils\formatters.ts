// Utility functions for formatting data

export const formatCurrency = (amount: number): string => {
  if (amount === 0) return '$0.00';
  
  if (amount < 0.01) {
    return `$${amount.toFixed(6)}`;
  }
  
  if (amount < 1) {
    return `$${amount.toFixed(4)}`;
  }
  
  if (amount < 1000) {
    return `$${amount.toFixed(2)}`;
  }
  
  if (amount < 1000000) {
    return `$${(amount / 1000).toFixed(1)}K`;
  }
  
  if (amount < 1000000000) {
    return `$${(amount / 1000000).toFixed(1)}M`;
  }
  
  return `$${(amount / 1000000000).toFixed(1)}B`;
};

export const formatDate = (timestamp: number): string => {
  const date = new Date(timestamp);
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  });
};

export const formatRelativeTime = (timestamp: number): string => {
  const now = Date.now();
  const diff = now - timestamp;
  
  const seconds = Math.floor(diff / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);
  const days = Math.floor(hours / 24);
  
  if (days > 0) {
    return `${days} day${days > 1 ? 's' : ''} ago`;
  }
  
  if (hours > 0) {
    return `${hours} hour${hours > 1 ? 's' : ''} ago`;
  }
  
  if (minutes > 0) {
    return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;
  }
  
  return `${seconds} second${seconds > 1 ? 's' : ''} ago`;
};

export const formatNumber = (num: number): string => {
  if (num < 1000) {
    return num.toString();
  }
  
  if (num < 1000000) {
    return `${(num / 1000).toFixed(1)}K`;
  }
  
  if (num < 1000000000) {
    return `${(num / 1000000).toFixed(1)}M`;
  }
  
  return `${(num / 1000000000).toFixed(1)}B`;
};

export const formatPercentage = (value: number): string => {
  return `${value.toFixed(1)}%`;
};

export const formatTokenAmount = (amount: number, decimals: number = 18): string => {
  const divisor = Math.pow(10, decimals);
  const tokenAmount = amount / divisor;
  
  if (tokenAmount < 0.0001) {
    return tokenAmount.toExponential(2);
  }
  
  if (tokenAmount < 1) {
    return tokenAmount.toFixed(6);
  }
  
  if (tokenAmount < 1000) {
    return tokenAmount.toFixed(4);
  }
  
  return formatNumber(tokenAmount);
};

export const formatAddress = (address: string, startChars: number = 6, endChars: number = 4): string => {
  if (!address || address.length <= startChars + endChars) {
    return address;
  }
  
  return `${address.slice(0, startChars)}...${address.slice(-endChars)}`;
};

export const formatBlockchainAmount = (amount: number, blockchain: string, symbol: string): string => {
  // Format amounts based on blockchain and token
  if (blockchain.toLowerCase() === 'sol' || symbol === 'SOL') {
    return `${amount.toFixed(6)} ${symbol}`;
  }
  
  if (symbol === 'ETH' || symbol === 'BNB') {
    return `${amount.toFixed(6)} ${symbol}`;
  }
  
  // For other tokens, use smart formatting
  if (amount < 0.0001) {
    return `${amount.toExponential(2)} ${symbol}`;
  }
  
  if (amount < 1) {
    return `${amount.toFixed(6)} ${symbol}`;
  }
  
  if (amount < 1000) {
    return `${amount.toFixed(4)} ${symbol}`;
  }
  
  return `${formatNumber(amount)} ${symbol}`;
};

export const getNativeTokenSymbol = (blockchain: string): string => {
  switch (blockchain.toLowerCase()) {
    case 'eth':
    case 'ethereum':
      return 'ETH';
    case 'bsc':
    case 'binance':
      return 'BNB';
    case 'base':
      return 'ETH';
    case 'sol':
    case 'solana':
      return 'SOL';
    default:
      return 'TOKEN';
  }
};

export const getExplorerUrl = (blockchain: string, hash: string): string => {
  const explorerUrls = {
    eth: 'https://etherscan.io/tx/',
    ethereum: 'https://etherscan.io/tx/',
    bsc: 'https://bscscan.com/tx/',
    binance: 'https://bscscan.com/tx/',
    base: 'https://basescan.org/tx/',
    sol: 'https://solscan.io/tx/',
    solana: 'https://solscan.io/tx/',
  };
  
  const baseUrl = explorerUrls[blockchain.toLowerCase() as keyof typeof explorerUrls];
  return baseUrl ? `${baseUrl}${hash}` : '#';
};

export const classNames = (...classes: (string | undefined | null | false)[]): string => {
  return classes.filter(Boolean).join(' ');
};
