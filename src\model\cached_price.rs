use serde::{Deserialize, Serialize};
use mongodb::bson::oid::ObjectId;
use std::time::{SystemTime, UNIX_EPOCH};
use crate::model::blockchain::Blockchain;

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct CachedPrice {
    #[serde(rename = "_id", skip_serializing_if = "Option::is_none")]
    pub id: Option<ObjectId>,
    pub blockchain: Blockchain,
    pub native_symbol: String,
    pub price_usd: f64,
    pub last_updated: u64,
    pub created_at: u64,
}

fn default_timestamp() -> u64 {
    SystemTime::now()
        .duration_since(UNIX_EPOCH)
        .unwrap_or_default()
        .as_secs()
}

impl CachedPrice {
    pub fn new(blockchain: Blockchain, native_symbol: String, price_usd: f64) -> Self {
        let now = default_timestamp();
        
        Self {
            id: None,
            blockchain,
            native_symbol,
            price_usd,
            last_updated: now,
            created_at: now,
        }
    }

    pub fn update_price(&mut self, new_price: f64) {
        self.price_usd = new_price;
        self.last_updated = default_timestamp();
    }

    pub fn is_stale(&self, max_age_seconds: u64) -> bool {
        let now = default_timestamp();
        now - self.last_updated > max_age_seconds
    }

    pub fn age_seconds(&self) -> u64 {
        let now = default_timestamp();
        now - self.last_updated
    }
}

impl Default for CachedPrice {
    fn default() -> Self {
        let now = default_timestamp();
        
        Self {
            id: None,
            blockchain: Blockchain::SOL,
            native_symbol: "SOL".to_string(),
            price_usd: 0.0,
            last_updated: now,
            created_at: now,
        }
    }
}
