use reqwest::Client;
use serde_json::{json, Value};
use std::fmt;
use std::str::FromStr;
use solana_sdk::{
    signature::Signature,
    transaction::VersionedTransaction,
};
use thiserror::Error;
use rand::seq::SliceRandom;
use base64;

// Error type for Jito operations
#[derive(Error, Debug)]
pub enum JitoServiceError {
    #[error("Jito API error: {0}")]
    ApiError(String),

    #[error("Serialization error: {0}")]
    SerializationError(String),

    #[error("Network error: {0}")]
    NetworkError(String),

    #[error("Timeout error")]
    TimeoutError,

    #[error("Transaction error: {0}")]
    TransactionError(String),
}

// Pretty formatter for JSON values
#[derive(Debug)]
pub struct PrettyJsonValue(pub Value);

impl fmt::Display for PrettyJsonValue {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "{}", serde_json::to_string_pretty(&self.0).unwrap_or_default())
    }
}

impl From<Value> for PrettyJsonValue {
    fn from(value: Value) -> Self {
        PrettyJsonValue(value)
    }
}

// Jito service for sending transactions and bundles
pub struct JitoService {
    base_url: String,
    api_key: Option<String>,
    client: Client,
}

impl JitoService {
    // Create a new JitoService
    pub fn new(base_url: &str, api_key: Option<String>) -> Self {
        Self {
            base_url: base_url.to_string(),
            api_key,
            client: Client::new(),
        }
    }

    // Send a JSON-RPC request to the Jito API
    async fn send_request(&self, endpoint: &str, method: &str, params: Option<Value>) -> Result<Value, JitoServiceError> {
        let url = format!("{}{}", self.base_url, endpoint);

        let data = json!({
            "jsonrpc": "2.0",
            "id": 1,
            "method": method,
            "params": params.unwrap_or(json!([]))
        });

        println!("Sending request to Jito: {}", url);
        println!("Request body: {}", serde_json::to_string_pretty(&data).unwrap_or_default());

        let response = self.client
            .post(&url)
            .header("Content-Type", "application/json")
            .json(&data)
            .send()
            .await
            .map_err(|e| JitoServiceError::NetworkError(e.to_string()))?;

        let status = response.status();
        println!("Jito response status: {}", status);

        let body = response.json::<Value>().await
            .map_err(|e| JitoServiceError::SerializationError(e.to_string()))?;

        println!("Jito response body: {}", serde_json::to_string_pretty(&body).unwrap_or_default());

        // Check for errors in the response
        if let Some(error) = body.get("error") {
            // Create a longer-lived error string
            let error_string = error.to_string();

            let error_message = if let Some(message) = error.get("message").and_then(|m| m.as_str()) {
                if message.contains("0x1") || message.contains("insufficient funds") {
                    "Insufficient funds to execute this transaction. Please try a smaller amount."
                } else if message.contains("custom program error") {
                    "Transaction failed due to a program error. The token may have trading restrictions."
                } else {
                    message
                }
            } else {
                &error_string
            };

            return Err(JitoServiceError::ApiError(error_message.to_string()));
        }

        Ok(body)
    }

    // Get available tip accounts
    pub async fn get_tip_accounts(&self) -> Result<Value, JitoServiceError> {
        let endpoint = if let Some(api_key) = &self.api_key {
            format!("/bundles?uuid={}", api_key)
        } else {
            "/bundles".to_string()
        };

        self.send_request(&endpoint, "getTipAccounts", None).await
    }

    // Get a random tip account
    pub async fn get_random_tip_account(&self) -> Result<String, JitoServiceError> {
        let tip_accounts_response = self.get_tip_accounts().await?;

        let tip_accounts = tip_accounts_response["result"]
            .as_array()
            .ok_or_else(|| JitoServiceError::SerializationError("Failed to parse tip accounts as array".to_string()))?;

        if tip_accounts.is_empty() {
            return Err(JitoServiceError::ApiError("No tip accounts available".to_string()));
        }

        let random_account = tip_accounts
            .choose(&mut rand::thread_rng())
            .ok_or_else(|| JitoServiceError::SerializationError("Failed to choose random tip account".to_string()))?;

        random_account
            .as_str()
            .ok_or_else(|| JitoServiceError::SerializationError("Failed to parse tip account as string".to_string()))
            .map(String::from)
    }

    // Send a bundle of transactions
    pub async fn send_bundle(&self, transactions: Vec<VersionedTransaction>) -> Result<Signature, JitoServiceError> {
        if transactions.is_empty() {
            return Err(JitoServiceError::TransactionError("Bundle must contain at least one transaction".to_string()));
        }

        if transactions.len() > 5 {
            return Err(JitoServiceError::TransactionError("Bundle can contain at most 5 transactions".to_string()));
        }

        // Serialize transactions to base64
        let serialized_txs: Vec<String> = transactions.iter()
            .map(|tx| {
                bincode::serialize(tx)
                    .map_err(|e| JitoServiceError::SerializationError(format!("Failed to serialize transaction: {}", e)))
                    .map(|bytes| base64::encode(bytes))
            })
            .collect::<Result<Vec<String>, JitoServiceError>>()?;

        // Create bundle parameters
        let params = json!([
            serialized_txs,
            {
                "encoding": "base64"
            }
        ]);

        // Determine endpoint with API key if available
        let endpoint = if let Some(api_key) = &self.api_key {
            format!("/bundles?uuid={}", api_key)
        } else {
            "/bundles".to_string()
        };

        // Send the bundle
        let response = self.send_request(&endpoint, "sendBundle", Some(params)).await?;

        // Extract bundle UUID from response
        let bundle_uuid = response["result"]
            .as_str()
            .ok_or_else(|| JitoServiceError::SerializationError("Failed to get bundle UUID from response".to_string()))?;

        println!("Bundle sent with UUID: {}", bundle_uuid);

        // Return the signature of the first transaction as the bundle signature
        Ok(transactions[0].signatures[0])
    }

    // Send a single transaction with Jito tip
    pub async fn send_transaction(&self, transaction: &VersionedTransaction) -> Result<Signature, JitoServiceError> {
        // Get a random tip account from Jito
        let tip_account = match self.get_random_tip_account().await {
            Ok(account) => account,
            Err(e) => {
                println!("Failed to get Jito tip account: {}, proceeding without tip", e);
                // Continue without a tip if we can't get a tip account
                // Serialize transaction to base64
                let serialized_tx = bincode::serialize(transaction)
                    .map_err(|e| JitoServiceError::SerializationError(format!("Failed to serialize transaction: {}", e)))
                    .map(|bytes| base64::encode(bytes))?;

                // Create transaction parameters
                let params = json!([
                    serialized_tx,
                    {
                        "encoding": "base64",
                        "skipPreflight": false
                    }
                ]);

                // Send the transaction
                let response = self.send_request("/transactions", "sendTransaction", Some(params)).await?;

                // Extract signature from response
                let signature_str = response["result"]
                    .as_str()
                    .ok_or_else(|| JitoServiceError::SerializationError("Failed to get signature from response".to_string()))?;

                // Parse signature
                let signature = Signature::from_str(signature_str)
                    .map_err(|e| JitoServiceError::SerializationError(format!("Failed to parse signature: {}", e)))?;

                return Ok(signature);
            }
        };

        println!("Using Jito tip account: {}", tip_account);

        // Serialize transaction to base64
        let serialized_tx = bincode::serialize(transaction)
            .map_err(|e| JitoServiceError::SerializationError(format!("Failed to serialize transaction: {}", e)))
            .map(|bytes| base64::encode(bytes))?;

        // Create transaction parameters with tip account
        let params = json!([
            serialized_tx,
            {
                "encoding": "base64",
                "skipPreflight": false,
                "jitoParams": {
                    "tipAccount": tip_account
                }
            }
        ]);

        // Send the transaction
        let response = self.send_request("/transactions", "sendTransaction", Some(params)).await?;

        // Extract signature from response
        let signature_str = response["result"]
            .as_str()
            .ok_or_else(|| JitoServiceError::SerializationError("Failed to get signature from response".to_string()))?;

        // Parse signature
        let signature = Signature::from_str(signature_str)
            .map_err(|e| JitoServiceError::SerializationError(format!("Failed to parse signature: {}", e)))?;

        Ok(signature)
    }
}
