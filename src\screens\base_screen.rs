use teloxide::types::{Inline<PERSON>eyboardButton, InlineKeyboardMarkup};
use teloxide::prelude::Requester;
use teloxide::payloads::EditMessageTextSetters;
use crate::model::{UserData, BotError, Blockchain};
use crate::service::BotService;
use crate::screens::bsc_screen::{create_dashboard_keyboard, format_config, format_token_amount_with_decimals};
use std::fmt::Write;

pub async fn show_base_dashboard(
    bot_service: &BotService,
    user_data: &UserData,
    message_id: i32,
) -> Result<(), BotError> {
    let (chat_id, wallet_address, initial_dashboard_text, keyboard, config_text) = {
        let wallet_address = &user_data.get_wallet(&Blockchain::BASE).address;
        let chat_id = user_data.chat_id();

        const DASHBOARD_HEADER: &str = "📊 <b>Base Dashboard</b>\n\n";
        const BLOCKCHAIN_INFO: &str = "🔗 <b>Blockchain:</b> Base (Coinbase L2)\n\n";
        const LOADING_BALANCE: &str = "💰 <b>Balance:</b> <code>Loading...</code>\n\n";
        const NETWORK_STATUS: &str = "📈 <b>Network Status:</b> Active\n\n";

        let mut text = String::with_capacity(512);
        text.push_str(DASHBOARD_HEADER);

        let _ = write!(text, "👤 <b>User:</b> {}\n\n", user_data.display_name());
        let _ = write!(text, "📍 <b>Address:</b> <code>{}</code>\n\n", wallet_address);

        text.push_str(BLOCKCHAIN_INFO);
        text.push_str(LOADING_BALANCE);
        text.push_str(NETWORK_STATUS);

        let config = format_config(user_data, Blockchain::BASE);
        let _ = write!(text, "⚙️ <b>Settings:</b> {}", config);

        let kb = create_dashboard_keyboard(Blockchain::BASE);

        (chat_id, wallet_address.clone(), text, kb, config)
    };

    bot_service.edit_message_with_keyboard(
        chat_id,
        message_id,
        &initial_dashboard_text,
        keyboard.clone(),
    ).await?;


    let background_data = Box::new((
        bot_service.clone(),
        wallet_address,
        user_data.display_name().to_string(),
        config_text,
        chat_id,
        message_id,
        keyboard,
    ));


    tokio::spawn(async move {
        let (
            ref bot_service_clone,
            ref wallet_address,
            ref display_name,
            ref config_text,
            chat_id,
            message_id,
            ref keyboard
        ) = *background_data;


        const DASHBOARD_HEADER: &str = "📊 <b>Base Dashboard</b>\n\n";
        const BLOCKCHAIN_INFO: &str = "🔗 <b>Blockchain:</b> Base (Coinbase L2)\n\n";
        const NETWORK_STATUS: &str = "📈 <b>Network Status:</b> Active\n\n";

        let blockchain_service = bot_service_clone.blockchain_service();
        let _token_info_service = bot_service_clone.token_info_service();

        // Get balance with timeout to prevent hanging and ensure buttons remain clickable
        let balance = match tokio::time::timeout(
            std::time::Duration::from_secs(10),
            blockchain_service.get_balance_by_address(&wallet_address, Blockchain::BASE)
        ).await {
            Ok(Ok(balance)) => balance,
            Ok(Err(_)) => 0.0,
            Err(_) => 0.0, // Timeout - don't block the UI
        };

        let dashboard_text = {
            let mut text = String::with_capacity(512);

            text.push_str(DASHBOARD_HEADER);
            let _ = write!(text, "👤 <b>User:</b> {}\n\n", display_name);
            let _ = write!(text, "📍 <b>Address:</b> <code>{}</code>\n", wallet_address);

            let explorer_url = crate::constants::explorer_urls::get_explorer_address_url("base", &wallet_address);
            let _ = write!(text, "🔍 <a href=\"{}\">View on BaseScan</a>\n\n", explorer_url);

            text.push_str(BLOCKCHAIN_INFO);
            let _ = write!(text, "💰 <b>Balance:</b> <code>{:.6} ETH</code>\n\n", balance);
            text.push_str(NETWORK_STATUS);
            let _ = write!(text, "⚙️ <b>Settings:</b> {}", config_text);

            text
        };

        let _ = bot_service_clone.edit_message_with_keyboard(
            chat_id,
            message_id,
            &dashboard_text,
            keyboard.clone(),
        ).await;

        drop(dashboard_text);
    });

    Ok(())
}

pub async fn execute_buy_token(
    bot_service: &BotService,
    user_data: &UserData,
    message_id: i32,
    token_address: &str,
    amount: f64,
) -> Result<(), BotError> {
    let chat_id = user_data.chat_id();

    let mut loading_text = String::with_capacity(256);
    loading_text.push_str("⏳ <b>Processing Buy Order...</b>\n\n");
    loading_text.push_str(&format!("Token: <code>{}</code>\n", token_address));
    loading_text.push_str(&format!("Amount: <b>{} ETH</b>\n\n", amount));
    loading_text.push_str("Please wait while your transaction is being processed...");

    bot_service.edit_message(
        chat_id,
        message_id,
        &loading_text,
    ).await?;

    let bot_clone = bot_service.bot().clone();
    let chat_id_clone = chat_id;
    let message_id_clone = message_id;
    let token_address_clone = token_address.to_string();
    let amount_clone = amount;



    let wallet = user_data.get_wallet(&Blockchain::BASE);

    let amount_str = {
        let eth_decimals = 18;
        let decimal_multiplier = 10_u128.pow(eth_decimals);
        let amount_in_wei = (amount * decimal_multiplier as f64) as u128;
        amount_in_wei.to_string()
    };

    let background_data = Box::new((
        bot_service.clone(),
        wallet.clone(),
        token_address.to_string(),
        amount,
        amount_str,
        chat_id,
        message_id,
    ));

    tokio::spawn(async move {
        let (
            ref bot_service_clone,
            ref wallet_clone,
            ref token_address_clone,
            amount,
            ref amount_str,
            chat_id,
            message_id_clone,
        ) = *background_data;

        let token_decimals = {
            let result = bot_service_clone.blockchain_service()
                .get_token_decimals(&token_address_clone, &Blockchain::BASE)
                .await;

            result.unwrap_or(18)
        };
        let quote_result = {
            bot_service_clone.evm_trader_service()
                .get_swap_quote(
                    "ETH",
                    &token_address_clone,
                    &amount_str,
                    &wallet_clone.address,
                    &Blockchain::BASE
                )
                .await
        };

        match quote_result {
            Ok(quote) => {
                let quote_clone = quote.clone();
                let tx_result = {
                    // 🚀 AUTO-TRIGGER: Use high-performance EVM trader with fallback
                    bot_service_clone.execute_high_performance_evm_buy(&wallet_clone, quote_clone, &Blockchain::BASE, Some(chat_id))
                        .await
                };

                match tx_result {
                    Ok(tx_hash) => {
                        // Calculate tokens received from quote
                        let tokens_received = format_token_amount_with_decimals(&quote.buyAmount, token_decimals);

                        let success_text = format!(
                            "✅ <b>Buy Order Successful!</b>\n\n\
                            Amount Spent: <b>{} ETH</b>\n\
                            Tokens Received: <b>{}</b>\n\
                            Contract: <code>{}</code>\n\n\
                            <a href=\"{}\">View Transaction on BaseScan</a>",
                            amount,
                            tokens_received,
                            token_address_clone,
                            crate::constants::explorer_urls::get_explorer_tx_url("base", &tx_hash)
                        );

                        // Store token address in user state for shorter callback data
                        {
                            let mut user_states = crate::screens::scan_screen::get_user_states().write().await;
                            user_states.insert(chat_id, format!("last_token:{}:{}", Blockchain::BASE.as_str(), token_address_clone));
                        }

                        let success_keyboard = InlineKeyboardMarkup::new(vec![
                            vec![
                                InlineKeyboardButton::callback(
                                    "◀️ Back to Token",
                                    "back_to_last_token"
                                ),
                            ],
                            vec![
                                InlineKeyboardButton::callback("🏠 Dashboard", "{\"command\":\"new_msg_view_base\"}"),
                                InlineKeyboardButton::callback("❌ Dismiss", "dismiss_message"),
                            ]
                        ]);

                        let _ = bot_service_clone.edit_message_with_keyboard(
                            chat_id,
                            message_id_clone,
                            &success_text,
                            success_keyboard,
                        ).await;
                    },
                    Err(e) => {
                        {
                            let mut error_text = String::with_capacity(256);
                            error_text.push_str("❌ <b>Error executing swap</b>\n\n");
                            error_text.push_str(&format!("Token: <code>{}</code>\n", token_address_clone));
                            error_text.push_str(&format!("Amount: {} ETH\n\n", amount));
                            error_text.push_str("The transaction could not be completed. Please try again later.");

                            println!("Swap execution error: {}", e);
                            let error_keyboard = InlineKeyboardMarkup::new(vec![
                                vec![
                                    InlineKeyboardButton::callback(
                                        "🔄 Reload Token",
                                        format!("refresh_token:{}:{}", Blockchain::BASE.as_str(), token_address_clone),
                                    )
                                ],
                                vec![
                                    InlineKeyboardButton::callback(
                                        "🏠 BASE Dashboard",
                                        "{\"command\":\"view_base\"}",
                                    ),
                                    InlineKeyboardButton::callback(
                                        "❌ Dismiss",
                                        "dismiss_message",
                                    )
                                ]
                            ]);

                            let _ = bot_service_clone.edit_message_with_keyboard(
                                chat_id,
                                message_id_clone,
                                &error_text,
                                error_keyboard,
                            ).await;
                        }
                    }
                }
            },
            Err(e) => {
                {
                    let mut error_text = String::with_capacity(256);
                    error_text.push_str("❌ <b>Error getting swap quote</b>\n\n");
                    error_text.push_str(&format!("Token: <code>{}</code>\n", token_address_clone));
                    error_text.push_str(&format!("Amount: {} ETH\n\n", amount));
                    error_text.push_str("Unable to get a quote for this token. It may have low liquidity or trading may be restricted.");

                    println!("Swap quote error: {}", e);
                    let error_keyboard = InlineKeyboardMarkup::new(vec![
                        vec![
                            InlineKeyboardButton::callback(
                                "🔄 Reload Token",
                                format!("refresh_token:{}:{}", Blockchain::BASE.as_str(), token_address_clone),
                            )
                        ],
                        vec![
                            InlineKeyboardButton::callback(
                                "🏠 BASE Dashboard",
                                "{\"command\":\"view_base\"}",
                            ),
                            InlineKeyboardButton::callback(
                                "❌ Dismiss",
                                "dismiss_message",
                            )
                        ]
                    ]);

                    let _ = bot_service_clone.edit_message_with_keyboard(
                        chat_id,
                        message_id_clone,
                        &error_text,
                        error_keyboard,
                    ).await;
                }
            }
        }
    });

    Ok(())
}

pub async fn execute_sell_token(
    bot_service: &BotService,
    user_data: &UserData,
    message_id: i32,
    token_address: &str,
    percentage: f64,
) -> Result<(), BotError> {
    let chat_id = user_data.chat_id();
    let mut loading_text = String::with_capacity(256);
    loading_text.push_str("⏳ <b>Processing Sell Order...</b>\n\n");
    loading_text.push_str(&format!("Token: <code>{}</code>\n", token_address));
    loading_text.push_str(&format!("Amount: <b>{:.1}%</b>\n\n", percentage * 100.0));
    loading_text.push_str("Please wait while your transaction is being processed...");

    bot_service.edit_message(
        chat_id,
        message_id,
        &loading_text,
    ).await?;
    let bot_clone = bot_service.bot().clone();
    let chat_id_clone = chat_id;
    let message_id_clone = message_id;
    let token_address_clone = token_address.to_string();
    let percentage_clone = percentage;



    let wallet = user_data.get_wallet(&Blockchain::BASE);
    let background_data = Box::new((
        bot_service.clone(),
        wallet.clone(),
        token_address.to_string(),
        percentage,
        chat_id,
        message_id,
    ));

    tokio::spawn(async move {
        let (
            ref bot_service_clone,
            ref wallet_clone,
            ref token_address_clone,
            percentage,
            chat_id,
            message_id_clone,
        ) = *background_data;
        let balance_result = {
            bot_service_clone.blockchain_service()
                .get_token_balance(&wallet_clone.address, &token_address_clone, &Blockchain::BASE)
                .await
        };

        match balance_result {
            Ok((balance, decimals)) => {
                // Apply a small buffer (0.99) when selling 100% to account for potential balance changes
                let adjusted_percentage = if (percentage * 100.0) as u64 == 100 {
                    0.99 // Use 99% instead of 100% to avoid "insufficient balance" errors
                } else {
                    percentage
                };

                let amount_to_sell = balance * adjusted_percentage;

                if amount_to_sell <= 0.0 {
                    {
                        let mut error_text = String::with_capacity(256);
                        error_text.push_str("❌ <b>No tokens to sell</b>\n\n");
                        error_text.push_str(&format!("Token: <code>{}</code>\n\n", token_address_clone));
                        error_text.push_str("You don't have any tokens to sell.");
                        let error_keyboard = InlineKeyboardMarkup::new(vec![
                            vec![
                                InlineKeyboardButton::callback(
                                    "🔄 Reload Token",
                                    format!("refresh_token:{}:{}", Blockchain::BASE.as_str(), token_address_clone),
                                )
                            ],
                            vec![
                                InlineKeyboardButton::callback(
                                    "🏠 BASE Dashboard",
                                    "{\"command\":\"view_base\"}",
                                ),
                                InlineKeyboardButton::callback(
                                    "❌ Dismiss",
                                    "dismiss_message",
                                )
                            ]
                        ]);

                        let _ = bot_service_clone.edit_message_with_keyboard(
                            chat_id,
                            message_id_clone,
                            &error_text,
                            error_keyboard,
                        ).await;
                    }
                    return;
                }


                let amount_str = {
                    let decimal_multiplier = 10_u128.pow(decimals as u32);
                    let amount_in_smallest = (amount_to_sell * decimal_multiplier as f64) as u128;
                    amount_in_smallest.to_string()
                };
                let quote_result = {
                    bot_service_clone.evm_trader_service()
                        .get_swap_quote(
                            &token_address_clone,
                            "ETH",
                            &amount_str,
                            &wallet_clone.address,
                            &Blockchain::BASE
                        )
                        .await
                };

                match quote_result {
                    Ok(quote) => {
                        let quote_clone = quote.clone();
                        let tx_result = {
                            bot_service_clone.evm_trader_service()
                                .execute_swap(&wallet_clone, quote_clone, &Blockchain::BASE, Some(chat_id))
                                .await
                        };

                        match tx_result {
                            Ok(tx_hash) => {
                                // Calculate ETH received from quote
                                let eth_received = format_token_amount_with_decimals(&quote.buyAmount, 18);

                                let success_text = format!(
                                    "✅ <b>Sell Order Successful!</b>\n\n\
                                    Token: <code>{}</code>\n\
                                    Amount Sold: <b>{:.1}%</b>\n\
                                    ETH Received: <b>{} ETH</b>\n\
                                    Contract: <code>{}</code>\n\n\
                                    <a href=\"{}\">View Transaction on BaseScan</a>",
                                    token_address_clone,
                                    percentage * 100.0,
                                    eth_received,
                                    token_address_clone,
                                    crate::constants::explorer_urls::get_explorer_tx_url("base", &tx_hash)
                                );

                                // Store token address in user state for shorter callback data
                                {
                                    let mut user_states = crate::screens::scan_screen::get_user_states().write().await;
                                    user_states.insert(chat_id, format!("last_token:{}:{}", Blockchain::BASE.as_str(), token_address_clone));
                                }

                                let success_keyboard = InlineKeyboardMarkup::new(vec![
                                    vec![
                                        InlineKeyboardButton::callback(
                                            "◀️ Back to Token",
                                            "back_to_last_token"
                                        ),
                                    ],
                                    vec![
                                        InlineKeyboardButton::callback("🏠 Dashboard", "{\"command\":\"new_msg_view_base\"}"),
                                        InlineKeyboardButton::callback("❌ Dismiss", "dismiss_message"),
                                    ]
                                ]);

                                let _ = bot_service_clone.edit_message_with_keyboard(
                                    chat_id,
                                    message_id_clone,
                                    &success_text,
                                    success_keyboard,
                                ).await;
                            },
                            Err(e) => {
                                {
                                    let mut error_text = String::with_capacity(256);
                                    error_text.push_str("❌ <b>Error executing swap</b>\n\n");
                                    error_text.push_str(&format!("Token: <code>{}</code>\n", token_address_clone));
                                    error_text.push_str(&format!("Amount: {} tokens ({}%)\n\n",
                                                               amount_to_sell,
                                                               (percentage * 100.0) as u64));
                                    error_text.push_str("The transaction could not be completed. Please try again later.");

                                    println!("Swap execution error: {}", e);
                                    let error_keyboard = InlineKeyboardMarkup::new(vec![
                                        vec![
                                            InlineKeyboardButton::callback(
                                                "🔄 Reload Token",
                                                format!("refresh_token:{}:{}", Blockchain::BASE.as_str(), token_address_clone),
                                            )
                                        ],
                                        vec![
                                            InlineKeyboardButton::callback(
                                                "🏠 BASE Dashboard",
                                                "{\"command\":\"view_base\"}",
                                            ),
                                            InlineKeyboardButton::callback(
                                                "❌ Dismiss",
                                                "dismiss_message",
                                            )
                                        ]
                                    ]);

                                    let _ = bot_service_clone.edit_message_with_keyboard(
                                        chat_id,
                                        message_id_clone,
                                        &error_text,
                                        error_keyboard,
                                    ).await;
                                }
                            }
                        }
                    },
                    Err(e) => {
                        {
                            let mut error_text = String::with_capacity(256);
                            error_text.push_str("❌ <b>Error getting swap quote</b>\n\n");
                            error_text.push_str(&format!("Token: <code>{}</code>\n", token_address_clone));
                            error_text.push_str(&format!("Amount: {} tokens ({}%)\n\n",
                                                       amount_to_sell,
                                                       (percentage * 100.0) as u64));
                            error_text.push_str("Unable to get a quote for this token. It may have low liquidity or trading may be restricted.");

                            println!("Swap quote error: {}", e);
                            let error_keyboard = InlineKeyboardMarkup::new(vec![
                                vec![
                                    InlineKeyboardButton::callback(
                                        "🔄 Reload Token",
                                        format!("refresh_token:{}:{}", Blockchain::BASE.as_str(), token_address_clone),
                                    )
                                ],
                                vec![
                                    InlineKeyboardButton::callback(
                                        "🏠 BASE Dashboard",
                                        "{\"command\":\"view_base\"}",
                                    ),
                                    InlineKeyboardButton::callback(
                                        "❌ Dismiss",
                                        "dismiss_message",
                                    )
                                ]
                            ]);

                            let _ = bot_service_clone.edit_message_with_keyboard(
                                chat_id,
                                message_id_clone,
                                &error_text,
                                error_keyboard,
                            ).await;
                        }
                    }
                }
            },
            Err(e) => {
                {
                    let mut error_text = String::with_capacity(256);
                    error_text.push_str("❌ <b>Error getting token balance</b>\n\n");
                    error_text.push_str(&format!("Token: <code>{}</code>\n\n", token_address_clone));
                    error_text.push_str("Unable to retrieve token balance. Please try again later.");

                    println!("Token balance error: {}", e);
                    let error_keyboard = InlineKeyboardMarkup::new(vec![
                        vec![
                            InlineKeyboardButton::callback(
                                "🔄 Reload Token",
                                format!("refresh_token:{}:{}", Blockchain::BASE.as_str(), token_address_clone),
                            )
                        ],
                        vec![
                            InlineKeyboardButton::callback(
                                "🏠 BASE Dashboard",
                                "{\"command\":\"view_base\"}",
                            ),
                            InlineKeyboardButton::callback(
                                "❌ Dismiss",
                                "dismiss_message",
                            )
                        ]
                    ]);

                    let _ = bot_service_clone.edit_message_with_keyboard(
                        chat_id,
                        message_id_clone,
                        &error_text,
                        error_keyboard,
                    ).await;
                }
            }
        }
    });

    Ok(())
}
