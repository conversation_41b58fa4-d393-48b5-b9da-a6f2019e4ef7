pub const ENV_EXPLORER_URL_ETH: &str = "EXPLORER_URL_ETH";
pub const ENV_EXPLORER_URL_BSC: &str = "EXPLORER_URL_BSC";
pub const ENV_EXPLORER_URL_SOL: &str = "EXPLORER_URL_SOL";
pub const ENV_EXPLORER_URL_BASE: &str = "EXPLORER_URL_BASE";

pub const DEFAULT_EXPLORER_URL_ETH: &str = "https://etherscan.io";
pub const DEFAULT_EXPLORER_URL_BSC: &str = "https://bscscan.com";
pub const DEFAULT_EXPLORER_URL_SOL: &str = "https://solscan.io";
pub const DEFAULT_EXPLORER_URL_BASE: &str = "https://basescan.org";

pub fn get_explorer_url(blockchain_name: &str) -> String {
    match blockchain_name.to_lowercase().as_str() {
        "eth" => std::env::var(ENV_EXPLORER_URL_ETH)
            .unwrap_or_else(|_| DEFAULT_EXPLORER_URL_ETH.to_string()),
        "bsc" => std::env::var(ENV_EXPLORER_URL_BSC)
            .unwrap_or_else(|_| DEFAULT_EXPLORER_URL_BSC.to_string()),
        "sol" => std::env::var(ENV_EXPLORER_URL_SOL)
            .unwrap_or_else(|_| DEFAULT_EXPLORER_URL_SOL.to_string()),
        "base" => std::env::var(ENV_EXPLORER_URL_BASE)
            .unwrap_or_else(|_| DEFAULT_EXPLORER_URL_BASE.to_string()),
        _ => DEFAULT_EXPLORER_URL_ETH.to_string(),
    }
}

pub fn get_explorer_address_url(blockchain_name: &str, address: &str) -> String {
    let base_url = get_explorer_url(blockchain_name);
    format!("{}/address/{}", base_url, address)
}

pub fn get_explorer_token_url(blockchain_name: &str, token_address: &str) -> String {
    let base_url = get_explorer_url(blockchain_name);
    match blockchain_name.to_lowercase().as_str() {
        "sol" => format!("{}/token/{}", base_url, token_address),
        _ => format!("{}/token/{}", base_url, token_address),
    }
}

pub fn get_explorer_tx_url(blockchain_name: &str, tx_hash: &str) -> String {
    let base_url = get_explorer_url(blockchain_name);
    format!("{}/tx/{}", base_url, tx_hash)
}
