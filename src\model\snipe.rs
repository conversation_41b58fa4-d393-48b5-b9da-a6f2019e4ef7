use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};
use mongodb::bson::{oid::ObjectId, Decimal128};
use crate::model::blockchain::Blockchain;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum SnipeStatus {
    #[serde(rename = "pending")]
    Pending,
    #[serde(rename = "executed")]
    Executed,
    #[serde(rename = "completed")]
    Completed,
    #[serde(rename = "failed")]
    Failed,
    #[serde(rename = "cancelled")]
    Cancelled,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Snipe {
    #[serde(rename = "_id", skip_serializing_if = "Option::is_none")]
    pub id: Option<ObjectId>,
    pub user_id: ObjectId,
    pub blockchain: Blockchain,
    pub contract_address: String,
    pub token_name: Option<String>,
    pub amount: Decimal128,
    pub slippage: Decimal128,
    pub antirug: bool,
    pub anti_mev: bool,
    pub status: SnipeStatus,
    pub transaction_hash: Option<String>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

impl Snipe {
    pub fn new(
        user_id: i64,  // This is the chat_id
        blockchain: Blockchain,
        contract_address: String,
        amount: Decimal128,
        slippage: Decimal128,
        antirug: bool,
        anti_mev: bool,
    ) -> Self {
        let now = Utc::now();

        // Store the chat_id directly in the user_id field
        // This will be converted to an ObjectId when saving to the database
        // We'll use a string representation of the user_id to create an ObjectId
        let user_id_str = user_id.to_string();
        let user_id_bytes = user_id_str.as_bytes();
        let mut id_bytes = [0; 12];

        // Fill the id_bytes with the user_id bytes, padding with zeros if needed
        for i in 0..std::cmp::min(user_id_bytes.len(), 12) {
            id_bytes[i] = user_id_bytes[i];
        }

        let object_id = ObjectId::from_bytes(id_bytes);

        Self {
            id: None,
            user_id: object_id, // Store the chat_id as an ObjectId
            blockchain,
            contract_address,
            token_name: None,
            amount,
            slippage,
            antirug,
            anti_mev,
            status: SnipeStatus::Pending,
            transaction_hash: None,
            created_at: now,
            updated_at: now,
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UserSnipes {
    #[serde(rename = "_id", skip_serializing_if = "Option::is_none")]
    pub id: Option<ObjectId>,
    pub user_id: ObjectId,
    pub snipes: SnipeReferences,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SnipeReferences {
    #[serde(default)]
    pub bsc: Vec<ObjectId>,
    #[serde(default)]
    pub sol: Vec<ObjectId>,
    #[serde(default)]
    pub eth: Vec<ObjectId>,
    #[serde(default)]
    pub base: Vec<ObjectId>,
}

impl Default for SnipeReferences {
    fn default() -> Self {
        Self {
            bsc: Vec::new(),
            sol: Vec::new(),
            eth: Vec::new(),
            base: Vec::new(),
        }
    }
}

impl UserSnipes {
    pub fn new(user_id: ObjectId) -> Self {
        Self {
            id: None,
            user_id,
            snipes: SnipeReferences::default(),
        }
    }

    pub fn add_snipe(&mut self, blockchain: &Blockchain, snipe_id: ObjectId) {
        match blockchain {
            Blockchain::BSC => self.snipes.bsc.push(snipe_id),
            Blockchain::SOL => self.snipes.sol.push(snipe_id),
            Blockchain::ETH => self.snipes.eth.push(snipe_id),
            Blockchain::BASE => self.snipes.base.push(snipe_id),
        }
    }

    pub fn remove_snipe(&mut self, blockchain: &Blockchain, snipe_id: ObjectId) -> bool {
        let snipes = match blockchain {
            Blockchain::BSC => &mut self.snipes.bsc,
            Blockchain::SOL => &mut self.snipes.sol,
            Blockchain::ETH => &mut self.snipes.eth,
            Blockchain::BASE => &mut self.snipes.base,
        };

        if let Some(index) = snipes.iter().position(|id| *id == snipe_id) {
            snipes.remove(index);
            true
        } else {
            false
        }
    }

    pub fn get_snipes(&self, blockchain: &Blockchain) -> &Vec<ObjectId> {
        match blockchain {
            Blockchain::BSC => &self.snipes.bsc,
            Blockchain::SOL => &self.snipes.sol,
            Blockchain::ETH => &self.snipes.eth,
            Blockchain::BASE => &self.snipes.base,
        }
    }
}
