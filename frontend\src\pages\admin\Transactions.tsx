import { useState, useEffect, useCallback, useRef } from 'react';
import { Card } from '../../components/ui/Card';
import Button from '../../components/ui/Button';
import { adminApi, type BlockchainVolume } from '../../services/adminApi';
import {
  CurrencyDollarIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  CheckCircleIcon,
  XCircleIcon,
  ClockIcon,
  ArrowPathIcon,
  ChevronDownIcon,
  FunnelIcon,
  ArrowsRightLeftIcon,
  BanknotesIcon,
  ExclamationTriangleIcon,
  XMarkIcon,
  EyeIcon,
  DocumentDuplicateIcon,
} from '@heroicons/react/24/outline';
import { Dialog, Transition } from '@headlessui/react';
import { Fragment } from 'react';

// Local interfaces to avoid import issues
interface Transaction {
  id: string;
  user_id: string;
  username?: string;
  first_name?: string;
  last_name?: string;
  bot_type: string;
  transaction_type: string;
  direction: string; // "buy" or "sell"
  amount: number; // For buy: native token spent, For sell: tokens sold
  token_symbol: string;
  token_address: string;
  status: string;
  timestamp: number;
  blockchain: string;
  gas_fee?: number;
  success: boolean;
  hash?: string;
  block_number?: number;
  error_message?: string;
  // Enhanced amount tracking
  native_token_amount?: number; // Amount of native token (ETH, SOL, BNB)
  token_amount?: number; // Amount of the specific token
  native_token_symbol?: string; // ETH, SOL, BNB, etc.

  // Admin fee information
  admin_fee_amount?: number;
  admin_fee_percentage?: number;
  admin_fee_status?: string;
  admin_fee_collection_method?: string;
  admin_fee_token_symbol?: string;
  admin_fee_token_address?: string;
  admin_fee_transaction_id?: string;
}

interface TransactionListResponse {
  transactions: Transaction[];
  total: number;
  page: number;
  per_page: number;
}

export default function AdminTransactions() {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filter, setFilter] = useState('all');
  const [timeRange, setTimeRange] = useState('24h');
  const [selectedTransaction, setSelectedTransaction] = useState<string | null>(null);
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [totalTransactions, setTotalTransactions] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [perPage] = useState(20);
  const [showTransactionModal, setShowTransactionModal] = useState(false);
  const [selectedTransactionData, setSelectedTransactionData] = useState<Transaction | null>(null);
  const [blockchainVolumes, setBlockchainVolumes] = useState<BlockchainVolume[]>([]);
  const [feeStats, setFeeStats] = useState({
    totalFees: 0,
    todayFees: 0,
    weeklyFees: 0,
    monthlyFees: 0,
  });
  const loadingRef = useRef(false);

  const fetchTransactions = useCallback(async () => {
    if (loadingRef.current) {
      console.log('⏸️ Skipping fetch - already loading');
      return;
    }
    try {
      loadingRef.current = true;
      setLoading(true);
      setError(null);

      const params: any = {
        page: currentPage,
        per_page: perPage,
      };

      if (filter !== 'all') {
        params.status = filter;
      }

      // Add time range filter
      if (timeRange !== 'all') {
        const now = new Date();
        let startDate: Date;

        switch (timeRange) {
          case '24h':
            startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000);
            break;
          case '7d':
            startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
            break;
          case '30d':
            startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
            break;
          default:
            startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000);
        }

        params.start_date = Math.floor(startDate.getTime() / 1000).toString();
        params.end_date = Math.floor(now.getTime() / 1000).toString();
      }

      const response: TransactionListResponse = await adminApi.getTransactions(params);
      setTransactions(response.transactions);
      setTotalTransactions(response.total);
      setBlockchainVolumes(response.blockchain_volumes || []);

      // Calculate fee statistics from the transactions
      calculateFeeStats(response.transactions);

    } catch (err: any) {
      console.error('Failed to fetch transactions:', err);
      setError(err.response?.data?.error || 'Failed to load transactions');
    } finally {
      loadingRef.current = false;
      setLoading(false);
    }
  }, [currentPage, perPage, filter, timeRange]);

  const calculateFeeStats = (transactionList: Transaction[]) => {
    const now = new Date();
    const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);
    const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    const oneMonthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);

    let totalFees = 0;
    let todayFees = 0;
    let weeklyFees = 0;
    let monthlyFees = 0;

    transactionList.forEach(transaction => {
      const txDate = new Date(transaction.timestamp * 1000);
      const fee = transaction.gas_fee || 0;

      totalFees += fee;

      if (txDate >= oneDayAgo) {
        todayFees += fee;
      }
      if (txDate >= oneWeekAgo) {
        weeklyFees += fee;
      }
      if (txDate >= oneMonthAgo) {
        monthlyFees += fee;
      }
    });

    setFeeStats({
      totalFees,
      todayFees,
      weeklyFees,
      monthlyFees,
    });
  };

  useEffect(() => {
    fetchTransactions();
  }, [fetchTransactions]);

  function classNames(...classes: string[]) {
    return classes.filter(Boolean).join(' ');
  }

  const formatDate = (timestamp: number) => {
    const date = new Date(timestamp * 1000);
    return date.toLocaleString();
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 6,
    }).format(amount);
  };

  const formatBlockchainAmount = (amount: number, blockchain: string, tokenSymbol: string) => {
    // Handle different blockchain decimals
    let decimals = 18; // Default for ETH, BSC, BASE
    if (blockchain === 'SOL') {
      decimals = 9; // SOL has 9 decimals
    }

    // Convert from raw blockchain amount to human readable
    const humanAmount = amount / Math.pow(10, decimals);

    // Format based on amount size
    if (humanAmount < 0.000001) {
      return `${humanAmount.toFixed(8)} ${tokenSymbol}`;
    } else if (humanAmount < 0.001) {
      return `${humanAmount.toFixed(6)} ${tokenSymbol}`;
    } else if (humanAmount < 1.0) {
      return `${humanAmount.toFixed(4)} ${tokenSymbol}`;
    } else if (humanAmount < 1000.0) {
      return `${humanAmount.toFixed(2)} ${tokenSymbol}`;
    } else {
      return `${humanAmount.toFixed(1)} ${tokenSymbol}`;
    }
  };

  const getNativeTokenSymbol = (blockchain: string) => {
    switch (blockchain.toUpperCase()) {
      case 'ETH':
        return 'ETH';
      case 'BSC':
        return 'BNB';
      case 'BASE':
        return 'ETH';
      case 'SOL':
        return 'SOL';
      default:
        return blockchain;
    }
  };

  const getTransactionDirection = (transaction: Transaction) => {
    // Fallback to transaction_type if direction is not available
    if (transaction.direction) {
      return transaction.direction.toLowerCase();
    }
    return transaction.transaction_type.toLowerCase();
  };

  const getExplorerUrl = (blockchain: string, hash: string) => {
    switch (blockchain.toUpperCase()) {
      case 'ETH':
        return `https://etherscan.io/tx/${hash}`;
      case 'BSC':
        return `https://bscscan.com/tx/${hash}`;
      case 'BASE':
        return `https://basescan.org/tx/${hash}`;
      case 'SOL':
        return `https://solscan.io/tx/${hash}`;
      default:
        return `https://etherscan.io/tx/${hash}`;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status.toLowerCase()) {
      case 'completed':
      case 'success':
        return <CheckCircleIcon className="h-5 w-5 text-green-400" />;
      case 'pending':
        return <ClockIcon className="h-5 w-5 text-yellow-400" />;
      case 'failed':
      case 'error':
        return <XCircleIcon className="h-5 w-5 text-red-400" />;
      default:
        return <ArrowPathIcon className="h-5 w-5 text-gray-400" />;
    }
  };

  const getStatusClass = (status: string) => {
    switch (status.toLowerCase()) {
      case 'completed':
      case 'success':
        return 'bg-green-900/30 text-green-400 border border-green-500/30';
      case 'pending':
        return 'bg-yellow-900/30 text-yellow-400 border border-yellow-500/30';
      case 'failed':
      case 'error':
        return 'bg-red-900/30 text-red-400 border border-red-500/30';
      default:
        return 'bg-gray-900/30 text-gray-400 border border-gray-500/30';
    }
  };

  const getTransactionStats = () => {
    const completed = transactions.filter(tx => tx.success || tx.status.toLowerCase() === 'completed').length;
    const pending = transactions.filter(tx => tx.status.toLowerCase() === 'pending').length;
    const failed = transactions.filter(tx => !tx.success && tx.status.toLowerCase() === 'failed').length;

    return { completed, pending, failed };
  };

  const handleViewTransaction = (transaction: Transaction) => {
    setSelectedTransactionData(transaction);
    setShowTransactionModal(true);
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  // Format decimal amounts for admin frontend display (readable format, not scientific notation)
  const formatDecimalAmount = (amount: number): string => {
    return amount.toFixed(10).replace(/\.?0+$/, '');
  };

  // Verify admin fee calculation
  const verifyAdminFeeCalculation = (transaction: Transaction) => {
    if (!transaction.admin_fee_amount || !transaction.admin_fee_percentage) {
      return { isCorrect: false, message: 'Missing fee data' };
    }

    const direction = getTransactionDirection(transaction);
    let baseAmount = 0;

    if (direction === 'buy') {
      // For buy: fee should be calculated on native token amount spent
      baseAmount = transaction.native_token_amount || transaction.amount;
    } else {
      // For sell: fee should be calculated on native token amount received
      baseAmount = transaction.native_token_amount || 0;
    }

    const expectedFee = (baseAmount * transaction.admin_fee_percentage) / 100;
    const actualFee = transaction.admin_fee_amount;
    const tolerance = 1e-10; // Very small tolerance for floating point comparison

    const isCorrect = Math.abs(expectedFee - actualFee) < tolerance;

    return {
      isCorrect,
      expectedFee,
      actualFee,
      baseAmount,
      percentage: transaction.admin_fee_percentage,
      message: isCorrect ? 'Calculation correct' : `Expected: ${formatDecimalAmount(expectedFee)}, Actual: ${formatDecimalAmount(actualFee)}`
    };
  };

  const handleCollectAdminFee = async (transactionId: string) => {
    try {
      setLoading(true);

      // Call the admin fee collection API
      const response = await adminApi.collectAdminFee(transactionId);

      if (response.success) {
        // Refresh transactions to show updated status
        await fetchTransactions();

        // Show success message
        alert('Admin fee collection initiated successfully!');
      } else {
        alert(`Failed to collect admin fee: ${response.message || 'Unknown error'}`);
      }
    } catch (error) {
      console.error('Error collecting admin fee:', error);
      alert('Failed to collect admin fee. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Intelligent transaction details calculation
  const getTransactionDetails = (transaction: Transaction) => {
    const direction = getTransactionDirection(transaction);
    const nativeSymbol = transaction.native_token_symbol || getNativeTokenSymbol(transaction.blockchain);

    if (direction === 'buy') {
      // For buy: user spent native tokens to get project tokens
      const nativeSpent = transaction.native_token_amount || transaction.amount;
      const tokensReceived = transaction.token_amount || 0;
      const pricePerToken = tokensReceived > 0 ? nativeSpent / tokensReceived : 0;

      return {
        primaryAmount: formatBlockchainAmount(nativeSpent, transaction.blockchain, nativeSymbol),
        primaryLabel: `Spent ${nativeSymbol}`,
        secondaryAmount: tokensReceived > 0 ? formatBlockchainAmount(tokensReceived, transaction.blockchain, transaction.token_symbol) : 'N/A',
        secondaryLabel: `Received ${transaction.token_symbol}`,
        pricePerToken: pricePerToken > 0 ? `${formatBlockchainAmount(pricePerToken, transaction.blockchain, nativeSymbol)} per ${transaction.token_symbol}` : 'N/A'
      };
    } else {
      // For sell: user sold project tokens to get native tokens
      const tokensSold = transaction.token_amount || transaction.amount;
      const nativeReceived = transaction.native_token_amount || 0;
      const pricePerToken = tokensSold > 0 ? nativeReceived / tokensSold : 0;

      return {
        primaryAmount: formatBlockchainAmount(tokensSold, transaction.blockchain, transaction.token_symbol),
        primaryLabel: `Sold ${transaction.token_symbol}`,
        secondaryAmount: nativeReceived > 0 ? formatBlockchainAmount(nativeReceived, transaction.blockchain, nativeSymbol) : 'N/A',
        secondaryLabel: `Received ${nativeSymbol}`,
        pricePerToken: pricePerToken > 0 ? `${formatBlockchainAmount(pricePerToken, transaction.blockchain, nativeSymbol)} per ${transaction.token_symbol}` : 'N/A'
      };
    }
  };

  // Calculate admin fees intelligently
  const getAdminFeeDetails = (transaction: Transaction) => {
    const direction = getTransactionDirection(transaction);
    const nativeSymbol = transaction.native_token_symbol || getNativeTokenSymbol(transaction.blockchain);

    // Use actual admin fee data if available, otherwise calculate
    if (transaction.admin_fee_amount && transaction.admin_fee_amount > 0) {
      const feeSymbol = transaction.admin_fee_token_symbol || nativeSymbol;
      return {
        amount: transaction.admin_fee_amount,
        display: formatBlockchainAmount(transaction.admin_fee_amount, transaction.blockchain, feeSymbol),
        method: transaction.admin_fee_collection_method || 'smart_collection',
        status: transaction.admin_fee_status || 'completed',
        percentage: transaction.admin_fee_percentage || 2.5,
        tokenSymbol: feeSymbol
      };
    }

    // Fallback calculation if no admin fee data
    const defaultFeePercentage = transaction.admin_fee_percentage || 2.5;
    let adminFeeAmount = 0;
    let adminFeeDisplay = 'N/A';
    let feeCalculationMethod = 'smart_collection';

    if (direction === 'buy') {
      // Admin fee on buy: percentage of native tokens spent
      const nativeSpent = transaction.native_token_amount || transaction.amount;
      adminFeeAmount = nativeSpent * (defaultFeePercentage / 100);
      adminFeeDisplay = formatBlockchainAmount(adminFeeAmount, transaction.blockchain, nativeSymbol);
      feeCalculationMethod = `${defaultFeePercentage}% of ${nativeSymbol} spent`;
    } else {
      // Admin fee on sell: percentage of native tokens received
      const nativeReceived = transaction.native_token_amount || 0;
      if (nativeReceived > 0) {
        adminFeeAmount = nativeReceived * (defaultFeePercentage / 100);
        adminFeeDisplay = formatBlockchainAmount(adminFeeAmount, transaction.blockchain, nativeSymbol);
        feeCalculationMethod = `${defaultFeePercentage}% of ${nativeSymbol} received`;
      }
    }

    return {
      amount: adminFeeAmount,
      display: adminFeeDisplay,
      method: feeCalculationMethod,
      status: transaction.admin_fee_status || 'pending',
      percentage: defaultFeePercentage,
      tokenSymbol: nativeSymbol
    };
  };

  // Get transaction fees (gas fees)
  const getTransactionFees = (transaction: Transaction) => {
    const nativeSymbol = transaction.native_token_symbol || getNativeTokenSymbol(transaction.blockchain);
    const gasFee = transaction.gas_fee || 0;

    return {
      gasFee: gasFee > 0 ? formatBlockchainAmount(gasFee, transaction.blockchain, nativeSymbol) : 'N/A',
      nativeSymbol
    };
  };

  const stats = getTransactionStats();

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-8">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h1 className="text-2xl font-bold text-white">Transactions</h1>
            <p className="mt-1 text-sm text-gray-400">Monitor bot trading transactions and fees</p>
          </div>
        </div>

        <Card className="p-6">
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <ExclamationTriangleIcon className="h-12 w-12 text-red-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-white mb-2">Failed to Load Transactions</h3>
              <p className="text-gray-400 mb-4">{error}</p>
              <Button onClick={fetchTransactions} variant="glass">
                Try Again
              </Button>
            </div>
          </div>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-2xl font-bold text-white">Transactions</h1>
          <p className="mt-1 text-sm text-gray-400">Monitor bot trading transactions and fees</p>
        </div>
        <div className="flex space-x-2">
          <Button
            variant={timeRange === '24h' ? 'glass' : 'outline'}
            size="sm"
            onClick={() => setTimeRange('24h')}
          >
            24h
          </Button>
          <Button
            variant={timeRange === '7d' ? 'glass' : 'outline'}
            size="sm"
            onClick={() => setTimeRange('7d')}
          >
            7d
          </Button>
          <Button
            variant={timeRange === '30d' ? 'glass' : 'outline'}
            size="sm"
            onClick={() => setTimeRange('30d')}
          >
            30d
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6">
        <Card className="p-4 md:p-6 relative overflow-hidden">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="h-10 w-10 md:h-12 md:w-12 rounded-full bg-indigo-800/30 flex items-center justify-center">
                <ArrowsRightLeftIcon className="h-5 w-5 md:h-6 md:w-6 text-indigo-400" aria-hidden="true" />
              </div>
            </div>
            <div className="ml-3 md:ml-5 w-0 flex-1">
              <dl>
                <dt className="text-xs md:text-sm font-medium text-gray-300 truncate">Total Transactions</dt>
                <dd>
                  <div className="text-base md:text-lg font-semibold text-white">{totalTransactions.toLocaleString()}</div>
                </dd>
              </dl>
            </div>
          </div>
        </Card>

        <Card className="p-4 md:p-6 relative overflow-hidden">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="h-10 w-10 md:h-12 md:w-12 rounded-full bg-green-800/30 flex items-center justify-center">
                <CheckCircleIcon className="h-5 w-5 md:h-6 md:w-6 text-green-400" aria-hidden="true" />
              </div>
            </div>
            <div className="ml-3 md:ml-5 w-0 flex-1">
              <dl>
                <dt className="text-xs md:text-sm font-medium text-gray-300 truncate">Completed</dt>
                <dd>
                  <div className="text-base md:text-lg font-semibold text-white">{stats.completed.toLocaleString()}</div>
                </dd>
              </dl>
            </div>
          </div>
        </Card>

        <Card className="p-4 md:p-6 relative overflow-hidden">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="h-10 w-10 md:h-12 md:w-12 rounded-full bg-yellow-800/30 flex items-center justify-center">
                <ClockIcon className="h-5 w-5 md:h-6 md:w-6 text-yellow-400" aria-hidden="true" />
              </div>
            </div>
            <div className="ml-3 md:ml-5 w-0 flex-1">
              <dl>
                <dt className="text-xs md:text-sm font-medium text-gray-300 truncate">Pending</dt>
                <dd>
                  <div className="text-base md:text-lg font-semibold text-white">{stats.pending.toLocaleString()}</div>
                </dd>
              </dl>
            </div>
          </div>
        </Card>

        <Card className="p-4 md:p-6 relative overflow-hidden">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="h-10 w-10 md:h-12 md:w-12 rounded-full bg-purple-800/30 flex items-center justify-center">
                <BanknotesIcon className="h-5 w-5 md:h-6 md:w-6 text-purple-400" aria-hidden="true" />
              </div>
            </div>
            <div className="ml-3 md:ml-5 w-0 flex-1">
              <dl>
                <dt className="text-xs md:text-sm font-medium text-gray-300 truncate">Total Fees</dt>
                <dd>
                  <div className="text-base md:text-lg font-semibold text-white">{formatCurrency(feeStats.totalFees)}</div>
                </dd>
              </dl>
            </div>
          </div>
        </Card>
      </div>

      {/* Blockchain Volume Breakdown */}
      {blockchainVolumes.length > 0 && (
        <Card className="p-6">
          <h3 className="text-base font-semibold leading-6 text-white mb-4">Volume by Blockchain</h3>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
            {blockchainVolumes.map((volume) => (
              <div key={volume.blockchain} className="bg-white/5 rounded-lg p-4 border border-white/10">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium text-gray-300">{volume.blockchain}</span>
                  <span className="text-xs text-gray-400">{volume.transaction_count} txns</span>
                </div>
                <div className="text-lg font-semibold text-white">
                  {volume.total_volume.toFixed(6)} {volume.native_symbol}
                </div>
                {volume.total_volume_usd && (
                  <div className="text-sm text-gray-400">
                    ≈ ${volume.total_volume_usd.toFixed(2)} USD
                  </div>
                )}
              </div>
            ))}
          </div>
        </Card>
      )}

      <div className="grid grid-cols-1 gap-6">
        <Card className="p-6 relative overflow-hidden">
          <div className="flex justify-between items-center mb-6">
            <h3 className="text-base font-semibold leading-6 text-white">Recent Transactions</h3>
            <div className="flex items-center space-x-2">
              <div className="relative">
                <Button
                  variant="outline"
                  size="sm"
                  className="flex items-center"
                >
                  <FunnelIcon className="h-4 w-4 mr-1" />
                  Filter: {filter.charAt(0).toUpperCase() + filter.slice(1)}
                  <ChevronDownIcon className="h-4 w-4 ml-1" />
                </Button>
                <div className="absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-gray-800 ring-1 ring-black ring-opacity-5 z-10 hidden">
                  <div className="py-1" role="menu" aria-orientation="vertical">
                    <button
                      className="block px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 w-full text-left"
                      onClick={() => setFilter('all')}
                    >
                      All
                    </button>
                    <button
                      className="block px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 w-full text-left"
                      onClick={() => setFilter('completed')}
                    >
                      Completed
                    </button>
                    <button
                      className="block px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 w-full text-left"
                      onClick={() => setFilter('pending')}
                    >
                      Pending
                    </button>
                    <button
                      className="block px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 w-full text-left"
                      onClick={() => setFilter('failed')}
                    >
                      Failed
                    </button>
                  </div>
                </div>
              </div>
              <Button variant="outline" size="sm">Export</Button>
            </div>
          </div>

          <div className="relative overflow-x-auto">
            {/* Mobile view */}
            <div className="block md:hidden">
              <div className="space-y-4 p-4">
                {transactions.length === 0 ? (
                  <div className="text-center py-8">
                    <ArrowsRightLeftIcon className="h-12 w-12 mx-auto mb-4 text-gray-500" />
                    <h3 className="text-lg font-medium text-white mb-2">No transactions found</h3>
                    <p className="text-gray-400">No transactions match your current filters.</p>
                  </div>
                ) : (
                  transactions.map((transaction) => (
                    <div
                      key={transaction.id}
                      className={classNames(
                        selectedTransaction === transaction.id ? 'bg-indigo-900/20' : '',
                        'rounded-lg p-4 border border-white/10 hover:bg-indigo-900/10 cursor-pointer'
                      )}
                      onClick={() => setSelectedTransaction(transaction.id)}
                    >
                      <div className="flex justify-between items-start mb-2">
                        <div className="flex-1">
                          <p className="text-sm font-medium text-white truncate">
                            {transaction.id}
                          </p>
                          <p className="text-xs text-gray-400">
                            {(() => {
                              if (transaction.first_name) {
                                const fullName = transaction.last_name
                                  ? `${transaction.first_name} ${transaction.last_name}`
                                  : transaction.first_name;
                                return fullName;
                              }
                              return transaction.username || `User ${transaction.user_id}`;
                            })()}
                          </p>
                        </div>
                        <span
                          className={classNames(
                            getStatusClass(transaction.status),
                            'inline-flex items-center rounded-full px-2 py-1 text-xs font-medium'
                          )}
                        >
                          <span className="mr-1">{getStatusIcon(transaction.status)}</span>
                          {transaction.status.charAt(0).toUpperCase() + transaction.status.slice(1)}
                        </span>
                      </div>

                      <div className="flex justify-between items-center mb-2">
                        <span
                          className={classNames(
                            getTransactionDirection(transaction) === 'buy'
                              ? 'bg-green-900/30 text-green-400 border border-green-500/30'
                              : 'bg-red-900/30 text-red-400 border border-red-500/30',
                            'inline-flex items-center rounded-full px-2 py-1 text-xs font-medium'
                          )}
                        >
                          {getTransactionDirection(transaction) === 'buy' ? (
                            <ArrowDownIcon className="-ml-0.5 mr-1 h-3 w-3 text-green-400" />
                          ) : (
                            <ArrowUpIcon className="-ml-0.5 mr-1 h-3 w-3 text-red-400" />
                          )}
                          {getTransactionDirection(transaction).charAt(0).toUpperCase() + getTransactionDirection(transaction).slice(1)}
                        </span>
                        <span className="text-xs text-gray-400 bg-gray-800/50 px-2 py-1 rounded">
                          {transaction.blockchain.toUpperCase()}
                        </span>
                      </div>

                      <div className="flex justify-between items-center">
                        <div className="flex-1">
                          {(() => {
                            const details = getTransactionDetails(transaction);
                            const adminFee = getAdminFeeDetails(transaction);
                            return (
                              <div className="space-y-1">
                                <p className="text-sm font-medium text-white">
                                  {details.primaryAmount}
                                </p>
                                <p className="text-xs text-gray-400">
                                  {details.primaryLabel}
                                </p>
                                {details.secondaryAmount !== 'N/A' && (
                                  <>
                                    <p className="text-xs text-gray-300">
                                      {details.secondaryAmount}
                                    </p>
                                    <p className="text-xs text-gray-400">
                                      {details.secondaryLabel}
                                    </p>
                                  </>
                                )}
                                <p className="text-xs text-green-400">
                                  Admin Fee: {transaction.admin_fee_amount ?
                                    (() => {
                                      const feeAmount = transaction.admin_fee_amount;
                                      const symbol = transaction.admin_fee_token_symbol || transaction.native_token_symbol || 'SOL';

                                      // Always show in readable decimal format
                                      return `${formatDecimalAmount(feeAmount)} ${symbol}`;
                                    })() :
                                    adminFee.display}
                                </p>
                              </div>
                            );
                          })()}
                        </div>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleViewTransaction(transaction);
                          }}
                        >
                          View
                        </Button>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </div>

            {/* Desktop view */}
            <table className="hidden md:table min-w-full divide-y divide-white/10" style={{ minWidth: '1400px' }}>
              <thead className="bg-gray-800/30">
                <tr>
                  <th scope="col" className="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-white sm:pl-6" style={{ minWidth: '200px' }}>
                    Transaction ID
                  </th>
                  <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-white" style={{ minWidth: '120px' }}>
                    User
                  </th>
                  <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-white" style={{ minWidth: '100px' }}>
                    Bot
                  </th>
                  <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-white" style={{ minWidth: '80px' }}>
                    Type
                  </th>
                  <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-white" style={{ minWidth: '180px' }}>
                    Transaction Details
                  </th>
                  <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-white" style={{ minWidth: '150px' }}>
                    Fees & Admin
                  </th>
                  <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-white" style={{ minWidth: '100px' }}>
                    Status
                  </th>
                  <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-white" style={{ minWidth: '100px' }}>
                    Blockchain
                  </th>
                  <th scope="col" className="relative py-3.5 pl-3 pr-4 sm:pr-6" style={{ minWidth: '80px' }}>
                    <span className="sr-only">Actions</span>
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-white/10">
                {transactions.length === 0 ? (
                  <tr>
                    <td colSpan={9} className="px-6 py-12 text-center">
                      <div className="text-gray-400">
                        <ArrowsRightLeftIcon className="h-12 w-12 mx-auto mb-4 text-gray-500" />
                        <h3 className="text-lg font-medium text-white mb-2">No transactions found</h3>
                        <p>No transactions match your current filters.</p>
                      </div>
                    </td>
                  </tr>
                ) : (
                  transactions.map((transaction) => (
                    <tr
                      key={transaction.id}
                      className={classNames(
                        selectedTransaction === transaction.id ? 'bg-indigo-900/20' : '',
                        'hover:bg-indigo-900/10 cursor-pointer'
                      )}
                      onClick={() => setSelectedTransaction(transaction.id)}
                    >
                      <td className="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-white sm:pl-6" style={{ minWidth: '200px' }}>
                        <div className="truncate max-w-[180px]" title={transaction.id}>
                          {transaction.id}
                        </div>
                      </td>
                      <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-300" style={{ minWidth: '120px' }}>
                        <div className="truncate max-w-[100px]" title={(() => {
                          if (transaction.first_name) {
                            const fullName = transaction.last_name
                              ? `${transaction.first_name} ${transaction.last_name}`
                              : transaction.first_name;
                            return fullName;
                          }
                          return transaction.username || `User ${transaction.user_id}`;
                        })()}>
                          {(() => {
                            if (transaction.first_name) {
                              const fullName = transaction.last_name
                                ? `${transaction.first_name} ${transaction.last_name}`
                                : transaction.first_name;
                              return fullName;
                            }
                            return transaction.username || `User ${transaction.user_id}`;
                          })()}
                        </div>
                      </td>
                      <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-300" style={{ minWidth: '100px' }}>
                        {transaction.bot_type} Bot
                      </td>
                      <td className="whitespace-nowrap px-3 py-4 text-sm" style={{ minWidth: '80px' }}>
                        <span
                          className={classNames(
                            getTransactionDirection(transaction) === 'buy'
                              ? 'bg-green-900/30 text-green-400 border border-green-500/30'
                              : 'bg-red-900/30 text-red-400 border border-red-500/30',
                            'inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium'
                          )}
                        >
                          {getTransactionDirection(transaction) === 'buy' ? (
                            <ArrowDownIcon className="-ml-0.5 mr-1.5 h-3 w-3 text-green-400" />
                          ) : (
                            <ArrowUpIcon className="-ml-0.5 mr-1.5 h-3 w-3 text-red-400" />
                          )}
                          {getTransactionDirection(transaction).charAt(0).toUpperCase() + getTransactionDirection(transaction).slice(1)}
                        </span>
                      </td>
                      <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-300" style={{ minWidth: '180px' }}>
                        {(() => {
                          const details = getTransactionDetails(transaction);
                          return (
                            <div className="space-y-1">
                              <div className="truncate max-w-[160px]" title={details.primaryAmount}>
                                <span className="font-medium text-white">{details.primaryAmount}</span>
                              </div>
                              <div className="text-xs text-gray-400">{details.primaryLabel}</div>
                              {details.secondaryAmount !== 'N/A' && (
                                <>
                                  <div className="truncate max-w-[160px]" title={details.secondaryAmount}>
                                    <span className="text-sm text-gray-300">{details.secondaryAmount}</span>
                                  </div>
                                  <div className="text-xs text-gray-400">{details.secondaryLabel}</div>
                                </>
                              )}
                            </div>
                          );
                        })()}
                      </td>
                      <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-300" style={{ minWidth: '150px' }}>
                        {(() => {
                          const adminFee = getAdminFeeDetails(transaction);
                          const txFees = getTransactionFees(transaction);
                          return (
                            <div className="space-y-1">
                              {/* Admin Fee */}
                              <div className="flex items-center space-x-2">
                                <span className="truncate max-w-[100px] font-medium text-white" title={adminFee.display}>
                                  {adminFee.display}
                                </span>
                                <span
                                  className={classNames(
                                    adminFee.status === 'completed'
                                      ? 'bg-green-900/30 text-green-400'
                                      : adminFee.status === 'pending'
                                      ? 'bg-yellow-900/30 text-yellow-400'
                                      : adminFee.status === 'retrying'
                                      ? 'bg-blue-900/30 text-blue-400'
                                      : 'bg-red-900/30 text-red-400',
                                    'inline-flex items-center rounded-full px-1.5 py-0.5 text-xs font-medium'
                                  )}
                                >
                                  {adminFee.status === 'completed' && '✓'}
                                  {adminFee.status === 'pending' && '⏳'}
                                  {adminFee.status === 'retrying' && '🔄'}
                                  {adminFee.status === 'failed' && '✗'}
                                </span>
                              </div>
                              <div className="text-xs text-gray-400 truncate" title={adminFee.method}>
                                Admin Fee ({adminFee.percentage}%)
                              </div>

                              {/* Transaction Fees */}
                              {txFees.gasFee !== 'N/A' && (
                                <div className="text-xs text-gray-300 truncate" title={txFees.gasFee}>
                                  Gas: {txFees.gasFee}
                                </div>
                              )}
                            </div>
                          );
                        })()}
                      </td>
                      <td className="whitespace-nowrap px-3 py-4 text-sm" style={{ minWidth: '100px' }}>
                        <span
                          className={classNames(
                            getStatusClass(transaction.status),
                            'inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium'
                          )}
                        >
                          <span className="mr-1.5">{getStatusIcon(transaction.status)}</span>
                          {transaction.status.charAt(0).toUpperCase() + transaction.status.slice(1)}
                        </span>
                      </td>
                      <td className="whitespace-nowrap px-3 py-4 text-sm" style={{ minWidth: '100px' }}>
                        <span className="text-gray-300">
                          {transaction.blockchain.toUpperCase()}
                        </span>
                      </td>
                      <td className="relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-6" style={{ minWidth: '80px' }}>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleViewTransaction(transaction);
                          }}
                        >
                          View
                        </Button>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        </Card>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card className="p-6 relative overflow-hidden">
          <h3 className="text-base font-semibold leading-6 text-white mb-4">Fee Statistics</h3>
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <div className="h-10 w-10 flex-shrink-0 rounded-full bg-indigo-800/30 flex items-center justify-center">
                  <CurrencyDollarIcon className="h-5 w-5 text-indigo-400" aria-hidden="true" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-white">Today's Fees</p>
                  <p className="text-xs text-gray-400">Last 24 hours</p>
                </div>
              </div>
              <p className="text-lg font-semibold text-white">{formatCurrency(feeStats.todayFees)}</p>
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <div className="h-10 w-10 flex-shrink-0 rounded-full bg-indigo-800/30 flex items-center justify-center">
                  <CurrencyDollarIcon className="h-5 w-5 text-indigo-400" aria-hidden="true" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-white">Weekly Fees</p>
                  <p className="text-xs text-gray-400">Last 7 days</p>
                </div>
              </div>
              <p className="text-lg font-semibold text-white">{formatCurrency(feeStats.weeklyFees)}</p>
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <div className="h-10 w-10 flex-shrink-0 rounded-full bg-indigo-800/30 flex items-center justify-center">
                  <CurrencyDollarIcon className="h-5 w-5 text-indigo-400" aria-hidden="true" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-white">Monthly Fees</p>
                  <p className="text-xs text-gray-400">Last 30 days</p>
                </div>
              </div>
              <p className="text-lg font-semibold text-white">{formatCurrency(feeStats.monthlyFees)}</p>
            </div>
          </div>
        </Card>

        <Card className="p-6 relative overflow-hidden">
          <h3 className="text-base font-semibold leading-6 text-white mb-4">Transaction Distribution</h3>
          <div className="space-y-4">
            {(() => {
              // Calculate bot type distribution
              const botTypes = transactions.reduce((acc, tx) => {
                acc[tx.bot_type] = (acc[tx.bot_type] || 0) + 1;
                return acc;
              }, {} as Record<string, number>);

              const totalTransactions = transactions.length;

              return Object.entries(botTypes).map(([botType, count]) => {
                const percentage = totalTransactions > 0 ? Math.round((count / totalTransactions) * 100) : 0;
                return (
                  <div key={botType}>
                    <div className="flex justify-between mb-1">
                      <span className="text-sm text-gray-300">{botType} Bot</span>
                      <span className="text-sm text-gray-300">{percentage}%</span>
                    </div>
                    <div className="w-full bg-gray-700 rounded-full h-2">
                      <div
                        className="bg-indigo-500 h-2 rounded-full"
                        style={{ width: `${percentage}%` }}
                      ></div>
                    </div>
                  </div>
                );
              });
            })()}

            <div className="pt-4 border-t border-white/10 mt-4">
              {(() => {
                // Calculate buy/sell distribution
                const buyCount = transactions.filter(tx => getTransactionDirection(tx) === 'buy').length;
                const sellCount = transactions.filter(tx => getTransactionDirection(tx) === 'sell').length;
                const total = buyCount + sellCount;

                const buyPercentage = total > 0 ? Math.round((buyCount / total) * 100) : 0;
                const sellPercentage = total > 0 ? Math.round((sellCount / total) * 100) : 0;

                return (
                  <>
                    <div>
                      <div className="flex justify-between mb-1">
                        <span className="text-sm text-gray-300">Buy Orders</span>
                        <span className="text-sm text-gray-300">{buyPercentage}%</span>
                      </div>
                      <div className="w-full bg-gray-700 rounded-full h-2">
                        <div
                          className="bg-green-500 h-2 rounded-full"
                          style={{ width: `${buyPercentage}%` }}
                        ></div>
                      </div>
                    </div>

                    <div>
                      <div className="flex justify-between mb-1">
                        <span className="text-sm text-gray-300">Sell Orders</span>
                        <span className="text-sm text-gray-300">{sellPercentage}%</span>
                      </div>
                      <div className="w-full bg-gray-700 rounded-full h-2">
                        <div
                          className="bg-red-500 h-2 rounded-full"
                          style={{ width: `${sellPercentage}%` }}
                        ></div>
                      </div>
                    </div>
                  </>
                );
              })()}
            </div>
          </div>
        </Card>
      </div>

      {/* Transaction Details Modal */}
      <Transition appear show={showTransactionModal} as={Fragment}>
        <Dialog as="div" className="relative z-50" onClose={() => setShowTransactionModal(false)}>
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-black/50 backdrop-blur-sm" />
          </Transition.Child>

          <div className="fixed inset-0 overflow-y-auto">
            <div className="flex min-h-full items-center justify-center p-4 text-center">
              <Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0 scale-95"
                enterTo="opacity-100 scale-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 scale-100"
                leaveTo="opacity-0 scale-95"
              >
                <Dialog.Panel className="w-full max-w-4xl transform overflow-hidden rounded-2xl bg-gray-900/95 backdrop-blur-xl border border-white/10 p-6 text-left align-middle shadow-xl transition-all">
                  <div className="flex items-center justify-between mb-6">
                    <Dialog.Title as="h3" className="text-lg font-medium leading-6 text-white">
                      Transaction Details
                    </Dialog.Title>
                    <button
                      type="button"
                      className="rounded-md bg-gray-800/50 p-2 text-gray-400 hover:text-white hover:bg-gray-700/50 transition-colors"
                      onClick={() => setShowTransactionModal(false)}
                    >
                      <XMarkIcon className="h-5 w-5" aria-hidden="true" />
                    </button>
                  </div>

                  {selectedTransactionData && (
                    <div className="space-y-6">
                      {/* Basic Information */}
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div className="space-y-4">
                          <h4 className="text-sm font-medium text-gray-300 border-b border-white/10 pb-2">Basic Information</h4>

                          <div className="space-y-3">
                            <div className="flex justify-between items-center">
                              <span className="text-sm text-gray-400">Transaction ID:</span>
                              <div className="flex items-center space-x-2">
                                <span className="text-sm text-white font-mono">{selectedTransactionData.id}</span>
                                <button
                                  onClick={() => copyToClipboard(selectedTransactionData.id)}
                                  className="text-gray-400 hover:text-white transition-colors"
                                >
                                  <DocumentDuplicateIcon className="h-4 w-4" />
                                </button>
                              </div>
                            </div>

                            <div className="flex justify-between items-center">
                              <span className="text-sm text-gray-400">User:</span>
                              <span className="text-sm text-white">
                                {(() => {
                                  if (selectedTransactionData.first_name) {
                                    const fullName = selectedTransactionData.last_name
                                      ? `${selectedTransactionData.first_name} ${selectedTransactionData.last_name}`
                                      : selectedTransactionData.first_name;
                                    return fullName;
                                  }
                                  return selectedTransactionData.username || `User ${selectedTransactionData.user_id}`;
                                })()}
                              </span>
                            </div>

                            <div className="flex justify-between items-center">
                              <span className="text-sm text-gray-400">User ID:</span>
                              <div className="flex items-center space-x-2">
                                <span className="text-sm text-white font-mono">{selectedTransactionData.user_id}</span>
                                <button
                                  onClick={() => copyToClipboard(selectedTransactionData.user_id)}
                                  className="text-gray-400 hover:text-white transition-colors"
                                >
                                  <DocumentDuplicateIcon className="h-4 w-4" />
                                </button>
                              </div>
                            </div>

                            <div className="flex justify-between items-center">
                              <span className="text-sm text-gray-400">Bot Type:</span>
                              <span className="text-sm text-white">{selectedTransactionData.bot_type} Bot</span>
                            </div>

                            <div className="flex justify-between items-center">
                              <span className="text-sm text-gray-400">Blockchain:</span>
                              <span className="text-sm text-white">{selectedTransactionData.blockchain.toUpperCase()}</span>
                            </div>

                            <div className="flex justify-between items-center">
                              <span className="text-sm text-gray-400">Type:</span>
                              <span
                                className={classNames(
                                  getTransactionDirection(selectedTransactionData) === 'buy'
                                    ? 'bg-green-900/30 text-green-400 border border-green-500/30'
                                    : 'bg-red-900/30 text-red-400 border border-red-500/30',
                                  'inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium'
                                )}
                              >
                                {getTransactionDirection(selectedTransactionData) === 'buy' ? (
                                  <ArrowDownIcon className="-ml-0.5 mr-1.5 h-3 w-3" />
                                ) : (
                                  <ArrowUpIcon className="-ml-0.5 mr-1.5 h-3 w-3" />
                                )}
                                {getTransactionDirection(selectedTransactionData).charAt(0).toUpperCase() + getTransactionDirection(selectedTransactionData).slice(1)}
                              </span>
                            </div>

                            <div className="flex justify-between items-center">
                              <span className="text-sm text-gray-400">Status:</span>
                              <span
                                className={classNames(
                                  getStatusClass(selectedTransactionData.status),
                                  'inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium'
                                )}
                              >
                                <span className="mr-1.5">{getStatusIcon(selectedTransactionData.status)}</span>
                                {selectedTransactionData.status.charAt(0).toUpperCase() + selectedTransactionData.status.slice(1)}
                              </span>
                            </div>

                            <div className="flex justify-between items-center">
                              <span className="text-sm text-gray-400">Timestamp:</span>
                              <span className="text-sm text-white">{formatDate(selectedTransactionData.timestamp)}</span>
                            </div>
                          </div>
                        </div>

                        <div className="space-y-4">
                          <h4 className="text-sm font-medium text-gray-300 border-b border-white/10 pb-2">Transaction Details</h4>

                          <div className="space-y-3">
                            <div className="flex justify-between items-center">
                              <span className="text-sm text-gray-400">Token Symbol:</span>
                              <span className="text-sm text-white">{selectedTransactionData.token_symbol}</span>
                            </div>

                            <div className="flex justify-between items-center">
                              <span className="text-sm text-gray-400">Token Address:</span>
                              <div className="flex items-center space-x-2">
                                <span className="text-sm text-white font-mono truncate max-w-[200px]">{selectedTransactionData.token_address}</span>
                                <button
                                  onClick={() => copyToClipboard(selectedTransactionData.token_address)}
                                  className="text-gray-400 hover:text-white transition-colors"
                                >
                                  <DocumentDuplicateIcon className="h-4 w-4" />
                                </button>
                              </div>
                            </div>

                            {/* Enhanced Amount Display */}
                            {selectedTransactionData.native_token_amount && (
                              <div className="flex justify-between items-center">
                                <span className="text-sm text-gray-400">
                                  {getTransactionDirection(selectedTransactionData) === 'buy' ? 'SOL Spent:' : 'SOL Received:'}
                                </span>
                                <span className="text-sm text-white">
                                  {selectedTransactionData.native_token_amount.toFixed(6)} {selectedTransactionData.native_token_symbol || 'SOL'}
                                </span>
                              </div>
                            )}

                            {selectedTransactionData.token_amount && (
                              <div className="flex justify-between items-center">
                                <span className="text-sm text-gray-400">
                                  {getTransactionDirection(selectedTransactionData) === 'buy' ? 'Tokens Received:' : 'Tokens Sold:'}
                                </span>
                                <span className="text-sm text-white">
                                  {selectedTransactionData.token_amount.toFixed(6)} {selectedTransactionData.token_symbol}
                                </span>
                              </div>
                            )}

                            {/* Legacy Amount Display (fallback) */}
                            {!selectedTransactionData.native_token_amount && !selectedTransactionData.token_amount && (
                              <div className="flex justify-between items-center">
                                <span className="text-sm text-gray-400">Amount:</span>
                                <span className="text-sm text-white">
                                  {(() => {
                                    if (getTransactionDirection(selectedTransactionData) === 'buy') {
                                      const nativeSymbol = getNativeTokenSymbol(selectedTransactionData.blockchain);
                                      return formatBlockchainAmount(selectedTransactionData.amount, selectedTransactionData.blockchain, nativeSymbol);
                                    } else {
                                      return formatBlockchainAmount(selectedTransactionData.amount, selectedTransactionData.blockchain, selectedTransactionData.token_symbol);
                                    }
                                  })()}
                                </span>
                              </div>
                            )}

                            <div className="flex justify-between items-center">
                              <span className="text-sm text-gray-400">Gas Fee:</span>
                              <span className="text-sm text-white">
                                {selectedTransactionData.gas_fee ? formatCurrency(selectedTransactionData.gas_fee) : 'N/A'}
                              </span>
                            </div>

                            {selectedTransactionData.hash && (
                              <div className="flex justify-between items-center">
                                <span className="text-sm text-gray-400">Transaction Hash:</span>
                                <div className="flex items-center space-x-2">
                                  <span className="text-sm text-white font-mono truncate max-w-[200px]">{selectedTransactionData.hash}</span>
                                  <button
                                    onClick={() => copyToClipboard(selectedTransactionData.hash!)}
                                    className="text-gray-400 hover:text-white transition-colors"
                                  >
                                    <DocumentDuplicateIcon className="h-4 w-4" />
                                  </button>
                                </div>
                              </div>
                            )}

                            {selectedTransactionData.block_number && (
                              <div className="flex justify-between items-center">
                                <span className="text-sm text-gray-400">Block Number:</span>
                                <span className="text-sm text-white">{selectedTransactionData.block_number}</span>
                              </div>
                            )}

                            {selectedTransactionData.error_message && (
                              <div className="flex justify-between items-start">
                                <span className="text-sm text-gray-400">Error:</span>
                                <span className="text-sm text-red-400 max-w-[200px] text-right">{selectedTransactionData.error_message}</span>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>

                      {/* Admin Fee Information */}
                      {(selectedTransactionData.admin_fee_amount || selectedTransactionData.admin_fee_status) && (
                        <div className="space-y-4">
                          <h4 className="text-sm font-medium text-gray-300 border-b border-white/10 pb-2">Admin Fee Information</h4>

                          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div className="space-y-3">
                              {selectedTransactionData.admin_fee_amount && (
                                <div className="flex justify-between items-center">
                                  <span className="text-sm text-gray-400">Fee Amount:</span>
                                  <span className="text-sm text-white">
                                    {(() => {
                                      const feeAmount = selectedTransactionData.admin_fee_amount;
                                      const symbol = selectedTransactionData.admin_fee_token_symbol || selectedTransactionData.native_token_symbol || 'SOL';

                                      // Always show in readable decimal format
                                      return `${formatDecimalAmount(feeAmount)} ${symbol}`;
                                    })()}
                                  </span>
                                </div>
                              )}

                              {selectedTransactionData.admin_fee_percentage && (
                                <div className="flex justify-between items-center">
                                  <span className="text-sm text-gray-400">Fee Percentage:</span>
                                  <span className="text-sm text-white">{selectedTransactionData.admin_fee_percentage}%</span>
                                </div>
                              )}
                            </div>

                            <div className="space-y-3">
                              {selectedTransactionData.admin_fee_status && (
                                <div className="flex justify-between items-center">
                                  <span className="text-sm text-gray-400">Fee Status:</span>
                                  <span
                                    className={classNames(
                                      selectedTransactionData.admin_fee_status === 'completed'
                                        ? 'bg-green-900/30 text-green-400'
                                        : selectedTransactionData.admin_fee_status === 'pending'
                                        ? 'bg-yellow-900/30 text-yellow-400'
                                        : selectedTransactionData.admin_fee_status === 'retrying'
                                        ? 'bg-blue-900/30 text-blue-400'
                                        : 'bg-red-900/30 text-red-400',
                                      'inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium'
                                    )}
                                  >
                                    {selectedTransactionData.admin_fee_status === 'completed' && '✓'}
                                    {selectedTransactionData.admin_fee_status === 'pending' && '⏳'}
                                    {selectedTransactionData.admin_fee_status === 'retrying' && '🔄'}
                                    {selectedTransactionData.admin_fee_status === 'failed' && '✗'}
                                    <span className="ml-1">{selectedTransactionData.admin_fee_status.charAt(0).toUpperCase() + selectedTransactionData.admin_fee_status.slice(1)}</span>
                                  </span>
                                </div>
                              )}

                              {selectedTransactionData.admin_fee_collection_method && (
                                <div className="flex justify-between items-center">
                                  <span className="text-sm text-gray-400">Collection Method:</span>
                                  <span className="text-sm text-white">{selectedTransactionData.admin_fee_collection_method}</span>
                                </div>
                              )}

                              {/* Admin Fee Calculation Verification */}
                              {selectedTransactionData.admin_fee_amount && selectedTransactionData.admin_fee_percentage && (
                                <div className="mt-2 p-2 bg-gray-800/50 rounded border border-white/10">
                                  <div className="text-xs text-gray-400 mb-1">Fee Calculation Verification:</div>
                                  {(() => {
                                    const verification = verifyAdminFeeCalculation(selectedTransactionData);
                                    return (
                                      <div className={`text-xs ${verification.isCorrect ? 'text-green-400' : 'text-red-400'}`}>
                                        {verification.isCorrect ? '✓' : '✗'} {verification.message}
                                        {!verification.isCorrect && (
                                          <div className="text-xs text-gray-400 mt-1">
                                            Base: {verification.baseAmount ? formatDecimalAmount(verification.baseAmount) : 'N/A'} |
                                            Rate: {verification.percentage}% |
                                            Expected: {verification.expectedFee ? formatDecimalAmount(verification.expectedFee) : 'N/A'}
                                          </div>
                                        )}
                                      </div>
                                    );
                                  })()}
                                </div>
                              )}

                              {/* Admin Fee Collection Button - Only show when status is pending */}
                              {selectedTransactionData.admin_fee_status === 'pending' && (
                                <div className="mt-3">
                                  <Button
                                    variant="glass"
                                    size="sm"
                                    onClick={() => handleCollectAdminFee(selectedTransactionData.id)}
                                    className="w-full"
                                  >
                                    <BanknotesIcon className="h-4 w-4 mr-2" />
                                    Collect Admin Fee
                                  </Button>
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                      )}

                      {/* Action Buttons */}
                      <div className="flex justify-between items-center pt-4 border-t border-white/10">
                        <div className="flex space-x-3">
                          {selectedTransactionData.hash && (
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => {
                                if (selectedTransactionData.hash) {
                                  window.open(getExplorerUrl(selectedTransactionData.blockchain, selectedTransactionData.hash), '_blank');
                                }
                              }}
                            >
                              <EyeIcon className="h-4 w-4 mr-2" />
                              View on Explorer
                            </Button>
                          )}
                        </div>

                        <Button
                          variant="glass"
                          onClick={() => setShowTransactionModal(false)}
                        >
                          Close
                        </Button>
                      </div>
                    </div>
                  )}
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition>
    </div>
  );
}
