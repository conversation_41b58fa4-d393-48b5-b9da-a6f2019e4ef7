use thiserror::Error;

#[derive(Error, Debug)]
pub enum BotError {
    #[error("Telegram API error: {0}")]
    TelegramError(#[from] teloxide::RequestError),

    #[error("Environment error: {0}")]
    EnvError(String),

    #[error("User error: {0}")]
    UserError(String),

    #[error("Message error: {0}")]
    MessageError(String),

    #[error("Bot error: {0}")]
    GeneralError(String),

    #[error("Blockchain error: {0}")]
    BlockchainError(String),

    #[error("Authentication failed")]
    AuthenticationFailed,

    #[error("Token generation failed")]
    TokenGenerationFailed,

    #[error("Invalid token")]
    InvalidToken,

    #[error("User not found")]
    UserNotFound,

    #[error("Database error")]
    DatabaseError,

    #[error("Password hash failed")]
    PasswordHashFailed,
}

impl BotError {
    pub fn env_error(msg: impl Into<String>) -> Self {
        Self::EnvError(msg.into())
    }

    pub fn user_error(msg: impl Into<String>) -> Self {
        Self::UserError(msg.into())
    }

    pub fn message_error(msg: impl Into<String>) -> Self {
        Self::MessageError(msg.into())
    }

    pub fn general_error(msg: impl Into<String>) -> Self {
        Self::GeneralError(msg.into())
    }

    pub fn blockchain_error(msg: impl Into<String>) -> Self {
        Self::BlockchainError(msg.into())
    }

    pub fn wallet_error(msg: impl Into<String>) -> Self {
        Self::UserError(msg.into())
    }

    pub fn validation_error(msg: impl Into<String>) -> Self {
        Self::GeneralError(msg.into())
    }

    pub fn network_error(msg: impl Into<String>) -> Self {
        Self::GeneralError(msg.into())
    }

    pub fn database_error(msg: impl Into<String>) -> Self {
        Self::DatabaseError
    }

    pub fn trading_error(msg: impl Into<String>) -> Self {
        Self::BlockchainError(msg.into())
    }

    pub fn transaction_error(msg: impl Into<String>) -> Self {
        Self::BlockchainError(msg.into())
    }
}
