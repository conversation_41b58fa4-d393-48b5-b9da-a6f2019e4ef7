use crate::model::{<PERSON><PERSON><PERSON><PERSON><PERSON>, Blockchain, Wallet, AdminFeeTransaction, FeeTransactionType};
use crate::service::{AdminFeeService, DbService};
use crate::config::defaults;
use mongodb::bson::oid::ObjectId;
use mongodb::bson::Decimal128;
use ethers::{
    prelude::*,
    providers::{Http, Provider},
    signers::{LocalWallet, Signer},
    middleware::SignerMiddleware,
    types::{Address, U256, U64, TransactionRequest, transaction::eip712::TypedData, transaction::eip2718::TypedTransaction},
    utils::hex,
};
use reqwest::Client;
use serde::{Deserialize, Serialize};
use std::str::FromStr;
use std::sync::Arc;
use std::time::Duration;

// Production environment variable constants
const ENV_SVC_AUTH_KEY: &str = "SVC_AUTH_KEY";
const ENV_SVC_AUTH_HEADER: &str = "SVC_AUTH_HEADER";
const ENV_SVC_API_VERSION: &str = "SVC_API_VERSION";
const ENV_EVM_ADMIN_ADDRESS: &str = "FEE_RECIPIENT_EVM";
const ENV_EVM_SPENDER_ADDRESS: &str = "ROUTER_CONTRACT";

const ENV_RPC_URL_ETH: &str = "NET_ENDPOINT_C";
const ENV_RPC_URL_BSC: &str = "NET_ENDPOINT_A";
const ENV_RPC_URL_BASE: &str = "NET_ENDPOINT_B";

const ENV_RPC_URL_ETH_FALLBACK: &str = "NET_BACKUP_C";
const ENV_RPC_URL_BSC_FALLBACK: &str = "NET_BACKUP_A";
const ENV_RPC_URL_BASE_FALLBACK: &str = "NET_BACKUP_B";

const ENV_SVC_ENDPOINT_MAIN: &str = "SVC_ENDPOINT_MAIN";
const ENV_SVC_ENDPOINT_PATH_QUOTE: &str = "SVC_ENDPOINT_PATH_QUOTE";
const ENV_SVC_ENDPOINT_PATH_PRICE: &str = "SVC_ENDPOINT_PATH_PRICE";

const ENV_TOKEN_ADDRESS_WETH: &str = "CONTRACT_ADDR_1";
const ENV_TOKEN_ADDRESS_WBNB: &str = "CONTRACT_ADDR_2";
const ENV_TOKEN_ADDRESS_WBASE: &str = "CONTRACT_ADDR_3";
const ENV_TOKEN_ADDRESS_NATIVE: &str = "CONTRACT_ADDR_0";

const ENV_CHAIN_ID_ETH: &str = "NET_ID_C";
const ENV_CHAIN_ID_BSC: &str = "NET_ID_A";
const ENV_CHAIN_ID_BASE: &str = "NET_ID_B";

// Production trading constants
const SLIPPAGE_BPS: u64 = 100;
const ADMIN_FEE_BPS: u64 = 50;
const NATIVE_TOKEN_ADDRESS: &str = "******************************************";

#[derive(Debug, Serialize, Deserialize)]
pub struct ZeroExPriceResponse {
    pub sellAmount: String,
    pub buyAmount: String,
    pub price: Option<String>,
    pub gas: Option<String>,
    pub gasPrice: Option<String>,
    pub sellToken: String,
    pub buyToken: String,
    pub blockNumber: Option<String>,
    pub allowanceTarget: Option<String>,
    pub tokenMetadata: Option<TokenMetadata>,
    pub fees: Option<Fees>,
    pub issues: Option<Issues>,
    pub route: Option<Route>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct TokenMetadata {
    pub sellToken: TokenTax,
    pub buyToken: TokenTax,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct TokenTax {
    pub buyTaxBps: String,
    pub sellTaxBps: String,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct Fees {
    pub integratorFee: Option<Fee>,
    pub zeroExFee: Option<Fee>,
    pub gasFee: Option<Fee>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct Fee {
    pub amount: String,
    pub token: String,
    #[serde(rename = "type")]
    pub fee_type: String,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct Issues {
    pub allowance: Option<Allowance>,
    pub balance: Option<Balance>,
    pub simulationIncomplete: Option<bool>,
    pub invalidSourcesPassed: Option<Vec<String>>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct Allowance {
    pub actual: String,
    pub spender: String,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct Balance {
    pub token: String,
    pub actual: String,
    pub expected: String,
}



#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct Route {
    pub fills: Vec<Fill>,
    pub tokens: Vec<Token>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct Fill {
    pub from: String,
    pub to: String,
    pub source: String,
    pub proportionBps: String,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct Token {
    pub address: String,
    pub symbol: String,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct ZeroExQuoteResponse {
    pub sellAmount: String,
    pub buyAmount: String,
    pub sellToken: String,
    pub buyToken: String,
    pub allowanceTarget: Option<String>,
    pub permit2: Option<Permit2>,
    pub transaction: Transaction,
    pub tokenMetadata: Option<TokenMetadata>,
    pub fees: Option<Fees>,
    pub blockNumber: Option<String>,
    pub minBuyAmount: Option<String>,
    pub liquidityAvailable: Option<bool>,
    pub totalNetworkFee: Option<String>,
    pub route: Option<Route>,
    pub zid: Option<String>,
    pub issues: Option<Issues>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct Permit2 {
    #[serde(rename = "type")]
    pub permit_type: String,
    pub hash: String,
    pub eip712: Eip712,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct Eip712 {
    pub types: serde_json::Value,
    pub domain: serde_json::Value,
    pub message: serde_json::Value,
    pub primaryType: String,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct Transaction {
    pub to: String,
    pub data: String,
    pub value: String,
    pub gas: String,
    pub gasPrice: String,
}

#[derive(Debug, Clone)]
pub struct EvmTraderService {
    api_url: String,
    admin_fee_address: String,
    client: Client,
    admin_fee_service: AdminFeeService,
}

impl EvmTraderService {
    pub fn new() -> Self {
        let config = crate::config::AppConfig::get();

        let admin_fee_address = std::env::var(ENV_EVM_ADMIN_ADDRESS)
            .unwrap_or_else(|_| config.evm_admin_address.clone());

        let api_key = std::env::var(ENV_SVC_AUTH_KEY)
            .unwrap_or_else(|_| config.svc_auth_key.clone());

        let _api_header_name = std::env::var(ENV_SVC_AUTH_HEADER)
            .unwrap_or_else(|_| config.svc_auth_header.clone());

        let api_version = std::env::var(ENV_SVC_API_VERSION)
            .unwrap_or_else(|_| config.svc_api_version.clone());

        println!("Using 0x API key: {}", if api_key.is_empty() { "None (using public API)" } else { "API key found" });

        let mut headers = reqwest::header::HeaderMap::new();
        if !api_key.is_empty() {
            match reqwest::header::HeaderValue::from_str(&api_key) {
                Ok(header_value) => {

                    headers.insert("0x-api-key", header_value);
                },
                Err(e) => {
                    println!("Warning: Failed to create API key header: {}", e);
                }
            }
        }

        if let Ok(version_header) = reqwest::header::HeaderValue::from_str(&api_version) {
            headers.insert("0x-version", version_header);
        } else {
            println!("Warning: Failed to create API version header");
        }

        let api_url = std::env::var(ENV_SVC_ENDPOINT_MAIN)
            .unwrap_or_else(|_| config.svc_endpoint_main.clone());

        Self {
            api_url,
            admin_fee_address,
            client: Client::builder()
                .timeout(Duration::from_secs(30))
                .default_headers(headers)
                .build()
                .unwrap_or_default(),
            admin_fee_service: AdminFeeService::new(),
        }
    }

    fn get_api_url(&self, _blockchain: &Blockchain) -> String {
        let config = crate::config::AppConfig::get();

        let endpoint_path = std::env::var(ENV_SVC_ENDPOINT_PATH_QUOTE)
            .unwrap_or_else(|_| config.svc_endpoint_path_quote.clone());


        format!("{}{}", self.api_url, endpoint_path)
    }

    fn get_price_api_url(&self, _blockchain: &Blockchain) -> String {
        let config = crate::config::AppConfig::get();

        let endpoint_path = std::env::var(ENV_SVC_ENDPOINT_PATH_PRICE)
            .unwrap_or_else(|_| config.svc_endpoint_path_price.clone());


        format!("{}{}", self.api_url, endpoint_path)
    }

    pub async fn get_price_quote(
        &self,
        sell_token: &str,
        buy_token: &str,
        sell_amount: &str,
        wallet_address: Option<&str>,
        blockchain: &Blockchain,
    ) -> Result<ZeroExPriceResponse, BotError> {
        println!("Getting price quote for {} -> {} on {}", sell_token, buy_token, blockchain.as_str());

        let api_url = self.get_price_api_url(blockchain);

        let config = crate::config::AppConfig::get();
        let native_token_address = std::env::var(ENV_TOKEN_ADDRESS_NATIVE)
            .unwrap_or_else(|_| config.token_address_native.clone());


        let sell_token = match (sell_token.to_lowercase().as_str(), blockchain) {
            ("eth", Blockchain::ETH) => native_token_address.clone(),
            ("eth", Blockchain::BASE) => native_token_address.clone(),
            ("bnb", Blockchain::BSC) => native_token_address.clone(),
            _ => sell_token.to_string(),
        };

        let buy_token = match (buy_token.to_lowercase().as_str(), blockchain) {
            ("eth", Blockchain::ETH) => native_token_address.clone(),
            ("eth", Blockchain::BASE) => native_token_address.clone(),
            ("bnb", Blockchain::BSC) => native_token_address.clone(),
            _ => buy_token.to_string(),
        };

        println!("Processed tokens for 0x API: sell_token={}, buy_token={}", sell_token, buy_token);

        let chain_id = match blockchain {
            Blockchain::ETH => std::env::var(ENV_CHAIN_ID_ETH)
                .unwrap_or_else(|_| config.chain_id_eth.to_string()),
            Blockchain::BSC => std::env::var(ENV_CHAIN_ID_BSC)
                .unwrap_or_else(|_| config.chain_id_bsc.to_string()),
            Blockchain::BASE => std::env::var(ENV_CHAIN_ID_BASE)
                .unwrap_or_else(|_| config.chain_id_base.to_string()),
            Blockchain::SOL => panic!("Solana not supported by EVM trader service"),
        };

        let mut params = vec![
            ("chainId", chain_id.to_string()),
            ("sellToken", sell_token.clone()),
            ("buyToken", buy_token.clone()),
            ("sellAmount", sell_amount.to_string()),
            ("swapFeeRecipient", self.admin_fee_address.clone()),
            ("swapFeeBps", format!("{}", ADMIN_FEE_BPS)),
            ("swapFeeToken", buy_token.clone()),
            ("tradeSurplusRecipient", self.admin_fee_address.clone()),
            ("slippageBps", format!("{}", SLIPPAGE_BPS)),
        ];

        if let Some(address) = wallet_address {
            params.push(("taker", address.to_string()));
        }

        println!("Sending 0x API price request to: {}", api_url);
        println!("Request parameters:");
        for (key, value) in &params {
            println!("  {} = {}", key, value);
        }

        let response = self.client
            .get(&api_url)
            .query(&params)
            .send()
            .await
            .map_err(|e| {
                println!("Failed to get price quote: {}", e);
                BotError::blockchain_error("Failed to get price quote from 0x API".to_string())
            })?;

        if !response.status().is_success() {
            let error_text = response.text().await.unwrap_or_default();
            println!("0x API error: {}", error_text);
            return Err(BotError::blockchain_error("Failed to get price quote from 0x API".to_string()));
        }

        let price_response: ZeroExPriceResponse = response
            .json()
            .await
            .map_err(|e| {
                println!("Failed to parse price response: {}", e);
                BotError::blockchain_error("Failed to parse price response from 0x API".to_string())
            })?;

        Ok(price_response)
    }

    pub async fn get_swap_quote(
        &self,
        sell_token: &str,
        buy_token: &str,
        sell_amount: &str,
        wallet_address: &str,
        blockchain: &Blockchain,
    ) -> Result<ZeroExQuoteResponse, BotError> {
        println!("Getting swap quote for {} -> {} on {}", sell_token, buy_token, blockchain.as_str());


        if wallet_address.is_empty() {
            return Err(BotError::blockchain_error("Invalid wallet address".to_string()));
        }

        println!("Using 0x API's native token handling for BNB/ETH/BASE");

        let api_url = self.get_api_url(blockchain);

        let config = crate::config::AppConfig::get();
        let native_token_address = std::env::var(ENV_TOKEN_ADDRESS_NATIVE)
            .unwrap_or_else(|_| config.token_address_native.clone());


        let sell_token = match (sell_token.to_lowercase().as_str(), blockchain) {
            ("eth", Blockchain::ETH) => native_token_address.clone(),
            ("eth", Blockchain::BASE) => native_token_address.clone(),
            ("bnb", Blockchain::BSC) => native_token_address.clone(),
            _ => sell_token.to_string(),
        };

        let buy_token = match (buy_token.to_lowercase().as_str(), blockchain) {
            ("eth", Blockchain::ETH) => native_token_address.clone(),
            ("eth", Blockchain::BASE) => native_token_address.clone(),
            ("bnb", Blockchain::BSC) => native_token_address.clone(),
            _ => buy_token.to_string(),
        };

        println!("Processed tokens for 0x API: sell_token={}, buy_token={}", sell_token, buy_token);

        let chain_id = match blockchain {
            Blockchain::ETH => std::env::var(ENV_CHAIN_ID_ETH)
                .unwrap_or_else(|_| config.chain_id_eth.to_string()),
            Blockchain::BSC => std::env::var(ENV_CHAIN_ID_BSC)
                .unwrap_or_else(|_| config.chain_id_bsc.to_string()),
            Blockchain::BASE => std::env::var(ENV_CHAIN_ID_BASE)
                .unwrap_or_else(|_| config.chain_id_base.to_string()),
            Blockchain::SOL => panic!("Solana not supported by EVM trader service"),
        };

        let params = [
            ("chainId", chain_id.to_string()),
            ("sellToken", sell_token),
            ("buyToken", buy_token),
            ("sellAmount", sell_amount.to_string()),
            ("taker", wallet_address.to_string()),
            ("slippageBps", format!("{}", SLIPPAGE_BPS)),
            ("feeRecipient", self.admin_fee_address.clone()),
            ("buyTokenPercentageFee", format!("{}", ADMIN_FEE_BPS)),
            ("enableUniversalRouter", "true".to_string()),
            ("intentOnFilling", "true".to_string()),
            ("skipValidation", "true".to_string()),
        ];

        println!("Sending 0x API swap quote request to: {}", api_url);
        println!("Request parameters:");
        for (key, value) in &params {
            println!("  {} = {}", key, value);
        }

        let response = self.client
            .get(&api_url)
            .query(&params)
            .send()
            .await
            .map_err(|e| {
                println!("Failed to get swap quote: {}", e);
                BotError::blockchain_error("Failed to get swap quote from 0x API".to_string())
            })?;

        if !response.status().is_success() {
            let error_text = response.text().await.unwrap_or_default();
            println!("0x API error: {}", error_text);
            return Err(BotError::blockchain_error("Failed to get swap quote from 0x API".to_string()));
        }

        let quote_response: ZeroExQuoteResponse = response
            .json()
            .await
            .map_err(|e| {
                println!("Failed to parse quote response: {}", e);
                BotError::blockchain_error("Failed to parse quote response from 0x API".to_string())
            })?;

        Ok(quote_response)
    }



    async fn check_token_balance(
        &self,
        client: &Arc<SignerMiddleware<Provider<Http>, LocalWallet>>,
        token_address: Address,
        amount_needed: U256,
    ) -> Result<bool, BotError> {
        println!("Checking token balance for {} on address {}", token_address, client.address());

        let function_selector = ethers::utils::id("balanceOf(address)");
        let selector_bytes = &function_selector.to_vec()[0..4];

        let params = ethers::abi::encode(&[
            ethers::abi::Token::Address(client.address()),
        ]);

        let mut data = Vec::with_capacity(4 + params.len());
        data.extend_from_slice(selector_bytes);
        data.extend_from_slice(&params);

        let tx = TransactionRequest::new()
            .to(token_address)
            .data(data.clone());
        let typed_tx: TypedTransaction = tx.into();

        let balance_bytes = match client.call(&typed_tx, None).await {
            Ok(bytes) => bytes,
            Err(e) => {
                println!("Failed to check token balance: {}", e);
                return Err(BotError::blockchain_error(format!("Failed to check token balance: {}", e)));
            }
        };

        let balance = if balance_bytes.len() >= 32 {
            U256::from_big_endian(&balance_bytes[0..32])
        } else {
            U256::zero()
        };

        println!("Token balance: {}, needed: {}", balance, amount_needed);

        if balance < amount_needed {
            println!("Insufficient token balance");
            return Ok(false);
        }

        println!("User has enough token balance");
        Ok(true)
    }


    async fn sign_typed_data(
        &self,
        wallet: &LocalWallet,
        client: &Arc<SignerMiddleware<Provider<Http>, LocalWallet>>,
        typed_data_json: serde_json::Value,
    ) -> Result<Signature, BotError> {

        println!("Trying to sign with wallet.sign_typed_data...");
        match serde_json::from_value::<TypedData>(typed_data_json.clone()) {
            Ok(typed_data) => {
                match wallet.sign_typed_data(&typed_data).await {
                    Ok(sig) => {
                        println!("Successfully signed with wallet.sign_typed_data");
                        return Ok(sig);
                    },
                    Err(e) => {
                        println!("Error signing with wallet.sign_typed_data: {}", e);
                        // Continue to next approach
                    }
                }
            },
            Err(e) => {
                println!("Error converting to TypedData: {}", e);
                // Continue to next approach
            }
        }

        // Approach 2: Try using the eth_signTypedData_v4 RPC method
        println!("Trying to sign with eth_signTypedData_v4...");
        let typed_data_str = typed_data_json.to_string();
        match client.provider().request::<[String; 2], Signature>(
            "eth_signTypedData_v4",
            [
                format!("{:?}", client.address()),
                typed_data_str.clone()
            ]
        ).await {
            Ok(sig) => {
                println!("Successfully signed with eth_signTypedData_v4");
                return Ok(sig);
            },
            Err(e) => {
                println!("Error signing with eth_signTypedData_v4: {}", e);
                // Continue to next approach
            }
        }

        // Approach 3: Try using personal_sign as a fallback
        println!("Trying to sign with personal_sign as fallback...");
        // Hash the typed data to create a message to sign
        let hash = ethers::utils::keccak256(typed_data_str.as_bytes());
        match wallet.sign_message(&hash).await {
            Ok(sig) => {
                println!("Successfully signed with personal_sign");
                return Ok(sig);
            },
            Err(e) => {
                println!("Error signing with personal_sign: {}", e);
                return Err(BotError::blockchain_error(format!("Failed to sign EIP-712 data with any method: {}", e)));
            }
        }
    }

    pub async fn check_user_balance(
        &self,
        blockchain: &Blockchain,
        wallet: &Wallet,
        token_address: &str,
        amount: u128,
    ) -> Result<bool, BotError> {
        println!("Checking if user has enough balance for token {} on {}", token_address, blockchain.as_str());

        let private_key = wallet.private_key.clone();

        let chain_id = match blockchain {
            Blockchain::ETH => 1u64,
            Blockchain::BSC => 56u64,
            Blockchain::BASE => 8453u64,
            Blockchain::SOL => panic!("Solana not supported by EVM trader service"),
        };

        let wallet = match LocalWallet::from_str(&private_key) {
            Ok(wallet) => wallet.with_chain_id(chain_id),
            Err(e) => {
                println!("Failed to create wallet: {}", e);
                return Err(BotError::blockchain_error("Failed to create wallet for balance check".to_string()));
            }
        };

        let config = crate::config::AppConfig::get();
        let rpc_url = match blockchain {
            Blockchain::ETH => std::env::var(ENV_RPC_URL_ETH)
                .unwrap_or_else(|_| config.rpc_url_eth.clone()),
            Blockchain::BSC => std::env::var(ENV_RPC_URL_BSC)
                .unwrap_or_else(|_| config.rpc_url_bsc.clone()),
            Blockchain::BASE => std::env::var(ENV_RPC_URL_BASE)
                .unwrap_or_else(|_| config.rpc_url_base.clone()),
            Blockchain::SOL => panic!("Solana not supported by EVM trader service"),
        };

        let provider = match Provider::<Http>::try_from(rpc_url) {
            Ok(provider) => provider,
            Err(e) => {
                println!("Failed to create provider: {}", e);
                return Err(BotError::blockchain_error("Failed to connect to blockchain network".to_string()));
            }
        };

        let client = SignerMiddleware::new(provider, wallet.clone());
        let client = Arc::new(client);

        let is_native = token_address.to_lowercase() == "eth" ||
                        token_address.to_lowercase() == "bnb" ||
                        token_address.to_lowercase() == "base" ||
                        token_address.to_lowercase() == NATIVE_TOKEN_ADDRESS.to_lowercase();

        if is_native {
            let balance = match client.get_balance(client.address(), None).await {
                Ok(bal) => bal,
                Err(e) => {
                    println!("Failed to get native token balance: {}", e);
                    return Err(BotError::blockchain_error("Failed to check user balance".to_string()));
                }
            };

            let amount_u256 = U256::from(amount);

            println!("Native token balance: {}, needed: {}", balance, amount_u256);

            // For native tokens, we need to be more careful with gas costs
            let gas_buffer = U256::from(21000) * U256::from(5000000000u64); // Basic gas buffer

            // Calculate a percentage-based buffer (configurable % of balance)
            let percentage_buffer = balance * U256::from((defaults::GAS_BUFFER_PERCENTAGE * 10.0) as u64) / U256::from(1000);
            let _buffer = if percentage_buffer > gas_buffer { percentage_buffer } else { gas_buffer };

            // Check if the requested amount is too close to the total balance
            let is_selling_almost_all = balance > U256::zero() &&
                                       (amount_u256 * U256::from(995) > balance * U256::from(990));

            if is_selling_almost_all {
                // This is likely a "sell 100%" case with potential issues
                println!("Detected attempt to use almost entire native token balance");

                // If the amount is exactly equal to balance, adjust it down by 1%
                if amount_u256 >= balance.saturating_sub(gas_buffer) {
                    let adjusted_amount = balance.saturating_sub(gas_buffer) * U256::from(95) / U256::from(100);
                    println!("Adjusting amount from {} to {} (95% of usable balance)", amount_u256, adjusted_amount);
                    return Err(BotError::blockchain_error(format!(
                        "Cannot use exact balance due to gas fees. Try using 95% instead of 100% (approximately {} {}).",
                        ethers::utils::format_units(adjusted_amount, "ether").unwrap_or_else(|_| "unknown".to_string()),
                        match blockchain {
                            Blockchain::ETH => "ETH",
                            Blockchain::BSC => "BNB",
                            Blockchain::BASE => "ETH",
                            _ => "tokens"
                        }
                    )));
                }
            }

            if balance < amount_u256 {
                println!("Insufficient native token balance");

                // Calculate how much more is needed
                let shortfall = amount_u256 - balance;
                let shortfall_formatted = ethers::utils::format_units(shortfall, "ether")
                    .unwrap_or_else(|_| "unknown".to_string());

                return Err(BotError::blockchain_error(format!(
                    "Insufficient balance. You have {} but need {} to complete this transaction. You need {} more {}.",
                    ethers::utils::format_units(balance, "ether").unwrap_or_else(|_| "unknown".to_string()),
                    ethers::utils::format_units(amount_u256, "ether").unwrap_or_else(|_| "unknown".to_string()),
                    shortfall_formatted,
                    match blockchain {
                        Blockchain::ETH => "ETH",
                        Blockchain::BSC => "BNB",
                        Blockchain::BASE => "ETH",
                        _ => "tokens"
                    }
                )));
            }
        } else {

            let function_selector = ethers::utils::id("balanceOf(address)");
            let selector_bytes = &function_selector.to_vec()[0..4];

            let params = ethers::abi::encode(&[
                ethers::abi::Token::Address(client.address()),
            ]);

            let mut data = Vec::with_capacity(4 + params.len());
            data.extend_from_slice(selector_bytes);
            data.extend_from_slice(&params);

            let token_address = match Address::from_str(token_address) {
                Ok(addr) => addr,
                Err(e) => {
                    println!("Invalid token address: {}", e);
                    return Err(BotError::blockchain_error("Invalid token address".to_string()));
                }
            };

            let tx = TransactionRequest::new()
                .to(token_address)
                .data(data.clone());
            let typed_tx: TypedTransaction = tx.into();

            let balance_bytes = match client.call(&typed_tx, None).await {
                Ok(bytes) => bytes,
                Err(e) => {
                    println!("Failed to check token balance: {}", e);
                    return Err(BotError::blockchain_error(format!("Failed to check token balance: {}", e)));
                }
            };

            let balance = if balance_bytes.len() >= 32 {
                U256::from_big_endian(&balance_bytes[0..32])
            } else {
                U256::zero()
            };

            let amount_u256 = U256::from(amount);

            println!("Token balance: {}, needed: {}", balance, amount_u256);

            // Calculate a buffer for potential gas fees, token taxes, and rounding errors
            let min_buffer = U256::from(1000); // Minimum fixed buffer for dust amounts
            let percentage_buffer = balance * U256::from((defaults::GAS_BUFFER_PERCENTAGE * 10.0) as u64) / U256::from(1000); // Configurable buffer
            let _buffer = if percentage_buffer > min_buffer { percentage_buffer } else { min_buffer };

            // Check if the requested amount is too close to the total balance (within buffer %)
            let buffer_factor = (1000.0 - defaults::GAS_BUFFER_PERCENTAGE * 10.0) as u64;
            let is_selling_almost_all = balance > U256::zero() &&
                                       (amount_u256 * U256::from(buffer_factor) > balance * U256::from(990));

            if is_selling_almost_all {
                // This is likely a "sell 100%" case with potential issues
                println!("Detected attempt to sell almost entire balance");

                // If the amount is exactly equal to balance, adjust it down by 1%
                if amount_u256 >= balance {
                    let adjusted_amount = balance * U256::from(99) / U256::from(100);
                    println!("Adjusting amount from {} to {} (99% of balance)", amount_u256, adjusted_amount);
                    return Err(BotError::blockchain_error(format!(
                        "Cannot sell exact balance due to potential gas fees or price fluctuations. Try selling 99% instead of 100% (approximately {} tokens).",
                        adjusted_amount
                    )));
                }
            }

            // Standard balance check with detailed error message
            if balance < amount_u256 {
                println!("Insufficient token balance");

                // Calculate how much more is needed
                let shortfall = amount_u256 - balance;
                let shortfall_percentage = shortfall * U256::from(100) / amount_u256;

                return Err(BotError::blockchain_error(format!(
                    "Insufficient token balance. You have {} tokens but need {} tokens to complete this transaction. You need {} more tokens ({}%).",
                    balance, amount_u256, shortfall, shortfall_percentage
                )));
            }
        }

        println!("User has enough balance");
        Ok(true)
    }

    /// Validate that the wallet is compatible with the target blockchain network
    async fn validate_network_compatibility(&self, wallet: &Wallet, blockchain: &Blockchain) -> Result<(), String> {
        println!("Validating network compatibility for {} on {}", wallet.address, blockchain.as_str());

        // Check if the wallet address format is valid for the blockchain
        match blockchain {
            Blockchain::ETH | Blockchain::BSC | Blockchain::BASE => {
                // EVM addresses should start with 0x and be 42 characters long
                if !wallet.address.starts_with("0x") || wallet.address.len() != 42 {
                    return Err(format!("Invalid EVM address format for {}: {}", blockchain.as_str(), wallet.address));
                }

                // Try to parse as a valid Ethereum address
                if let Err(_) = wallet.address.parse::<ethers::types::Address>() {
                    return Err(format!("Invalid EVM address for {}: {}", blockchain.as_str(), wallet.address));
                }
            },
            Blockchain::SOL => {
                return Err("Solana is not supported by EVM trader service".to_string());
            }
        }

        println!("Network compatibility validation passed for {} on {}", wallet.address, blockchain.as_str());
        Ok(())
    }

    pub async fn execute_swap(
        &self,
        wallet: &Wallet,
        quote: ZeroExQuoteResponse,
        blockchain: &Blockchain,
        chat_id: Option<i64>,
    ) -> Result<String, BotError> {
        println!("Executing swap on {}", blockchain.as_str());

        // Validate that we're on the correct network
        if let Err(e) = self.validate_network_compatibility(wallet, blockchain).await {
            return Err(BotError::blockchain_error(format!("Network validation failed: {}", e)));
        }

        // Get real user ID from database using actual chat_id
        let user_id = if let Some(chat_id_val) = chat_id {
            match crate::service::DbService::find_user_by_chat_id(chat_id_val).await {
                Ok(Some(user)) => user.id.unwrap_or_else(|| ObjectId::new()),
                Ok(None) => {
                    println!("⚠️ No user found for chat_id: {}", chat_id_val);
                    ObjectId::new()
                }
                Err(e) => {
                    println!("❌ Error finding user by chat_id {}: {}", chat_id_val, e);
                    ObjectId::new()
                }
            }
        } else {
            println!("⚠️ No chat_id provided for EVM swap, using new ObjectId");
            ObjectId::new()
        };


        let config = crate::config::AppConfig::get();
        let native_token_address = std::env::var(ENV_TOKEN_ADDRESS_NATIVE)
            .unwrap_or_else(|_| config.token_address_native.clone());

        let is_native_token = quote.sellToken.to_lowercase() == native_token_address.to_lowercase();

        if !is_native_token {
            let sell_amount = match U256::from_dec_str(&quote.sellAmount) {
                Ok(amount) => amount.as_u128(),
                Err(_) => 0,
            };

            if sell_amount > 0 {
                match self.check_user_balance(blockchain, wallet, &quote.sellToken, sell_amount).await {
                    Ok(_) => println!("Balance check passed for non-native token"),
                    Err(e) => {
                        println!("Balance check failed: {}", e);
                        return Err(e);
                    }
                }
            }
        } else {
            println!("Native token detected ({}), skipping balance check and passing directly to 0x API",
                     match blockchain {
                         Blockchain::ETH => "ETH",
                         Blockchain::BSC => "BNB",
                         Blockchain::BASE => "BASE",
                         _ => "Unknown"
                     });
        }

        let private_key = wallet.private_key.clone();

        let chain_id = match blockchain {
            Blockchain::ETH => std::env::var(ENV_CHAIN_ID_ETH)
                .unwrap_or_else(|_| config.chain_id_eth.to_string())
                .parse::<u64>()
                .unwrap_or(1u64),
            Blockchain::BSC => std::env::var(ENV_CHAIN_ID_BSC)
                .unwrap_or_else(|_| config.chain_id_bsc.to_string())
                .parse::<u64>()
                .unwrap_or(56u64),
            Blockchain::BASE => std::env::var(ENV_CHAIN_ID_BASE)
                .unwrap_or_else(|_| config.chain_id_base.to_string())
                .parse::<u64>()
                .unwrap_or(8453u64),
            Blockchain::SOL => panic!("Solana not supported by EVM trader service"),
        };

        println!("Using chain ID: {}", chain_id);

        let wallet = match LocalWallet::from_str(&private_key) {
            Ok(wallet) => wallet.with_chain_id(chain_id),
            Err(e) => {
                println!("Failed to create wallet: {}", e);
                return Err(BotError::blockchain_error("Failed to create wallet for transaction".to_string()));
            }
        };

        let rpc_url = match blockchain {
            Blockchain::ETH => std::env::var(ENV_RPC_URL_ETH)
                .unwrap_or_else(|_| config.rpc_url_eth.clone()),
            Blockchain::BSC => std::env::var(ENV_RPC_URL_BSC)
                .unwrap_or_else(|_| config.rpc_url_bsc.clone()),
            Blockchain::BASE => std::env::var(ENV_RPC_URL_BASE)
                .unwrap_or_else(|_| config.rpc_url_base.clone()),
            Blockchain::SOL => panic!("Solana not supported by EVM trader service"),
        };

        println!("Using RPC URL for {}: {}", blockchain.as_str(), rpc_url);

        let provider = match Provider::<Http>::try_from(rpc_url) {
            Ok(provider) => provider,
            Err(e) => {
                println!("Failed to create provider: {}", e);
                return Err(BotError::blockchain_error("Failed to connect to blockchain network".to_string()));
            }
        };

        let client = SignerMiddleware::new(provider, wallet.clone());
        let client = Arc::new(client);

        let to_address = match Address::from_str(&quote.transaction.to) {
            Ok(address) => address,
            Err(e) => {
                println!("Invalid to address: {}", e);
                return Err(BotError::blockchain_error("Invalid transaction data".to_string()));
            }
        };

        let mut data = match hex::decode(&quote.transaction.data[2..]) {
            Ok(data) => data,
            Err(e) => {
                println!("Invalid data: {}", e);
                return Err(BotError::blockchain_error("Invalid transaction data".to_string()));
            }
        };


        if let Some(permit2) = &quote.permit2 {
            println!("Permit2 signature required, signing message...");

            let is_native_token = quote.sellToken.to_lowercase() == native_token_address.to_lowercase();

            if is_native_token {
                println!("Native token detected ({}), skipping allowance check",
                         match blockchain {
                             Blockchain::ETH => "ETH",
                             Blockchain::BSC => "BNB",
                             Blockchain::BASE => "BASE",
                             _ => "Unknown"
                         });
            } else {

                if let Some(issues) = &quote.issues {
                    if let Some(allowance_issue) = &issues.allowance {
                        println!("Allowance issue detected: {:?}", allowance_issue);

                        let token_address = match Address::from_str(&quote.sellToken) {
                            Ok(addr) => addr,
                            Err(e) => {
                                println!("Invalid token address: {}", e);
                                return Err(BotError::blockchain_error("Invalid token address".to_string()));
                            }
                        };

                        let spender_address = match Address::from_str(&allowance_issue.spender) {
                            Ok(addr) => addr,
                            Err(e) => {
                                println!("Invalid spender address: {}", e);
                                return Err(BotError::blockchain_error("Invalid spender address".to_string()));
                            }
                        };

                        println!("Setting approval for token {} to spender {}", token_address, spender_address);


                        println!("Creating direct approval transaction for the token");

                        let function_selector = ethers::utils::id("approve(address,uint256)");
                        let selector_bytes = &function_selector.to_vec()[0..4];

                        let max_uint = U256::MAX;
                        let params = ethers::abi::encode(&[
                            ethers::abi::Token::Address(spender_address),
                            ethers::abi::Token::Uint(max_uint),
                        ]);

                        let mut approve_data = Vec::with_capacity(4 + params.len());
                        approve_data.extend_from_slice(selector_bytes);
                        approve_data.extend_from_slice(&params);

                        let approve_tx = TransactionRequest::new()
                            .to(token_address)
                            .data(approve_data)
                            .from(client.address());

                        // Send approval transaction
                        println!("Sending approval transaction...");
                        let pending_tx = match client.send_transaction(approve_tx, None).await {
                            Ok(tx) => tx,
                            Err(e) => {
                                println!("Failed to send approval transaction: {}", e);
                                return Err(BotError::blockchain_error(format!("Failed to approve token: {}", e)));
                            }
                        };

                        // Wait for the transaction to be mined
                        println!("Waiting for approval transaction to be mined...");
                        match pending_tx.await {
                            Ok(receipt) => {
                                if let Some(receipt) = receipt {
                                    println!("Approval transaction confirmed: {:?}", receipt.transaction_hash);

                                    // Check if the transaction was successful
                                    if let Some(status) = receipt.status {
                                        if status == U64::from(0) {
                                            println!("Approval transaction failed with status 0");
                                            return Err(BotError::blockchain_error("Approval transaction failed on-chain. Please check your token balance.".to_string()));
                                        }
                                    }
                                } else {
                                    println!("Approval transaction receipt not available");
                                }
                            },
                            Err(e) => {
                                println!("Approval transaction failed: {}", e);
                                return Err(BotError::blockchain_error(format!("Approval transaction failed: {}", e)));
                            }
                        }

                        // Wait a moment for the approval to be recognized
                        println!("Waiting for approval to be recognized...");
                        tokio::time::sleep(tokio::time::Duration::from_secs(5)).await;

                        println!("Token approved for Permit2");
                    } else {
                        println!("No allowance issue detected, token already approved for Permit2");
                    }
                }
            }

            // For EIP-712 signing, we need to handle different RPC providers
            println!("Preparing to sign Permit2 data...");

            // Create a JSON representation of the typed data
            let typed_data_json = serde_json::json!({
                "types": permit2.eip712.types,
                "domain": permit2.eip712.domain,
                "primaryType": permit2.eip712.primaryType,
                "message": permit2.eip712.message
            });

            // Log the data for debugging
            let typed_data_str = typed_data_json.to_string();
            println!("Signing EIP-712 data: {}", typed_data_str);

            let signature = self.sign_typed_data(&wallet, &client, typed_data_json).await?;

            println!("Permit2 signature obtained: {}", signature);

            let sig_bytes = signature.to_vec();
            let sig_length = sig_bytes.len() as u32;

            let mut sig_length_bytes = [0u8; 32];
            sig_length_bytes[28..32].copy_from_slice(&sig_length.to_be_bytes());

            data.extend_from_slice(&sig_length_bytes);
            data.extend_from_slice(&sig_bytes);

            println!("Appended signature to transaction data");
        }

        let value = match U256::from_dec_str(&quote.transaction.value) {
            Ok(value) => value,
            Err(e) => {
                println!("Invalid value: {}", e);
                return Err(BotError::blockchain_error("Invalid transaction data".to_string()));
            }
        };

        let gas_price = match U256::from_dec_str(&quote.transaction.gasPrice) {
            Ok(gas_price) => gas_price,
            Err(e) => {
                println!("Invalid gas price: {}", e);
                return Err(BotError::blockchain_error("Invalid transaction data".to_string()));
            }
        };

        let gas_limit = match U256::from_dec_str(&quote.transaction.gas) {
            Ok(gas_limit) => gas_limit,
            Err(e) => {
                println!("Invalid gas limit: {}", e);
                return Err(BotError::blockchain_error("Invalid transaction data".to_string()));
            }
        };

        let data_length = data.len();

        let tx = TransactionRequest::new()
            .to(to_address)
            .value(value)
            .data(data)
            .gas_price(gas_price)
            .gas(gas_limit);

        println!("Checking if user has enough balance...");
        let sell_token_address = match Address::from_str(&quote.sellToken) {
            Ok(addr) => addr,
            Err(e) => {
                println!("Invalid sell token address: {}", e);
                return Err(BotError::blockchain_error("Invalid sell token address".to_string()));
            }
        };

        let is_native = sell_token_address.to_string().to_lowercase() == native_token_address.to_lowercase();

        if is_native {
            let balance = match client.get_balance(client.address(), None).await {
                Ok(bal) => bal,
                Err(e) => {
                    println!("Failed to get native token balance: {}", e);
                    return Err(BotError::blockchain_error("Failed to check user balance".to_string()));
                }
            };

            let required = value + (gas_price * gas_limit);
            // Add a small buffer for potential gas price fluctuations
            let gas_buffer = gas_limit * U256::from(1000000000u64); // Additional buffer for gas price spikes
            let total_required = required + gas_buffer;

            if balance < required {
                println!("Insufficient native token balance. Have: {}, Need: {}", balance, required);

                // Calculate how much more is needed with a detailed message
                let shortfall = required - balance;
                let shortfall_formatted = ethers::utils::format_units(shortfall, "ether")
                    .unwrap_or_else(|_| "unknown".to_string());

                let native_token_name = match blockchain {
                    Blockchain::ETH => "ETH",
                    Blockchain::BSC => "BNB",
                    Blockchain::BASE => "ETH",
                    _ => "tokens"
                };

                return Err(BotError::blockchain_error(format!(
                    "Insufficient balance for transaction. You have {} {} but need {} {} (including gas fees). You need {} {} more.",
                    ethers::utils::format_units(balance, "ether").unwrap_or_else(|_| "unknown".to_string()),
                    native_token_name,
                    ethers::utils::format_units(required, "ether").unwrap_or_else(|_| "unknown".to_string()),
                    native_token_name,
                    shortfall_formatted,
                    native_token_name
                )));
            }

            // Even if we have enough, warn if we're cutting it close
            if balance < total_required && balance >= required {
                println!("Warning: Low native token balance for gas fluctuations. Have: {}, Recommended: {}",
                         balance, total_required);
            }
        } else {

            println!("Non-native token detected, allowance check already handled in Permit2 section");
        }

        // Get the current nonce
        println!("Getting current nonce...");            let min_buffer = U256::from(1000); // Minimum fixed buffer for dust amounts

        let nonce = match client.get_transaction_count(client.address(), None).await {
            Ok(nonce) => nonce,
            Err(e) => {
                println!("Failed to get nonce: {}", e);
                return Err(BotError::blockchain_error(format!("Failed to get nonce: {}", e)));
            }
        };

        // Update the transaction with the nonce
        let tx = tx.nonce(nonce);

        // Log the transaction details
        println!("Transaction details:");
        println!("  To: {:?}", to_address);
        println!("  Value: {:?}", value);
        println!("  Gas Price: {:?}", gas_price);
        println!("  Gas Limit: {:?}", gas_limit);
        println!("  Nonce: {:?}", nonce);
        println!("  Data length: {} bytes", data_length);

        // Send the transaction
        println!("Sending transaction to the network...");
        let pending_tx = match client.send_transaction(tx, None).await {
            Ok(tx) => tx,
            Err(e) => {
                println!("Failed to send transaction: {}", e);
                // Check for specific error messages
                let error_msg = e.to_string();
                if error_msg.contains("insufficient funds") {
                    return Err(BotError::blockchain_error("Insufficient funds to complete this transaction".to_string()));
                } else if error_msg.contains("TRANSFER_FROM_FAILED") {
                    return Err(BotError::blockchain_error("Token transfer failed. Please check your token allowance and balance.".to_string()));
                } else {
                    return Err(BotError::blockchain_error(format!("Failed to send transaction: {}", e)));
                }
            }
        };

        println!("Transaction hash: {:?}", pending_tx.tx_hash());

        // Wait for the transaction to be mined with timeout
        println!("Transaction sent! Waiting for confirmation...");
        let receipt = match tokio::time::timeout(
            std::time::Duration::from_secs(defaults::SWAP_TIMEOUT_SECONDS), // Configurable timeout for confirmation
            pending_tx
        ).await {
            Ok(Ok(receipt)) => receipt,
            Ok(Err(e)) => {
                println!("Transaction failed: {}", e);
                // Check for specific error messages
                let error_msg = e.to_string();
                if error_msg.contains("TRANSFER_FROM_FAILED") {
                    return Err(BotError::blockchain_error("Token transfer failed. Please check your token allowance and balance.".to_string()));
                } else {
                    return Err(BotError::blockchain_error(format!("Transaction failed: {}", e)));
                }
            },
            Err(_) => {
                println!("Transaction confirmation timeout after {} seconds", defaults::SWAP_TIMEOUT_SECONDS);
                return Err(BotError::blockchain_error("Transaction confirmation timeout. The transaction may still be pending.".to_string()));
            }
        };

        // Check if the transaction was successful
        if let Some(receipt_value) = &receipt {
            if let Some(status) = receipt_value.status {
                if status == U64::from(0) {
                    println!("Transaction failed with status 0");

                    let tx_hash = receipt_value.transaction_hash;
                    println!("Failed transaction hash: 0x{:x}", tx_hash);

                    let error_message = if let Some(_permit2) = &quote.permit2 {
                        if quote.sellToken.to_lowercase() == native_token_address.to_lowercase() {
                            "Transaction failed: TRANSFER_FROM_FAILED. This usually means you don't have enough native tokens (BNB/ETH/BASE). Please check your balance and try again with a smaller amount."
                        } else if blockchain == &Blockchain::BSC && quote.sellToken.to_lowercase() == config.token_address_wbnb.to_lowercase() {
                            "Transaction failed: TRANSFER_FROM_FAILED. This usually means you don't have enough WBNB tokens. Please try using native BNB directly or wrap your BNB to WBNB first."
                        } else {
                            "Transaction failed: TRANSFER_FROM_FAILED. Please check your token balance and make sure you have approved the token for trading."
                        }
                    } else {
                        "Transaction failed on-chain. Check the transaction details for more information."
                    };

                    return Err(BotError::blockchain_error(error_message.to_string()));
                }
            }
        }

        // Get the transaction hash
        let tx_hash = format!("0x{:x}", receipt.unwrap().transaction_hash);

        // Determine if this is a buy or sell transaction
        let is_buy = quote.sellToken.to_lowercase() == native_token_address.to_lowercase();

        // Store the transaction hash for later use
        let transaction_hash = tx_hash.clone();

        // Save the trade to the database first to get trade_id
        use crate::model::Trade;
        use crate::model::trade::TradeStatus;
        use mongodb::bson::Decimal128;
        use chrono::Utc;
        use std::str::FromStr;

        // Get user information for the trade record
        let (user_first_name, user_username) = if let Some(chat_id) = chat_id {
            match crate::service::DbService::find_user_by_chat_id(chat_id).await {
                Ok(Some(user)) => (Some(user.first_name), user.username),
                Ok(None) => (None, None),
                Err(_) => (None, None),
            }
        } else {
            (None, None)
        };

        // Get token address (the non-native token in the pair)
        let token_address = if is_buy {
            quote.buyToken.clone()
        } else {
            quote.sellToken.clone()
        };

        // Use the provided chat_id directly (no derivation needed)
        let actual_chat_id = match chat_id {
            Some(id) => id,
            None => {
                println!("⚠️ WARNING: No chat_id provided, trade will not be saved properly");
                0 // Fallback to 0 if no chat_id provided
            }
        };

        // Get actual user ID from database using actual chat_id - CRITICAL: Don't use fallback ObjectId::new()
        let user_id = if actual_chat_id != 0 {
            match crate::service::DbService::find_user_by_chat_id(actual_chat_id).await {
                Ok(Some(user)) => {
                    if let Some(id) = user.id {
                        id
                    } else {
                        println!("❌ CRITICAL: User found but has no ID for chat_id: {}", actual_chat_id);
                        return Err(BotError::validation_error("User has no ID in database".to_string()));
                    }
                }
                Ok(None) => {
                    println!("❌ CRITICAL: No user found for chat_id: {}", actual_chat_id);
                    return Err(BotError::validation_error(format!("No user found for chat_id: {}", actual_chat_id)));
                }
                Err(e) => {
                    println!("❌ CRITICAL: Error finding user by chat_id {}: {}", actual_chat_id, e);
                    return Err(BotError::database_error(format!("Failed to find user: {}", e)));
                }
            }
        } else {
            println!("❌ CRITICAL: No valid chat_id provided for EVM operation");
            return Err(BotError::validation_error("chat_id is required for EVM operations".to_string()));
        };

        // Get token symbol and name from contract
        let token_symbol = self.get_token_symbol(&token_address, blockchain).await
            .unwrap_or_else(|_| format!("TOKEN_{}", &token_address[2..8].to_uppercase()));
        let token_name = self.get_token_name(&token_address, blockchain).await
            .unwrap_or_else(|_| format!("Token {}", &token_address[2..8].to_uppercase()));

        // Calculate real gas fee from transaction data
        let gas_fee_eth = {
            if let Ok(gas_used) = quote.transaction.gas.parse::<u64>() {
                if let Ok(gas_price) = quote.transaction.gasPrice.parse::<u64>() {
                    Some((gas_used * gas_price) as f64 / 1e18) // Convert wei to ETH
                } else { None }
            } else { None }
        };

        // Calculate real amount out (not placeholder)
        let real_amount_out = quote.buyAmount.parse::<f64>()
            .map_err(|e| BotError::validation_error(format!("Invalid buyAmount: {}", e)))?;

        // Get real admin fee percentage for this blockchain
        let real_admin_fee_percentage = self.admin_fee_service.get_admin_fee_percentage_for_blockchain(blockchain).await
            .unwrap_or(defaults::DEFAULT_ADMIN_FEE_PERCENTAGE);

        // Get native token symbol based on blockchain
        let native_symbol = match blockchain {
            Blockchain::ETH => "ETH",
            Blockchain::BSC => "BNB",
            Blockchain::BASE => "ETH",
            Blockchain::SOL => "SOL", // Should not happen in EVM context
        };

        // Calculate amounts based on trade type
        let (native_token_amount, token_amount) = if is_buy {
            // For buy: native_token_amount = ETH/BNB spent, token_amount = tokens received
            let native_spent = quote.sellAmount.parse::<f64>().unwrap_or(0.0) / 1e18;
            let tokens_received = quote.buyAmount.parse::<f64>().unwrap_or(0.0) / 1e18;
            (Some(native_spent), Some(tokens_received))
        } else {
            // For sell: token_amount = tokens sold, native_token_amount = ETH/BNB received
            let tokens_sold = quote.sellAmount.parse::<f64>().unwrap_or(0.0) / 1e18;
            let native_received = quote.buyAmount.parse::<f64>().unwrap_or(0.0) / 1e18;
            (Some(native_received), Some(tokens_sold))
        };

        // Calculate admin fee amount
        let admin_fee_amount = if let Some(native_amount) = native_token_amount {
            Some((native_amount * real_admin_fee_percentage) / 100.0)
        } else {
            None
        };

        // Create a new trade record with real data
        let trade = Trade {
            id: None,
            user_id,
            user_first_name,
            user_username,
            blockchain: blockchain.clone(),
            token_address: token_address.clone(),
            token_symbol: token_symbol.clone(),
            trade_type: if is_buy { "buy".to_string() } else { "sell".to_string() },

            // Enhanced amount tracking
            native_token_amount,
            token_amount,
            native_token_symbol: Some(native_symbol.to_string()),

            // Legacy field for backward compatibility
            amount_out: Some(if is_buy {
                native_token_amount.unwrap_or(0.0) // For buy, show native tokens spent
            } else {
                token_amount.unwrap_or(0.0) // For sell, show tokens sold
            }),

            status: Some("completed".to_string()),
            timestamp: Utc::now().timestamp(),
            gas_fee: gas_fee_eth,
            hash: Some(tx_hash.clone()),
            block_number: None, // Will be updated when we get transaction receipt
            error_message: None,

            // Admin fee information
            admin_fee_amount,
            admin_fee_percentage: Some(real_admin_fee_percentage),
            admin_fee_status: Some("pending".to_string()),
            admin_fee_collection_method: Some("smart_collection".to_string()),
            admin_fee_token_symbol: Some(native_symbol.to_string()),
            admin_fee_token_address: Some(match blockchain {
                Blockchain::ETH => "******************************************", // ETH
                Blockchain::BSC => "******************************************", // BNB
                Blockchain::BASE => "******************************************", // ETH on Base
                Blockchain::SOL => "So11111111111111111111111111111111111111112", // Should not happen
            }.to_string()),
            admin_fee_transaction_id: None, // Will be set by admin fee processing

            // Legacy fields for backward compatibility
            contract_address: Some(token_address.clone()),
            token_name: Some(token_name),
            amount: Some(Decimal128::from_str(&quote.buyAmount).unwrap_or_else(|_| Decimal128::from_str("0").unwrap())),
            price: Some(Decimal128::from_str(&quote.sellAmount).unwrap_or_else(|_| Decimal128::from_str("0").unwrap())),
            transaction_hash: Some(tx_hash.clone()),
            created_at: Some(Utc::now()),
            updated_at: Some(Utc::now()),
        };

        // Save trade and get the generated trade ID
        let trade_id = match crate::service::DbService::save_trade(&trade).await {
            Ok(saved_trade) => {
                println!("Trade saved to database successfully");
                Some(saved_trade)
            }
            Err(e) => {
                println!("Warning: Failed to save trade to database: {}", e);
                None
            }
        };

        // Process admin fee collection in background (truly async) with trade_id
        let self_clone = self.clone();
        let wallet_address_clone = format!("{:?}", wallet.address());
        let buy_amount = quote.buyAmount.clone();
        let buy_token = quote.buyToken.clone();
        let buy_token_for_approval = quote.buyToken.clone(); // Clone for later use
        let blockchain_clone = blockchain.clone();

        // OPTIMIZED PARALLEL FEE COLLECTION FOR EVM CHAINS
        tokio::spawn(async move {
            let admin_fee_result = if is_buy {
                // For buy operations: Smart parallel fee collection
                self_clone.process_smart_buy_admin_fee_evm(
                    &wallet_address_clone,
                    user_id,
                    trade_id,
                    &quote.sellAmount, // Original native amount user wanted to spend
                    &quote.buyToken,   // Token being purchased
                    &blockchain_clone,
                ).await
            } else {
                // For sell operations: Smart immediate fee collection
                self_clone.process_smart_sell_admin_fee_evm(
                    &wallet_address_clone,
                    user_id,
                    trade_id,
                    &buy_amount,      // Native tokens received
                    &buy_token,       // Token being sold
                    &blockchain_clone,
                ).await
            };

            match admin_fee_result {
                Ok(Some(fee_tx_id)) => {
                    println!("Admin fee collected successfully in background: {}", fee_tx_id);
                }
                Ok(None) => {
                    println!("Admin fee collection skipped (0% fee or disabled)");
                }
                Err(e) => {
                    println!("Admin fee collection failed in background: {}", e);
                }
            }
        });

        // Update the user's trades collection if trade was saved successfully
        if let Some(saved_trade_id) = trade_id {
            if let Ok(Some(user)) = crate::service::DbService::find_user_by_chat_id(actual_chat_id).await {
                if let Some(user_id) = user.id {
                    // Get the user's trades
                    if let Ok(Some(mut user_trades)) = crate::service::DbService::find_user_trades(user_id).await {
                        // Add the trade to the appropriate blockchain list
                        match blockchain {
                            Blockchain::BSC => user_trades.trades.bsc.push(saved_trade_id),
                            Blockchain::ETH => user_trades.trades.eth.push(saved_trade_id),
                            Blockchain::BASE => user_trades.trades.base.push(saved_trade_id),
                            _ => {}
                        }

                        // Save the updated user trades
                        if let Err(e) = crate::service::DbService::save_user_trades(&user_trades).await {
                            println!("Warning: Failed to update user trades: {}", e);
                        }
                    }
                }
            }
        }

        // If this was a buy transaction, automatically approve the token for future sells
        if is_buy {
            let self_clone = self.clone();
            let wallet_clone = wallet.clone();
            let token_address_clone = buy_token_for_approval.clone();
            let blockchain_clone = blockchain.clone();

            tokio::spawn(async move {
                if let Err(e) = self_clone.auto_approve_token_after_buy_with_ethers_wallet(
                    &wallet_clone,
                    &token_address_clone,
                    &blockchain_clone,
                ).await {
                    println!("Warning: Auto-approval after buy failed: {}", e);
                } else {
                    println!("Auto-approval after buy completed successfully for token {}", token_address_clone);
                }
            });
        }

        // Return the transaction hash
        Ok(tx_hash)
    }

    /// Check if user has sufficient balance for trade + admin fee
    pub async fn check_sufficient_balance_for_buy(
        &self,
        wallet_address: &str,
        amount_in_wei: &str,
        blockchain: &Blockchain,
    ) -> Result<bool, BotError> {
        use ethers::prelude::*;
        use ethers::types::{Address, U256};
        use std::str::FromStr;

        // Parse wallet address and amount
        let wallet_addr = Address::from_str(wallet_address)
            .map_err(|e| BotError::validation_error(format!("Invalid wallet address: {}", e)))?;

        let trade_amount = U256::from_dec_str(amount_in_wei)
            .map_err(|e| BotError::validation_error(format!("Invalid amount: {}", e)))?;

        // Get admin fee percentage for this blockchain
        let fee_percentage = match self.admin_fee_service.get_admin_fee_percentage_for_blockchain(blockchain).await {
            Ok(percentage) => percentage,
            Err(_) => defaults::DEFAULT_ADMIN_FEE_PERCENTAGE, // Default fallback
        };

        // Calculate admin fee (fee is taken from received tokens, but we need to account for slippage)
        // Add buffer for slippage and gas fees
        let buffer_percentage = defaults::SLIPPAGE_BUFFER_PERCENTAGE;
        let total_percentage = fee_percentage + buffer_percentage;
        let required_amount = trade_amount + (trade_amount * U256::from((total_percentage * 100.0) as u64) / U256::from(10000));

        // Get RPC URL with fallback
        let rpc_url = self.get_rpc_url_with_fallback(blockchain);

        // Create provider
        let provider = Provider::<Http>::try_from(&rpc_url)
            .map_err(|e| BotError::network_error(format!("Failed to create provider: {}", e)))?;

        // Get user's native token balance
        match tokio::time::timeout(Duration::from_secs(30), provider.get_balance(wallet_addr, None)).await {
            Ok(Ok(balance)) => {
                let has_sufficient = balance >= required_amount;

                if !has_sufficient {
                    let balance_eth = balance.as_u128() as f64 / 1e18;
                    let required_eth = required_amount.as_u128() as f64 / 1e18;
                    let trade_eth = trade_amount.as_u128() as f64 / 1e18;
                    let fee_buffer_eth = (required_amount - trade_amount).as_u128() as f64 / 1e18;

                    println!("Insufficient balance for buy: has {:.6} ETH, needs {:.6} ETH (trade: {:.6} + fee/buffer: {:.6})",
                             balance_eth, required_eth, trade_eth, fee_buffer_eth);
                }

                Ok(has_sufficient)
            }
            Ok(Err(e)) => {
                println!("Failed to get balance: {}", e);
                Err(BotError::network_error(format!("Failed to get balance: {}", e)))
            }
            Err(_) => {
                println!("Timeout getting balance");
                Err(BotError::network_error("Timeout getting balance".to_string()))
            }
        }
    }

    /// Check if user has sufficient token balance for sell + admin fee
    pub async fn check_sufficient_balance_for_sell(
        &self,
        wallet_address: &str,
        token_address: &str,
        sell_amount: &str,
        blockchain: &Blockchain,
    ) -> Result<bool, BotError> {
        use ethers::prelude::*;
        use ethers::types::{Address, U256};
        use std::str::FromStr;

        // Parse addresses and amount
        let wallet_addr = Address::from_str(wallet_address)
            .map_err(|e| BotError::validation_error(format!("Invalid wallet address: {}", e)))?;
        let token_addr = Address::from_str(token_address)
            .map_err(|e| BotError::validation_error(format!("Invalid token address: {}", e)))?;
        let sell_amount_wei = U256::from_dec_str(sell_amount)
            .map_err(|e| BotError::validation_error(format!("Invalid sell amount: {}", e)))?;

        // Get token decimals and user's token balance
        let token_decimals = self.get_token_decimals(token_address, blockchain).await.unwrap_or(18);

        // Get RPC URL with fallback
        let rpc_url = self.get_rpc_url_with_fallback(blockchain);

        // Create provider
        let provider = Provider::<Http>::try_from(&rpc_url)
            .map_err(|e| BotError::network_error(format!("Failed to create provider: {}", e)))?;

        // Get user's token balance using proper ERC20 balanceOf call
        // ERC20 balanceOf(address) function selector: 0x70a08231
        let balance_selector = [0x70, 0xa0, 0x82, 0x31];
        let mut call_data = Vec::new();
        call_data.extend_from_slice(&balance_selector);

        // Encode wallet address parameter (32 bytes, left-padded with zeros)
        let mut address_param = [0u8; 32];
        address_param[12..32].copy_from_slice(wallet_addr.as_bytes());
        call_data.extend_from_slice(&address_param);

        let call_request = TransactionRequest::new()
            .to(token_addr)
            .data(call_data);

        let typed_tx: TypedTransaction = call_request.into();
        match tokio::time::timeout(Duration::from_secs(30), provider.call(&typed_tx, None)).await {
            Ok(Ok(result)) => {
                if result.len() >= 32 {
                    let balance = U256::from_big_endian(&result[0..32]);
                    let has_sufficient = balance >= sell_amount_wei;

                    if !has_sufficient {
                        let balance_tokens = balance.as_u128() as f64 / 10_f64.powi(token_decimals as i32);
                        let required_tokens = sell_amount_wei.as_u128() as f64 / 10_f64.powi(token_decimals as i32);

                        println!("Insufficient token balance for sell: has {:.6} tokens, needs {:.6} tokens",
                                 balance_tokens, required_tokens);
                    }

                    Ok(has_sufficient)
                } else {
                    println!("Invalid balance response from token contract");
                    Ok(false)
                }
            }
            Ok(Err(e)) => {
                println!("Failed to get token balance: {}", e);
                Err(BotError::network_error(format!("Failed to get token balance: {}", e)))
            }
            Err(_) => {
                println!("Timeout getting token balance");
                Err(BotError::network_error("Timeout getting token balance".to_string()))
            }
        }
    }

    /// Get ERC20 token balance using proper ethers-rs pattern
    pub async fn get_token_balance(
        &self,
        wallet_address: &str,
        token_address: &str,
        blockchain: &Blockchain,
    ) -> Result<u64, BotError> {
        use ethers::prelude::*;
        use ethers::types::{Address, U256};
        use std::str::FromStr;

        // Parse addresses
        let wallet_addr = Address::from_str(wallet_address)
            .map_err(|e| BotError::validation_error(format!("Invalid wallet address: {}", e)))?;
        let token_addr = Address::from_str(token_address)
            .map_err(|e| BotError::validation_error(format!("Invalid token address: {}", e)))?;

        // Get RPC URL with fallback
        let rpc_url = self.get_rpc_url_with_fallback(blockchain);

        // Create provider
        let provider = Provider::<Http>::try_from(&rpc_url)
            .map_err(|e| BotError::network_error(format!("Failed to create provider: {}", e)))?;

        // ERC20 balanceOf(address) function selector: 0x70a08231
        let balance_selector = [0x70, 0xa0, 0x82, 0x31];
        let mut call_data = Vec::new();
        call_data.extend_from_slice(&balance_selector);

        // Encode wallet address parameter (32 bytes, left-padded with zeros)
        let mut address_param = [0u8; 32];
        address_param[12..32].copy_from_slice(wallet_addr.as_bytes());
        call_data.extend_from_slice(&address_param);

        let call_request = TransactionRequest::new()
            .to(token_addr)
            .data(call_data);

        let typed_tx: TypedTransaction = call_request.into();
        match tokio::time::timeout(Duration::from_secs(30), provider.call(&typed_tx, None)).await {
            Ok(Ok(result)) => {
                if result.len() >= 32 {
                    let balance = U256::from_big_endian(&result[0..32]);
                    Ok(balance.as_u64())
                } else {
                    println!("Invalid balance response from token contract");
                    Ok(0)
                }
            }
            Ok(Err(e)) => {
                println!("Failed to get token balance: {}", e);
                Err(BotError::network_error(format!("Failed to get token balance: {}", e)))
            }
            Err(_) => {
                println!("Timeout getting token balance");
                Err(BotError::network_error("Timeout getting token balance".to_string()))
            }
        }
    }

    /// Automatically approve token for 0x after a successful buy to make future sells faster (with ethers wallet)
    pub async fn auto_approve_token_after_buy_with_ethers_wallet(
        &self,
        wallet: &ethers::signers::Wallet<ethers::core::k256::ecdsa::SigningKey>,
        token_address: &str,
        blockchain: &Blockchain,
    ) -> Result<(), BotError> {
        println!("Starting auto-approval for token {} after buy", token_address);

        // Wait a bit for the buy transaction to be fully confirmed
        tokio::time::sleep(tokio::time::Duration::from_secs(10)).await;

        let config = crate::config::AppConfig::get();

        // Get chain ID
        let chain_id = match blockchain {
            Blockchain::ETH => std::env::var(ENV_CHAIN_ID_ETH)
                .unwrap_or_else(|_| config.chain_id_eth.to_string())
                .parse::<u64>()
                .unwrap_or(1u64),
            Blockchain::BSC => std::env::var(ENV_CHAIN_ID_BSC)
                .unwrap_or_else(|_| config.chain_id_bsc.to_string())
                .parse::<u64>()
                .unwrap_or(56u64),
            Blockchain::BASE => std::env::var(ENV_CHAIN_ID_BASE)
                .unwrap_or_else(|_| config.chain_id_base.to_string())
                .parse::<u64>()
                .unwrap_or(8453u64),
            Blockchain::SOL => return Err(BotError::blockchain_error("Solana not supported by EVM trader service".to_string())),
        };

        // Get RPC URL
        let rpc_url = match blockchain {
            Blockchain::ETH => std::env::var(ENV_RPC_URL_ETH)
                .unwrap_or_else(|_| config.rpc_url_eth.clone()),
            Blockchain::BSC => std::env::var(ENV_RPC_URL_BSC)
                .unwrap_or_else(|_| config.rpc_url_bsc.clone()),
            Blockchain::BASE => std::env::var(ENV_RPC_URL_BASE)
                .unwrap_or_else(|_| config.rpc_url_base.clone()),
            Blockchain::SOL => return Err(BotError::blockchain_error("Solana not supported by EVM trader service".to_string())),
        };

        // Create provider and client
        let provider = match Provider::<Http>::try_from(rpc_url) {
            Ok(provider) => provider,
            Err(e) => {
                println!("Failed to create provider for auto-approval: {}", e);
                return Err(BotError::blockchain_error("Failed to connect to blockchain network".to_string()));
            }
        };

        let wallet_with_chain = wallet.clone().with_chain_id(chain_id);
        let client = SignerMiddleware::new(provider, wallet_with_chain);
        let client = Arc::new(client);

        // Parse token address
        let token_addr = match Address::from_str(token_address) {
            Ok(addr) => addr,
            Err(e) => {
                println!("Invalid token address for auto-approval: {}", e);
                return Err(BotError::blockchain_error("Invalid token address".to_string()));
            }
        };

        // Get the 0x allowance target (spender address) by making a dummy quote request
        let allowance_target = match self.get_zerox_allowance_target(token_address, blockchain).await {
            Ok(target) => target,
            Err(e) => {
                println!("Failed to get 0x allowance target for auto-approval: {}", e);
                return Err(e);
            }
        };

        let spender_address = match Address::from_str(&allowance_target) {
            Ok(addr) => addr,
            Err(e) => {
                println!("Invalid allowance target address: {}", e);
                return Err(BotError::blockchain_error("Invalid allowance target address".to_string()));
            }
        };

        println!("Auto-approving token {} for spender {}", token_address, allowance_target);

        // Create approval transaction with max uint256 allowance
        let function_selector = ethers::utils::id("approve(address,uint256)");
        let selector_bytes = &function_selector.to_vec()[0..4];

        let max_uint = U256::MAX;
        let params = ethers::abi::encode(&[
            ethers::abi::Token::Address(spender_address),
            ethers::abi::Token::Uint(max_uint),
        ]);

        let mut approve_data = Vec::with_capacity(4 + params.len());
        approve_data.extend_from_slice(selector_bytes);
        approve_data.extend_from_slice(&params);

        // Get current nonce
        let nonce = match client.get_transaction_count(client.address(), None).await {
            Ok(nonce) => nonce,
            Err(e) => {
                println!("Failed to get nonce for auto-approval: {}", e);
                return Err(BotError::blockchain_error("Failed to get nonce".to_string()));
            }
        };

        // Get gas price with buffer
        let gas_price = match client.get_gas_price().await {
            Ok(price) => price,
            Err(e) => {
                println!("Failed to get gas price for auto-approval: {}", e);
                return Err(BotError::blockchain_error("Failed to get gas price".to_string()));
            }
        };

        let gas_price_with_buffer = gas_price * 110 / 100; // 10% buffer

        let approve_tx = TransactionRequest::new()
            .to(token_addr)
            .data(approve_data)
            .from(client.address())
            .gas_price(gas_price_with_buffer)
            .gas(100000u64) // Standard gas limit for approval
            .nonce(nonce);

        // Send approval transaction
        println!("Sending auto-approval transaction...");
        let pending_tx = match client.send_transaction(approve_tx, None).await {
            Ok(tx) => tx,
            Err(e) => {
                println!("Failed to send auto-approval transaction: {}", e);
                return Err(BotError::blockchain_error(format!("Failed to send auto-approval: {}", e)));
            }
        };

        // Wait for confirmation
        match pending_tx.await {
            Ok(receipt) => {
                if let Some(receipt) = receipt {
                    let tx_hash = format!("0x{:x}", receipt.transaction_hash);
                    println!("Auto-approval transaction confirmed: {}", tx_hash);

                    // Check if transaction was successful
                    if receipt.status == Some(U64::from(1)) {
                        println!("Auto-approval successful for token {}", token_address);
                    } else {
                        println!("Auto-approval transaction failed on-chain for token {}", token_address);
                        return Err(BotError::blockchain_error("Auto-approval transaction failed on-chain".to_string()));
                    }
                } else {
                    println!("Auto-approval transaction confirmed but no receipt");
                }
            }
            Err(e) => {
                println!("Auto-approval transaction failed: {}", e);
                return Err(BotError::blockchain_error(format!("Auto-approval transaction failed: {}", e)));
            }
        }

        Ok(())
    }

    /// Automatically approve token for 0x after a successful buy to make future sells faster (with model wallet)
    pub async fn auto_approve_token_after_buy(
        &self,
        wallet: &Wallet,
        token_address: &str,
        blockchain: &Blockchain,
    ) -> Result<(), BotError> {
        println!("Starting auto-approval for token {} after buy", token_address);

        // Wait a bit for the buy transaction to be fully confirmed
        tokio::time::sleep(tokio::time::Duration::from_secs(10)).await;

        let config = crate::config::AppConfig::get();

        // Get chain ID
        let chain_id = match blockchain {
            Blockchain::ETH => std::env::var(ENV_CHAIN_ID_ETH)
                .unwrap_or_else(|_| config.chain_id_eth.to_string())
                .parse::<u64>()
                .unwrap_or(1u64),
            Blockchain::BSC => std::env::var(ENV_CHAIN_ID_BSC)
                .unwrap_or_else(|_| config.chain_id_bsc.to_string())
                .parse::<u64>()
                .unwrap_or(56u64),
            Blockchain::BASE => std::env::var(ENV_CHAIN_ID_BASE)
                .unwrap_or_else(|_| config.chain_id_base.to_string())
                .parse::<u64>()
                .unwrap_or(8453u64),
            Blockchain::SOL => return Err(BotError::blockchain_error("Solana not supported by EVM trader service".to_string())),
        };

        // Get RPC URL
        let rpc_url = match blockchain {
            Blockchain::ETH => std::env::var(ENV_RPC_URL_ETH)
                .unwrap_or_else(|_| config.rpc_url_eth.clone()),
            Blockchain::BSC => std::env::var(ENV_RPC_URL_BSC)
                .unwrap_or_else(|_| config.rpc_url_bsc.clone()),
            Blockchain::BASE => std::env::var(ENV_RPC_URL_BASE)
                .unwrap_or_else(|_| config.rpc_url_base.clone()),
            Blockchain::SOL => return Err(BotError::blockchain_error("Solana not supported by EVM trader service".to_string())),
        };

        // Create provider and client
        let provider = match Provider::<Http>::try_from(rpc_url) {
            Ok(provider) => provider,
            Err(e) => {
                println!("Failed to create provider for auto-approval: {}", e);
                return Err(BotError::blockchain_error("Failed to connect to blockchain network".to_string()));
            }
        };

        let local_wallet = LocalWallet::from_str(&wallet.private_key)
            .map_err(|e| BotError::wallet_error(format!("Invalid private key: {}", e)))?
            .with_chain_id(chain_id);
        let client = SignerMiddleware::new(provider, local_wallet);
        let client = Arc::new(client);

        // Parse token address
        let token_addr = match Address::from_str(token_address) {
            Ok(addr) => addr,
            Err(e) => {
                println!("Invalid token address for auto-approval: {}", e);
                return Err(BotError::blockchain_error("Invalid token address".to_string()));
            }
        };

        // Get the 0x allowance target (spender address) by making a dummy quote request
        let allowance_target = match self.get_zerox_allowance_target(token_address, blockchain).await {
            Ok(target) => target,
            Err(e) => {
                println!("Failed to get 0x allowance target for auto-approval: {}", e);
                return Err(e);
            }
        };

        let spender_address = match Address::from_str(&allowance_target) {
            Ok(addr) => addr,
            Err(e) => {
                println!("Invalid allowance target address: {}", e);
                return Err(BotError::blockchain_error("Invalid allowance target address".to_string()));
            }
        };

        println!("Auto-approving token {} for spender {}", token_address, allowance_target);

        // Create approval transaction with max uint256 allowance
        let function_selector = ethers::utils::id("approve(address,uint256)");
        let selector_bytes = &function_selector.to_vec()[0..4];

        let max_uint = U256::MAX;
        let params = ethers::abi::encode(&[
            ethers::abi::Token::Address(spender_address),
            ethers::abi::Token::Uint(max_uint),
        ]);

        let mut approve_data = Vec::with_capacity(4 + params.len());
        approve_data.extend_from_slice(selector_bytes);
        approve_data.extend_from_slice(&params);

        // Get current nonce
        let nonce = match client.get_transaction_count(client.address(), None).await {
            Ok(nonce) => nonce,
            Err(e) => {
                println!("Failed to get nonce for auto-approval: {}", e);
                return Err(BotError::blockchain_error("Failed to get nonce".to_string()));
            }
        };

        // Get gas price with buffer
        let gas_price = match client.get_gas_price().await {
            Ok(price) => price,
            Err(e) => {
                println!("Failed to get gas price for auto-approval: {}", e);
                return Err(BotError::blockchain_error("Failed to get gas price".to_string()));
            }
        };

        let gas_price_with_buffer = gas_price * 110 / 100; // 10% buffer

        let approve_tx = TransactionRequest::new()
            .to(token_addr)
            .data(approve_data)
            .from(client.address())
            .gas_price(gas_price_with_buffer)
            .gas(100000u64) // Standard gas limit for approval
            .nonce(nonce);

        // Send approval transaction
        println!("Sending auto-approval transaction...");
        let pending_tx = match client.send_transaction(approve_tx, None).await {
            Ok(tx) => tx,
            Err(e) => {
                println!("Failed to send auto-approval transaction: {}", e);
                return Err(BotError::blockchain_error(format!("Failed to send auto-approval: {}", e)));
            }
        };

        // Wait for confirmation
        match pending_tx.await {
            Ok(receipt) => {
                if let Some(receipt) = receipt {
                    let tx_hash = format!("0x{:x}", receipt.transaction_hash);
                    println!("Auto-approval transaction confirmed: {}", tx_hash);

                    // Check if transaction was successful
                    if receipt.status == Some(U64::from(1)) {
                        println!("Auto-approval successful for token {}", token_address);
                    } else {
                        println!("Auto-approval transaction failed on-chain for token {}", token_address);
                        return Err(BotError::blockchain_error("Auto-approval transaction failed on-chain".to_string()));
                    }
                } else {
                    println!("Auto-approval transaction confirmed but no receipt");
                }
            }
            Err(e) => {
                println!("Auto-approval transaction failed: {}", e);
                return Err(BotError::blockchain_error(format!("Auto-approval transaction failed: {}", e)));
            }
        }

        Ok(())
    }

    /// Get the 0x allowance target for a token by making a dummy quote request
    async fn get_zerox_allowance_target(
        &self,
        token_address: &str,
        blockchain: &Blockchain,
    ) -> Result<String, BotError> {
        let config = crate::config::AppConfig::get();
        let native_token_address = std::env::var(ENV_TOKEN_ADDRESS_NATIVE)
            .unwrap_or_else(|_| config.token_address_native.clone());

        // Make a small dummy quote request to get the allowance target
        let sell_amount = "1000000000000000000"; // 1 token in wei (18 decimals)

        let base_url = match blockchain {
            Blockchain::ETH => "https://api.0x.org",
            Blockchain::BSC => "https://bsc.api.0x.org",
            Blockchain::BASE => "https://base.api.0x.org",
            _ => return Err(BotError::blockchain_error("Unsupported blockchain for 0x".to_string())),
        };

        let url = format!(
            "{}/swap/v1/price?sellToken={}&buyToken={}&sellAmount={}",
            base_url,
            token_address,
            native_token_address,
            sell_amount
        );

        let client = reqwest::Client::new();
        let response = match client.get(&url).send().await {
            Ok(response) => response,
            Err(e) => {
                println!("Failed to get 0x price for allowance target: {}", e);
                return Err(BotError::network_error(format!("Failed to get 0x price: {}", e)));
            }
        };

        if !response.status().is_success() {
            println!("0x API error for allowance target: {}", response.status());
            return Err(BotError::network_error("0x API error".to_string()));
        }

        let price_response: ZeroExPriceResponse = match response.json().await {
            Ok(response) => response,
            Err(e) => {
                println!("Failed to parse 0x price response for allowance target: {}", e);
                return Err(BotError::network_error("Failed to parse 0x response".to_string()));
            }
        };

        match price_response.allowanceTarget {
            Some(target) => Ok(target),
            None => {
                println!("No allowance target in 0x price response");
                Err(BotError::blockchain_error("No allowance target found".to_string()))
            }
        }
    }

    /// Get user private key from wallet address 
    async fn get_user_private_key(&self, wallet_address: &str, blockchain: &Blockchain) -> Result<String, BotError> {
        let user_id = self.get_user_id_from_wallet(wallet_address).await?;
        let user_wallets = crate::service::db_service::DbService::get_user_wallets(user_id).await?
            .ok_or_else(|| BotError::wallet_error("No wallets found for user".to_string()))?;

        let wallet_info = match blockchain {
            Blockchain::ETH => &user_wallets.eth_wallet,
            Blockchain::BSC => &user_wallets.bsc_wallet,
            Blockchain::BASE => &user_wallets.base_wallet,
            _ => return Err(BotError::validation_error("Unsupported blockchain for EVM".to_string())),
        };

        Ok(wallet_info.private_key.clone())
    }

    /// Get user ID from wallet address by looking up in the database
    /// 🚀 UPDATED: Only uses wallet address lookup, no chat_id derivation
    pub async fn get_user_id_from_wallet(&self, wallet_address: &str) -> Result<ObjectId, BotError> {
        // Find user by wallet address in user_wallets collection
        match self.find_user_by_wallet_address(wallet_address).await {
            Ok(user_id) => {
                println!("✅ Found user ID {} for wallet {}", user_id, wallet_address);
                Ok(user_id)
            }
            Err(e) => {
                println!("❌ No user found for wallet {}: {}", wallet_address, e);
                Err(BotError::wallet_error(format!("No user found for wallet address: {}", e)))
            }
        }
    }

    /// Find user by wallet address in user_wallets collection 
    async fn find_user_by_wallet_address(&self, wallet_address: &str) -> Result<ObjectId, BotError> {
        use mongodb::bson::doc;
        use crate::service::db_service::DbService;

        // Search in all wallet fields for the given address
        let filter = doc! {
            "$or": [
                { "eth_wallet.address": wallet_address },
                { "bsc_wallet.address": wallet_address },
                { "base_wallet.address": wallet_address },
                { "sol_wallet.address": wallet_address }
            ]
        };

        let collection = DbService::user_wallets_collection();
        match collection.find_one(filter, None).await {
            Ok(Some(user_wallets)) => {
                Ok(user_wallets.user_id)
            }
            Ok(None) => {
                Err(BotError::wallet_error("No user found with this wallet address".to_string()))
            }
            Err(e) => {
                Err(BotError::general_error(format!("Database error: {}", e)))
            }
        }
    }

    /// Transfer native token (ETH/BNB/BASE) to admin address for fee collection with smart $0.25 threshold
    pub async fn transfer_native_admin_fee(
        &self,
        _wallet_address: &str,
        private_key: &str,
        admin_address: &str,
        amount_wei: &str,
        blockchain: &Blockchain,
    ) -> Result<String, BotError> {
        let fee_amount_native = amount_wei.parse::<f64>().unwrap_or(0.0) / 1e18;
        println!("🔍 Checking EVM admin fee threshold: {} wei ({} {})",
                 amount_wei, fee_amount_native, blockchain.get_native_symbol());

        // Check if fee meets minimum and adjust ONLY if below $0.25 equivalent
        let price_service = crate::service::price_service::PriceService::new();

        let final_fee_native = match price_service.get_adjusted_fee_amount(blockchain, fee_amount_native).await {
            Ok(adjusted_amount) => adjusted_amount,
            Err(e) => {
                println!("⚠️ Failed to get adjusted fee amount, using original: {}", e);
                fee_amount_native
            }
        };

        let final_amount_wei = (final_fee_native * 1e18) as u64;
        let original_amount_wei = amount_wei.parse::<u64>().unwrap_or(0);

        if final_amount_wei != original_amount_wei {
            println!("💰 EVM admin fee adjusted from {} wei to {} wei (minimum $0.25 equivalent)",
                     original_amount_wei, final_amount_wei);
        } else {
            println!("✅ EVM admin fee using calculated amount: {} wei", final_amount_wei);
        }

        let config = crate::config::AppConfig::get();

        // Get chain ID
        let chain_id = match blockchain {
            Blockchain::ETH => std::env::var(ENV_CHAIN_ID_ETH)
                .unwrap_or_else(|_| config.chain_id_eth.to_string())
                .parse::<u64>()
                .unwrap_or(1u64),
            Blockchain::BSC => std::env::var(ENV_CHAIN_ID_BSC)
                .unwrap_or_else(|_| config.chain_id_bsc.to_string())
                .parse::<u64>()
                .unwrap_or(56u64),
            Blockchain::BASE => std::env::var(ENV_CHAIN_ID_BASE)
                .unwrap_or_else(|_| config.chain_id_base.to_string())
                .parse::<u64>()
                .unwrap_or(8453u64),
            Blockchain::SOL => return Err(BotError::blockchain_error("Solana not supported by EVM trader service".to_string())),
        };

        // Create wallet
        let wallet = match LocalWallet::from_str(private_key) {
            Ok(wallet) => wallet.with_chain_id(chain_id),
            Err(e) => {
                println!("Failed to create wallet: {}", e);
                return Err(BotError::blockchain_error("Failed to create wallet for fee transfer".to_string()));
            }
        };

        // Get RPC URL
        let rpc_url = match blockchain {
            Blockchain::ETH => std::env::var(ENV_RPC_URL_ETH)
                .unwrap_or_else(|_| config.rpc_url_eth.clone()),
            Blockchain::BSC => std::env::var(ENV_RPC_URL_BSC)
                .unwrap_or_else(|_| config.rpc_url_bsc.clone()),
            Blockchain::BASE => std::env::var(ENV_RPC_URL_BASE)
                .unwrap_or_else(|_| config.rpc_url_base.clone()),
            Blockchain::SOL => return Err(BotError::blockchain_error("Solana not supported by EVM trader service".to_string())),
        };

        // Create provider and client
        let provider = match Provider::<Http>::try_from(rpc_url) {
            Ok(provider) => provider,
            Err(e) => {
                println!("Failed to create provider: {}", e);
                return Err(BotError::blockchain_error("Failed to connect to blockchain network".to_string()));
            }
        };

        let client = SignerMiddleware::new(provider, wallet.clone());
        let client = Arc::new(client);

        // Parse addresses and amount
        let to_address = match Address::from_str(admin_address) {
            Ok(address) => address,
            Err(e) => {
                println!("Invalid admin address: {}", e);
                return Err(BotError::blockchain_error("Invalid admin address".to_string()));
            }
        };

        let amount = U256::from(final_amount_wei);

        // Get nonce
        let nonce = match client.get_transaction_count(client.address(), None).await {
            Ok(nonce) => nonce,
            Err(e) => {
                println!("Failed to get nonce: {}", e);
                return Err(BotError::blockchain_error("Failed to get nonce".to_string()));
            }
        };

        // Create transaction
        let tx = TransactionRequest::new()
            .to(to_address)
            .value(amount)
            .nonce(nonce);

        // Send transaction
        let pending_tx = match client.send_transaction(tx, None).await {
            Ok(tx) => tx,
            Err(e) => {
                println!("Failed to send admin fee transfer: {}", e);
                return Err(BotError::blockchain_error(format!("Failed to send admin fee transfer: {}", e)));
            }
        };

        // Wait for confirmation
        let receipt = match pending_tx.await {
            Ok(receipt) => receipt,
            Err(e) => {
                println!("Admin fee transfer failed: {}", e);
                return Err(BotError::blockchain_error(format!("Admin fee transfer failed: {}", e)));
            }
        };

        let tx_hash = format!("0x{:x}", receipt.unwrap().transaction_hash);
        println!("Native token admin fee transfer successful: {}", tx_hash);
        Ok(tx_hash)
    }

    /// Process admin fee for buy operations (collect fee from received tokens after swap)
    pub async fn process_buy_admin_fee_evm(
        &self,
        wallet_address: &str,
        user_id: ObjectId,
        received_amount: &str,
        token_address: &str,
        blockchain: &Blockchain,
    ) -> Result<Option<ObjectId>, BotError> {
        self.process_buy_admin_fee_evm_with_trade_id(
            wallet_address,
            user_id,
            None, // No trade_id for backward compatibility
            received_amount,
            token_address,
            blockchain,
        ).await
    }

    /// Process admin fee for buy operations with trade ID tracking (collect fee from user's native token balance)
    pub async fn process_buy_admin_fee_evm_native_with_trade_id(
        &self,
        wallet_address: &str,
        user_id: ObjectId,
        trade_id: Option<ObjectId>,
        user_buy_amount: &str, // Amount user wanted to spend in native tokens
        blockchain: &Blockchain,
    ) -> Result<Option<ObjectId>, BotError> {
        // Get admin fee percentage for this blockchain
        let fee_percentage = self.admin_fee_service.get_admin_fee_percentage_for_blockchain(blockchain).await
            .unwrap_or(defaults::DEFAULT_ADMIN_FEE_PERCENTAGE);

        if fee_percentage <= 0.0 {
            println!("Admin fee disabled ({}%) for buy operation", fee_percentage);
            return Ok(None);
        }

        // Get admin wallet address
        let admin_wallet_address = match self.admin_fee_service.get_admin_wallet_address_for_blockchain(blockchain).await {
            Ok(Some(address)) => address,
            Ok(None) => {
                println!("❌ Admin wallet not configured for {:?} - skipping fee collection", blockchain);
                return Ok(None);
            }
            Err(e) => {
                println!("❌ Failed to get admin wallet address: {}", e);
                return Ok(None);
            }
        };

        // Validate admin wallet address format
        if !self.admin_fee_service.validate_wallet_address(&admin_wallet_address, blockchain) {
            println!("❌ Invalid admin wallet address format for {:?}: {}", blockchain, admin_wallet_address);
            return Ok(None);
        }

        // Parse user buy amount
        let user_buy_amount_wei = user_buy_amount.parse::<u64>()
            .map_err(|e| BotError::validation_error(format!("Invalid buy amount: {}", e)))?;
        let user_buy_amount_eth = user_buy_amount_wei as f64 / 1e18;

        // Calculate admin fee amount
        let fee_amount_eth = user_buy_amount_eth * (fee_percentage / 100.0);
        let fee_amount_wei = (fee_amount_eth * 1e18) as u64;

        // Check if user has enough native tokens for buy amount + fee
        let total_required_eth = user_buy_amount_eth + fee_amount_eth;
        let total_required_wei = (total_required_eth * 1e18) as u64;

        // Check user's native token balance
        match self.check_sufficient_balance_for_buy(wallet_address, &total_required_wei.to_string(), blockchain).await {
            Ok(true) => {
                println!("✅ User has sufficient balance for buy amount + fee: {} ETH", total_required_eth);
            }
            Ok(false) => {
                println!("❌ Insufficient balance for buy + fee. Required: {} ETH", total_required_eth);
                return Ok(None);
            }
            Err(e) => {
                println!("❌ Failed to check user balance: {}", e);
                return Ok(None);
            }
        }

        // Log the buy fee calculation
        println!("📊 BUY FEE CALCULATION (Native Token):");
        println!("   User: {}", wallet_address);
        println!("   Blockchain: {:?}", blockchain);
        println!("   User Buy Amount: {} ETH ({} wei)", user_buy_amount_eth, user_buy_amount_wei);
        println!("   Fee Percentage: {}%", fee_percentage);
        println!("   Fee Amount: {} ETH ({} wei)", fee_amount_eth, fee_amount_wei);
        println!("   Total Required: {} ETH", total_required_eth);

        // Get native token symbol
        let native_symbol = match blockchain {
            Blockchain::ETH => "ETH",
            Blockchain::BSC => "BNB",
            Blockchain::BASE => "ETH",
            _ => "ETH",
        };

        // Create fee transaction record
        let fee_transaction_id = match self.admin_fee_service.create_fee_transaction(
            user_id,
            trade_id, // Link to the specific trade
            blockchain.clone(),
            FeeTransactionType::BuyFee,
            fee_percentage,
            user_buy_amount_eth,
            fee_amount_eth,
            native_symbol.to_string(),
            "native".to_string(), // Native token address placeholder
            admin_wallet_address.to_string(),
            wallet_address.to_string(),
        ).await {
            Ok(fee_tx_id) => fee_tx_id,
            Err(e) => {
                println!("❌ Failed to create fee transaction record: {}", e);
                return Err(e);
            }
        };

        // Get user's private key from database for real transaction execution
        let user_wallets = match crate::service::db_service::DbService::get_user_wallets(user_id).await {
            Ok(Some(wallets)) => wallets,
            Ok(None) => {
                println!("❌ User wallets not found");
                return Ok(None);
            }
            Err(e) => {
                println!("❌ Failed to get user wallets: {}", e);
                return Ok(None);
            }
        };

        let private_key = match blockchain {
            Blockchain::ETH => &user_wallets.eth_wallet.private_key,
            Blockchain::BSC => &user_wallets.bsc_wallet.private_key,
            Blockchain::BASE => &user_wallets.base_wallet.private_key,
            _ => {
                println!("❌ Unsupported blockchain for EVM fee collection");
                return Ok(None);
            }
        };

        // Execute real native token transfer using user's private key
        let transfer_result = self.transfer_native_tokens_with_private_key(
            wallet_address,
            &admin_wallet_address,
            fee_amount_wei,
            private_key,
            blockchain,
        ).await;

        match transfer_result {
            Ok(transaction_hash) => {
                println!("✅ Native token admin fee collected successfully: {}", transaction_hash);

                // Mark fee transaction as completed
                if let Err(e) = self.admin_fee_service.mark_fee_transaction_completed(
                    fee_transaction_id,
                    transaction_hash,
                    None, // block_number will be filled by confirmation
                    None, // gas_fee
                ).await {
                    println!("Warning: Failed to mark fee transaction as completed: {}", e);
                }

                // Update the trade record with the fee transaction ID
                if let Some(trade_id) = trade_id {
                    if let Err(e) = self.update_trade_with_fee_transaction_id(trade_id, fee_transaction_id).await {
                        println!("Warning: Failed to update trade with fee transaction ID: {}", e);
                    }
                }

                Ok(Some(fee_transaction_id))
            }
            Err(e) => {
                println!("❌ Native token admin fee transfer failed: {}", e);

                // Mark fee transaction as failed
                if let Err(mark_err) = self.admin_fee_service.mark_fee_transaction_failed(
                    fee_transaction_id,
                    format!("Transfer failed: {}", e),
                ).await {
                    println!("Warning: Failed to mark fee transaction as failed: {}", mark_err);
                }

                Ok(None)
            }
        }
    }

    /// Process admin fee for buy operations with trade ID tracking (collect fee from received tokens after swap)
    pub async fn process_buy_admin_fee_evm_with_trade_id(
        &self,
        wallet_address: &str,
        user_id: ObjectId,
        trade_id: Option<ObjectId>,
        received_amount: &str,
        token_address: &str,
        blockchain: &Blockchain,
    ) -> Result<Option<ObjectId>, BotError> {
        // Get admin fee percentage for this specific blockchain
        let fee_percentage = match self.admin_fee_service.get_admin_fee_percentage_for_blockchain(blockchain).await {
            Ok(percentage) => percentage,
            Err(e) => {
                println!("Warning: Failed to get admin fee percentage for {:?}, using default {}%: {}", blockchain, defaults::DEFAULT_ADMIN_FEE_PERCENTAGE, e);
                defaults::DEFAULT_ADMIN_FEE_PERCENTAGE
            }
        };

        if fee_percentage <= 0.0 {
            println!("Admin fee percentage is 0%, skipping fee collection");
            return Ok(None);
        }

        // Parse received amount (could be in wei or token units)
        let received_amount_wei = match received_amount.parse::<u64>() {
            Ok(amount) => amount,
            Err(e) => {
                println!("Warning: Failed to parse received amount {}: {}", received_amount, e);
                return Ok(None);
            }
        };

        // Get token decimals to convert to human-readable amount
        let token_decimals = self.get_token_decimals(token_address, blockchain).await.unwrap_or(18);
        let received_amount_tokens = received_amount_wei as f64 / 10_f64.powi(token_decimals as i32);

        // Log the buy operation details for fee calculation
        println!("📊 BUY FEE CALCULATION:");
        println!("   User: {}", wallet_address);
        println!("   Blockchain: {:?}", blockchain);
        println!("   Token: {}", token_address);
        println!("   Received Amount: {} tokens ({} wei)", received_amount_tokens, received_amount_wei);
        println!("   Fee Percentage: {}%", fee_percentage);

        // Calculate fee amount in tokens with minimum threshold enforcement
        let fee_amount_tokens = match self.admin_fee_service.calculate_fee_amount_with_minimum(
            received_amount_tokens,
            fee_percentage,
            blockchain
        ).await {
            Ok(adjusted_fee) => adjusted_fee,
            Err(e) => {
                println!("⚠️ Failed to calculate fee with minimum enforcement, using basic calculation: {}", e);
                self.admin_fee_service.calculate_fee_amount(received_amount_tokens, fee_percentage)
            }
        };

        println!("   Calculated Fee: {} tokens", fee_amount_tokens);

        if fee_amount_tokens <= 0.0 {
            println!("Calculated fee amount is 0, skipping fee collection");
            return Ok(None);
        }

        // Convert fee amount back to wei for transfer
        let fee_amount_wei = (fee_amount_tokens * 10_f64.powi(token_decimals as i32)) as u64;

        // Get token symbol from contract (production implementation)
        let token_symbol = self.get_token_symbol(token_address, blockchain).await
            .unwrap_or_else(|_| format!("TOKEN_{}", &token_address[2..8].to_uppercase()));

        // Get admin wallet address for this blockchain
        let admin_wallet_address = match self.admin_fee_service.get_admin_wallet_address(blockchain).await {
            Ok(address) => address,
            Err(e) => {
                println!("Warning: Failed to get admin wallet address for {:?}: {}", blockchain, e);
                return Ok(None); // Skip fee collection if no admin wallet configured
            }
        };

        // Create fee transaction record
        let fee_transaction_id = match self.admin_fee_service.create_fee_transaction(
            user_id,
            trade_id, // Link to the specific trade
            blockchain.clone(),
            FeeTransactionType::BuyFee,
            fee_percentage,
            received_amount_tokens,
            fee_amount_tokens,
            token_symbol.clone(),
            token_address.to_string(),
            admin_wallet_address.clone(),
            wallet_address.to_string(),
        ).await {
            Ok(id) => id,
            Err(e) => {
                if e.to_string().contains("already exists") {
                    println!("Fee transaction already exists for this trade, skipping duplicate collection");
                    return Ok(None); // Skip duplicate fee collection
                } else {
                    println!("Warning: Failed to create fee transaction record: {}", e);
                    return Ok(None); // Continue without fee collection
                }
            }
        };

        // fee_amount_wei is already calculated above

        // Execute actual ERC-20 token transfer for admin fee
        match self.transfer_erc20_admin_fee(
            wallet_address,
            token_address,
            &admin_wallet_address,
            &fee_amount_wei.to_string(),
            blockchain,
        ).await {
            Ok(transaction_hash) => {
                println!("ERC-20 admin fee collected successfully: {}", transaction_hash);

                // Mark fee transaction as completed with real transaction hash
                if let Err(e) = self.admin_fee_service.mark_fee_transaction_completed(
                    fee_transaction_id,
                    transaction_hash,
                    None, // block_number will be filled by confirmation
                    None, // gas_fee
                ).await {
                    println!("Warning: Failed to mark fee transaction as completed: {}", e);
                }

                // Update the trade record with the fee transaction ID
                if let Some(trade_id) = trade_id {
                    if let Err(e) = self.update_trade_with_fee_transaction_id(trade_id, fee_transaction_id).await {
                        println!("Warning: Failed to update trade with fee transaction ID: {}", e);
                    }
                }
            }
            Err(e) => {
                println!("Failed to collect ERC-20 admin fee: {}", e);

                // Mark fee transaction as failed
                if let Err(mark_err) = self.admin_fee_service.mark_fee_transaction_failed(
                    fee_transaction_id,
                    format!("Transfer failed: {}", e),
                ).await {
                    println!("Warning: Failed to mark fee transaction as failed: {}", mark_err);
                }

                // Don't return error - continue with trade execution
                println!("Continuing trade execution despite fee collection failure");
            }
        }

        Ok(Some(fee_transaction_id))
    }

    /// Process admin fee for sell operations (collect fee from received native tokens after swap)
    pub async fn process_sell_admin_fee_evm(
        &self,
        wallet_address: &str,
        user_id: ObjectId,
        received_amount: &str,
        token_address: &str,
        blockchain: &Blockchain,
    ) -> Result<Option<ObjectId>, BotError> {
        self.process_sell_admin_fee_evm_with_trade_id(
            wallet_address,
            user_id,
            None, // No trade_id for backward compatibility
            received_amount,
            token_address,
            blockchain,
        ).await
    }

    /// Process admin fee for sell operations with trade ID tracking (collect fee from received native tokens after swap)
    pub async fn process_sell_admin_fee_evm_with_trade_id(
        &self,
        wallet_address: &str,
        user_id: ObjectId,
        trade_id: Option<ObjectId>,
        received_amount: &str,
        token_address: &str,
        blockchain: &Blockchain,
    ) -> Result<Option<ObjectId>, BotError> {
        // Get admin fee percentage for this specific blockchain
        let fee_percentage = match self.admin_fee_service.get_admin_fee_percentage_for_blockchain(blockchain).await {
            Ok(percentage) => percentage,
            Err(e) => {
                println!("Warning: Failed to get admin fee percentage for {:?}, using default {}%: {}", blockchain, defaults::DEFAULT_ADMIN_FEE_PERCENTAGE, e);
                defaults::DEFAULT_ADMIN_FEE_PERCENTAGE
            }
        };

        if fee_percentage <= 0.0 {
            println!("Admin fee percentage is 0%, skipping fee collection");
            return Ok(None);
        }

        // Parse received amount (in wei)
        let received_amount_wei = match received_amount.parse::<u64>() {
            Ok(amount) => amount,
            Err(e) => {
                println!("Warning: Failed to parse received amount {}: {}", received_amount, e);
                return Ok(None);
            }
        };

        // Convert wei to ETH for fee calculation
        let received_amount_eth = received_amount_wei as f64 / 1e18;

        // Get the equivalent native token value of the tokens being sold
        let token_value_in_native = self.get_token_value_in_native_currency(
            token_address,
            received_amount,
            blockchain,
        ).await.unwrap_or(received_amount_eth);

        // Log the sell operation details for fee calculation
        println!("📊 SELL FEE CALCULATION:");
        println!("   User: {}", wallet_address);
        println!("   Blockchain: {:?}", blockchain);
        println!("   Token Sold: {}", token_address);
        println!("   Received Amount: {} ETH ({} wei)", received_amount_eth, received_amount_wei);
        println!("   Token Value in Native Currency: {} ETH", token_value_in_native);
        println!("   Fee Percentage: {}%", fee_percentage);

        // Calculate fee amount in ETH with minimum threshold enforcement
        let fee_amount_eth = match self.admin_fee_service.calculate_fee_amount_with_minimum(
            received_amount_eth,
            fee_percentage,
            blockchain
        ).await {
            Ok(adjusted_fee) => adjusted_fee,
            Err(e) => {
                println!("⚠️ Failed to calculate fee with minimum enforcement, using basic calculation: {}", e);
                self.admin_fee_service.calculate_fee_amount(received_amount_eth, fee_percentage)
            }
        };

        println!("   Calculated Fee: {} ETH", fee_amount_eth);

        // Convert fee amount back to wei
        let fee_amount_wei = (fee_amount_eth * 1e18) as u64;

        if fee_amount_wei == 0 {
            println!("Calculated fee amount is 0, skipping fee collection");
            return Ok(None);
        }

        // Get native token symbol
        let token_symbol = match blockchain {
            Blockchain::ETH => "ETH",
            Blockchain::BSC => "BNB",
            Blockchain::BASE => "ETH",
            _ => "UNKNOWN",
        };

        // Get admin wallet address for this blockchain
        let admin_wallet_address = match self.admin_fee_service.get_admin_wallet_address(blockchain).await {
            Ok(address) => address,
            Err(e) => {
                println!("Warning: Failed to get admin wallet address for {:?}: {}", blockchain, e);
                return Ok(None); // Skip fee collection if no admin wallet configured
            }
        };

        // Create fee transaction record
        let fee_transaction_id = match self.admin_fee_service.create_fee_transaction(
            user_id,
            trade_id, // Link to the specific trade
            blockchain.clone(),
            FeeTransactionType::SellFee,
            fee_percentage,
            received_amount_eth,
            fee_amount_eth,
            token_symbol.to_string(),
            token_address.to_string(),
            admin_wallet_address.clone(),
            wallet_address.to_string(),
        ).await {
            Ok(id) => id,
            Err(e) => {
                if e.to_string().contains("already exists") {
                    println!("Fee transaction already exists for this trade, skipping duplicate collection");
                    return Ok(None); // Skip duplicate fee collection
                } else {
                    println!("Warning: Failed to create fee transaction record: {}", e);
                    return Ok(None); // Continue without fee collection
                }
            }
        };

        // Use smart fee collection - try native tokens first, then fallback to received tokens
        let mut fee_transaction = AdminFeeTransaction::new(
            user_id,
            trade_id, // Link to the specific trade
            blockchain.clone(),
            FeeTransactionType::SellFee,
            fee_percentage,
            received_amount_eth,
            fee_amount_eth,
            token_symbol.to_string(),
            token_address.to_string(),
            admin_wallet_address.clone(),
            wallet_address.to_string(),
        );
        fee_transaction.id = Some(fee_transaction_id);

        match self.admin_fee_service.execute_smart_sell_fee_collection(&fee_transaction).await {
            Ok(transaction_hash) => {
                println!("Smart sell fee collection successful: {}", transaction_hash);

                // Mark fee transaction as completed with real transaction hash
                if let Err(e) = self.admin_fee_service.mark_fee_transaction_completed(
                    fee_transaction_id,
                    transaction_hash,
                    None, // block_number will be filled by confirmation
                    None, // gas_fee
                ).await {
                    println!("Warning: Failed to mark fee transaction as completed: {}", e);
                }

                // Update the trade record with the fee transaction ID
                if let Some(trade_id) = trade_id {
                    if let Err(e) = self.update_trade_with_fee_transaction_id(trade_id, fee_transaction_id).await {
                        println!("Warning: Failed to update trade with fee transaction ID: {}", e);
                    }
                }
            }
            Err(e) => {
                println!("Smart sell fee collection failed: {}", e);

                // Mark fee transaction as failed
                if let Err(mark_err) = self.admin_fee_service.mark_fee_transaction_failed(
                    fee_transaction_id,
                    format!("Smart collection failed: {}", e),
                ).await {
                    println!("Warning: Failed to mark fee transaction as failed: {}", mark_err);
                }

                // Don't return error - continue with trade execution
                println!("Continuing trade execution despite fee collection failure");
            }
        }

        Ok(Some(fee_transaction_id))
    }

    /// Transfer ERC-20 tokens to admin address for fee collection
    pub async fn transfer_erc20_admin_fee(
        &self,
        from_address: &str,
        token_address: &str,
        admin_address: &str,
        amount: &str,
        blockchain: &Blockchain,
    ) -> Result<String, BotError> {
        use ethers::prelude::*;
        use ethers::types::{Address, U256};
        use std::str::FromStr;

        println!("Transferring ERC-20 admin fee: {} tokens from {} to {}",
                 amount, from_address, admin_address);

        // Parse addresses
        let from_addr = Address::from_str(from_address)
            .map_err(|e| BotError::validation_error(format!("Invalid from address: {}", e)))?;
        let token_addr = Address::from_str(token_address)
            .map_err(|e| BotError::validation_error(format!("Invalid token address: {}", e)))?;
        let admin_addr = Address::from_str(admin_address)
            .map_err(|e| BotError::validation_error(format!("Invalid admin address: {}", e)))?;

        // Parse amount
        let transfer_amount = U256::from_dec_str(amount)
            .map_err(|e| BotError::validation_error(format!("Invalid amount: {}", e)))?;

        // Get user's private key from database
        let user_id = self.get_user_id_from_wallet(from_address).await?;
        let user_wallets = crate::service::db_service::DbService::get_user_wallets(user_id).await?
            .ok_or_else(|| BotError::wallet_error("No wallets found for user".to_string()))?;

        let wallet_info = match blockchain {
            Blockchain::ETH => &user_wallets.eth_wallet,
            Blockchain::BSC => &user_wallets.bsc_wallet,
            Blockchain::BASE => &user_wallets.base_wallet,
            _ => return Err(BotError::validation_error("Unsupported blockchain for EVM".to_string())),
        };

        // Get chain ID
        let config = crate::config::AppConfig::get();
        let chain_id = match blockchain {
            Blockchain::ETH => config.chain_id_eth,
            Blockchain::BSC => config.chain_id_bsc,
            Blockchain::BASE => config.chain_id_base,
            _ => return Err(BotError::validation_error("Unsupported blockchain for EVM".to_string())),
        };

        // Create wallet from private key
        let wallet = LocalWallet::from_str(&wallet_info.private_key)
            .map_err(|e| BotError::wallet_error(format!("Invalid private key: {}", e)))?
            .with_chain_id(chain_id);

        // Get RPC URL from environment variables (production configuration)
        let rpc_url = match blockchain {
            Blockchain::ETH => std::env::var(ENV_RPC_URL_ETH)
                .unwrap_or_else(|_| config.rpc_url_eth.clone()),
            Blockchain::BSC => std::env::var(ENV_RPC_URL_BSC)
                .unwrap_or_else(|_| config.rpc_url_bsc.clone()),
            Blockchain::BASE => std::env::var(ENV_RPC_URL_BASE)
                .unwrap_or_else(|_| config.rpc_url_base.clone()),
            _ => return Err(BotError::validation_error("Unsupported blockchain for EVM".to_string())),
        };

        // Create provider with timeout configuration
        let provider = Provider::<Http>::try_from(&rpc_url)
            .map_err(|e| BotError::network_error(format!("Failed to create provider: {}", e)))?
            .interval(Duration::from_millis(2000));

        // Create signer middleware
        let client = Arc::new(SignerMiddleware::new(provider.clone(), wallet.clone()));

        // Create ERC-20 transfer transaction manually (production implementation)
        let transfer_function_selector = [0xa9, 0x05, 0x9c, 0xbb]; // transfer(address,uint256)
        let mut call_data = Vec::new();
        call_data.extend_from_slice(&transfer_function_selector);

        // Encode recipient address (32 bytes, padded)
        let mut recipient_bytes = [0u8; 32];
        recipient_bytes[12..].copy_from_slice(admin_addr.as_bytes());
        call_data.extend_from_slice(&recipient_bytes);

        // Encode amount (32 bytes, big endian)
        let mut amount_bytes = [0u8; 32];
        transfer_amount.to_big_endian(&mut amount_bytes);
        call_data.extend_from_slice(&amount_bytes);

        // Get current gas price with buffer
        let gas_price = provider.get_gas_price().await
            .map_err(|e| BotError::network_error(format!("Failed to get gas price: {}", e)))?;
        let gas_price_with_buffer = gas_price * 110 / 100; // 10% buffer

        // Estimate gas for the transfer (use a reasonable default for ERC-20 transfers)
        let gas_limit = U256::from(100000); // Standard ERC-20 transfer gas limit
        let gas_limit_with_buffer = gas_limit * 120 / 100; // 20% buffer

        // Get current nonce
        let nonce = provider.get_transaction_count(from_addr, None).await
            .map_err(|e| BotError::network_error(format!("Failed to get nonce: {}", e)))?;

        println!("Sending ERC-20 transfer: amount={}, gas_price={}, gas_limit={}, nonce={}",
                 transfer_amount, gas_price_with_buffer, gas_limit_with_buffer, nonce);

        // Create transaction request
        let tx_request = TransactionRequest::new()
            .from(from_addr)
            .to(token_addr)
            .data(call_data)
            .gas_price(gas_price_with_buffer)
            .gas(gas_limit_with_buffer)
            .nonce(nonce);

        // Send transaction with retry logic
        let mut last_error = None;
        for attempt in 1..=3 {
            match client.send_transaction(tx_request.clone(), None).await
            {
                Ok(pending_tx) => {
                    let tx_hash = pending_tx.tx_hash();
                    println!("ERC-20 transfer sent successfully on attempt {}: {:?}", attempt, tx_hash);

                    // Wait for confirmation with timeout
                    match tokio::time::timeout(
                        Duration::from_secs(300), // 5 minute timeout
                        pending_tx.confirmations(1)
                    ).await {
                        Ok(Ok(receipt)) => {
                            if let Some(receipt) = receipt {
                                let tx_hash = format!("{:?}", receipt.transaction_hash);
                                println!("ERC-20 admin fee transfer confirmed: {}", tx_hash);
                                return Ok(tx_hash);
                            } else {
                                println!("Transaction receipt is None, but transaction was sent");
                                return Ok(format!("{:?}", tx_hash));
                            }
                        }
                        Ok(Err(e)) => {
                            println!("ERC-20 transfer confirmation failed on attempt {}: {}", attempt, e);
                            last_error = Some(format!("Confirmation failed: {}", e));
                        }
                        Err(_) => {
                            println!("ERC-20 transfer confirmation timeout on attempt {}", attempt);
                            last_error = Some("Transaction confirmation timeout".to_string());
                        }
                    }
                }
                Err(e) => {
                    println!("Failed to send ERC-20 transfer on attempt {}: {}", attempt, e);
                    last_error = Some(format!("Send failed: {}", e));

                    // Wait before retry
                    if attempt < 3 {
                        tokio::time::sleep(Duration::from_secs(5)).await;
                    }
                }
            }
        }

        // If all attempts failed, return the last error
        Err(BotError::transaction_error(
            last_error.unwrap_or_else(|| "All ERC-20 transfer attempts failed".to_string())
        ))
    }



    /// Get token decimals from contract using proper ethers-rs pattern
    async fn get_token_decimals(&self, token_address: &str, blockchain: &Blockchain) -> Result<u8, BotError> {
        use ethers::prelude::*;
        use ethers::types::{Address, U256};
        use std::str::FromStr;

        // Parse token address
        let token_addr = Address::from_str(token_address)
            .map_err(|e| BotError::validation_error(format!("Invalid token address: {}", e)))?;

        // Get RPC URL with fallback
        let rpc_url = self.get_rpc_url_with_fallback(blockchain);

        // Create provider with timeout
        let provider = Provider::<Http>::try_from(&rpc_url)
            .map_err(|e| BotError::network_error(format!("Failed to create provider: {}", e)))?
            .interval(Duration::from_millis(2000));

        // ERC20 decimals() function selector: 0x313ce567
        let decimals_selector = [0x31, 0x3c, 0xe5, 0x67];

        let call_request = TransactionRequest::new()
            .to(token_addr)
            .data(decimals_selector.to_vec());

        let typed_tx: TypedTransaction = call_request.into();
        match tokio::time::timeout(Duration::from_secs(30), provider.call(&typed_tx, None)).await {
            Ok(Ok(result)) => {
                if result.len() >= 32 {
                    // Parse the result as U256 and convert to u8
                    let decimals_u256 = U256::from_big_endian(&result);
                    let decimals = decimals_u256.as_u32() as u8;

                    // Validate reasonable decimals range (0-77 is max for U256)
                    if decimals <= 77 {
                        Ok(decimals)
                    } else {
                        println!("Invalid decimals value {} for token {}, using default 18", decimals, token_address);
                        Ok(18)
                    }
                } else {
                    println!("Invalid decimals response length for token {}, using default 18", token_address);
                    Ok(18)
                }
            }
            Ok(Err(e)) => {
                println!("Failed to get token decimals for {}: {}, using default 18", token_address, e);
                Ok(18)
            }
            Err(_) => {
                println!("Timeout getting token decimals for {}, using default 18", token_address);
                Ok(18)
            }
        }
    }

    /// Get token symbol from contract
    async fn get_token_symbol(&self, token_address: &str, blockchain: &Blockchain) -> Result<String, BotError> {
        use ethers::prelude::*;
        use ethers::types::Address;
        use std::str::FromStr;

        // Parse token address
        let token_addr = Address::from_str(token_address)
            .map_err(|e| BotError::validation_error(format!("Invalid token address: {}", e)))?;

        // Get RPC URL with fallback
        let rpc_url = self.get_rpc_url_with_fallback(blockchain);

        // Create provider with timeout
        let provider = Provider::<Http>::try_from(&rpc_url)
            .map_err(|e| BotError::network_error(format!("Failed to create provider: {}", e)))?
            .interval(Duration::from_millis(2000));

        // Call symbol() function manually (production implementation)
        let symbol_function_selector = [0x95, 0xd8, 0x9b, 0x41]; // symbol()
        let call_data = symbol_function_selector.to_vec();

        let call_request = TransactionRequest::new()
            .to(token_addr)
            .data(call_data);
        let typed_tx: TypedTransaction = call_request.into();

        match tokio::time::timeout(Duration::from_secs(30), provider.call(&typed_tx, None)).await {
            Ok(Ok(result)) => {
                // Decode the result (simplified - assumes string return)
                if result.len() >= 64 {
                    // Skip the first 32 bytes (offset) and next 32 bytes (length), then read string
                    let string_data = &result[64..];
                    if let Ok(symbol) = String::from_utf8(string_data.to_vec()) {
                        Ok(symbol.trim_end_matches('\0').to_string())
                    } else {
                        Ok(format!("TOKEN_{}", &token_address[2..8].to_uppercase()))
                    }
                } else {
                    Ok(format!("TOKEN_{}", &token_address[2..8].to_uppercase()))
                }
            }
            Ok(Err(e)) => {
                println!("Failed to get token symbol for {}: {}", token_address, e);
                Ok(format!("TOKEN_{}", &token_address[2..8].to_uppercase()))
            }
            Err(_) => {
                println!("Timeout getting token symbol for {}", token_address);
                Ok(format!("TOKEN_{}", &token_address[2..8].to_uppercase()))
            }
        }
    }

    /// Get token name from contract 
    async fn get_token_name(&self, token_address: &str, blockchain: &Blockchain) -> Result<String, BotError> {
        use ethers::prelude::*;
        use ethers::types::Address;
        use std::str::FromStr;

        // Parse token address
        let token_addr = Address::from_str(token_address)
            .map_err(|e| BotError::validation_error(format!("Invalid token address: {}", e)))?;

        // Get RPC URL with fallback
        let rpc_url = self.get_rpc_url_with_fallback(blockchain);

        // Create provider with timeout
        let provider = Provider::<Http>::try_from(&rpc_url)
            .map_err(|e| BotError::network_error(format!("Failed to create provider: {}", e)))?
            .interval(Duration::from_millis(2000));

        // Call name() function manually (production implementation)
        let name_function_selector = [0x06, 0xfd, 0xde, 0x03]; // name()
        let call_data = name_function_selector.to_vec();

        let call_request = TransactionRequest::new()
            .to(token_addr)
            .data(call_data);
        let typed_tx: TypedTransaction = call_request.into();

        match tokio::time::timeout(Duration::from_secs(30), provider.call(&typed_tx, None)).await {
            Ok(Ok(result)) => {
                // Decode the result (simplified - assumes string return)
                if result.len() >= 64 {
                    // Skip the first 32 bytes (offset) and next 32 bytes (length), then read string
                    let string_data = &result[64..];
                    if let Ok(name) = String::from_utf8(string_data.to_vec()) {
                        Ok(name.trim_end_matches('\0').to_string())
                    } else {
                        Ok(format!("Token {}", &token_address[2..8].to_uppercase()))
                    }
                } else {
                    Ok(format!("Token {}", &token_address[2..8].to_uppercase()))
                }
            }
            Ok(Err(e)) => {
                println!("Failed to get token name for {}: {}", token_address, e);
                Ok(format!("Token {}", &token_address[2..8].to_uppercase()))
            }
            Err(_) => {
                println!("Timeout getting token name for {}", token_address);
                Ok(format!("Token {}", &token_address[2..8].to_uppercase()))
            }
        }
    }

    /// Transfer native tokens (ETH/BNB/BASE) to admin wallet using user's private key
    async fn transfer_native_tokens_with_private_key(
        &self,
        from_address: &str,
        to_address: &str,
        amount_wei: u64,
        private_key: &str,
        blockchain: &Blockchain,
    ) -> Result<String, BotError> {
        use ethers::prelude::*;
        use ethers::providers::{Http, Provider};
        use ethers::signers::{LocalWallet, Signer};
        use ethers::middleware::SignerMiddleware;
        use ethers::types::{Address, U256, TransactionRequest};
        use std::str::FromStr;
        use std::sync::Arc;

        println!("🔄 Executing real native token transfer: {} wei ({} {}) from {} to {} on {:?}",
                 amount_wei,
                 amount_wei as f64 / 1e18,
                 blockchain.get_native_symbol(),
                 from_address,
                 to_address,
                 blockchain);

        // Get chain ID for the blockchain
        let chain_id = match blockchain {
            Blockchain::ETH => 1u64,
            Blockchain::BSC => 56u64,
            Blockchain::BASE => 8453u64,
            _ => return Err(BotError::validation_error("Unsupported blockchain for native transfer".to_string())),
        };

        // Create wallet from private key
        let wallet = LocalWallet::from_str(private_key)
            .map_err(|e| BotError::wallet_error(format!("Invalid private key: {}", e)))?
            .with_chain_id(chain_id);

        // Get RPC URL for the blockchain
        let rpc_url = self.get_rpc_url_with_fallback(blockchain);

        // Create provider
        let provider = Provider::<Http>::try_from(&rpc_url)
            .map_err(|e| BotError::network_error(format!("Failed to create provider: {}", e)))?;

        // Create signer middleware
        let client = SignerMiddleware::new(provider, wallet.clone());
        let client = Arc::new(client);

        // Parse addresses
        let to_addr = Address::from_str(to_address)
            .map_err(|e| BotError::validation_error(format!("Invalid to address: {}", e)))?;

        // Get current nonce
        let nonce = client.get_transaction_count(client.address(), None).await
            .map_err(|e| BotError::network_error(format!("Failed to get nonce: {}", e)))?;

        // Get current gas price
        let gas_price = client.get_gas_price().await
            .map_err(|e| BotError::network_error(format!("Failed to get gas price: {}", e)))?;

        // Create transaction request
        let tx_request = TransactionRequest::new()
            .to(to_addr)
            .value(U256::from(amount_wei))
            .gas_price(gas_price)
            .gas(U256::from(21000u64)) // Standard gas limit for native transfer
            .nonce(nonce);

        println!("📋 Real Transaction Details:");
        println!("   From: {}", client.address());
        println!("   To: {}", to_addr);
        println!("   Amount: {} wei ({} {})", amount_wei, amount_wei as f64 / 1e18, blockchain.get_native_symbol());
        println!("   Gas Price: {} wei", gas_price);
        println!("   Gas Limit: 21000");
        println!("   Nonce: {}", nonce);

        // Send the transaction
        println!("Sending real native token transfer transaction...");
        let pending_tx = client.send_transaction(tx_request, None).await
            .map_err(|e| BotError::transaction_error(format!("Failed to send transaction: {}", e)))?;

        println!("📤 Transaction sent! Hash: {:?}", pending_tx.tx_hash());

        // Wait for confirmation with timeout
        let receipt = match tokio::time::timeout(
            std::time::Duration::from_secs(defaults::SWAP_TIMEOUT_SECONDS),
            pending_tx
        ).await {
            Ok(Ok(receipt)) => receipt,
            Ok(Err(e)) => {
                println!("❌ Transaction failed: {}", e);
                return Err(BotError::transaction_error(format!("Transaction failed: {}", e)));
            },
            Err(_) => {
                println!("⏰ Transaction confirmation timeout after {} seconds", defaults::SWAP_TIMEOUT_SECONDS);
                return Err(BotError::transaction_error("Transaction confirmation timeout".to_string()));
            }
        };

        // Check if the transaction was successful
        if let Some(receipt_value) = &receipt {
            if let Some(status) = receipt_value.status {
                if status == U64::from(0) {
                    println!("❌ Transaction failed with status 0");
                    return Err(BotError::transaction_error("Transaction failed on-chain".to_string()));
                }
            }
        }

        // Get the transaction hash
        let tx_hash = format!("0x{:x}", receipt.unwrap().transaction_hash);
        println!("✅ Native token transfer completed successfully: {}", tx_hash);

        Ok(tx_hash)
    }



    /// Update trade record with fee transaction ID for bidirectional linking
    async fn update_trade_with_fee_transaction_id(
        &self,
        trade_id: ObjectId,
        fee_transaction_id: ObjectId,
    ) -> Result<(), BotError> {
        use mongodb::bson::doc;
        use crate::model::Trade;
        use mongodb::Collection;

        let collection: Collection<Trade> = self.admin_fee_service.db.collection("trades");

        let filter = doc! { "_id": trade_id };
        let update = doc! {
            "$set": {
                "admin_fee_transaction_id": fee_transaction_id,
                "updated_at": chrono::Utc::now()
            }
        };

        match collection.update_one(filter, update, None).await {
            Ok(result) => {
                if result.modified_count > 0 {
                    println!("✅ Trade {} updated with fee transaction ID {}", trade_id, fee_transaction_id);
                    Ok(())
                } else {
                    println!("⚠️ Trade {} not found for fee transaction ID update", trade_id);
                    Err(BotError::database_error("Trade not found for update".to_string()))
                }
            }
            Err(e) => {
                println!("❌ Failed to update trade {} with fee transaction ID: {}", trade_id, e);
                Err(BotError::database_error(format!("Failed to update trade: {}", e)))
            }
        }
    }

    /// Get the equivalent native token value of a token amount using 0x API
    async fn get_token_value_in_native_currency(
        &self,
        token_address: &str,
        token_amount: &str,
        blockchain: &Blockchain,
    ) -> Result<f64, BotError> {
        // Get native token address for the blockchain
        let native_token_address = match blockchain {
            Blockchain::ETH => "******************************************",
            Blockchain::BSC => "******************************************",
            Blockchain::BASE => "******************************************",
            _ => return Err(BotError::validation_error("Unsupported blockchain for token value lookup".to_string())),
        };

        // Get 0x API base URL for the blockchain
        let base_url = match blockchain {
            Blockchain::ETH => "https://api.0x.org",
            Blockchain::BSC => "https://bsc.api.0x.org",
            Blockchain::BASE => "https://base.api.0x.org",
            _ => return Err(BotError::validation_error("Unsupported blockchain for 0x API".to_string())),
        };

        // Create HTTP client
        let client = reqwest::Client::new();

        // Build the quote request URL to get token value in native currency
        let url = format!(
            "{}/swap/v1/quote?sellToken={}&buyToken={}&sellAmount={}",
            base_url,
            token_address,
            native_token_address,
            token_amount
        );

        // Add API key if available
        let mut request = client.get(&url);
        if let Ok(api_key) = std::env::var("ZEROX_API_KEY") {
            if !api_key.is_empty() {
                request = request.header("0x-api-key", api_key);
                println!("Using 0x API key: API key found");
            }
        }

        // Make the request with timeout
        match tokio::time::timeout(Duration::from_secs(10), request.send()).await {
            Ok(Ok(response)) => {
                if response.status().is_success() {
                    match response.json::<serde_json::Value>().await {
                        Ok(quote_data) => {
                            if let Some(buy_amount) = quote_data.get("buyAmount") {
                                if let Some(buy_amount_str) = buy_amount.as_str() {
                                    if let Ok(buy_amount_wei) = buy_amount_str.parse::<u64>() {
                                        let native_value = buy_amount_wei as f64 / 1e18;
                                        println!("Token value lookup: {} tokens = {} native tokens", token_amount, native_value);
                                        return Ok(native_value);
                                    }
                                }
                            }
                        }
                        Err(e) => {
                            println!("Failed to parse 0x quote response for token value: {}", e);
                        }
                    }
                } else {
                    println!("0x API request failed for token value lookup: {}", response.status());
                }
            }
            Ok(Err(e)) => {
                println!("Network error getting token value from 0x API: {}", e);
            }
            Err(_) => {
                println!("Timeout getting token value from 0x API");
            }
        }

        // Fallback: return 0 if we can't get the value
        println!("Could not determine token value in native currency, using 0");
        Ok(0.0)
    }

    /// Get RPC URL with fallback support (production implementation)
    /// Priority: 1. Admin Settings -> 2. Environment Variables -> 3. Config -> 4. Default URLs
    fn get_rpc_url_with_fallback(&self, blockchain: &Blockchain) -> String {
        // 1. First priority: Try to get RPC URL from admin settings
        if let Ok(Some(admin_rpc_url)) = tokio::task::block_in_place(|| {
            tokio::runtime::Handle::current().block_on(async {
                self.admin_fee_service.get_rpc_url_from_admin_settings(blockchain).await
            })
        }) {
            if !admin_rpc_url.is_empty() && admin_rpc_url.starts_with("http") {
                println!("🔧 Using RPC URL from admin settings for {:?}: {}", blockchain, admin_rpc_url);
                return admin_rpc_url;
            }
        }

        let config = crate::config::AppConfig::get();

        // 2. Second priority: Try environment variables
        let env_rpc = match blockchain {
            Blockchain::ETH => std::env::var(ENV_RPC_URL_ETH).ok(),
            Blockchain::BSC => std::env::var(ENV_RPC_URL_BSC).ok(),
            Blockchain::BASE => std::env::var(ENV_RPC_URL_BASE).ok(),
            _ => None,
        };

        if let Some(env_url) = env_rpc {
            if !env_url.is_empty() && env_url.starts_with("http") {
                println!("🌍 Using RPC URL from environment variables for {:?}: {}", blockchain, env_url);
                return env_url;
            }
        }

        // 3. Third priority: Try config file
        let config_rpc = match blockchain {
            Blockchain::ETH => &config.rpc_url_eth,
            Blockchain::BSC => &config.rpc_url_bsc,
            Blockchain::BASE => &config.rpc_url_base,
            _ => &config.rpc_url_eth,
        };

        if !config_rpc.is_empty() && config_rpc.starts_with("http") {
            println!("📄 Using RPC URL from config for {:?}: {}", blockchain, config_rpc);
            return config_rpc.clone();
        }

        // 4. Fourth priority: Try fallback environment variables
        let fallback_env_rpc = match blockchain {
            Blockchain::ETH => std::env::var(ENV_RPC_URL_ETH_FALLBACK).ok(),
            Blockchain::BSC => std::env::var(ENV_RPC_URL_BSC_FALLBACK).ok(),
            Blockchain::BASE => std::env::var(ENV_RPC_URL_BASE_FALLBACK).ok(),
            _ => None,
        };

        if let Some(fallback_url) = fallback_env_rpc {
            if !fallback_url.is_empty() && fallback_url.starts_with("http") {
                println!("🔄 Using fallback RPC URL from environment for {:?}: {}", blockchain, fallback_url);
                return fallback_url;
            }
        }

        // 5. Final fallback: Use default URLs
        let default_url = match blockchain {
            Blockchain::ETH => "https://eth-mainnet.g.alchemy.com/v2/demo".to_string(),
            Blockchain::BSC => "https://bsc-dataseed.binance.org".to_string(),
            Blockchain::BASE => "https://mainnet.base.org".to_string(),
            _ => "https://eth-mainnet.g.alchemy.com/v2/demo".to_string(),
        };

        println!("⚙️ Using default RPC URL for {:?}: {}", blockchain, default_url);
        default_url
    }

    /// OPTIMIZED: Smart buy admin fee collection for EVM chains (parallel execution)
    pub async fn process_smart_buy_admin_fee_evm(
        &self,
        wallet_address: &str,
        user_id: ObjectId,
        trade_id: Option<ObjectId>,
        user_buy_amount: &str, // Amount user wanted to spend in native tokens
        token_address: &str,   // Token being purchased
        blockchain: &Blockchain,
    ) -> Result<Option<ObjectId>, BotError> {
        println!("Processing smart buy admin fee for EVM chain: {:?}", blockchain);

        // Get admin fee percentage
        let fee_percentage = match self.admin_fee_service.get_admin_fee_percentage_for_blockchain(blockchain).await {
            Ok(percentage) => percentage,
            Err(e) => {
                println!("⚠️ Failed to get admin fee percentage for {:?}, using default {}%: {}", blockchain, defaults::DEFAULT_ADMIN_FEE_PERCENTAGE, e);
                defaults::DEFAULT_ADMIN_FEE_PERCENTAGE
            }
        };

        if fee_percentage <= 0.0 {
            println!("ℹ️ Admin fee disabled ({}%) for buy operation", fee_percentage);
            return Ok(None);
        }

        // Get admin wallet address
        let admin_wallet_address = match self.admin_fee_service.get_admin_wallet_address(blockchain).await {
            Ok(address) => address,
            Err(e) => {
                println!("❌ No admin address configured for {:?}: {}", blockchain, e);
                return Ok(None);
            }
        };

        // 🎯 STRATEGY: Collect fee from user's native token balance BEFORE swap
        // This ensures immediate fee collection without waiting for swap completion

        use ethers::types::U256;
        let buy_amount_wei = U256::from_dec_str(user_buy_amount)
            .map_err(|e| BotError::validation_error(format!("Invalid buy amount: {}", e)))?;

        let fee_amount_wei = buy_amount_wei * U256::from((fee_percentage * 100.0) as u64) / U256::from(10000);
        let fee_amount_f64 = fee_amount_wei.as_u128() as f64 / 1e18;

        // Check if user has sufficient balance for trade + fee + gas
        let gas_buffer = match blockchain {
            Blockchain::ETH => 0.01,      // 0.01 ETH for gas
            Blockchain::BSC => 0.005,     // 0.005 BNB for gas
            Blockchain::BASE => 0.001,    // 0.001 ETH for gas
            _ => 0.01,
        };

        let total_required = buy_amount_wei + fee_amount_wei + U256::from((gas_buffer * 1e18) as u64);

        match self.check_sufficient_balance_for_buy(
            wallet_address,
            &total_required.to_string(),
            blockchain
        ).await {
            Ok(true) => {
                println!("✅ User has sufficient balance, collecting fee from native tokens");

                // Create fee transaction record with REAL values
                let fee_transaction_id = match self.admin_fee_service.create_fee_transaction(
                    user_id,
                    trade_id, // Use actual trade_id
                    blockchain.clone(),
                    FeeTransactionType::BuyFee,
                    fee_percentage, // REAL fee percentage from settings
                    buy_amount_wei.as_u128() as f64 / 1e18, // Original buy amount in native tokens
                    fee_amount_f64, // Actual fee amount in native tokens
                    blockchain.get_native_symbol().to_string(),
                    "native".to_string(),
                    admin_wallet_address.clone(),
                    wallet_address.to_string(),
                ).await {
                    Ok(id) => id,
                    Err(e) => {
                        println!("❌ Failed to create fee transaction record: {}", e);
                        return Ok(None);
                    }
                };

                // Get user's private key for fee transfer
                let user_wallets = match crate::service::db_service::DbService::get_user_wallets(user_id).await {
                    Ok(Some(wallets)) => wallets,
                    Ok(None) => {
                        println!("❌ User wallets not found");
                        return Ok(None);
                    }
                    Err(e) => {
                        println!("❌ Failed to get user wallets: {}", e);
                        return Ok(None);
                    }
                };

                let private_key = match blockchain {
                    Blockchain::ETH => &user_wallets.eth_wallet.private_key,
                    Blockchain::BSC => &user_wallets.bsc_wallet.private_key,
                    Blockchain::BASE => &user_wallets.base_wallet.private_key,
                    _ => {
                        println!("❌ Unsupported blockchain for EVM fee collection");
                        return Ok(None);
                    }
                };

                // Execute native token transfer for admin fee
                match self.transfer_native_admin_fee(
                    wallet_address,
                    private_key,
                    &admin_wallet_address,
                    &fee_amount_wei.to_string(),
                    blockchain,
                ).await {
                    Ok(transaction_hash) => {
                        println!("✅ Buy admin fee collected from native tokens: {}", transaction_hash);

                        // Mark fee transaction as completed
                        if let Err(e) = self.admin_fee_service.mark_fee_transaction_completed(
                            fee_transaction_id,
                            transaction_hash,
                            None,
                            None,
                        ).await {
                            println!("⚠️ Failed to mark fee transaction as completed: {}", e);
                        }

                        Ok(Some(fee_transaction_id))
                    }
                    Err(e) => {
                        println!("⚠️ Buy admin fee collection failed: {}", e);

                        // Mark fee transaction as failed
                        if let Err(mark_err) = self.admin_fee_service.mark_fee_transaction_failed(
                            fee_transaction_id,
                            e.to_string(),
                        ).await {
                            println!("⚠️ Failed to mark fee transaction as failed: {}", mark_err);
                        }

                        // DON'T BLOCK TRADE - Continue with trade execution despite fee collection failure
                        println!("✅ Continuing trade execution despite fee collection failure");
                        Ok(None)
                    }
                }
            }
            Ok(false) => {
                println!("⚠️ User doesn't have sufficient balance for immediate fee collection");
                // Fall back to collecting from received tokens after swap
                self.process_buy_admin_fee_evm_with_trade_id(
                    wallet_address,
                    user_id,
                    trade_id,
                    user_buy_amount, // Will be converted to received tokens
                    token_address,
                    blockchain,
                ).await
            }
            Err(e) => {
                println!("⚠️ Failed to check user balance: {}", e);
                // DON'T BLOCK TRADE - Continue without fee collection
                println!("✅ Continuing trade execution despite balance check failure");
                Ok(None)
            }
        }
    }

    /// OPTIMIZED: Smart sell admin fee collection for EVM chains (immediate execution)
    pub async fn process_smart_sell_admin_fee_evm(
        &self,
        wallet_address: &str,
        user_id: ObjectId,
        trade_id: Option<ObjectId>,
        received_amount: &str,  // Native tokens received from sell
        token_address: &str,    // Token that was sold
        blockchain: &Blockchain,
    ) -> Result<Option<ObjectId>, BotError> {
        println!("Processing smart sell admin fee for EVM chain: {:?}", blockchain);

        // Get admin fee percentage
        let fee_percentage = match self.admin_fee_service.get_admin_fee_percentage_for_blockchain(blockchain).await {
            Ok(percentage) => percentage,
            Err(e) => {
                println!("⚠️ Failed to get admin fee percentage for {:?}, using default {}%: {}", blockchain, defaults::DEFAULT_ADMIN_FEE_PERCENTAGE, e);
                defaults::DEFAULT_ADMIN_FEE_PERCENTAGE
            }
        };

        if fee_percentage <= 0.0 {
            println!("ℹ️ Admin fee disabled ({}%) for sell operation", fee_percentage);
            return Ok(None);
        }

        // Get admin wallet address
        let admin_wallet_address = match self.admin_fee_service.get_admin_wallet_address(blockchain).await {
            Ok(address) => address,
            Err(e) => {
                println!("❌ No admin address configured for {:?}: {}", blockchain, e);
                return Ok(None);
            }
        };

        use ethers::types::U256;
        let received_amount_wei = U256::from_dec_str(received_amount)
            .map_err(|e| BotError::validation_error(format!("Invalid received amount: {}", e)))?;

        let fee_amount_wei = received_amount_wei * U256::from((fee_percentage * 100.0) as u64) / U256::from(10000);
        let fee_amount_f64 = fee_amount_wei.as_u128() as f64 / 1e18;

        // 🎯 SMART COLLECTION STRATEGY FOR SELL OPERATIONS

        // Strategy 1: Try to collect from existing native token balance (fastest)
        let current_balance = match self.get_native_balance(wallet_address, blockchain).await {
            Ok(balance) => balance,
            Err(_) => U256::zero(),
        };

        let gas_reserve = match blockchain {
            Blockchain::ETH => U256::from((0.01 * 1e18) as u64),   // 0.01 ETH
            Blockchain::BSC => U256::from((0.005 * 1e18) as u64), // 0.005 BNB
            Blockchain::BASE => U256::from((0.001 * 1e18) as u64), // 0.001 ETH
            _ => U256::from((0.01 * 1e18) as u64),
        };

        let available_balance = current_balance.saturating_sub(gas_reserve);

        if available_balance >= fee_amount_wei {
            println!("✅ Collecting fee from existing native token balance: {} {}",
                fee_amount_f64, blockchain.get_native_symbol());

            return self.execute_native_fee_transfer(
                wallet_address,
                user_id,
                trade_id,
                &admin_wallet_address,
                &fee_amount_wei.to_string(),
                fee_percentage,
                received_amount_wei.as_u128() as f64 / 1e18,
                fee_amount_f64,
                blockchain,
                FeeTransactionType::SellFee,
            ).await;
        }

        // Strategy 2: Collect from received native tokens (from the sell transaction)
        if received_amount_wei >= fee_amount_wei {
            println!("✅ Collecting fee from received native tokens: {} {}",
                fee_amount_f64, blockchain.get_native_symbol());

            // Wait a moment for the sell transaction to settle
            tokio::time::sleep(tokio::time::Duration::from_millis(defaults::SETTLEMENT_DELAY_MS)).await;

            return self.execute_native_fee_transfer(
                wallet_address,
                user_id,
                trade_id,
                &admin_wallet_address,
                &fee_amount_wei.to_string(),
                fee_percentage,
                received_amount_wei.as_u128() as f64 / 1e18,
                fee_amount_f64,
                blockchain,
                FeeTransactionType::SellFee,
            ).await;
        }

        // Strategy 3: Collect from remaining tokens (if user still has some)
        let remaining_token_balance = match self.get_token_balance(wallet_address, token_address, blockchain).await {
            Ok(balance) => balance,
            Err(_) => 0,
        };

        if remaining_token_balance > 0 {
            println!("✅ Collecting fee from remaining tokens");

            let token_fee_amount = (remaining_token_balance as f64 * (fee_percentage / 100.0)) as u64;

            if token_fee_amount > 0 {
                let original_token_amount = remaining_token_balance as f64 / 10_f64.powi(18); // Assume 18 decimals for calculation
                return self.execute_token_fee_transfer(
                    wallet_address,
                    user_id,
                    trade_id,
                    &admin_wallet_address,
                    token_address,
                    &token_fee_amount.to_string(),
                    fee_percentage,
                    blockchain,
                    original_token_amount, // Pass the original token amount
                ).await;
            }
        }

        println!("⚠️ Unable to collect sell admin fee using any strategy");
        Ok(None)
    }

    /// Helper: Execute native token fee transfer
    async fn execute_native_fee_transfer(
        &self,
        wallet_address: &str,
        user_id: ObjectId,
        trade_id: Option<ObjectId>,
        admin_address: &str,
        fee_amount_wei: &str,
        fee_percentage: f64,
        transaction_value: f64,
        fee_amount_f64: f64,
        blockchain: &Blockchain,
        transaction_type: FeeTransactionType,
    ) -> Result<Option<ObjectId>, BotError> {
        // Create fee transaction record with REAL values
        let fee_transaction_id = match self.admin_fee_service.create_fee_transaction(
            user_id,
            trade_id, // Use actual trade_id
            blockchain.clone(),
            transaction_type,
            fee_percentage, // REAL fee percentage from settings
            transaction_value, // Actual transaction value
            fee_amount_f64, // Actual fee amount calculated
            blockchain.get_native_symbol().to_string(),
            "native".to_string(),
            admin_address.to_string(),
            wallet_address.to_string(),
        ).await {
            Ok(id) => id,
            Err(e) => {
                println!("❌ Failed to create fee transaction record: {}", e);
                return Ok(None);
            }
        };

        // Get user's private key
        let user_wallets = match crate::service::db_service::DbService::get_user_wallets(user_id).await {
            Ok(Some(wallets)) => wallets,
            Ok(None) => {
                println!("❌ User wallets not found");
                return Ok(None);
            }
            Err(e) => {
                println!("❌ Failed to get user wallets: {}", e);
                return Ok(None);
            }
        };

        let private_key = match blockchain {
            Blockchain::ETH => &user_wallets.eth_wallet.private_key,
            Blockchain::BSC => &user_wallets.bsc_wallet.private_key,
            Blockchain::BASE => &user_wallets.base_wallet.private_key,
            _ => {
                println!("❌ Unsupported blockchain for native fee transfer");
                return Ok(None);
            }
        };

        // Execute native token transfer
        match self.transfer_native_admin_fee(
            wallet_address,
            private_key,
            admin_address,
            fee_amount_wei,
            blockchain,
        ).await {
            Ok(transaction_hash) => {
                println!("✅ Native fee transfer completed: {}", transaction_hash);

                if let Err(e) = self.admin_fee_service.mark_fee_transaction_completed(
                    fee_transaction_id,
                    transaction_hash,
                    None,
                    None,
                ).await {
                    println!("⚠️ Failed to mark fee transaction as completed: {}", e);
                }

                Ok(Some(fee_transaction_id))
            }
            Err(e) => {
                println!("❌ Native fee transfer failed: {}", e);

                if let Err(mark_err) = self.admin_fee_service.mark_fee_transaction_failed(
                    fee_transaction_id,
                    e.to_string(),
                ).await {
                    println!("⚠️ Failed to mark fee transaction as failed: {}", mark_err);
                }

                Err(e)
            }
        }
    }

    /// Helper: Execute token fee transfer
    async fn execute_token_fee_transfer(
        &self,
        wallet_address: &str,
        user_id: ObjectId,
        trade_id: Option<ObjectId>,
        admin_address: &str,
        token_address: &str,
        fee_amount: &str,
        fee_percentage: f64,
        blockchain: &Blockchain,
        original_token_amount: f64,
    ) -> Result<Option<ObjectId>, BotError> {
        // Get token decimals for proper calculation
        let token_decimals = self.get_token_decimals(token_address, blockchain).await.unwrap_or(18);
        let fee_amount_tokens = fee_amount.parse::<f64>().unwrap_or(0.0) / 10_f64.powi(token_decimals as i32);

        // Create fee transaction record with REAL values
        let fee_transaction_id = match self.admin_fee_service.create_fee_transaction(
            user_id,
            trade_id, // Use actual trade_id
            blockchain.clone(),
            FeeTransactionType::SellFee,
            fee_percentage, // REAL fee percentage from settings
            original_token_amount, // Original token amount being sold
            fee_amount_tokens, // Actual fee amount in token units
            "TOKEN".to_string(),
            token_address.to_string(),
            admin_address.to_string(),
            wallet_address.to_string(),
        ).await {
            Ok(id) => id,
            Err(e) => {
                println!("❌ Failed to create fee transaction record: {}", e);
                return Ok(None);
            }
        };

        // Execute ERC-20 token transfer
        match self.transfer_erc20_admin_fee(
            wallet_address,
            token_address,
            admin_address,
            fee_amount,
            blockchain,
        ).await {
            Ok(transaction_hash) => {
                println!("✅ Token fee transfer completed: {}", transaction_hash);

                if let Err(e) = self.admin_fee_service.mark_fee_transaction_completed(
                    fee_transaction_id,
                    transaction_hash,
                    None,
                    None,
                ).await {
                    println!("⚠️ Failed to mark fee transaction as completed: {}", e);
                }

                Ok(Some(fee_transaction_id))
            }
            Err(e) => {
                println!("❌ Token fee transfer failed: {}", e);

                if let Err(mark_err) = self.admin_fee_service.mark_fee_transaction_failed(
                    fee_transaction_id,
                    e.to_string(),
                ).await {
                    println!("⚠️ Failed to mark fee transaction as failed: {}", mark_err);
                }

                Err(e)
            }
        }
    }

    /// Helper: Get native token balance
    async fn get_native_balance(
        &self,
        wallet_address: &str,
        blockchain: &Blockchain,
    ) -> Result<ethers::types::U256, BotError> {
        use ethers::prelude::*;
        use std::str::FromStr;

        let wallet_addr = Address::from_str(wallet_address)
            .map_err(|e| BotError::validation_error(format!("Invalid wallet address: {}", e)))?;

        let rpc_url = self.get_rpc_url_with_fallback(blockchain);
        let provider = Provider::<Http>::try_from(&rpc_url)
            .map_err(|e| BotError::network_error(format!("Failed to create provider: {}", e)))?;

        match tokio::time::timeout(
            std::time::Duration::from_secs(30),
            provider.get_balance(wallet_addr, None)
        ).await {
            Ok(Ok(balance)) => Ok(balance),
            Ok(Err(e)) => Err(BotError::network_error(format!("Failed to get balance: {}", e))),
            Err(_) => Err(BotError::network_error("Timeout getting balance".to_string())),
        }
    }

    /// OPTIMIZED: Parallel buy admin fee collection for EVM chains (non-blocking) with smart $0.25 threshold
    pub async fn collect_buy_admin_fee_parallel_evm(
        &self,
        wallet_address: &str,
        private_key: &str,
        user_id: ObjectId,
        trade_id: Option<ObjectId>,
        fee_amount_wei: &str,
        original_amount_wei: &str,
        blockchain: &Blockchain,
    ) -> Result<Option<ObjectId>, BotError> {
        let fee_amount_native = fee_amount_wei.parse::<f64>().unwrap_or(0.0) / 1e18;
        println!("🚀 Smart parallel EVM admin fee collection: {} {}",
            fee_amount_native, blockchain.get_native_symbol());

        // Check if fee meets minimum and adjust ONLY if below $0.25 equivalent
        let price_service = crate::service::price_service::PriceService::new();

        let final_fee_native = match price_service.get_adjusted_fee_amount(blockchain, fee_amount_native).await {
            Ok(adjusted_amount) => adjusted_amount,
            Err(e) => {
                println!("⚠️ Failed to get adjusted fee amount, using original: {}", e);
                fee_amount_native
            }
        };

        let final_fee_wei = (final_fee_native * 1e18) as u64;
        let original_fee_wei = fee_amount_wei.parse::<u64>().unwrap_or(0);

        if final_fee_wei != original_fee_wei {
            println!("💰 Parallel EVM admin fee adjusted from {} wei to {} wei (minimum $0.25 equivalent)",
                     original_fee_wei, final_fee_wei);
        } else {
            println!("✅ Parallel EVM admin fee using calculated amount: {} wei", final_fee_wei);
        }

        // Get actual admin fee percentage
        let fee_percentage = match self.admin_fee_service.get_admin_fee_percentage_for_blockchain(blockchain).await {
            Ok(percentage) => percentage,
            Err(e) => {
                println!("⚠️ Failed to get admin fee percentage for {:?}, using default {}%: {}", blockchain, defaults::DEFAULT_ADMIN_FEE_PERCENTAGE, e);
                defaults::DEFAULT_ADMIN_FEE_PERCENTAGE
            }
        };

        if fee_percentage <= 0.0 {
            println!("ℹ️ Admin fee disabled ({}%) for buy operation", fee_percentage);
            return Ok(None);
        }

        // Get admin wallet address
        let admin_wallet_address = match self.admin_fee_service.get_admin_wallet_address(blockchain).await {
            Ok(address) => address,
            Err(e) => {
                println!("❌ No admin address configured for {:?}: {}", blockchain, e);
                return Ok(None);
            }
        };

        // Create fee transaction record with REAL values
        let fee_transaction_id = match self.admin_fee_service.create_fee_transaction(
            user_id,
            trade_id, // Use actual trade_id
            blockchain.clone(),
            FeeTransactionType::BuyFee,
            fee_percentage, // REAL fee percentage from settings
            original_amount_wei.parse::<f64>().unwrap_or(0.0) / 1e18, // Original buy amount in native tokens
            fee_amount_wei.parse::<f64>().unwrap_or(0.0) / 1e18, // Actual fee amount in native tokens
            blockchain.get_native_symbol().to_string(),
            "native".to_string(),
            admin_wallet_address.clone(),
            wallet_address.to_string(),
        ).await {
            Ok(id) => id,
            Err(e) => {
                println!("❌ Failed to create fee transaction record: {}", e);
                return Ok(None);
            }
        };

        // Execute native token transfer for admin fee
        match self.transfer_native_admin_fee(
            wallet_address,
            private_key,
            &admin_wallet_address,
            fee_amount_wei,
            blockchain,
        ).await {
            Ok(transaction_hash) => {
                println!("✅ EVM buy admin fee collected in parallel: {}", transaction_hash);

                // Mark fee transaction as completed
                if let Err(e) = self.admin_fee_service.mark_fee_transaction_completed(
                    fee_transaction_id,
                    transaction_hash,
                    None,
                    None,
                ).await {
                    println!("⚠️ Failed to mark fee transaction as completed: {}", e);
                }

                Ok(Some(fee_transaction_id))
            }
            Err(e) => {
                println!("⚠️ EVM buy admin fee collection failed: {}", e);

                // Mark fee transaction as failed
                if let Err(mark_err) = self.admin_fee_service.mark_fee_transaction_failed(
                    fee_transaction_id,
                    e.to_string(),
                ).await {
                    println!("⚠️ Failed to mark fee transaction as failed: {}", mark_err);
                }

                // Continue with trade execution - don't fail the entire trade due to fee collection failure
                println!("✅ Continuing trade execution despite fee collection failure");
                // Return success since the main swap succeeded
                Ok(None)
            }
        }
    }

    /// OPTIMIZED: Execute EVM buy with parallel fee collection
    pub async fn execute_buy_with_parallel_fee_collection(
        &self,
        wallet: &Wallet,
        quote: ZeroExQuoteResponse,
        blockchain: &Blockchain,
        chat_id: Option<i64>,
    ) -> Result<String, BotError> {
        println!("Starting parallel EVM buy execution: swap + fee collection");

        // CRITICAL: chat_id is required for proper data linking
        let chat_id_val = if let Some(id) = chat_id {
            id
        } else {
            println!("❌ CRITICAL: No chat_id provided for EVM buy operation");
            return Err(BotError::validation_error("chat_id is required for buy operations".to_string()));
        };

        // Get real user ID from database using actual chat_id - CRITICAL: Don't use fallback ObjectId::new()
        let user_id = match crate::service::DbService::find_user_by_chat_id(chat_id_val).await {
            Ok(Some(user)) => {
                if let Some(id) = user.id {
                    id
                } else {
                    println!("❌ CRITICAL: User found but has no ID for chat_id: {}", chat_id_val);
                    return Err(BotError::validation_error("User has no ID in database".to_string()));
                }
            }
            Ok(None) => {
                println!("❌ CRITICAL: No user found for chat_id: {}", chat_id_val);
                return Err(BotError::validation_error(format!("No user found for chat_id: {}", chat_id_val)));
            }
            Err(e) => {
                println!("❌ CRITICAL: Error finding user by chat_id {}: {}", chat_id_val, e);
                return Err(BotError::database_error(format!("Failed to find user: {}", e)));
            }
        };

        // Calculate fee amount for parallel collection
        use ethers::types::U256;
        let sell_amount_wei = U256::from_dec_str(&quote.sellAmount)
            .map_err(|e| BotError::validation_error(format!("Invalid sell amount: {}", e)))?;

        let fee_percentage = self.admin_fee_service.get_admin_fee_percentage_for_blockchain(blockchain).await.unwrap_or(defaults::DEFAULT_ADMIN_FEE_PERCENTAGE);
        let fee_amount_wei = if fee_percentage > 0.0 {
            sell_amount_wei * U256::from((fee_percentage * 100.0) as u64) / U256::from(10000)
        } else {
            U256::zero()
        };

        let actual_swap_amount = sell_amount_wei.saturating_sub(fee_amount_wei);

        // Create modified quote for reduced amount
        let mut modified_quote = quote.clone();
        modified_quote.sellAmount = actual_swap_amount.to_string();

        // Create futures for parallel execution
        let swap_future = async {
            println!("🔄 Executing swap with reduced amount: {} {}",
                actual_swap_amount.as_u128() as f64 / 1e18,
                blockchain.get_native_symbol());
            self.execute_0x_swap_internal(&modified_quote, &wallet.address, &wallet.private_key, blockchain).await
        };

        let fee_future = async {
            if fee_amount_wei > U256::zero() {
                println!("💰 Collecting admin fee in parallel: {} {}",
                    fee_amount_wei.as_u128() as f64 / 1e18,
                    blockchain.get_native_symbol());
                self.collect_buy_admin_fee_parallel_evm(
                    &wallet.address,
                    &wallet.private_key,
                    user_id,
                    None, // trade_id will be set after trade is saved
                    &fee_amount_wei.to_string(),
                    &sell_amount_wei.to_string(),
                    blockchain,
                ).await
            } else {
                Ok(None)
            }
        };

        // EXECUTE BOTH SIMULTANEOUSLY WITH HIGH PRIORITY
        println!("Starting parallel EVM execution with maximum resource allocation");
        let start_time = std::time::Instant::now();
        let (swap_result, fee_result) = tokio::try_join!(swap_future, fee_future)?;
        println!("✅ Parallel EVM execution completed in {:?}", start_time.elapsed());
        let transaction_hash = swap_result;

        // Store fee transaction ID for later linking with trade
        let fee_transaction_id = fee_result;

        // Log parallel execution results
        if let Some(fee_tx_id) = fee_transaction_id {
            println!("✅ EVM buy admin fee collected in parallel: {}", fee_tx_id);
        } else {
            println!("ℹ️ EVM buy admin fee collection skipped or disabled");
        }

        println!("✅ Parallel EVM buy execution completed: swap + fee collection");

        // Now save the trade and link the fee transaction
        self.save_trade_and_link_fee(
            wallet,
            &quote,
            &transaction_hash,
            blockchain,
            chat_id,
            user_id,
            fee_transaction_id,
            true, // is_buy
        ).await?;

        Ok(transaction_hash)
    }

    /// Helper function to save trade and link fee transaction
    async fn save_trade_and_link_fee(
        &self,
        wallet: &Wallet,
        quote: &ZeroExQuoteResponse,
        transaction_hash: &str,
        blockchain: &Blockchain,
        chat_id: Option<i64>,
        user_id: ObjectId,
        fee_transaction_id: Option<ObjectId>,
        is_buy: bool,
    ) -> Result<(), BotError> {
        use crate::model::Trade;
        use mongodb::bson::Decimal128;
        use chrono::Utc;
        use std::str::FromStr;

        // Get user information for the trade record
        let (user_first_name, user_username) = if let Some(chat_id) = chat_id {
            match crate::service::DbService::find_user_by_chat_id(chat_id).await {
                Ok(Some(user)) => (Some(user.first_name), user.username),
                Ok(None) => (None, None),
                Err(_) => (None, None),
            }
        } else {
            (None, None)
        };

        // Get token address (the non-native token in the pair)
        let token_address = if is_buy {
            quote.buyToken.clone()
        } else {
            quote.sellToken.clone()
        };

        // Get token symbol and name from contract
        let token_symbol = self.get_token_symbol(&token_address, blockchain).await
            .unwrap_or_else(|_| format!("TOKEN_{}", &token_address[2..8].to_uppercase()));
        let token_name = self.get_token_name(&token_address, blockchain).await
            .unwrap_or_else(|_| format!("Token {}", &token_address[2..8].to_uppercase()));

        // Calculate real gas fee from transaction data
        let gas_fee_eth = {
            if let Ok(gas_used) = quote.transaction.gas.parse::<u64>() {
                if let Ok(gas_price) = quote.transaction.gasPrice.parse::<u64>() {
                    Some((gas_used * gas_price) as f64 / 1e18) // Convert wei to ETH
                } else { None }
            } else { None }
        };

        // Calculate real amount out (not placeholder)
        let real_amount_out = quote.buyAmount.parse::<f64>()
            .map_err(|e| BotError::validation_error(format!("Invalid buyAmount: {}", e)))?;

        // Get admin fee percentage for this blockchain with intelligent fallback
        // Priority: 1. Blockchain-specific fee -> 2. Default admin fee -> 3. Hardcoded default (0.5%)
        let admin_fee_percentage = match self.admin_fee_service.get_admin_fee_percentage_for_blockchain(blockchain).await {
            Ok(percentage) => {
                println!("✅ Retrieved admin fee percentage for {:?}: {}%", blockchain, percentage);
                percentage
            }
            Err(e) => {
                println!("⚠️ Failed to get admin fee percentage for {:?}, using hardcoded default: {}", blockchain, e);
                defaults::DEFAULT_ADMIN_FEE_PERCENTAGE
            }
        };

        // Get native token symbol based on blockchain
        let native_symbol = match blockchain {
            Blockchain::ETH => "ETH",
            Blockchain::BSC => "BNB",
            Blockchain::BASE => "ETH",
            Blockchain::SOL => "SOL", // Should not happen in EVM context
        };

        // Calculate amounts based on trade type
        let (native_token_amount, token_amount) = if is_buy {
            // For buy: native_token_amount = ETH/BNB spent, token_amount = tokens received
            let native_spent = quote.sellAmount.parse::<f64>().unwrap_or(0.0) / 1e18;
            let tokens_received = quote.buyAmount.parse::<f64>().unwrap_or(0.0) / 1e18;
            (Some(native_spent), Some(tokens_received))
        } else {
            // For sell: token_amount = tokens sold, native_token_amount = ETH/BNB received
            let tokens_sold = quote.sellAmount.parse::<f64>().unwrap_or(0.0) / 1e18;
            let native_received = quote.buyAmount.parse::<f64>().unwrap_or(0.0) / 1e18;
            (Some(native_received), Some(tokens_sold))
        };

        // Calculate admin fee amount
        let admin_fee_amount = if let Some(native_amount) = native_token_amount {
            Some((native_amount * admin_fee_percentage) / 100.0)
        } else {
            None
        };

        // Create a new trade record with real data
        let trade = Trade {
            id: None,
            user_id,
            user_first_name,
            user_username,
            blockchain: blockchain.clone(),
            token_address: token_address.clone(),
            token_symbol: token_symbol.clone(),
            trade_type: if is_buy { "buy".to_string() } else { "sell".to_string() },

            // Enhanced amount tracking
            native_token_amount,
            token_amount,
            native_token_symbol: Some(native_symbol.to_string()),

            // Legacy field for backward compatibility
            amount_out: Some(if is_buy {
                native_token_amount.unwrap_or(0.0) // For buy, show native tokens spent
            } else {
                token_amount.unwrap_or(0.0) // For sell, show tokens sold
            }),

            status: Some("completed".to_string()),
            timestamp: Utc::now().timestamp(),
            gas_fee: gas_fee_eth,
            hash: Some(transaction_hash.to_string()),
            block_number: None, // Will be updated when we get transaction receipt
            error_message: None,

            // Admin fee information
            admin_fee_amount,
            admin_fee_percentage: Some(admin_fee_percentage),
            admin_fee_status: Some("pending".to_string()),
            admin_fee_collection_method: Some("smart_collection".to_string()),
            admin_fee_token_symbol: Some(native_symbol.to_string()),
            admin_fee_token_address: Some(match blockchain {
                Blockchain::ETH => "******************************************", // ETH
                Blockchain::BSC => "******************************************", // BNB
                Blockchain::BASE => "******************************************", // ETH on Base
                Blockchain::SOL => "So11111111111111111111111111111111111111112", // Should not happen
            }.to_string()),
            admin_fee_transaction_id: fee_transaction_id,
            // Legacy fields for backward compatibility
            contract_address: Some(token_address.clone()),
            token_name: Some(token_name),
            amount: Some(Decimal128::from_str(&quote.buyAmount).unwrap_or_else(|_| Decimal128::from_str("0").unwrap())),
            price: Some(Decimal128::from_str(&quote.sellAmount).unwrap_or_else(|_| Decimal128::from_str("0").unwrap())),
            transaction_hash: Some(transaction_hash.to_string()),
            created_at: Some(Utc::now()),
            updated_at: Some(Utc::now()),
        };

        // Save trade and get the generated trade ID
        let trade_id = match crate::service::DbService::save_trade(&trade).await {
            Ok(saved_trade) => {
                println!("Trade saved to database successfully");

                // Link fee transaction with trade_id if fee was collected
                if let (Some(fee_tx_id), Some(actual_trade_id)) = (fee_transaction_id, Some(saved_trade)) {
                    if let Err(e) = self.admin_fee_service.update_fee_transaction_trade_id(fee_tx_id, actual_trade_id).await {
                        println!("⚠️ Failed to link fee transaction with trade: {}", e);
                    } else {
                        println!("✅ Fee transaction {} linked with trade {}", fee_tx_id, actual_trade_id);
                    }
                }

                Some(saved_trade)
            }
            Err(e) => {
                println!("Warning: Failed to save trade to database: {}", e);
                None
            }
        };

        // Update user trades collection if needed
        if let Some(saved_trade_id) = trade_id {
            if let Some(chat_id) = chat_id {
                if let Ok(Some(user)) = crate::service::DbService::find_user_by_chat_id(chat_id).await {
                    if let Some(user_id) = user.id {
                        if let Ok(Some(mut user_trades)) = crate::service::DbService::find_user_trades(user_id).await {
                            match blockchain {
                                Blockchain::BSC => user_trades.trades.bsc.push(saved_trade_id),
                                Blockchain::ETH => user_trades.trades.eth.push(saved_trade_id),
                                Blockchain::BASE => user_trades.trades.base.push(saved_trade_id),
                                _ => {}
                            }

                            if let Err(e) = crate::service::DbService::save_user_trades(&user_trades).await {
                                println!("Warning: Failed to update user trades: {}", e);
                            }
                        }
                    }
                }
            }
        }

        Ok(())
    }

    /// Internal function to execute 0x swap without trade saving (for parallel execution)
    async fn execute_0x_swap_internal(
        &self,
        quote: &ZeroExQuoteResponse,
        wallet_address: &str,
        private_key: &str,
        blockchain: &Blockchain,
    ) -> Result<String, BotError> {
        println!("Executing 0x swap internally for parallel execution");

        let config = crate::config::AppConfig::get();
        let native_token_address = std::env::var(ENV_TOKEN_ADDRESS_NATIVE)
            .unwrap_or_else(|_| config.token_address_native.clone());

        let chain_id = match blockchain {
            Blockchain::ETH => std::env::var(ENV_CHAIN_ID_ETH)
                .unwrap_or_else(|_| config.chain_id_eth.to_string())
                .parse::<u64>()
                .unwrap_or(1u64),
            Blockchain::BSC => std::env::var(ENV_CHAIN_ID_BSC)
                .unwrap_or_else(|_| config.chain_id_bsc.to_string())
                .parse::<u64>()
                .unwrap_or(56u64),
            Blockchain::BASE => std::env::var(ENV_CHAIN_ID_BASE)
                .unwrap_or_else(|_| config.chain_id_base.to_string())
                .parse::<u64>()
                .unwrap_or(8453u64),
            Blockchain::SOL => return Err(BotError::blockchain_error("Solana not supported by EVM trader service".to_string())),
        };

        let wallet = match LocalWallet::from_str(private_key) {
            Ok(wallet) => wallet.with_chain_id(chain_id),
            Err(e) => {
                println!("Failed to create wallet: {}", e);
                return Err(BotError::blockchain_error("Failed to create wallet for transaction".to_string()));
            }
        };

        let rpc_url = self.get_rpc_url_with_fallback(blockchain);
        let provider = match Provider::<Http>::try_from(rpc_url) {
            Ok(provider) => provider,
            Err(e) => {
                println!("Failed to create provider: {}", e);
                return Err(BotError::blockchain_error("Failed to connect to blockchain network".to_string()));
            }
        };

        let client = SignerMiddleware::new(provider, wallet.clone());
        let client = Arc::new(client);

        let to_address = match Address::from_str(&quote.transaction.to) {
            Ok(address) => address,
            Err(e) => {
                println!("Invalid to address: {}", e);
                return Err(BotError::blockchain_error("Invalid transaction data".to_string()));
            }
        };

        let data = match hex::decode(&quote.transaction.data[2..]) {
            Ok(data) => data,
            Err(e) => {
                println!("Invalid data: {}", e);
                return Err(BotError::blockchain_error("Invalid transaction data".to_string()));
            }
        };

        let value = match U256::from_dec_str(&quote.transaction.value) {
            Ok(value) => value,
            Err(e) => {
                println!("Invalid value: {}", e);
                return Err(BotError::blockchain_error("Invalid transaction data".to_string()));
            }
        };

        let gas_price = match U256::from_dec_str(&quote.transaction.gasPrice) {
            Ok(gas_price) => gas_price,
            Err(e) => {
                println!("Invalid gas price: {}", e);
                return Err(BotError::blockchain_error("Invalid transaction data".to_string()));
            }
        };

        let gas_limit = match U256::from_dec_str(&quote.transaction.gas) {
            Ok(gas_limit) => gas_limit,
            Err(e) => {
                println!("Invalid gas limit: {}", e);
                return Err(BotError::blockchain_error("Invalid transaction data".to_string()));
            }
        };

        // Get the current nonce
        let nonce = match client.get_transaction_count(client.address(), None).await {
            Ok(nonce) => nonce,
            Err(e) => {
                println!("Failed to get nonce: {}", e);
                return Err(BotError::blockchain_error(format!("Failed to get nonce: {}", e)));
            }
        };

        let tx = TransactionRequest::new()
            .to(to_address)
            .value(value)
            .data(data)
            .gas_price(gas_price)
            .gas(gas_limit)
            .nonce(nonce);

        // Send the transaction
        println!("Sending 0x swap transaction...");
        let pending_tx = match client.send_transaction(tx, None).await {
            Ok(tx) => tx,
            Err(e) => {
                println!("Failed to send transaction: {}", e);
                return Err(BotError::blockchain_error(format!("Failed to send transaction: {}", e)));
            }
        };

        println!("Transaction hash: {:?}", pending_tx.tx_hash());

        // Wait for the transaction to be mined with timeout
        let receipt = match tokio::time::timeout(
            std::time::Duration::from_secs(defaults::SWAP_TIMEOUT_SECONDS),
            pending_tx
        ).await {
            Ok(Ok(receipt)) => receipt,
            Ok(Err(e)) => {
                println!("Transaction failed: {}", e);
                return Err(BotError::blockchain_error(format!("Transaction failed: {}", e)));
            },
            Err(_) => {
                println!("Transaction confirmation timeout after 60 seconds");
                return Err(BotError::blockchain_error("Transaction confirmation timeout".to_string()));
            }
        };

        // Check if the transaction was successful
        if let Some(receipt_value) = &receipt {
            if let Some(status) = receipt_value.status {
                if status == U64::from(0) {
                    println!("Transaction failed with status 0");
                    return Err(BotError::blockchain_error("Transaction failed on-chain".to_string()));
                }
            }
        }

        // Get the transaction hash
        let tx_hash = format!("0x{:x}", receipt.unwrap().transaction_hash);
        println!("✅ 0x swap completed successfully: {}", tx_hash);

        Ok(tx_hash)
    }

    /// High-performance native token transfer for admin fee collection
    pub async fn execute_high_performance_native_transfer(
        &self,
        blockchain: &Blockchain,
        private_key: &str,
        recipient: &str,
        amount_wei: u64,
    ) -> Result<String, BotError> {
        println!("🚀 High-performance EVM native transfer: {} wei to {}", amount_wei, recipient);

        let chain_id = match blockchain {
            Blockchain::ETH => 1u64,
            Blockchain::BSC => 56u64,
            Blockchain::BASE => 8453u64,
            Blockchain::SOL => return Err(BotError::blockchain_error("Solana not supported by EVM trader service".to_string())),
        };

        let wallet = LocalWallet::from_str(private_key)
            .map_err(|e| BotError::blockchain_error(format!("Invalid private key: {}", e)))?
            .with_chain_id(chain_id);

        let config = crate::config::AppConfig::get();
        let rpc_url = match blockchain {
            Blockchain::ETH => std::env::var(ENV_RPC_URL_ETH)
                .unwrap_or_else(|_| config.rpc_url_eth.clone()),
            Blockchain::BSC => std::env::var(ENV_RPC_URL_BSC)
                .unwrap_or_else(|_| config.rpc_url_bsc.clone()),
            Blockchain::BASE => std::env::var(ENV_RPC_URL_BASE)
                .unwrap_or_else(|_| config.rpc_url_base.clone()),
            Blockchain::SOL => return Err(BotError::blockchain_error("Solana not supported".to_string())),
        };

        let provider = Provider::<Http>::try_from(rpc_url)
            .map_err(|e| BotError::blockchain_error(format!("Failed to create provider: {}", e)))?;

        let client = Arc::new(SignerMiddleware::new(provider, wallet));

        let recipient_address = Address::from_str(recipient)
            .map_err(|e| BotError::blockchain_error(format!("Invalid recipient address: {}", e)))?;

        // Create high-priority transfer transaction
        let tx = TransactionRequest::new()
            .to(recipient_address)
            .value(U256::from(amount_wei))
            .from(client.address());

        // Send with high priority
        let pending_tx = client
            .send_transaction(tx, None)
            .await
            .map_err(|e| BotError::blockchain_error(format!("Failed to send native transfer: {}", e)))?;

        let receipt = pending_tx
            .await
            .map_err(|e| BotError::blockchain_error(format!("Transfer transaction failed: {}", e)))?;

        let tx_hash = if let Some(receipt) = receipt {
            format!("0x{:x}", receipt.transaction_hash)
        } else {
            return Err(BotError::blockchain_error("No transaction receipt received".to_string()));
        };

        println!("✅ High-performance EVM native transfer completed: {}", tx_hash);
        Ok(tx_hash)
    }

    /// Standard native token transfer fallback method
    pub async fn transfer_native_token(
        &self,
        blockchain: &Blockchain,
        private_key: &str,
        recipient: &str,
        amount_wei: u64,
    ) -> Result<String, BotError> {
        println!("🔄 Standard EVM native transfer: {} wei to {}", amount_wei, recipient);

        // Use the existing transfer_native_tokens_with_private_key method
        self.transfer_native_tokens_with_private_key(
            &format!("{:?}", Address::from_str(private_key).unwrap_or_default()), // from_address (not used in implementation)
            recipient,
            amount_wei,
            private_key,
            blockchain,
        ).await
    }
}
