import { useAdminStore } from '../store/adminStore';

export default function DebugAuth() {
  const { isAuthenticated, currentUser, token } = useAdminStore();

  return (
    <div style={{
      position: 'fixed',
      top: '10px',
      right: '10px',
      background: '#1f2937',
      color: 'white',
      padding: '10px',
      borderRadius: '5px',
      fontSize: '12px',
      zIndex: 9999,
      maxWidth: '300px',
      border: '1px solid #374151'
    }}>
      <h4 style={{ margin: '0 0 10px 0', color: '#60a5fa' }}>Auth Debug</h4>
      <div><strong>Authenticated:</strong> {isAuthenticated ? 'Yes' : 'No'}</div>
      <div><strong>User:</strong> {currentUser ? currentUser.username : 'None'}</div>
      <div><strong>Token:</strong> {token ? `${token.substring(0, 20)}...` : 'None'}</div>
      <div><strong>LocalStorage Token:</strong> {localStorage.getItem('admin_token') ? 'Present' : 'None'}</div>
    </div>
  );
}
