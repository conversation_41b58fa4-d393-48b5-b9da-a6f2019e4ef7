[env]
# Configure OpenSSL paths to use bin directory for both lib and include
OPENSSL_NO_VENDOR = "1"
OPENSSL_STATIC = "0"
OPENSSL_DIR = "C:\\Program Files\\OpenSSL"
OPENSSL_LIB_DIR = "C:\\Program Files\\OpenSSL\\bin"
OPENSSL_INCLUDE_DIR = "C:\\Program Files\\OpenSSL\\bin"
RUSTLS_NATIVE_CERTS = "1"

[target.'cfg(windows)']
rustflags = ["-C", "target-feature=+crt-static"]

[net]
git-fetch-with-cli = true
retry = 10

