use std::str::FromStr;
use solana_sdk::instruction::{AccountMeta, Instruction};
use solana_sdk::pubkey::Pubkey;
use solana_sdk::signature::Keypair;
use solana_sdk::signer::Signer;
use solana_sdk::transaction::Transaction;
use solana_sdk::message::Message;
use solana_sdk::compute_budget::ComputeBudgetInstruction;
use solana_sdk::system_instruction;
use spl_token::instruction as token_instruction;
use spl_associated_token_account::instruction as ata_instruction;
use spl_associated_token_account::get_associated_token_address;
use thiserror::Error;

use crate::service::direct_swap_service::RaydiumPool;

// Constants for Raydium AMM
const RAYDIUM_SWAP_PROGRAM_ID: &str = "675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8";

// Error type for Raydium swap operations
#[derive(Error, Debug)]
pub enum RaydiumSwapError {
    #[error("Invalid pool: {0}")]
    InvalidPool(String),
    
    #[error("Invalid mint: {0}")]
    InvalidMint(String),
    
    #[error("Invalid amount: {0}")]
    InvalidAmount(String),
    
    #[error("Instruction creation error: {0}")]
    InstructionError(String),
}

// Raydium swap instruction data layout
#[derive(Debug, Clone)]
pub struct SwapInstructionData {
    pub instruction: u8,
    pub amount_in: u64,
    pub min_amount_out: u64,
}

impl SwapInstructionData {
    pub fn new(amount_in: u64, min_amount_out: u64) -> Self {
        Self {
            instruction: 9, // Swap instruction code for Raydium
            amount_in,
            min_amount_out,
        }
    }
    
    pub fn serialize(&self) -> Vec<u8> {
        let mut data = Vec::with_capacity(17);
        data.push(self.instruction);
        data.extend_from_slice(&self.amount_in.to_le_bytes());
        data.extend_from_slice(&self.min_amount_out.to_le_bytes());
        data
    }
}

// Create a Raydium swap instruction
pub fn create_swap_instruction(
    pool: &RaydiumPool,
    user_pubkey: &Pubkey,
    user_source_token: &Pubkey,
    user_destination_token: &Pubkey,
    amount_in: u64,
    min_amount_out: u64,
    is_base_input: bool,
) -> Result<Instruction, RaydiumSwapError> {
    // Parse program ID
    let program_id = Pubkey::from_str(RAYDIUM_SWAP_PROGRAM_ID)
        .map_err(|e| RaydiumSwapError::InvalidPool(e.to_string()))?;
    
    // Parse pool accounts
    let amm_id = Pubkey::from_str(&pool.id)
        .map_err(|e| RaydiumSwapError::InvalidPool(e.to_string()))?;
    let amm_authority = Pubkey::from_str(&pool.authority)
        .map_err(|e| RaydiumSwapError::InvalidPool(e.to_string()))?;
    let amm_open_orders = Pubkey::from_str(&pool.open_orders)
        .map_err(|e| RaydiumSwapError::InvalidPool(e.to_string()))?;
    let amm_target_orders = Pubkey::from_str(&pool.target_orders)
        .map_err(|e| RaydiumSwapError::InvalidPool(e.to_string()))?;
    let pool_base_vault = Pubkey::from_str(&pool.base_vault)
        .map_err(|e| RaydiumSwapError::InvalidPool(e.to_string()))?;
    let pool_quote_vault = Pubkey::from_str(&pool.quote_vault)
        .map_err(|e| RaydiumSwapError::InvalidPool(e.to_string()))?;
    
    // Parse market accounts
    let market_program_id = Pubkey::from_str(&pool.market_program_id)
        .map_err(|e| RaydiumSwapError::InvalidPool(e.to_string()))?;
    let market_id = Pubkey::from_str(&pool.market_id)
        .map_err(|e| RaydiumSwapError::InvalidPool(e.to_string()))?;
    let market_bids = Pubkey::from_str(&pool.market_bids)
        .map_err(|e| RaydiumSwapError::InvalidPool(e.to_string()))?;
    let market_asks = Pubkey::from_str(&pool.market_asks)
        .map_err(|e| RaydiumSwapError::InvalidPool(e.to_string()))?;
    let market_event_queue = Pubkey::from_str(&pool.market_event_queue)
        .map_err(|e| RaydiumSwapError::InvalidPool(e.to_string()))?;
    let market_base_vault = Pubkey::from_str(&pool.market_base_vault)
        .map_err(|e| RaydiumSwapError::InvalidPool(e.to_string()))?;
    let market_quote_vault = Pubkey::from_str(&pool.market_quote_vault)
        .map_err(|e| RaydiumSwapError::InvalidPool(e.to_string()))?;
    let market_authority = Pubkey::from_str(&pool.market_authority)
        .map_err(|e| RaydiumSwapError::InvalidPool(e.to_string()))?;
    
    // Determine source and destination vaults based on direction
    let (source_vault, destination_vault) = if is_base_input {
        (pool_base_vault, pool_quote_vault)
    } else {
        (pool_quote_vault, pool_base_vault)
    };
    
    // Create instruction data
    let instruction_data = SwapInstructionData::new(amount_in, min_amount_out).serialize();
    
    // Create account metas
    let accounts = vec![
        AccountMeta::new(amm_id, false),
        AccountMeta::new_readonly(amm_authority, false),
        AccountMeta::new_readonly(amm_open_orders, false),
        AccountMeta::new(amm_target_orders, false),
        AccountMeta::new(source_vault, false),
        AccountMeta::new(destination_vault, false),
        AccountMeta::new(*user_source_token, false),
        AccountMeta::new(*user_destination_token, false),
        AccountMeta::new(*user_pubkey, true),
        AccountMeta::new_readonly(market_program_id, false),
        AccountMeta::new(market_id, false),
        AccountMeta::new(market_bids, false),
        AccountMeta::new(market_asks, false),
        AccountMeta::new(market_event_queue, false),
        AccountMeta::new(market_base_vault, false),
        AccountMeta::new(market_quote_vault, false),
        AccountMeta::new_readonly(market_authority, false),
        AccountMeta::new_readonly(spl_token::id(), false),
    ];
    
    // Create instruction
    Ok(Instruction {
        program_id,
        accounts,
        data: instruction_data,
    })
}

// Create a transaction for Raydium swap
pub fn create_swap_transaction(
    pool: &RaydiumPool,
    wallet: &Keypair,
    input_mint: &Pubkey,
    output_mint: &Pubkey,
    amount_in: u64,
    min_amount_out: u64,
    recent_blockhash: solana_sdk::hash::Hash,
) -> Result<Transaction, RaydiumSwapError> {
    // Parse base and quote mints
    let base_mint = Pubkey::from_str(&pool.base_mint)
        .map_err(|e| RaydiumSwapError::InvalidPool(e.to_string()))?;
    let quote_mint = Pubkey::from_str(&pool.quote_mint)
        .map_err(|e| RaydiumSwapError::InvalidPool(e.to_string()))?;
    
    // Determine if input is base or quote
    let is_base_input = input_mint == &base_mint;
    
    // Get associated token accounts
    let user_source_token = get_associated_token_address(&wallet.pubkey(), input_mint);
    let user_destination_token = get_associated_token_address(&wallet.pubkey(), output_mint);
    
    // Create compute budget instruction to increase compute units
    let compute_budget_ix = ComputeBudgetInstruction::set_compute_unit_limit(1_400_000);
    
    // Create compute budget instruction to set priority fee
    let priority_fee_ix = ComputeBudgetInstruction::set_compute_unit_price(1000);
    
    // Create instruction to create destination token account if it doesn't exist
    let create_ata_ix = ata_instruction::create_associated_token_account(
        &wallet.pubkey(),
        &wallet.pubkey(),
        output_mint,
        &spl_token::id(),
    );
    
    // Create swap instruction
    let swap_ix = create_swap_instruction(
        pool,
        &wallet.pubkey(),
        &user_source_token,
        &user_destination_token,
        amount_in,
        min_amount_out,
        is_base_input,
    )?;
    
    // Create transaction
    let message = Message::new(
        &[compute_budget_ix, priority_fee_ix, create_ata_ix, swap_ix],
        Some(&wallet.pubkey()),
    );
    
    let transaction = Transaction::new(&[wallet], message, recent_blockhash);
    
    Ok(transaction)
}
