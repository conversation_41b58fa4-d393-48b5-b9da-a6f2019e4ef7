use axum::{
    extract::{Query, State},
    http::{HeaderMap, StatusCode},
    response::IntoResponse,
    routing::{get, post, put},
    Json, Router,
};
use mongodb::{bson::doc, Collection, Database};
use serde::{Deserialize, Serialize};
use std::{collections::HashMap, sync::Arc, time::Duration};
use tokio::time::timeout;
use futures_util::TryStreamExt;
use log::{debug, error, info, warn};
use serde_json;

use crate::{
    config::AppConfig,
    model::{AdminFeeTransaction, Blockchain, BotError, Trade},
    service::DbService,
    controllers::auth_controller::Claims,
};

const DB_OPERATION_TIMEOUT: Duration = Duration::from_secs(30);

#[derive(Debug)]
pub struct FeeAnalyticsController {
    config: AppConfig,
    jwt_secret: String,
}

#[derive(Debug, Serialize)]
pub struct FeeAnalyticsResponse {
    pub total_fees_collected: f64,
    pub fees_collected_24h: f64,
    pub fees_collected_7d: f64,
    pub fees_collected_30d: f64,
    pub fee_transactions_count: i64,
    pub pending_fee_transactions: i64,
    pub failed_fee_transactions: i64,
    pub blockchain_breakdown: HashMap<String, BlockchainFeeStats>,
    pub hourly_fee_collection: Vec<HourlyFeeStats>,
    pub top_fee_contributors: Vec<UserFeeStats>,
    pub fee_collection_rate: f64,
    pub average_fee_amount: f64,
}

#[derive(Debug, Serialize)]
pub struct BlockchainFeeStats {
    pub blockchain: String,
    pub total_fees: f64,
    pub fees_24h: f64,
    pub transaction_count: i64,
    pub pending_count: i64,
    pub failed_count: i64,
    pub success_rate: f64,
    pub average_fee: f64,
    pub top_token: String,
    pub top_token_fees: f64,
}

#[derive(Debug, Serialize)]
pub struct HourlyFeeStats {
    pub hour: u64,
    pub fees_collected: f64,
    pub transaction_count: i64,
    pub unique_users: i64,
    pub average_fee: f64,
}

#[derive(Debug, Serialize)]
pub struct UserFeeStats {
    pub user_id: String,
    pub total_fees_paid: f64,
    pub transaction_count: i64,
    pub favorite_blockchain: String,
    pub last_fee_payment: u64,
}

#[derive(Debug, Deserialize)]
pub struct FeeAnalyticsQuery {
    pub start_date: Option<u64>,
    pub end_date: Option<u64>,
    pub blockchain: Option<String>,
    pub status: Option<String>,
    pub page: Option<i64>,
    pub per_page: Option<i64>,
}

#[derive(Debug, Serialize)]
pub struct AggregatedFeeTransaction {
    pub id: String,
    pub user_id: String,
    pub username: Option<String>,
    pub first_name: Option<String>,
    pub transaction_type: String,
    pub blockchain: String,
    pub fee_amount: f64,
    pub fee_token_symbol: String,
    pub fee_token_address: String,
    pub status: String,
    pub created_at: u64,
    pub retry_count: i32,
    pub transaction_hash: Option<String>,
}

#[derive(Debug, Serialize)]
pub struct AggregatedFeeStatistics {
    pub total_fees_collected: f64,
    pub pending_fees: f64,
    pub failed_fees: f64,
    pub success_rate: f64,
    pub fees_by_blockchain: HashMap<String, f64>,
    pub fees_by_status: HashMap<String, i64>,
    pub total_transactions: i64,
}

#[derive(Debug, Serialize)]
pub struct AggregatedFeeResponse {
    pub transactions: Vec<AggregatedFeeTransaction>,
    pub statistics: AggregatedFeeStatistics,
    pub total: i64,
    pub page: i64,
    pub per_page: i64,
}

impl FeeAnalyticsController {
    pub fn new(config: AppConfig, jwt_secret: String) -> Self {
        Self { config, jwt_secret }
    }

    pub fn create_router(&self) -> Router {
        let controller = Arc::new(self.clone());

        Router::new()
            // Keep only the essential routes that might be used by the transactions page
            .route("/api/admin/fee-transactions", get(Self::get_fee_transactions))
            .route("/api/admin/fee-transactions/:id/retry", post(Self::retry_fee_transaction))
            .with_state(controller)
    }

    async fn get_fee_analytics(
        State(controller): State<Arc<FeeAnalyticsController>>,
        headers: HeaderMap,
        Query(params): Query<FeeAnalyticsQuery>,
    ) -> impl IntoResponse {
        if let Some(_claims) = controller.verify_auth_header(&headers).await {
            match controller.calculate_fee_analytics(params).await {
                Ok(analytics) => (StatusCode::OK, Json(analytics)).into_response(),
                Err(e) => {
                    error!("Failed to calculate fee analytics: {:?}", e);
                    (StatusCode::INTERNAL_SERVER_ERROR, Json(serde_json::json!({
                        "error": "Failed to calculate fee analytics",
                        "code": "FEE_ANALYTICS_ERROR"
                    }))).into_response()
                }
            }
        } else {
            (StatusCode::UNAUTHORIZED, Json(serde_json::json!({
                "error": "Unauthorized"
            }))).into_response()
        }
    }

    async fn get_detailed_fee_analytics(
        State(controller): State<Arc<FeeAnalyticsController>>,
        headers: HeaderMap,
        Query(params): Query<FeeAnalyticsQuery>,
    ) -> impl IntoResponse {
        if let Some(_claims) = controller.verify_auth_header(&headers).await {
            match controller.get_detailed_analytics(params).await {
                Ok(analytics) => (StatusCode::OK, Json(analytics)).into_response(),
                Err(e) => {
                    error!("Failed to get detailed fee analytics: {:?}", e);
                    (StatusCode::INTERNAL_SERVER_ERROR, Json(serde_json::json!({
                        "error": "Failed to get detailed fee analytics",
                        "code": "DETAILED_FEE_ANALYTICS_ERROR"
                    }))).into_response()
                }
            }
        } else {
            (StatusCode::UNAUTHORIZED, Json(serde_json::json!({
                "error": "Unauthorized"
            }))).into_response()
        }
    }

    async fn get_blockchain_fee_analytics(
        State(controller): State<Arc<FeeAnalyticsController>>,
        headers: HeaderMap,
        axum::extract::Path(blockchain): axum::extract::Path<String>,
        Query(params): Query<FeeAnalyticsQuery>,
    ) -> impl IntoResponse {
        if let Some(_claims) = controller.verify_auth_header(&headers).await {
            match controller.get_blockchain_specific_analytics(&blockchain, params).await {
                Ok(analytics) => (StatusCode::OK, Json(analytics)).into_response(),
                Err(e) => {
                    error!("Failed to get blockchain fee analytics: {:?}", e);
                    (StatusCode::INTERNAL_SERVER_ERROR, Json(serde_json::json!({
                        "error": "Failed to get blockchain fee analytics",
                        "code": "BLOCKCHAIN_FEE_ANALYTICS_ERROR"
                    }))).into_response()
                }
            }
        } else {
            (StatusCode::UNAUTHORIZED, Json(serde_json::json!({
                "error": "Unauthorized"
            }))).into_response()
        }
    }

    async fn get_fee_transactions(
        State(controller): State<Arc<FeeAnalyticsController>>,
        headers: HeaderMap,
        Query(params): Query<FeeAnalyticsQuery>,
    ) -> impl IntoResponse {
        if let Some(_claims) = controller.verify_auth_header(&headers).await {
            match controller.get_aggregated_fee_data(params).await {
                Ok(response) => (StatusCode::OK, Json(response)).into_response(),
                Err(e) => {
                    error!("Failed to get aggregated fee data: {:?}", e);
                    (StatusCode::INTERNAL_SERVER_ERROR, Json(serde_json::json!({
                        "error": "Failed to get aggregated fee data",
                        "code": "FEE_DATA_ERROR"
                    }))).into_response()
                }
            }
        } else {
            (StatusCode::UNAUTHORIZED, Json(serde_json::json!({
                "error": "Unauthorized"
            }))).into_response()
        }
    }

    async fn retry_fee_transaction(
        State(controller): State<Arc<FeeAnalyticsController>>,
        headers: HeaderMap,
        axum::extract::Path(transaction_id): axum::extract::Path<String>,
    ) -> impl IntoResponse {
        if let Some(_claims) = controller.verify_auth_header(&headers).await {
            match controller.retry_failed_fee_transaction(&transaction_id).await {
                Ok(_) => (StatusCode::OK, Json(serde_json::json!({
                    "message": "Fee transaction retry initiated successfully"
                }))).into_response(),
                Err(e) => {
                    error!("Failed to retry fee transaction: {:?}", e);
                    (StatusCode::INTERNAL_SERVER_ERROR, Json(serde_json::json!({
                        "error": "Failed to retry fee transaction",
                        "code": "FEE_RETRY_ERROR"
                    }))).into_response()
                }
            }
        } else {
            (StatusCode::UNAUTHORIZED, Json(serde_json::json!({
                "error": "Unauthorized"
            }))).into_response()
        }
    }

    async fn get_fee_statistics(
        State(controller): State<Arc<FeeAnalyticsController>>,
        headers: HeaderMap,
        Query(params): Query<FeeAnalyticsQuery>,
    ) -> impl IntoResponse {
        if let Some(_claims) = controller.verify_auth_header(&headers).await {
            match controller.calculate_fee_analytics(params).await {
                Ok(analytics) => (StatusCode::OK, Json(analytics)).into_response(),
                Err(e) => {
                    error!("Failed to get fee statistics: {:?}", e);
                    (StatusCode::INTERNAL_SERVER_ERROR, Json(serde_json::json!({
                        "error": "Failed to get fee statistics",
                        "code": "FEE_STATISTICS_ERROR"
                    }))).into_response()
                }
            }
        } else {
            (StatusCode::UNAUTHORIZED, Json(serde_json::json!({
                "error": "Unauthorized"
            }))).into_response()
        }
    }

    async fn get_fee_settings(
        State(controller): State<Arc<FeeAnalyticsController>>,
        headers: HeaderMap,
    ) -> impl IntoResponse {
        if let Some(_claims) = controller.verify_auth_header(&headers).await {
            match controller.get_current_fee_settings().await {
                Ok(settings) => (StatusCode::OK, Json(settings)).into_response(),
                Err(e) => {
                    error!("Failed to get fee settings: {:?}", e);
                    (StatusCode::INTERNAL_SERVER_ERROR, Json(serde_json::json!({
                        "error": "Failed to get fee settings",
                        "code": "FEE_SETTINGS_ERROR"
                    }))).into_response()
                }
            }
        } else {
            (StatusCode::UNAUTHORIZED, Json(serde_json::json!({
                "error": "Unauthorized"
            }))).into_response()
        }
    }

    async fn update_fee_settings(
        State(controller): State<Arc<FeeAnalyticsController>>,
        headers: HeaderMap,
        Json(settings): Json<serde_json::Value>,
    ) -> impl IntoResponse {
        if let Some(_claims) = controller.verify_auth_header(&headers).await {
            match controller.update_fee_settings_impl(settings).await {
                Ok(_) => (StatusCode::OK, Json(serde_json::json!({
                    "message": "Fee settings updated successfully"
                }))).into_response(),
                Err(e) => {
                    error!("Failed to update fee settings: {:?}", e);
                    (StatusCode::INTERNAL_SERVER_ERROR, Json(serde_json::json!({
                        "error": "Failed to update fee settings",
                        "code": "FEE_SETTINGS_UPDATE_ERROR"
                    }))).into_response()
                }
            }
        } else {
            (StatusCode::UNAUTHORIZED, Json(serde_json::json!({
                "error": "Unauthorized"
            }))).into_response()
        }
    }

    async fn calculate_fee_analytics(&self, params: FeeAnalyticsQuery) -> Result<FeeAnalyticsResponse, BotError> {
        let db = DbService::get_db();
        let collection: Collection<AdminFeeTransaction> = db.collection("admin_fee_transactions");

        let now = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap()
            .as_secs();

        let end_time = params.end_date.unwrap_or(now);
        let start_time = params.start_date.unwrap_or(0);
        let twenty_four_hours_ago = now - (24 * 60 * 60);
        let seven_days_ago = now - (7 * 24 * 60 * 60);
        let thirty_days_ago = now - (30 * 24 * 60 * 60);

        info!("Calculating fee analytics for period: {} to {}", start_time, end_time);

        // Build base filter
        let mut base_filter = doc! {
            "created_at": {
                "$gte": start_time as i64,
                "$lte": end_time as i64
            }
        };

        if let Some(blockchain) = &params.blockchain {
            base_filter.insert("blockchain", blockchain);
        }

        if let Some(status) = &params.status {
            base_filter.insert("status", status);
        }

        // Execute aggregations concurrently
        let total_fees_future = self.calculate_total_fees(&collection, base_filter.clone());
        let fees_24h_future = self.calculate_period_fees(&collection, twenty_four_hours_ago);
        let fees_7d_future = self.calculate_period_fees(&collection, seven_days_ago);
        let fees_30d_future = self.calculate_period_fees(&collection, thirty_days_ago);
        let transaction_counts_future = self.calculate_transaction_counts(&collection, base_filter.clone());
        let blockchain_breakdown_future = self.calculate_blockchain_breakdown(&collection, base_filter.clone());
        let hourly_stats_future = self.calculate_hourly_fee_stats(&collection, twenty_four_hours_ago);
        let top_contributors_future = self.calculate_top_fee_contributors(&collection, base_filter.clone());

        let results = timeout(
            DB_OPERATION_TIMEOUT,
            async {
                tokio::try_join!(
                    total_fees_future,
                    fees_24h_future,
                    fees_7d_future,
                    fees_30d_future,
                    transaction_counts_future,
                    blockchain_breakdown_future,
                    hourly_stats_future,
                    top_contributors_future
                )
            }
        ).await
        .map_err(|_| BotError::database_error("Fee analytics calculation timeout".to_string()))?;

        match results {
            Ok((
                total_fees,
                fees_24h,
                fees_7d,
                fees_30d,
                (total_count, pending_count, failed_count),
                blockchain_breakdown,
                hourly_stats,
                top_contributors
            )) => {
                let fee_collection_rate = if total_count > 0 {
                    ((total_count - failed_count) as f64 / total_count as f64) * 100.0
                } else {
                    0.0
                };

                let average_fee_amount = if total_count > 0 {
                    total_fees / total_count as f64
                } else {
                    0.0
                };

                Ok(FeeAnalyticsResponse {
                    total_fees_collected: total_fees,
                    fees_collected_24h: fees_24h,
                    fees_collected_7d: fees_7d,
                    fees_collected_30d: fees_30d,
                    fee_transactions_count: total_count,
                    pending_fee_transactions: pending_count,
                    failed_fee_transactions: failed_count,
                    blockchain_breakdown,
                    hourly_fee_collection: hourly_stats,
                    top_fee_contributors: top_contributors,
                    fee_collection_rate,
                    average_fee_amount,
                })
            }
            Err(e) => {
                error!("Fee analytics calculation failed: {:?}", e);
                Err(BotError::database_error(format!("Fee analytics calculation failed: {}", e)))
            }
        }
    }

    async fn calculate_total_fees(
        &self,
        collection: &Collection<AdminFeeTransaction>,
        filter: mongodb::bson::Document,
    ) -> Result<f64, BotError> {
        let pipeline = vec![
            doc! { "$match": filter },
            doc! { "$match": { "status": "completed" } },
            doc! {
                "$group": {
                    "_id": null,
                    "total_fees": { "$sum": "$fee_amount" }
                }
            }
        ];

        let mut cursor = collection.aggregate(pipeline, None).await
            .map_err(|e| BotError::database_error(format!("Failed to aggregate total fees: {}", e)))?;

        if let Some(result) = cursor.try_next().await
            .map_err(|e| BotError::database_error(format!("Failed to read total fees result: {}", e)))? {
            Ok(result.get_f64("total_fees").unwrap_or(0.0))
        } else {
            Ok(0.0)
        }
    }

    async fn calculate_period_fees(
        &self,
        collection: &Collection<AdminFeeTransaction>,
        start_time: u64,
    ) -> Result<f64, BotError> {
        let filter = doc! {
            "created_at": { "$gte": start_time as i64 },
            "status": "completed"
        };

        let pipeline = vec![
            doc! { "$match": filter },
            doc! {
                "$group": {
                    "_id": null,
                    "period_fees": { "$sum": "$fee_amount" }
                }
            }
        ];

        let mut cursor = collection.aggregate(pipeline, None).await
            .map_err(|e| BotError::database_error(format!("Failed to aggregate period fees: {}", e)))?;

        if let Some(result) = cursor.try_next().await
            .map_err(|e| BotError::database_error(format!("Failed to read period fees result: {}", e)))? {
            Ok(result.get_f64("period_fees").unwrap_or(0.0))
        } else {
            Ok(0.0)
        }
    }

    async fn calculate_transaction_counts(
        &self,
        collection: &Collection<AdminFeeTransaction>,
        filter: mongodb::bson::Document,
    ) -> Result<(i64, i64, i64), BotError> {
        let pipeline = vec![
            doc! { "$match": filter },
            doc! {
                "$group": {
                    "_id": "$status",
                    "count": { "$sum": 1 }
                }
            }
        ];

        let mut cursor = collection.aggregate(pipeline, None).await
            .map_err(|e| BotError::database_error(format!("Failed to aggregate transaction counts: {}", e)))?;

        let mut total_count = 0i64;
        let mut pending_count = 0i64;
        let mut failed_count = 0i64;

        while let Some(result) = cursor.try_next().await
            .map_err(|e| BotError::database_error(format!("Failed to read transaction counts: {}", e)))? {
            let status = result.get_str("_id").unwrap_or("");
            let count = result.get_i64("count").unwrap_or(0);

            total_count += count;
            match status {
                "pending" => pending_count = count,
                "failed" => failed_count = count,
                _ => {}
            }
        }

        Ok((total_count, pending_count, failed_count))
    }

    async fn calculate_blockchain_breakdown(
        &self,
        collection: &Collection<AdminFeeTransaction>,
        filter: mongodb::bson::Document,
    ) -> Result<HashMap<String, BlockchainFeeStats>, BotError> {
        let pipeline = vec![
            doc! { "$match": filter },
            doc! {
                "$group": {
                    "_id": {
                        "blockchain": "$blockchain",
                        "status": "$status"
                    },
                    "total_fees": { "$sum": "$fee_amount" },
                    "count": { "$sum": 1 },
                    "avg_fee": { "$avg": "$fee_amount" },
                    "tokens": { "$push": { "symbol": "$fee_token_symbol", "amount": "$fee_amount" } }
                }
            },
            doc! {
                "$group": {
                    "_id": "$_id.blockchain",
                    "stats": {
                        "$push": {
                            "status": "$_id.status",
                            "total_fees": "$total_fees",
                            "count": "$count",
                            "avg_fee": "$avg_fee",
                            "tokens": "$tokens"
                        }
                    }
                }
            }
        ];

        let mut cursor = collection.aggregate(pipeline, None).await
            .map_err(|e| BotError::database_error(format!("Failed to aggregate blockchain breakdown: {}", e)))?;

        let mut breakdown = HashMap::new();

        while let Some(result) = cursor.try_next().await
            .map_err(|e| BotError::database_error(format!("Failed to read blockchain breakdown: {}", e)))? {

            if let (Ok(blockchain), Ok(stats_array)) = (
                result.get_str("_id"),
                result.get_array("stats")
            ) {
                let mut total_fees = 0.0;
                let mut fees_24h = 0.0;
                let mut transaction_count = 0i64;
                let mut pending_count = 0i64;
                let mut failed_count = 0i64;
                let mut completed_count = 0i64;
                let mut token_fees: HashMap<String, f64> = HashMap::new();

                for stat in stats_array {
                    if let Some(stat_doc) = stat.as_document() {
                        let status = stat_doc.get_str("status").unwrap_or("");
                        let fees = stat_doc.get_f64("total_fees").unwrap_or(0.0);
                        let count = stat_doc.get_i64("count").unwrap_or(0);

                        transaction_count += count;

                        match status {
                            "completed" => {
                                completed_count += count;
                                total_fees += fees;
                                fees_24h += fees; // Simplified - would need time filtering for accurate 24h
                            },
                            "pending" => pending_count += count,
                            "failed" => failed_count += count,
                            _ => {}
                        }

                        // Process token breakdown
                        if let Ok(tokens_array) = stat_doc.get_array("tokens") {
                            for token in tokens_array {
                                if let Some(token_doc) = token.as_document() {
                                    if let (Ok(symbol), Ok(amount)) = (
                                        token_doc.get_str("symbol"),
                                        token_doc.get_f64("amount")
                                    ) {
                                        *token_fees.entry(symbol.to_string()).or_insert(0.0) += amount;
                                    }
                                }
                            }
                        }
                    }
                }

                let success_rate = if transaction_count > 0 {
                    (completed_count as f64 / transaction_count as f64) * 100.0
                } else {
                    0.0
                };

                let average_fee = if completed_count > 0 {
                    total_fees / completed_count as f64
                } else {
                    0.0
                };

                // Find top token by fees
                let (top_token, top_token_fees) = token_fees.iter()
                    .max_by(|a, b| a.1.partial_cmp(b.1).unwrap_or(std::cmp::Ordering::Equal))
                    .map(|(k, v)| (k.clone(), *v))
                    .unwrap_or_else(|| ("N/A".to_string(), 0.0));

                breakdown.insert(blockchain.to_string(), BlockchainFeeStats {
                    blockchain: blockchain.to_string(),
                    total_fees,
                    fees_24h,
                    transaction_count,
                    pending_count,
                    failed_count,
                    success_rate,
                    average_fee,
                    top_token,
                    top_token_fees,
                });
            }
        }

        Ok(breakdown)
    }

    async fn calculate_hourly_fee_stats(
        &self,
        collection: &Collection<AdminFeeTransaction>,
        start_time: u64,
    ) -> Result<Vec<HourlyFeeStats>, BotError> {
        let pipeline = vec![
            doc! {
                "$match": {
                    "created_at": { "$gte": start_time as i64 },
                    "status": "completed"
                }
            },
            doc! {
                "$addFields": {
                    "hour_bucket": {
                        "$floor": {
                            "$divide": [
                                { "$subtract": ["$created_at", start_time as i64] },
                                3600
                            ]
                        }
                    }
                }
            },
            doc! {
                "$group": {
                    "_id": "$hour_bucket",
                    "fees_collected": { "$sum": "$fee_amount" },
                    "transaction_count": { "$sum": 1 },
                    "unique_users": { "$addToSet": "$user_id" }
                }
            },
            doc! {
                "$project": {
                    "hour": "$_id",
                    "fees_collected": 1,
                    "transaction_count": 1,
                    "unique_users": { "$size": "$unique_users" },
                    "average_fee": { "$divide": ["$fees_collected", "$transaction_count"] }
                }
            },
            doc! { "$sort": { "hour": 1 } }
        ];

        let mut cursor = collection.aggregate(pipeline, None).await
            .map_err(|e| BotError::database_error(format!("Failed to aggregate hourly stats: {}", e)))?;

        let mut hourly_data: HashMap<i64, HourlyFeeStats> = HashMap::new();

        while let Some(result) = cursor.try_next().await
            .map_err(|e| BotError::database_error(format!("Failed to read hourly stats: {}", e)))? {

            if let Ok(hour) = result.get_i64("hour") {
                let stats = HourlyFeeStats {
                    hour: hour as u64,
                    fees_collected: result.get_f64("fees_collected").unwrap_or(0.0),
                    transaction_count: result.get_i64("transaction_count").unwrap_or(0),
                    unique_users: result.get_i64("unique_users").unwrap_or(0),
                    average_fee: result.get_f64("average_fee").unwrap_or(0.0),
                };
                hourly_data.insert(hour, stats);
            }
        }

        // Fill in missing hours with zero data
        let mut hourly_stats = Vec::with_capacity(24);
        for hour in 0..24 {
            if let Some(stats) = hourly_data.remove(&hour) {
                hourly_stats.push(stats);
            } else {
                hourly_stats.push(HourlyFeeStats {
                    hour: hour as u64,
                    fees_collected: 0.0,
                    transaction_count: 0,
                    unique_users: 0,
                    average_fee: 0.0,
                });
            }
        }

        Ok(hourly_stats)
    }

    async fn calculate_top_fee_contributors(
        &self,
        collection: &Collection<AdminFeeTransaction>,
        filter: mongodb::bson::Document,
    ) -> Result<Vec<UserFeeStats>, BotError> {
        let pipeline = vec![
            doc! { "$match": filter },
            doc! { "$match": { "status": "completed" } },
            doc! {
                "$group": {
                    "_id": "$user_id",
                    "total_fees_paid": { "$sum": "$fee_amount" },
                    "transaction_count": { "$sum": 1 },
                    "blockchains": { "$addToSet": "$blockchain" },
                    "last_fee_payment": { "$max": "$created_at" }
                }
            },
            doc! {
                "$project": {
                    "user_id": { "$toString": "$_id" },
                    "total_fees_paid": 1,
                    "transaction_count": 1,
                    "favorite_blockchain": { "$arrayElemAt": ["$blockchains", 0] },
                    "last_fee_payment": 1
                }
            },
            doc! { "$sort": { "total_fees_paid": -1 } },
            doc! { "$limit": 10 }
        ];

        let mut cursor = collection.aggregate(pipeline, None).await
            .map_err(|e| BotError::database_error(format!("Failed to aggregate top contributors: {}", e)))?;

        let mut contributors = Vec::new();

        while let Some(result) = cursor.try_next().await
            .map_err(|e| BotError::database_error(format!("Failed to read top contributors: {}", e)))? {

            contributors.push(UserFeeStats {
                user_id: result.get_str("user_id").unwrap_or("unknown").to_string(),
                total_fees_paid: result.get_f64("total_fees_paid").unwrap_or(0.0),
                transaction_count: result.get_i64("transaction_count").unwrap_or(0),
                favorite_blockchain: result.get_str("favorite_blockchain").unwrap_or("unknown").to_string(),
                last_fee_payment: result.get_i64("last_fee_payment").unwrap_or(0) as u64,
            });
        }

        Ok(contributors)
    }

    async fn get_detailed_analytics(&self, _params: FeeAnalyticsQuery) -> Result<serde_json::Value, BotError> {
        // Implementation for detailed analytics
        Ok(serde_json::json!({
            "message": "Detailed analytics implementation pending"
        }))
    }

    async fn get_blockchain_specific_analytics(&self, _blockchain: &str, _params: FeeAnalyticsQuery) -> Result<serde_json::Value, BotError> {
        // Implementation for blockchain-specific analytics
        Ok(serde_json::json!({
            "message": "Blockchain-specific analytics implementation pending"
        }))
    }

    async fn get_fee_transaction_list(&self, params: FeeAnalyticsQuery) -> Result<serde_json::Value, BotError> {
        let db = DbService::get_db();
        let collection: Collection<AdminFeeTransaction> = db.collection("admin_fee_transactions");

        // Build filter
        let mut filter = doc! {};

        if let Some(blockchain) = &params.blockchain {
            filter.insert("blockchain", blockchain);
        }

        if let Some(status) = &params.status {
            filter.insert("status", status);
        }

        if let Some(start_date) = params.start_date {
            if let Some(end_date) = params.end_date {
                filter.insert("created_at", doc! {
                    "$gte": start_date as i64,
                    "$lte": end_date as i64
                });
            } else {
                filter.insert("created_at", doc! { "$gte": start_date as i64 });
            }
        } else if let Some(end_date) = params.end_date {
            filter.insert("created_at", doc! { "$lte": end_date as i64 });
        }

        // Get transactions with pagination
        let mut cursor = collection.find(filter, None).await
            .map_err(|e| BotError::database_error(format!("Failed to query fee transactions: {}", e)))?;

        let mut transactions = Vec::new();
        while let Some(transaction) = cursor.try_next().await
            .map_err(|e| BotError::database_error(format!("Failed to read fee transaction: {}", e)))? {
            transactions.push(transaction);
        }

        Ok(serde_json::json!({
            "data": transactions,
            "total": transactions.len(),
            "page": 1,
            "per_page": transactions.len()
        }))
    }

    async fn retry_failed_fee_transaction(&self, transaction_id: &str) -> Result<(), BotError> {
        use mongodb::bson::oid::ObjectId;
        use std::str::FromStr;

        let object_id = ObjectId::from_str(transaction_id)
            .map_err(|e| BotError::validation_error(format!("Invalid transaction ID: {}", e)))?;

        let db = DbService::get_db();
        let collection: Collection<AdminFeeTransaction> = db.collection("admin_fee_transactions");

        // Update status to retrying
        collection.update_one(
            doc! { "_id": object_id },
            doc! {
                "$set": {
                    "status": "retrying",
                    "updated_at": chrono::Utc::now().timestamp() as i64
                }
            },
            None
        ).await
        .map_err(|e| BotError::database_error(format!("Failed to update transaction status: {}", e)))?;

        info!("Fee transaction retry initiated for ID: {}", transaction_id);
        Ok(())
    }

    async fn get_current_fee_settings(&self) -> Result<serde_json::Value, BotError> {
        let settings = serde_json::json!({
            "eth_fee_percentage": 0.5,
            "bsc_fee_percentage": 0.5,
            "base_fee_percentage": 0.5,
            "sol_fee_percentage": 0.5,
            "default_fee_percentage": 0.5,
            "eth_admin_wallet": "",
            "bsc_admin_wallet": "",
            "base_admin_wallet": "",
            "sol_admin_wallet": ""
        });

        Ok(settings)
    }

    async fn update_fee_settings_impl(&self, settings: serde_json::Value) -> Result<(), BotError> {
        info!("Updating fee settings: {:?}", settings);
        // This would typically update the settings in the database
        Ok(())
    }

    async fn get_aggregated_fee_data(&self, params: FeeAnalyticsQuery) -> Result<AggregatedFeeResponse, BotError> {
        let db = DbService::get_db();
        let collection: Collection<Trade> = db.collection("transactions");

        let page = params.page.unwrap_or(1).max(1);
        let per_page = params.per_page.unwrap_or(10).min(100);
        let skip = (page - 1) * per_page;

        // Build filter for transactions with admin fees
        let mut filter = doc! {
            "admin_fee_amount": { "$exists": true, "$ne": null }
        };

        if let Some(blockchain) = &params.blockchain {
            // Handle both uppercase and lowercase blockchain values
            let blockchain_filter = doc! {
                "$in": [blockchain, &blockchain.to_lowercase(), &blockchain.to_uppercase()]
            };
            filter.insert("blockchain", blockchain_filter);
        }

        if let Some(status) = &params.status {
            // Map status to admin_fee_status
            filter.insert("admin_fee_status", status);
        }

        info!("Fee transactions filter: {:?}", filter);

        if let Some(start_date) = params.start_date {
            filter.insert("timestamp", doc! { "$gte": start_date as i64 });
        }

        if let Some(end_date) = params.end_date {
            if filter.contains_key("timestamp") {
                if let Some(timestamp_filter) = filter.get_document_mut("timestamp").ok() {
                    timestamp_filter.insert("$lte", end_date as i64);
                }
            } else {
                filter.insert("timestamp", doc! { "$lte": end_date as i64 });
            }
        }

        // Get total count
        let total = collection.count_documents(filter.clone(), None).await
            .map_err(|e| BotError::database_error(format!("Failed to count transactions: {}", e)))? as i64;

        info!("Found {} transactions matching filter", total);

        // Get transactions with pagination
        let find_options = mongodb::options::FindOptions::builder()
            .skip(skip as u64)
            .limit(per_page)
            .build();

        let mut cursor = collection.find(filter.clone(), find_options).await
            .map_err(|e| BotError::database_error(format!("Failed to query transactions: {}", e)))?;

        let mut transactions = Vec::new();
        while let Some(trade) = cursor.try_next().await
            .map_err(|e| BotError::database_error(format!("Failed to read transaction: {}", e)))? {

            let fee_transaction = AggregatedFeeTransaction {
                id: trade.id.map(|id| id.to_hex()).unwrap_or_else(|| "unknown".to_string()),
                user_id: trade.user_id.to_hex(),
                username: trade.user_username.clone(),
                first_name: trade.user_first_name.clone(),
                transaction_type: if trade.trade_type == "buy" { "BuyFee".to_string() } else { "SellFee".to_string() },
                blockchain: trade.blockchain.to_string(),
                fee_amount: trade.admin_fee_amount.unwrap_or(0.0),
                fee_token_symbol: trade.admin_fee_token_symbol.unwrap_or_else(|| {
                    match trade.blockchain.to_string().as_str() {
                        "sol" => "SOL".to_string(),
                        "eth" => "ETH".to_string(),
                        "bsc" => "BNB".to_string(),
                        "base" => "ETH".to_string(),
                        _ => "UNKNOWN".to_string(),
                    }
                }),
                fee_token_address: trade.admin_fee_token_address.unwrap_or_else(|| "native".to_string()),
                status: trade.admin_fee_status.unwrap_or_else(|| "pending".to_string()),
                created_at: trade.timestamp as u64,
                retry_count: 0, // We don't track retries in the main transaction table
                transaction_hash: trade.admin_fee_transaction_id.map(|id| id.to_hex()),
            };
            transactions.push(fee_transaction);
        }

        // Calculate statistics
        let statistics = self.calculate_aggregated_statistics(&collection, filter).await?;

        Ok(AggregatedFeeResponse {
            transactions,
            statistics,
            total,
            page,
            per_page,
        })
    }

    async fn calculate_aggregated_statistics(&self, collection: &Collection<Trade>, filter: mongodb::bson::Document) -> Result<AggregatedFeeStatistics, BotError> {
        // Calculate total fees collected (completed status)
        let completed_filter = {
            let mut f = filter.clone();
            f.insert("admin_fee_status", "completed");
            f
        };

        let total_fees_pipeline = vec![
            doc! { "$match": completed_filter },
            doc! {
                "$group": {
                    "_id": null,
                    "total_fees": { "$sum": "$admin_fee_amount" }
                }
            }
        ];

        let mut cursor = collection.aggregate(total_fees_pipeline, None).await
            .map_err(|e| BotError::database_error(format!("Failed to aggregate total fees: {}", e)))?;

        let total_fees_collected = if let Some(result) = cursor.try_next().await
            .map_err(|e| BotError::database_error(format!("Failed to read total fees: {}", e)))? {
            result.get_f64("total_fees").unwrap_or(0.0)
        } else {
            0.0
        };

        // Calculate pending and failed fees
        let status_pipeline = vec![
            doc! { "$match": filter.clone() },
            doc! {
                "$group": {
                    "_id": "$admin_fee_status",
                    "total_amount": { "$sum": "$admin_fee_amount" },
                    "count": { "$sum": 1 }
                }
            }
        ];

        let mut cursor = collection.aggregate(status_pipeline, None).await
            .map_err(|e| BotError::database_error(format!("Failed to aggregate status fees: {}", e)))?;

        let mut pending_fees = 0.0;
        let mut failed_fees = 0.0;
        let mut fees_by_status = HashMap::new();
        let mut total_transactions = 0i64;

        while let Some(result) = cursor.try_next().await
            .map_err(|e| BotError::database_error(format!("Failed to read status fees: {}", e)))? {
            let status = result.get_str("_id").unwrap_or("unknown");
            let amount = result.get_f64("total_amount").unwrap_or(0.0);
            let count = result.get_i64("count").unwrap_or(0);

            total_transactions += count;
            fees_by_status.insert(status.to_string(), count);

            match status {
                "pending" => pending_fees = amount,
                "failed" => failed_fees = amount,
                _ => {}
            }
        }

        // Calculate fees by blockchain
        let blockchain_pipeline = vec![
            doc! { "$match": filter },
            doc! { "$match": { "admin_fee_status": "completed" } },
            doc! {
                "$group": {
                    "_id": "$blockchain",
                    "total_fees": { "$sum": "$admin_fee_amount" }
                }
            }
        ];

        let mut cursor = collection.aggregate(blockchain_pipeline, None).await
            .map_err(|e| BotError::database_error(format!("Failed to aggregate blockchain fees: {}", e)))?;

        let mut fees_by_blockchain = HashMap::new();
        while let Some(result) = cursor.try_next().await
            .map_err(|e| BotError::database_error(format!("Failed to read blockchain fees: {}", e)))? {
            let blockchain = result.get_str("_id").unwrap_or("unknown");
            let amount = result.get_f64("total_fees").unwrap_or(0.0);
            fees_by_blockchain.insert(blockchain.to_string(), amount);
        }

        // Calculate success rate
        let completed_count = fees_by_status.get("completed").unwrap_or(&0);
        let success_rate = if total_transactions > 0 {
            (*completed_count as f64 / total_transactions as f64) * 100.0
        } else {
            0.0
        };

        Ok(AggregatedFeeStatistics {
            total_fees_collected,
            pending_fees,
            failed_fees,
            success_rate,
            fees_by_blockchain,
            fees_by_status,
            total_transactions,
        })
    }

    async fn verify_auth_header(&self, headers: &HeaderMap) -> Option<Claims> {
        if let Some(auth_header) = headers.get("authorization") {
            if let Ok(auth_str) = auth_header.to_str() {
                if let Some(token) = auth_str.strip_prefix("Bearer ") {
                    // Simple JWT verification using jsonwebtoken crate
                    use jsonwebtoken::{decode, DecodingKey, Validation, Algorithm};

                    let mut validation = Validation::new(Algorithm::HS256);
                    validation.validate_exp = true;

                    if let Ok(token_data) = decode::<Claims>(
                        token,
                        &DecodingKey::from_secret(self.jwt_secret.as_ref()),
                        &validation,
                    ) {
                        return Some(token_data.claims);
                    }
                }
            }
        }
        None
    }
}

impl Clone for FeeAnalyticsController {
    fn clone(&self) -> Self {
        Self {
            config: self.config.clone(),
            jwt_secret: self.jwt_secret.clone(),
        }
    }
}
