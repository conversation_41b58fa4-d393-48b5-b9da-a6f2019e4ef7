import { useState, useEffect, useCallback, useRef } from 'react';
import { Card } from '../../components/ui/Card';
import Button from '../../components/ui/Button';
import { adminApi } from '../../services/adminApi';
import {
  MagnifyingGlassIcon,
  EyeIcon,
  ExclamationTriangleIcon,
  UserIcon,
  XMarkIcon,
} from '@heroicons/react/24/outline';

// Local interfaces to avoid import issues
interface User {
  id: string;
  chat_id: number;
  first_name: string;
  last_name?: string;
  username?: string;
  current_blockchain: 'bsc' | 'sol' | 'eth' | 'base';
  created_at: number;
  last_seen: number;
  total_transactions: number;
  total_volume: number;
  is_active: boolean;
}

interface UserListResponse {
  users: User[];
  total: number;
  page: number;
  per_page: number;
}

export default function Users() {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [blockchainFilter, setBlockchainFilter] = useState('all');
  const [statusFilter, setStatusFilter] = useState('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [usersPerPage] = useState(10);
  const [users, setUsers] = useState<User[]>([]);
  const [totalUsers, setTotalUsers] = useState(0);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [showUserModal, setShowUserModal] = useState(false);
  const [isInitialized, setIsInitialized] = useState(false);
  const loadingRef = useRef(false);

  const fetchUsers = useCallback(async () => {
    // Prevent duplicate calls if already loading
    if (loadingRef.current) {
      console.log('⏸️ Skipping fetch - already loading');
      return;
    }

    try {
      console.log('🔄 Fetching users...');
      loadingRef.current = true;
      setLoading(true);
      setError(null);

      const params: any = {
        page: currentPage,
        per_page: usersPerPage,
      };

      if (searchTerm.trim()) {
        params.search = searchTerm.trim();
      }

      if (blockchainFilter !== 'all') {
        params.blockchain = blockchainFilter;
      }

      if (statusFilter !== 'all') {
        params.status = statusFilter;
      }

      console.log('📤 API call params:', params);
      const response: UserListResponse = await adminApi.getUsers(params);
      console.log('📥 API response:', response);

      setUsers(response.users);
      setTotalUsers(response.total);

    } catch (err: any) {
      console.error('❌ Failed to fetch users:', err);
      setError(err.response?.data?.error || 'Failed to load users');
    } finally {
      loadingRef.current = false;
      setLoading(false);
    }
  }, [currentPage, usersPerPage, searchTerm, blockchainFilter, statusFilter]);

  // Initial load
  useEffect(() => {
    if (!isInitialized) {
      fetchUsers();
      setIsInitialized(true);
    }
  }, [fetchUsers, isInitialized]);

  // Handle filter and page changes (but not on initial load)
  useEffect(() => {
    if (isInitialized) {
      fetchUsers();
    }
  }, [currentPage, blockchainFilter, statusFilter, isInitialized, fetchUsers]);

  // Debounced search
  useEffect(() => {
    if (!isInitialized) return;

    const timer = setTimeout(() => {
      if (currentPage === 1) {
        fetchUsers();
      } else {
        setCurrentPage(1); // This will trigger the above useEffect
      }
    }, 500);

    return () => clearTimeout(timer);
  }, [searchTerm, isInitialized, fetchUsers, currentPage]);

  const totalPages = Math.ceil(totalUsers / usersPerPage);

  const handleViewUser = (user: User) => {
    setSelectedUser(user);
    setShowUserModal(true);
  };

  const handleCloseModal = () => {
    setSelectedUser(null);
    setShowUserModal(false);
  };

  const formatDate = (timestamp: number) => {
    const date = new Date(timestamp * 1000);
    return date.toLocaleDateString();
  };

  const formatLastSeen = (timestamp: number) => {
    const now = Date.now();
    const lastSeen = timestamp * 1000;
    const diffInMinutes = Math.floor((now - lastSeen) / (1000 * 60));

    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;

    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) return `${diffInHours}h ago`;

    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 30) return `${diffInDays}d ago`;

    const diffInMonths = Math.floor(diffInDays / 30);
    return `${diffInMonths}mo ago`;
  };

  const getStatusBadge = (user: User) => {
    const now = Date.now();
    const lastSeen = user.last_seen * 1000;
    const diffInHours = (now - lastSeen) / (1000 * 60 * 60);

    // Consider user active if seen within last 24 hours
    const isActive = diffInHours < 24;

    if (isActive) {
      return (
        <span className="inline-flex items-center rounded-full bg-green-900/30 border border-green-500/30 backdrop-blur-sm px-2.5 py-0.5 text-xs font-medium text-green-300">
          <span className="h-1.5 w-1.5 rounded-full bg-green-400 mr-1.5 animate-pulse"></span>
          Active
        </span>
      );
    } else {
      return (
        <span className="inline-flex items-center rounded-full bg-gray-900/30 border border-gray-500/30 backdrop-blur-sm px-2.5 py-0.5 text-xs font-medium text-gray-300">
          <span className="h-1.5 w-1.5 rounded-full bg-gray-400 mr-1.5"></span>
          Inactive
        </span>
      );
    }
  };

  const getBlockchainBadge = (blockchain: string) => {
    const colors = {
      sol: 'bg-purple-900/30 border-purple-500/30 text-purple-300',
      eth: 'bg-blue-900/30 border-blue-500/30 text-blue-300',
      bsc: 'bg-yellow-900/30 border-yellow-500/30 text-yellow-300',
      base: 'bg-indigo-900/30 border-indigo-500/30 text-indigo-300',
    };

    const colorClass = colors[blockchain as keyof typeof colors] || 'bg-gray-900/30 border-gray-500/30 text-gray-300';

    return (
      <span className={`inline-flex items-center rounded-full ${colorClass} backdrop-blur-sm px-2.5 py-0.5 text-xs font-medium`}>
        {blockchain.toUpperCase()}
      </span>
    );
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-8">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h1 className="text-2xl font-bold text-white">User Management</h1>
            <p className="mt-1 text-sm text-gray-400">Manage user accounts and permissions</p>
          </div>
        </div>

        <Card className="p-6">
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <ExclamationTriangleIcon className="h-12 w-12 text-red-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-white mb-2">Failed to Load Users</h3>
              <p className="text-gray-400 mb-4">{error}</p>
              <Button onClick={fetchUsers} variant="glass">
                Try Again
              </Button>
            </div>
          </div>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-2xl font-bold text-white">User Management</h1>
          <p className="mt-1 text-sm text-gray-400">Manage user accounts and permissions</p>
        </div>
        <Button
          variant="glass"
          glow={true}
          onClick={fetchUsers}
        >
          Refresh Users
        </Button>
      </div>

      <Card className="p-6">
        <div className="flex flex-col sm:flex-row justify-between gap-4 mb-6">
          <div className="relative flex-grow max-w-md">
            <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
              <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
            </div>
            <input
              type="text"
              className="block w-full rounded-xl border-0 py-2 pl-10 pr-4 text-white ring-1 ring-inset ring-white/10
                placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-red-500
                sm:text-sm sm:leading-6 bg-white/5 backdrop-blur-sm transition-all duration-300"
              placeholder="Search users by name or email"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>

          <div className="flex flex-col sm:flex-row gap-2 sm:gap-4">
            <select
              className="rounded-xl border-0 py-2 pl-3 pr-10 text-white ring-1 ring-inset ring-white/10
                focus:ring-2 focus:ring-inset focus:ring-red-500
                sm:text-sm sm:leading-6 bg-white/5 backdrop-blur-sm transition-all duration-300"
              value={blockchainFilter}
              onChange={(e) => setBlockchainFilter(e.target.value)}
            >
              <option value="all">All Blockchains</option>
              <option value="sol">Solana</option>
              <option value="eth">Ethereum</option>
              <option value="bsc">BSC</option>
              <option value="base">Base</option>
            </select>

            <select
              className="rounded-xl border-0 py-2 pl-3 pr-10 text-white ring-1 ring-inset ring-white/10
                focus:ring-2 focus:ring-inset focus:ring-red-500
                sm:text-sm sm:leading-6 bg-white/5 backdrop-blur-sm transition-all duration-300"
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
            >
              <option value="all">All Status</option>
              <option value="active">Active</option>
              <option value="inactive">Inactive</option>
            </select>
          </div>
        </div>

        <div className="overflow-hidden rounded-xl border border-white/10 bg-white/5 backdrop-blur-sm">
          <table className="min-w-full divide-y divide-white/10">
            <thead>
              <tr>
                <th scope="col" className="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-white sm:pl-6">
                  User
                </th>
                <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-white">
                  Username
                </th>
                <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-white">
                  Blockchain
                </th>
                <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-white">
                  Status
                </th>
                <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-white">
                  Created
                </th>
                <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-white">
                  Last Seen
                </th>
                <th scope="col" className="relative py-3.5 pl-3 pr-4 sm:pr-6">
                  <span className="sr-only">Actions</span>
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-white/10">
              {users.length === 0 ? (
                <tr>
                  <td colSpan={7} className="px-6 py-12 text-center">
                    <div className="text-gray-400">
                      <UserIcon className="h-12 w-12 mx-auto mb-4 text-gray-500" />
                      <h3 className="text-lg font-medium text-white mb-2">No users found</h3>
                      <p>No users match your current filters.</p>
                    </div>
                  </td>
                </tr>
              ) : (
                users.map((user) => (
                  <tr key={user.id} className="hover:bg-white/5 transition-colors duration-200">
                    <td className="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-white sm:pl-6">
                      <div className="flex items-center">
                        <div className="h-8 w-8 flex-shrink-0 rounded-full bg-indigo-800/30 flex items-center justify-center mr-3">
                          <UserIcon className="h-4 w-4 text-indigo-400" />
                        </div>
                        <div>
                          <div className="font-medium text-white">
                            {user.first_name} {user.last_name || ''}
                          </div>
                          <div className="text-xs text-gray-400">ID: {user.chat_id}</div>
                        </div>
                      </div>
                    </td>
                    <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-300">
                      {user.username ? `@${user.username}` : 'N/A'}
                    </td>
                    <td className="whitespace-nowrap px-3 py-4 text-sm">
                      {getBlockchainBadge(user.current_blockchain)}
                    </td>
                    <td className="whitespace-nowrap px-3 py-4 text-sm">
                      {getStatusBadge(user)}
                    </td>
                    <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-300">
                      {formatDate(user.created_at)}
                    </td>
                    <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-300">
                      {formatLastSeen(user.last_seen)}
                    </td>
                    <td className="relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-6">
                      <Button
                        variant="outline"
                        size="sm"
                        className="p-1"
                        title="View User Details"
                        onClick={() => handleViewUser(user)}
                      >
                        <EyeIcon className="h-4 w-4" />
                      </Button>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="flex items-center justify-between border-t border-white/10 bg-white/5 px-4 py-3 sm:px-6 mt-4 rounded-xl">
            <div className="flex flex-1 justify-between sm:hidden">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                disabled={currentPage === 1}
              >
                Previous
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                disabled={currentPage === totalPages}
              >
                Next
              </Button>
            </div>
            <div className="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
              <div>
                <p className="text-sm text-gray-400">
                  Showing <span className="font-medium text-white">{((currentPage - 1) * usersPerPage) + 1}</span> to{' '}
                  <span className="font-medium text-white">
                    {Math.min(currentPage * usersPerPage, totalUsers)}
                  </span>{' '}
                  of <span className="font-medium text-white">{totalUsers}</span> results
                </p>
              </div>
              <div>
                <nav className="isolate inline-flex -space-x-px rounded-md shadow-sm" aria-label="Pagination">
                  <Button
                    variant="outline"
                    size="sm"
                    className="rounded-l-md"
                    onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                    disabled={currentPage === 1}
                  >
                    Previous
                  </Button>
                  {Array.from({ length: totalPages }).map((_, index) => (
                    <Button
                      key={index}
                      variant={currentPage === index + 1 ? 'glass' : 'outline'}
                      size="sm"
                      className="px-4"
                      onClick={() => setCurrentPage(index + 1)}
                    >
                      {index + 1}
                    </Button>
                  ))}
                  <Button
                    variant="outline"
                    size="sm"
                    className="rounded-r-md"
                    onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                    disabled={currentPage === totalPages}
                  >
                    Next
                  </Button>
                </nav>
              </div>
            </div>
          </div>
        )}
      </Card>

      {/* User Details Modal */}
      {showUserModal && selectedUser && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex min-h-screen items-center justify-center px-4 pt-4 pb-20 text-center sm:block sm:p-0">
            {/* Background overlay */}
            <div
              className="fixed inset-0 bg-black bg-opacity-75 transition-opacity"
              onClick={handleCloseModal}
            ></div>

            {/* Modal panel */}
            <div className="inline-block transform overflow-hidden rounded-xl bg-gray-900 border border-white/10 px-4 pt-5 pb-4 text-left align-bottom shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-2xl sm:p-6 sm:align-middle">
              <div className="absolute top-0 right-0 pt-4 pr-4">
                <button
                  type="button"
                  className="rounded-md bg-gray-900 text-gray-400 hover:text-white focus:outline-none focus:ring-2 focus:ring-indigo-500"
                  onClick={handleCloseModal}
                >
                  <span className="sr-only">Close</span>
                  <XMarkIcon className="h-6 w-6" aria-hidden="true" />
                </button>
              </div>

              <div className="sm:flex sm:items-start">
                <div className="mx-auto flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-full bg-indigo-900/30 sm:mx-0 sm:h-10 sm:w-10">
                  <UserIcon className="h-6 w-6 text-indigo-400" aria-hidden="true" />
                </div>
                <div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left flex-1">
                  <h3 className="text-lg font-medium leading-6 text-white">
                    User Details
                  </h3>
                  <div className="mt-4">
                    <div className="bg-gray-800/50 rounded-lg p-4 space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-300">User ID</label>
                          <p className="mt-1 text-sm text-white font-mono">{selectedUser.id}</p>
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-300">Chat ID</label>
                          <p className="mt-1 text-sm text-white">{selectedUser.chat_id}</p>
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-300">First Name</label>
                          <p className="mt-1 text-sm text-white">{selectedUser.first_name}</p>
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-300">Last Name</label>
                          <p className="mt-1 text-sm text-white">{selectedUser.last_name || 'N/A'}</p>
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-300">Username</label>
                          <p className="mt-1 text-sm text-white">{selectedUser.username ? `@${selectedUser.username}` : 'N/A'}</p>
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-300">Current Blockchain</label>
                          <p className="mt-1 text-sm">
                            {getBlockchainBadge(selectedUser.current_blockchain)}
                          </p>
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-300">Status</label>
                          <p className="mt-1 text-sm">
                            {getStatusBadge(selectedUser)}
                          </p>
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-300">Active</label>
                          <p className="mt-1 text-sm text-white">{selectedUser.is_active ? 'Yes' : 'No'}</p>
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-300">Total Transactions</label>
                          <p className="mt-1 text-sm text-white">{selectedUser.total_transactions}</p>
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-300">Total Volume</label>
                          <p className="mt-1 text-sm text-white">${selectedUser.total_volume.toFixed(2)}</p>
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-300">Created At</label>
                          <p className="mt-1 text-sm text-white">{formatDate(selectedUser.created_at)}</p>
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-300">Last Seen</label>
                          <p className="mt-1 text-sm text-white">{formatLastSeen(selectedUser.last_seen)}</p>
                        </div>
                      </div>

                      {/* Raw JSON Data */}
                      <div className="mt-6">
                        <label className="block text-sm font-medium text-gray-300 mb-2">Raw JSON Data</label>
                        <div className="bg-gray-900 rounded-md p-3 overflow-x-auto">
                          <pre className="text-xs text-gray-300 whitespace-pre-wrap">
                            {JSON.stringify(selectedUser, null, 2)}
                          </pre>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="mt-5 sm:mt-4 sm:flex sm:flex-row-reverse">
                <Button
                  variant="glass"
                  onClick={handleCloseModal}
                  className="w-full sm:w-auto"
                >
                  Close
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
