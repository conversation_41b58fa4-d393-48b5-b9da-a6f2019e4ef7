use std::sync::Arc;
use mongodb::bson::{doc, oid::ObjectId};
use bcrypt::{hash, DEFAULT_COST};
use crate::config::AppConfig;
use crate::service::db_service::DbService;
use crate::model::{AdminUser, AdminRole};

pub struct AdminInitService;

impl AdminInitService {
    /// Initialize default super admin if it doesn't exist
    pub async fn initialize_default_admin() -> Result<(), Box<dyn std::error::Error>> {
        let config = AppConfig::get();
        let db = DbService::get_db();
        let collection = db.collection::<AdminUser>("admin_users");

        // Check if any super admin exists
        let super_admin_count = collection
            .count_documents(doc! { "role": "SuperAdmin" }, None)
            .await?;

        if super_admin_count == 0 {
            log::info!("No SuperAdmin found. Creating default super admin...");

            // Hash the default password
            let hashed_password = hash(&config.default_admin_password, DEFAULT_COST)?;

            // Create default super admin
            let default_admin = AdminUser {
                id: Some(ObjectId::new()),
                username: config.default_admin_username.clone(),
                email: config.default_admin_email.clone(),
                password_hash: hashed_password,
                role: AdminRole::SuperAdmin,
                created_at: std::time::SystemTime::now()
                    .duration_since(std::time::UNIX_EPOCH)?
                    .as_secs(),
                last_login: None,
                is_active: true,
                created_by: None,
                updated_at: std::time::SystemTime::now()
                    .duration_since(std::time::UNIX_EPOCH)?
                    .as_secs(),
            };

            // Insert the default admin
            collection.insert_one(&default_admin, None).await?;

            log::info!("Default SuperAdmin created successfully");
            log::info!("Username: {}", config.default_admin_username);
            log::info!("Email: {}", config.default_admin_email);
            log::warn!("Please change the default password after first login!");
        } else {
            log::info!("SuperAdmin already exists. Skipping default admin creation.");
        }

        Ok(())
    }

    /// Check if default admin credentials are still being used
    pub async fn check_default_credentials_security() -> Result<(), Box<dyn std::error::Error>> {
        let config = AppConfig::get();
        let db = DbService::get_db();
        let collection = db.collection::<AdminUser>("admin_users");

        // Find admin with default username
        if let Some(admin) = collection
            .find_one(doc! { "username": &config.default_admin_username }, None)
            .await?
        {
            // Check if password is still the default (this is a basic check)
            if bcrypt::verify(&config.default_admin_password, &admin.password_hash).unwrap_or(false) {
                log::warn!("⚠️  SECURITY WARNING: Default admin is still using default password!");
                log::warn!("⚠️  Please change the password immediately for security!");
                log::warn!("⚠️  Username: {}", config.default_admin_username);
                log::warn!("⚠️  Email: {}", config.default_admin_email);
            }
        }

        Ok(())
    }
}
