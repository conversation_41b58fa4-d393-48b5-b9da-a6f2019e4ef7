use crate::model::blockchain::Blockchain;
use crate::service::evm_trader_service::EvmTraderService;
use crate::service::solana_trader_service::SolanaTraderService;
use crate::model::error::BotError;
use solana_sdk::signature::Keypair;
use std::sync::Arc;

#[derive(Debug)]
pub struct BalanceValidationResult {
    pub has_sufficient_balance: bool,
    pub current_balance: f64,
    pub required_balance: f64,
    pub balance_currency: String,
    pub error_message: Option<String>,
}

pub struct BalanceValidationService {
    evm_trader_service: Arc<EvmTraderService>,
    solana_trader_service: Arc<SolanaTraderService>,
}

impl BalanceValidationService {
    pub fn new(
        evm_trader_service: Arc<EvmTraderService>,
        solana_trader_service: Arc<SolanaTraderService>,
    ) -> Self {
        Self {
            evm_trader_service,
            solana_trader_service,
        }
    }

    /// Validate balance for buy operations
    pub async fn validate_buy_balance(
        &self,
        wallet_address: &str,
        amount: &str,
        blockchain: &Blockchain,
    ) -> Result<BalanceValidationResult, BotError> {
        match blockchain {
            Blockchain::ETH | Blockchain::BSC | Blockchain::BASE => {
                match self.evm_trader_service.check_sufficient_balance_for_buy(
                    wallet_address,
                    amount,
                    blockchain,
                ).await {
                    Ok(has_sufficient) => {
                        let currency = match blockchain {
                            Blockchain::ETH => "ETH",
                            Blockchain::BSC => "BNB", 
                            Blockchain::BASE => "ETH",
                            _ => "UNKNOWN",
                        };

                        Ok(BalanceValidationResult {
                            has_sufficient_balance: has_sufficient,
                            current_balance: 0.0, // Would need additional call to get exact balance
                            required_balance: 0.0, // Would need calculation
                            balance_currency: currency.to_string(),
                            error_message: if !has_sufficient {
                                Some(format!("Insufficient {} balance for trade + admin fee + gas", currency))
                            } else {
                                None
                            },
                        })
                    }
                    Err(e) => Ok(BalanceValidationResult {
                        has_sufficient_balance: false,
                        current_balance: 0.0,
                        required_balance: 0.0,
                        balance_currency: "UNKNOWN".to_string(),
                        error_message: Some(format!("Failed to check balance: {}", e)),
                    }),
                }
            }
            Blockchain::SOL => {
                // For Solana, we need the wallet keypair
                // This would typically be retrieved from the database
                Err(BotError::validation_error("Solana balance validation requires wallet keypair".to_string()))
            }
        }
    }

    /// Validate balance for sell operations
    pub async fn validate_sell_balance(
        &self,
        wallet_address: &str,
        token_address: &str,
        amount: &str,
        blockchain: &Blockchain,
    ) -> Result<BalanceValidationResult, BotError> {
        match blockchain {
            Blockchain::ETH | Blockchain::BSC | Blockchain::BASE => {
                match self.evm_trader_service.check_sufficient_balance_for_sell(
                    wallet_address,
                    token_address,
                    amount,
                    blockchain,
                ).await {
                    Ok(has_sufficient) => {
                        Ok(BalanceValidationResult {
                            has_sufficient_balance: has_sufficient,
                            current_balance: 0.0,
                            required_balance: 0.0,
                            balance_currency: "TOKEN".to_string(),
                            error_message: if !has_sufficient {
                                Some("Insufficient token balance for sell".to_string())
                            } else {
                                None
                            },
                        })
                    }
                    Err(e) => Ok(BalanceValidationResult {
                        has_sufficient_balance: false,
                        current_balance: 0.0,
                        required_balance: 0.0,
                        balance_currency: "TOKEN".to_string(),
                        error_message: Some(format!("Failed to check token balance: {}", e)),
                    }),
                }
            }
            Blockchain::SOL => {
                // For Solana, we need the wallet keypair and token mint
                Err(BotError::validation_error("Solana balance validation requires wallet keypair and token mint".to_string()))
            }
        }
    }

    /// Validate Solana buy balance with keypair
    pub async fn validate_solana_buy_balance(
        &self,
        wallet: &Keypair,
        amount_lamports: u64,
    ) -> Result<BalanceValidationResult, BotError> {
        match self.solana_trader_service.check_sufficient_balance_for_buy(wallet, amount_lamports).await {
            Ok(has_sufficient) => {
                Ok(BalanceValidationResult {
                    has_sufficient_balance: has_sufficient,
                    current_balance: 0.0, // Would need additional call
                    required_balance: amount_lamports as f64 / 1_000_000_000.0,
                    balance_currency: "SOL".to_string(),
                    error_message: if !has_sufficient {
                        Some("Insufficient SOL balance for trade + admin fee + rent".to_string())
                    } else {
                        None
                    },
                })
            }
            Err(e) => Ok(BalanceValidationResult {
                has_sufficient_balance: false,
                current_balance: 0.0,
                required_balance: 0.0,
                balance_currency: "SOL".to_string(),
                error_message: Some(format!("Failed to check SOL balance: {}", e)),
            }),
        }
    }

    /// Validate Solana sell balance with keypair
    pub async fn validate_solana_sell_balance(
        &self,
        wallet: &Keypair,
        token_mint: &solana_sdk::pubkey::Pubkey,
        sell_amount: u64,
        decimals: u8,
    ) -> Result<BalanceValidationResult, BotError> {
        match self.solana_trader_service.check_sufficient_balance_for_sell(
            wallet,
            token_mint,
            sell_amount,
            decimals,
        ).await {
            Ok(has_sufficient) => {
                Ok(BalanceValidationResult {
                    has_sufficient_balance: has_sufficient,
                    current_balance: 0.0,
                    required_balance: sell_amount as f64 / 10_u64.pow(decimals as u32) as f64,
                    balance_currency: "TOKEN".to_string(),
                    error_message: if !has_sufficient {
                        Some("Insufficient token balance for sell".to_string())
                    } else {
                        None
                    },
                })
            }
            Err(e) => Ok(BalanceValidationResult {
                has_sufficient_balance: false,
                current_balance: 0.0,
                required_balance: 0.0,
                balance_currency: "TOKEN".to_string(),
                error_message: Some(format!("Failed to check token balance: {}", e)),
            }),
        }
    }

    /// Generate user-friendly error message for insufficient balance
    pub fn generate_insufficient_balance_message(
        &self,
        result: &BalanceValidationResult,
        operation: &str, // "buy" or "sell"
    ) -> String {
        if let Some(error) = &result.error_message {
            return error.clone();
        }

        if !result.has_sufficient_balance {
            match operation {
                "buy" => {
                    // Get real admin fee percentage from settings
                    let admin_fee_percentage = crate::config::defaults::DEFAULT_ADMIN_FEE_PERCENTAGE;
                    format!(
                        "❌ Insufficient {} balance for purchase.\n\n\
                         💡 You need enough {} to cover:\n\
                         • Trade amount\n\
                         • Admin fee ({:.1}%)\n\
                         • Gas fees\n\
                         • Network buffer\n\n\
                         Please add more {} to your wallet and try again.",
                        result.balance_currency,
                        result.balance_currency,
                        admin_fee_percentage,
                        result.balance_currency
                    )
                },
                "sell" => format!(
                    "❌ Insufficient token balance for sale.\n\n\
                     💡 Make sure you have enough tokens in your wallet.\n\
                     Check your token balance and try again with a smaller amount.",
                ),
                _ => "❌ Insufficient balance for this operation.".to_string(),
            }
        } else {
            "✅ Balance check passed.".to_string()
        }
    }
}
