use teloxide::types::Message;
use teloxide::prelude::Requester;
use crate::model::{BotError, Blockchain};
use crate::service::BotService;
use crate::screens;
use std::str::FromStr;


/// Handles a regular message
pub async fn handle_message(
    bot_service: BotService,
    msg: Message,
) -> Result<(), BotError> {
    // Extract chat ID and user ID
    let chat_id = msg.chat.id.0 as i64;

    if let Some(_tg_user) = msg.from() {
        // Track the user's message in the conversation (optimized)
        bot_service.track_conversation_message(chat_id, msg.id.0).await?;

        // If this is a private chat and not a command, check if it's a response to a prompt
        if msg.chat.is_private() && msg.text().is_some() && !msg.text().unwrap().starts_with('/') {
            let text = msg.text().unwrap();


            // Snipe functionality has been removed

            // Process contract address input immediately if it looks like a contract address
            // This is a fallback for when the conversation handler doesn't catch it
            if text.len() > 30 && !text.contains(" ") {


                // Check if there's an active state for this user
                let user_states = screens::scan_screen::get_user_states();
                let state_opt = {
                    let states = user_states.read().await;
                    states.get(&chat_id).cloned()
                };

                // If there's an active state, don't auto-process the contract address
                // The state-specific handlers below will handle it
                if state_opt.is_some() {
                } else {
                    // Get the user data
                    let user_data = bot_service.user_service().get_or_create_user_data(chat_id, &msg.from().unwrap()).await?;
                    let blockchain = &user_data.user.current_blockchain;

                    // Create a loading message
                    let loading_text = format!(
                        "⏳ <b>Scanning Contract...</b>\n\n\
                        Contract: <code>{}</code>\n\n\
                        Please wait while we fetch information...",
                        text
                    );

                    let loading_msg = bot_service.send_message(chat_id, &loading_text).await?;

                    // Delete the user's message to keep the chat clean
                    let _ = bot_service.delete_message(chat_id, msg.id.0).await;

                    // Scan the contract
                    if let Err(e) = screens::scan_screen::scan_contract(
                        &bot_service,
                        &user_data,
                        loading_msg.id.0,
                        text,
                        blockchain
                    ).await {
                        // Show error message
                        let error_text = format!(
                            "❌ <b>Error Scanning Contract</b>\n\n\
                            Contract: <code>{}</code>\n\n\
                            Error: {}",
                            text, e
                        );

                        bot_service.edit_message(chat_id, loading_msg.id.0, &error_text).await?;
                    }

                    return Ok(());
                }
            }

            // Check if this is a response to a buy token prompt
            let user_states = screens::scan_screen::get_user_states();
            let state_opt = {
                let states = user_states.read().await;
                println!("🔍 DEBUG: Checking user states for chat_id: {}", chat_id);
                println!("🔍 DEBUG: Current states: {:?}", *states);
                states.get(&chat_id).cloned()
            };

            if let Some(state) = state_opt {
                println!("🔍 DEBUG: Found state for chat_id {}: {}", chat_id, state);
                if state.starts_with("scan_contract:") {
                    // Parse the state
                    let parts: Vec<&str> = state.split(':').collect();
                    if parts.len() >= 3 {
                        let blockchain_str = parts[1];
                        let prompt_msg_id = parts[2].parse::<i32>().unwrap_or(0);

                        // Convert blockchain string to enum
                        let blockchain = match blockchain_str {
                            "bsc" => Blockchain::BSC,
                            "eth" => Blockchain::ETH,
                            "sol" => Blockchain::SOL,
                            "base" => Blockchain::BASE,
                            _ => return Ok(()),
                        };

                        // Delete the prompt message
                        let _ = bot_service.delete_message(chat_id, prompt_msg_id).await;

                        // Get user data
                        let user_data = bot_service.user_service().get_or_create_user_data(chat_id, &msg.from().unwrap()).await?;

                        // Create a loading message
                        let loading_text = format!(
                            "⏳ <b>Scanning Contract...</b>\n\n\
                            Contract: <code>{}</code>\n\n\
                            Please wait while we fetch information...",
                            text
                        );

                        let loading_msg = bot_service.send_message(chat_id, &loading_text).await?;

                        // Delete the user's message to keep the chat clean
                        let _ = bot_service.delete_message(chat_id, msg.id.0).await;

                        // Clear the state
                        {
                            let mut states = user_states.write().await;
                            states.remove(&chat_id);
                        }

                        // Scan the contract
                        if let Err(e) = screens::scan_screen::scan_contract(
                            &bot_service,
                            &user_data,
                            loading_msg.id.0,
                            text,
                            &blockchain
                        ).await {
                            // Show error message
                            let error_text = format!(
                                "❌ <b>Error Scanning Contract</b>\n\n\
                                Contract: <code>{}</code>\n\n\
                                Error: {}",
                                text, e
                            );

                            bot_service.edit_message(chat_id, loading_msg.id.0, &error_text).await?;
                        }

                        return Ok(());
                    }
                } else if state.starts_with("snipe_token:") {
                    // Parse the state
                    let parts: Vec<&str> = state.split(':').collect();
                    if parts.len() >= 3 {
                        let blockchain_str = parts[1];
                        let prompt_msg_id = parts[2].parse::<i32>().unwrap_or(0);

                        // Convert blockchain string to enum
                        let blockchain = match blockchain_str {
                            "bsc" => Blockchain::BSC,
                            "eth" => Blockchain::ETH,
                            "sol" => Blockchain::SOL,
                            "base" => Blockchain::BASE,
                            _ => return Ok(()),
                        };

                        // Delete the prompt message
                        let _ = bot_service.delete_message(chat_id, prompt_msg_id).await;

                        // Delete the user's message to keep the chat clean
                        let _ = bot_service.delete_message(chat_id, msg.id.0).await;

                        // Get user data
                        let user_data = bot_service.user_service().get_or_create_user_data(chat_id, &msg.from().unwrap()).await?;

                        // Create a loading message
                        let loading_text = format!(
                            "⏳ <b>Setting Up Snipe...</b>\n\n\
                            Contract: <code>{}</code>\n\n\
                            Please wait while we check the token...",
                            text
                        );

                        let loading_msg = bot_service.send_message(chat_id, &loading_text).await?;

                        // Clear the state
                        {
                            let mut states = user_states.write().await;
                            states.remove(&chat_id);
                        }

                        // First, check if a snipe already exists for this token
                        let existing_snipes = crate::service::DbService::get_user_snipes_by_blockchain(chat_id, &blockchain).await?;
                        let mut snipe_exists = false;

                        for snipe in &existing_snipes {
                            if snipe.contract_address.to_lowercase() == text.to_lowercase() {
                                snipe_exists = true;
                                break;
                            }
                        }

                        if snipe_exists {
                            // Snipe already exists, show message and redirect to config
                            let existing_text = format!(
                                "ℹ️ <b>Snipe Already Exists</b>\n\n\
                                Contract: <code>{}</code>\n\n\
                                You already have a snipe set up for this token. Showing the existing snipe configuration.",
                                text
                            );

                            // Edit the loading message
                            bot_service.edit_message(chat_id, loading_msg.id.0, &existing_text).await?;

                            // Wait a moment before showing the config screen
                            tokio::time::sleep(tokio::time::Duration::from_secs(2)).await;

                            // Show the snipe config screen
                            crate::screens::snipe_config_screen::show_snipe_config_screen(
                                &bot_service,
                                &user_data,
                                loading_msg.id.0,
                                blockchain.clone(),
                                text
                            ).await?;

                            return Ok(());
                        }

                        // Check if the token is already listed on Dexscreener
                        let token_service = crate::service::TokenInfoService::new();
                        let token_info_result = token_service.check_token_for_snipe(text, &blockchain).await;

                        match token_info_result {
                            Ok(Some(token_info)) if token_info.liquidity_usd > 0.0 || token_info.pair_address.is_some() => {
                                // Token already has liquidity, show warning
                                let warning_text = format!(
                                    "⚠️ <b>Token Already Listed</b>\n\n\
                                    Contract: <code>{}</code>\n\n\
                                    This token already has liquidity on DEX and is trading. \
                                    Sniping is intended for tokens that haven't launched yet.\n\n\
                                    Would you like to set up a snipe anyway?",
                                    text
                                );

                                // Create keyboard with yes/no buttons
                                let keyboard = teloxide::types::InlineKeyboardMarkup::new(vec![
                                    vec![
                                        teloxide::types::InlineKeyboardButton::callback(
                                            "✅ Yes, Continue".to_string(),
                                            format!("show_snipe_config:{}:{}", blockchain.as_str(), text),
                                        ),
                                        teloxide::types::InlineKeyboardButton::callback(
                                            "❌ No, Cancel".to_string(),
                                            format!("sniper_dashboard_{}", blockchain.as_str()),
                                        ),
                                    ],
                                ]);

                                // Edit the message with the warning
                                bot_service.edit_message_with_keyboard(chat_id, loading_msg.id.0, &warning_text, keyboard).await?;
                            },
                            Ok(_) => {
                                // Token not listed yet, show the config screen without saving to database
                                // Store the token address in the user's config for the current blockchain
                                let mut user_data_mut = user_data.clone();
                                match blockchain {
                                    Blockchain::BSC => user_data_mut.config.bsc_current_contract_address = Some(text.to_string()),
                                    Blockchain::SOL => user_data_mut.config.sol_current_contract_address = Some(text.to_string()),
                                    Blockchain::ETH => user_data_mut.config.eth_current_contract_address = Some(text.to_string()),
                                    Blockchain::BASE => user_data_mut.config.base_current_contract_address = Some(text.to_string()),
                                }

                                // Save the updated config
                                crate::service::DbService::save_user_config(&user_data_mut.config).await?;

                                // Update the cache
                                bot_service.user_service().cache_user_data(user_data_mut.clone()).await;

                                // Show a message that we're setting up the snipe config
                                let setup_text = format!(
                                    "🔧 <b>Setting Up Snipe Configuration</b>\n\n\
                                    Blockchain: <b>{}</b>\n\
                                    Token: <code>{}</code>\n\n\
                                    Please configure your snipe settings below. The snipe will not be saved until you click 'Execute Snipe'.",
                                    blockchain.to_string().to_uppercase(),
                                    text
                                );

                                // Create the snipe config keyboard
                                let keyboard = crate::screens::snipe_config_screen::create_snipe_config_keyboard(blockchain, text);

                                // Edit the loading message with the keyboard
                                bot_service.edit_message_with_keyboard(chat_id, loading_msg.id.0, &setup_text, keyboard).await?;
                            },
                            Err(e) => {
                                // Error checking token, show error message
                                let error_text = format!(
                                    "❌ <b>Error Checking Token</b>\n\n\
                                    Contract: <code>{}</code>\n\
                                    Current Blockchain: <b>{}</b>\n\n\
                                    Error: {}\n\n\
                                    💡 <i>Please confirm you are on the correct blockchain. If the token is on a different blockchain, please switch to the correct one.</i>",
                                    text,
                                    blockchain.as_str().to_uppercase(),
                                    e
                                );

                                bot_service.edit_message(chat_id, loading_msg.id.0, &error_text).await?;
                            }
                        }

                        return Ok(());
                    }
                } else if state.starts_with("buy_token:") {
                    // Parse the state
                    let parts: Vec<&str> = state.split(':').collect();
                    if parts.len() >= 4 {
                        let blockchain_str = parts[1];
                        let token_address = parts[2];
                        let prompt_msg_id = parts[3].parse::<i32>().unwrap_or(0);

                        // Convert blockchain string to enum
                        let blockchain = match blockchain_str {
                            "bsc" => Blockchain::BSC,
                            "eth" => Blockchain::ETH,
                            "sol" => Blockchain::SOL,
                            "base" => Blockchain::BASE,
                            _ => return Ok(()),
                        };

                        // Handle the buy token amount
                        screens::scan_screen::handle_buy_token_amount(
                            &bot_service,
                            chat_id,
                            prompt_msg_id,
                            msg.id.0,
                            text,
                            token_address,
                            &blockchain
                        ).await?;

                        // Clear the state
                        let mut states = user_states.write().await;
                        states.remove(&chat_id);

                        return Ok(());
                    }
                } else if state.starts_with("sell_token_custom:") {
                    // Parse the state
                    let parts: Vec<&str> = state.split(':').collect();
                    if parts.len() >= 4 {
                        let blockchain_str = parts[1];
                        let token_address = parts[2];
                        let prompt_msg_id = parts[3].parse::<i32>().unwrap_or(0);

                        // Convert blockchain string to enum
                        let blockchain = match blockchain_str {
                            "bsc" => Blockchain::BSC,
                            "eth" => Blockchain::ETH,
                            "sol" => Blockchain::SOL,
                            "base" => Blockchain::BASE,
                            _ => return Ok(()),
                        };

                        // Handle the sell token amount
                        screens::scan_screen::handle_sell_token_amount(
                            &bot_service,
                            chat_id,
                            prompt_msg_id,
                            msg.id.0,
                            text,
                            token_address,
                            &blockchain
                        ).await?;

                        // Clear the state
                        let mut states = user_states.write().await;
                        states.remove(&chat_id);

                        return Ok(());
                    }
                } else if state.starts_with("snipe_setting:") {
                    // Parse the state
                    let parts: Vec<&str> = state.split(':').collect();
                    if parts.len() >= 6 {
                        let setting = parts[1];
                        let blockchain_str = parts[2];
                        let token_address = parts[3];
                        let config_msg_id = parts[4].parse::<i32>().unwrap_or(0);
                        let prompt_msg_id = parts[5].parse::<i32>().unwrap_or(0);

                        // Convert blockchain string to enum
                        let blockchain = match blockchain_str {
                            "bsc" => Blockchain::BSC,
                            "eth" => Blockchain::ETH,
                            "sol" => Blockchain::SOL,
                            "base" => Blockchain::BASE,
                            _ => return Ok(()),
                        };

                        // Delete the user's message and prompt
                        let _ = bot_service.delete_message(chat_id, msg.id.0).await;
                        let _ = bot_service.delete_message(chat_id, prompt_msg_id).await;

                        // Get user data - use a safer approach
                        let user_data = match bot_service.bot().get_chat_member(
                            teloxide::types::ChatId(chat_id),
                            teloxide::types::UserId(chat_id as u64)
                        ).await {
                            Ok(member) => {
                                bot_service.user_service().get_or_create_user_data(chat_id, &member.user).await?
                            },
                            Err(_e) => {
                                return Ok(());
                            }
                        };

                        // Update the setting
                        let mut user_data_mut = user_data.clone();
                        if let Err(e) = crate::screens::snipe_config_screen::update_snipe_numeric_setting(
                            &bot_service,
                            &mut user_data_mut,
                            config_msg_id,
                            blockchain,
                            token_address,
                            setting,
                            text
                        ).await {
                            // Show error message
                            let error_msg = bot_service.send_message(chat_id, &format!("❌ Error: {}", e)).await?;

                            // Delete error message after a few seconds
                            tokio::time::sleep(tokio::time::Duration::from_secs(3)).await;
                            let _ = bot_service.delete_message(chat_id, error_msg.id.0).await;
                        }

                        // Clear the state
                        let mut states = user_states.write().await;
                        states.remove(&chat_id);

                        return Ok(());
                    }
                } else if state.starts_with("config_setting:") {
                    // Parse the state
                    let parts: Vec<&str> = state.split(':').collect();
                    if parts.len() >= 5 {
                        let setting = parts[1];
                        let blockchain_str = parts[2];
                        let config_msg_id = parts[3].parse::<i32>().unwrap_or(0);
                        let prompt_msg_id = parts[4].parse::<i32>().unwrap_or(0);

                        // Convert blockchain string to enum
                        let blockchain = match blockchain_str {
                            "bsc" => Blockchain::BSC,
                            "eth" => Blockchain::ETH,
                            "sol" => Blockchain::SOL,
                            "base" => Blockchain::BASE,
                            _ => return Ok(()),
                        };

                        // Delete the user's message and prompt
                        let _ = bot_service.delete_message(chat_id, msg.id.0).await;
                        let _ = bot_service.delete_message(chat_id, prompt_msg_id).await;

                        // Get user data - use a safer approach
                        let user_data = match bot_service.bot().get_chat_member(
                            teloxide::types::ChatId(chat_id),
                            teloxide::types::UserId(chat_id as u64)
                        ).await {
                            Ok(member) => {
                                bot_service.user_service().get_or_create_user_data(chat_id, &member.user).await?
                            },
                            Err(_e) => {

                                return Ok(());
                            }
                        };

                        // Update the setting
                        let mut user_data_mut = user_data.clone();
                        if let Err(e) = screens::config_screen::update_numeric_setting(
                            &bot_service,
                            &mut user_data_mut,
                            config_msg_id,
                            blockchain,
                            setting,
                            text
                        ).await {
                            // Show error message
                            let error_msg = bot_service.send_message(chat_id, &format!("❌ Error: {}", e)).await?;

                            // Delete error message after a few seconds
                            tokio::time::sleep(tokio::time::Duration::from_secs(3)).await;
                            let _ = bot_service.delete_message(chat_id, error_msg.id.0).await;
                        }

                        // Clear the state
                        let mut states = user_states.write().await;
                        states.remove(&chat_id);

                        return Ok(());
                    }
                }
            }
        }
    }

    // Delete the user's message to keep the chat clean
    let _ = bot_service.delete_message(chat_id, msg.id.0).await;

    Ok(())
}
