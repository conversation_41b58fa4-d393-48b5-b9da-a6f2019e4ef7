use serde::{Deserialize, Serialize};
use mongodb::bson::{self, oid::ObjectId};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UserDashboard {
    #[serde(rename = "_id", skip_serializing_if = "Option::is_none")]
    pub id: Option<ObjectId>,
    pub user_id: ObjectId,
    pub bsc_dashboard_message_id: Option<i32>,
    pub bsc_dashboard_content: Option<String>,
    pub bsc_dashboard_markup: Option<bson::Document>,
    pub eth_dashboard_message_id: Option<i32>,
    pub eth_dashboard_content: Option<String>,
    pub eth_dashboard_markup: Option<bson::Document>,
    pub sol_dashboard_message_id: Option<i32>,
    pub sol_dashboard_content: Option<String>,
    pub sol_dashboard_markup: Option<bson::Document>,
    pub base_dashboard_message_id: Option<i32>,
    pub base_dashboard_content: Option<String>,
    pub base_dashboard_markup: Option<bson::Document>,
}

impl UserDashboard {
    pub fn new(user_id: ObjectId) -> Self {
        Self {
            id: None,
            user_id,
            bsc_dashboard_message_id: None,
            bsc_dashboard_content: None,
            bsc_dashboard_markup: None,
            eth_dashboard_message_id: None,
            eth_dashboard_content: None,
            eth_dashboard_markup: None,
            sol_dashboard_message_id: None,
            sol_dashboard_content: None,
            sol_dashboard_markup: None,
            base_dashboard_message_id: None,
            base_dashboard_content: None,
            base_dashboard_markup: None,
        }
    }
}
