use crate::model::{<PERSON>r<PERSON><PERSON>, Bot<PERSON>rror, Blockchain};
use crate::service::BotService;
use crate::screens::{
    bsc_screen::show_bsc_dashboard,
    sol_screen::show_sol_dashboard,
    eth_screen::show_eth_dashboard,
    base_screen::show_base_dashboard
};

pub async fn show_dashboard(
    bot_service: &BotService,
    user_data: &UserData,
    message_id: i32,
) -> Result<(), BotError> {
    println!("Dashboard: {} - {:?}", user_data.display_name(), user_data.current_blockchain());

    let mut updated_user_data = user_data.clone();
    updated_user_data.session.dashboard_message_id = Some(message_id);
    bot_service.user_service().cache_user_data(updated_user_data).await;

    match user_data.current_blockchain() {
        Blockchain::BSC => show_bsc_dashboard(bot_service, user_data, message_id).await?,
        Blockchain::SOL => show_sol_dashboard(bot_service, user_data, message_id).await?,
        Blockchain::ETH => show_eth_dashboard(bot_service, user_data, message_id).await?,
        Blockchain::BASE => show_base_dashboard(bot_service, user_data, message_id).await?,
    }

    Ok(())
}

pub async fn show_fresh_dashboard(
    bot_service: &BotService,
    user_data: &UserData,
) -> Result<(), BotError> {
    let welcome_text = format!(
        "📊 <b>Dashboard</b>\n\n\
        Loading your dashboard..."
    );

    let sent_msg = bot_service.send_message(user_data.chat_id(), &welcome_text).await?;

    show_dashboard(bot_service, user_data, sent_msg.id.0).await?;

    Ok(())
}
