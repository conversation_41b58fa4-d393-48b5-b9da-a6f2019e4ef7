use teloxide::types::Message;
use teloxide::prelude::Requester;
use crate::service::BotService;
use crate::model::{BotError, Blockchain};
use crate::screens::welcome::handle_welcome;
use crate::screens::dashboard::show_fresh_dashboard;

pub async fn handle_command(
    bot_service: &BotService,
    message: Message,
    command: &str,
) -> Result<(), BotError> {
    let chat_id = message.chat.id.0 as i64;
    let message_id = message.id.0;

    match command {
        "start" => {
            let tg_user = message.from()
                .ok_or_else(|| BotError::user_error("Message has no sender"))?;

            handle_welcome(bot_service, chat_id, tg_user).await?;
        }
        "help" => {
            let help_text = "\
                <b>Available Commands:</b>\n\n\
                <b>General Commands:</b>\n\
                /start - Start the bot and show dashboard\n\
                /help - Show this help message\n\
                /about - About this bot\n\
                /privatekeys - View your private keys (auto-deletes after 60s)\n\n\
                <b>Blockchain Commands:</b>\n\
                /bsc - Switch to BSC blockchain\n\
                /sol - Switch to Solana blockchain\n\
                /eth - Switch to Ethereum blockchain\n\
                /base - Switch to Base blockchain\n\n\
                <b>Trading Commands:</b>\n\
                /scan [address] - Scan a contract\n\
                /buy [amount] [address] - Buy a token\n\
                /sell [amount] [address] - Sell a token\n\
                /trades - Show your active trades\n\
                /balance - Show your wallet balance\
            ";

            bot_service.send_message(chat_id, help_text).await?;
        }
        "about" => {
            let about_text = "\
                <b>About EasyBot</b>\n\n\
                EasyBot is a multi-chain cryptocurrency trading bot built with Rust.\n\n\
                <b>Supported Blockchains:</b>\n\
                • Binance Smart Chain (BSC)\n\
                • Solana (SOL)\n\
                • Ethereum (ETH)\n\
                • Base\n\n\
                <b>Features:</b>\n\
                • Wallet management for multiple blockchains\n\
                • Token swapping\n\
                • Contract scanning\n\
                • Trading configuration\n\n\
                Version: 0.1.0\
            ";

            bot_service.send_message(chat_id, about_text).await?;
        }
        "bsc" => {
            handle_blockchain_switch(bot_service, chat_id, Blockchain::BSC).await?;
        }
        "sol" => {
            handle_blockchain_switch(bot_service, chat_id, Blockchain::SOL).await?;
        }
        "eth" => {
            handle_blockchain_switch(bot_service, chat_id, Blockchain::ETH).await?;
        }
        "base" => {
            handle_blockchain_switch(bot_service, chat_id, Blockchain::BASE).await?;
        }
        "scan" => {
            let scan_text = "\
                <b>Select Blockchain to Scan Contract</b>\n\n\
                Please select the blockchain where the contract is deployed:
            ";

            let keyboard = teloxide::types::InlineKeyboardMarkup::new(vec![
                vec![
                    teloxide::types::InlineKeyboardButton::callback(
                        "BSC".to_string(),
                        "scan_contract_bsc"
                    ),
                    teloxide::types::InlineKeyboardButton::callback(
                        "Solana".to_string(),
                        "scan_contract_sol"
                    ),
                ],
                vec![
                    teloxide::types::InlineKeyboardButton::callback(
                        "Ethereum".to_string(),
                        "scan_contract_eth"
                    ),
                    teloxide::types::InlineKeyboardButton::callback(
                        "Base".to_string(),
                        "scan_contract_base"
                    ),
                ],
                vec![
                    teloxide::types::InlineKeyboardButton::callback(
                        "❌ Cancel".to_string(),
                        "dismiss_message"
                    ),
                ],
            ]);

            bot_service.send_message_with_keyboard(chat_id, scan_text, keyboard).await?;
        }
        "sniper" => {
            let snipe_text = "\
                <b>🎯 Sniper Dashboard</b>\n\n\
                Select the blockchain where you want to manage snipes:
            ";
            let keyboard = teloxide::types::InlineKeyboardMarkup::new(vec![
                vec![
                    teloxide::types::InlineKeyboardButton::callback(
                        "BSC".to_string(),
                        "sniper_dashboard_bsc"
                    ),
                    teloxide::types::InlineKeyboardButton::callback(
                        "Solana".to_string(),
                        "sniper_dashboard_sol"
                    ),
                ],
                vec![
                    teloxide::types::InlineKeyboardButton::callback(
                        "Ethereum".to_string(),
                        "sniper_dashboard_eth"
                    ),
                    teloxide::types::InlineKeyboardButton::callback(
                        "Base".to_string(),
                        "sniper_dashboard_base"
                    ),
                ],
                vec![
                    teloxide::types::InlineKeyboardButton::callback(
                        "❌ Cancel".to_string(),
                        "dismiss_message"
                    ),
                ],
            ]);

            bot_service.send_message_with_keyboard(chat_id, snipe_text, keyboard).await?;
        }
        "alert" => {
            use crate::service::AlertType;

            bot_service.send_alert(
                chat_id,
                "This is a test alert. It appears as a notification but won't clutter your chat history.",
                AlertType::SystemAlert
            ).await?;

            let msg = bot_service.send_message(
                chat_id,
                "✅ Alert sent! Check your notifications. This message will be deleted in 5 seconds."
            ).await?;
            let bot_service_clone = bot_service.clone();
            let msg_id = msg.id.0;
            tokio::spawn(async move {
                tokio::time::sleep(tokio::time::Duration::from_secs(5)).await;
                let _ = bot_service_clone.delete_message(chat_id, msg_id).await;
            });
        }
        _ => {
            let unknown_text = format!(
                "Unknown command: /{}\n\nUse /help to see available commands.",
                command
            );

            bot_service.send_message(chat_id, &unknown_text).await?;
        }
    }
    let _ = bot_service.delete_message(chat_id, message_id).await;

    Ok(())
}

async fn handle_blockchain_switch(
    bot_service: &BotService,
    chat_id: i64,
    blockchain: Blockchain,
) -> Result<(), BotError> {
    let _tg_user = match bot_service.bot().get_chat_member(teloxide::types::ChatId(chat_id), teloxide::types::UserId(chat_id as u64)).await {
        Ok(member) => member.user,
        Err(_) => {
            bot_service.send_message(chat_id, "Failed to get user information. Please try again.").await?;
            return Ok(());
        }
    };

    let user_data = bot_service.switch_blockchain(chat_id, blockchain).await?;
    show_fresh_dashboard(bot_service, &user_data).await?;

    Ok(())
}
