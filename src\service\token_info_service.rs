use reqwest::Client;
use serde::{Deserialize, Serialize};
use std::time::Duration;
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::RwLock;
use crate::model::Blockchain;

// Cache TTL in seconds (5 minutes for token info, 1 hour for honeypot, 15 minutes for prices)
const TOKEN_CACHE_TTL: u64 = 300;
const HONEYPOT_CACHE_TTL: u64 = 3600;
const PRICE_CACHE_TTL: u64 = 900;

// Global static price cache for instant access
lazy_static::lazy_static! {
    static ref GLOBAL_PRICE_CACHE: std::sync::RwLock<std::collections::HashMap<String, (f64, u64)>> = {
        let mut m = std::collections::HashMap::new();
        // Initialize with default values (symbol, price, timestamp)
        let now = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs();
        m.insert("eth".to_string(), (3400.0, now));
        m.insert("bnb".to_string(), (550.0, now));
        m.insert("sol".to_string(), (140.0, now));
        std::sync::RwLock::new(m)
    };
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TokenInfo {
    pub name: String,
    pub symbol: String,
    pub price_usd: f64,
    pub liquidity_usd: f64,
    pub market_cap: f64,
    pub volume_24h: f64,
    pub price_change_1h: f64,
    pub price_change_24h: f64,
    pub pair_address: Option<String>,
    pub dex_id: Option<String>,
    pub chain_id: Option<String>,
    pub is_honeypot: Option<bool>,
    pub buy_tax: Option<String>,
    pub sell_tax: Option<String>,
    pub transfer_tax: Option<String>,
    pub risk_level: Option<String>,
    pub warnings: Vec<String>,
    pub last_updated: u64,
}

impl Default for TokenInfo {
    fn default() -> Self {
        Self {
            name: "Unknown".to_string(),
            symbol: "UNKNOWN".to_string(),
            price_usd: 0.0,
            liquidity_usd: 0.0,
            market_cap: 0.0,
            volume_24h: 0.0,
            price_change_1h: 0.0,
            price_change_24h: 0.0,
            pair_address: None,
            dex_id: None,
            chain_id: None,
            is_honeypot: None,
            buy_tax: None,
            sell_tax: None,
            transfer_tax: None,
            risk_level: None,
            warnings: Vec::new(),
            last_updated: 0,
        }
    }
}

// Cache entry with expiration time
#[derive(Clone)]
struct CacheEntry<T> {
    data: T,
    last_updated: u64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct HoneypotCheckResult {
    pub is_honeypot: bool,
    pub risk: String,
    pub buy_tax: String,
    pub sell_tax: String,
    pub transfer_tax: Option<String>,
    pub warnings: Vec<String>,
    pub details: String,
    pub timestamp: u64,
}

#[derive(Clone)]
pub struct TokenInfoService {
    client: Client,
    token_cache: Arc<RwLock<HashMap<String, CacheEntry<TokenInfo>>>>,
    honeypot_cache: Arc<RwLock<HashMap<String, CacheEntry<HoneypotCheckResult>>>>,
    token_cache_expiration: Duration,
    honeypot_cache_expiration: Duration,
}

impl TokenInfoService {
    pub fn new() -> Self {
        let service = Self {
            client: Client::new(),
            token_cache: Arc::new(RwLock::new(HashMap::new())),
            honeypot_cache: Arc::new(RwLock::new(HashMap::new())),
            token_cache_expiration: Duration::from_secs(TOKEN_CACHE_TTL),
            honeypot_cache_expiration: Duration::from_secs(HONEYPOT_CACHE_TTL),
        };

        // Start background task to periodically update the global price cache
        tokio::spawn(Self::price_update_task());

        service
    }

    // Background task to update prices periodically
    async fn price_update_task() {
        loop {
            // Sleep first to avoid immediate update on startup
            tokio::time::sleep(Duration::from_secs(3600)).await; // Update every hour

            println!("Updating global price cache in background");

            // Update ETH price
            if let Ok(eth_price) = Self::fetch_price_from_api("ethereum").await {
                Self::update_global_price("eth", eth_price);
            }

            // Sleep between requests to avoid rate limits
            tokio::time::sleep(Duration::from_secs(5)).await;

            // Update BNB price
            if let Ok(bnb_price) = Self::fetch_price_from_api("binancecoin").await {
                Self::update_global_price("bnb", bnb_price);
            }

            // Sleep between requests to avoid rate limits
            tokio::time::sleep(Duration::from_secs(5)).await;

            // Update SOL price
            if let Ok(sol_price) = Self::fetch_price_from_api("solana").await {
                Self::update_global_price("sol", sol_price);
            }
        }
    }

    // Helper method to fetch price from CoinGecko API
    async fn fetch_price_from_api(coin_id: &str) -> Result<f64, String> {
        // Use CoinGecko API for real price data
        let api_url = std::env::var("COINGECKO_API_URL")
            .unwrap_or_else(|_| "https://api.coingecko.com/api/v3".to_string());

        let url = format!("{}/simple/price?ids={}&vs_currencies=usd", api_url, coin_id);

        // Create client with timeout
        let client = reqwest::Client::builder()
            .timeout(std::time::Duration::from_secs(30))
            .build()
            .map_err(|e| format!("Failed to create HTTP client: {}", e))?;

        // Make request with retry logic
        for attempt in 1..=3 {
            match client.get(&url).send().await {
                Ok(response) => {
                    if response.status().is_success() {
                        match response.json::<serde_json::Value>().await {
                            Ok(data) => {
                                if let Some(price) = data.get(coin_id)
                                    .and_then(|coin| coin.get("usd"))
                                    .and_then(|price| price.as_f64()) {
                                    return Ok(price);
                                } else {
                                    return Err(format!("Price not found for {}", coin_id));
                                }
                            }
                            Err(e) => {
                                println!("Failed to parse price response (attempt {}): {}", attempt, e);
                            }
                        }
                    } else {
                        println!("Price API returned error status (attempt {}): {}", attempt, response.status());
                    }
                }
                Err(e) => {
                    println!("Failed to fetch price (attempt {}): {}", attempt, e);
                }
            }

            // Wait before retry (except on last attempt)
            if attempt < 3 {
                tokio::time::sleep(std::time::Duration::from_secs(2)).await;
            }
        }

        // Try to get cached price as last resort
        println!("All price API attempts failed for {}, checking cache for last known price", coin_id);
        let symbol = match coin_id {
            "ethereum" => "eth",
            "binancecoin" => "bnb",
            "solana" => "sol",
            _ => return Err("Unsupported coin and no fallback available".to_string())
        };

        // Get last known price from cache (even if expired)
        if let Ok(cache) = GLOBAL_PRICE_CACHE.read() {
            if let Some((price, _)) = cache.get(symbol) {
                println!("Using last known cached price for {}: ${}", symbol, price);
                return Ok(*price);
            }
        }

        // Only use hardcoded fallback as absolute last resort
        println!("No cached price available for {}, using emergency fallback", coin_id);
        match coin_id {
            "ethereum" => Ok(3400.0),
            "binancecoin" => Ok(550.0),
            "solana" => Ok(140.0),
            _ => Err("Unsupported coin and no fallback available".to_string())
        }
    }

    // Update the global price cache
    fn update_global_price(symbol: &str, price: f64) {
        let now = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs();

        if let Ok(mut cache) = GLOBAL_PRICE_CACHE.write() {
            cache.insert(symbol.to_lowercase(), (price, now));
            println!("Updated global price for {}: ${}", symbol, price);
        }
    }

    // Get price from global cache (extremely fast, no async)
    pub fn get_price_sync(symbol: &str) -> f64 {
        let symbol = symbol.to_lowercase();

        if let Ok(cache) = GLOBAL_PRICE_CACHE.read() {
            if let Some((price, timestamp)) = cache.get(&symbol) {
                let now = std::time::SystemTime::now()
                    .duration_since(std::time::UNIX_EPOCH)
                    .unwrap_or_default()
                    .as_secs();

                // Check if the price is still valid
                if now - timestamp < PRICE_CACHE_TTL {
                    return *price;
                }
            }
        }

        // Try to get expired cached price first (better than hardcoded)
        if let Ok(cache) = GLOBAL_PRICE_CACHE.read() {
            if let Some((price, _)) = cache.get(&symbol) {
                println!("Using expired cached price for {}: ${} (cache expired but better than hardcoded)", symbol, price);
                return *price;
            }
        }

        // Return emergency fallback prices only if no cache available
        println!("No cache available for {}, using emergency fallback price", symbol);
        match symbol.as_str() {
            "eth" => 3400.0,
            "bnb" => 550.0,
            "sol" => 140.0,
            _ => 0.0
        }
    }

    // Get blockchain ID for Dexscreener API
    fn get_dexscreener_chain_id(blockchain: &Blockchain) -> &'static str {
        match blockchain {
            Blockchain::ETH => "ethereum",
            Blockchain::BSC => "bsc",
            Blockchain::SOL => "solana",
            Blockchain::BASE => "base",
        }
    }

    // Detect blockchain from DexScreener response
    pub fn detect_blockchain_from_response(&self, response_text: &str) -> Option<Blockchain> {
        // Try to parse the response
        if let Ok(data) = serde_json::from_str::<serde_json::Value>(response_text) {
            // The response is an array at the root level
            if let Some(pairs) = data.as_array() {
                if !pairs.is_empty() {
                    // Get the chain ID from the first pair
                    if let Some(chain_id) = pairs[0].get("chainId").and_then(|c| c.as_str()) {
                        return match chain_id {
                            "ethereum" => Some(Blockchain::ETH),
                            "bsc" => Some(Blockchain::BSC),
                            "solana" => Some(Blockchain::SOL),
                            "base" => Some(Blockchain::BASE),
                            _ => None,
                        };
                    }
                }
            }
        }
        None
    }

    // Format numbers for display
    pub fn format_number(num: f64) -> String {
        if num >= 1_000_000_000_000.0 {
            format!("{:.2}T", num / 1_000_000_000_000.0) // Trillions
        } else if num >= 1_000_000_000.0 {
            format!("{:.2}B", num / 1_000_000_000.0) // Billions
        } else if num >= 1_000_000.0 {
            format!("{:.2}M", num / 1_000_000.0) // Millions - Fixed division bug
        } else if num >= 1_000.0 {
            format!("{:.2}K", num / 1_000.0) // Thousands
        } else {
            format!("{:.2}", num) // Less than a thousand
        }
    }

    // Get token information from cache
    pub async fn get_token_info_from_cache(&self, token_address: &str, blockchain: &Blockchain) -> Option<TokenInfo> {
        let cache_key = format!("{}:{}", blockchain.as_str(), token_address);
        let cache = self.token_cache.read().await;

        if let Some(entry) = cache.get(&cache_key) {
            let now = std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap_or_default()
                .as_secs();

            if now - entry.last_updated < TOKEN_CACHE_TTL {
                return Some(entry.data.clone());
            }
        }

        None
    }

    // Cache token information
    pub async fn cache_token_info(&self, token_address: &str, blockchain: &Blockchain, token_info: TokenInfo) {
        let cache_key = format!("{}:{}", blockchain.as_str(), token_address);
        let mut cache = self.token_cache.write().await;

        let now = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs();

        cache.insert(cache_key, CacheEntry {
            data: token_info,
            last_updated: now,
        });
    }

    // Get honeypot check result from cache
    pub async fn get_honeypot_from_cache(&self, token_address: &str, blockchain: &Blockchain) -> Option<HoneypotCheckResult> {
        let cache_key = format!("honeypot:{}:{}", blockchain.as_str(), token_address);
        let cache = self.honeypot_cache.read().await;

        if let Some(entry) = cache.get(&cache_key) {
            let now = std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap_or_default()
                .as_secs();

            if now - entry.last_updated < HONEYPOT_CACHE_TTL {
                return Some(entry.data.clone());
            }
        }

        None
    }

    // Cache honeypot check result
    pub async fn cache_honeypot(&self, token_address: &str, blockchain: &Blockchain, result: HoneypotCheckResult) {
        let cache_key = format!("honeypot:{}:{}", blockchain.as_str(), token_address);
        let mut cache = self.honeypot_cache.write().await;

        let now = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs();

        cache.insert(cache_key, CacheEntry {
            data: result,
            last_updated: now,
        });
    }

    // Get token information from Dexscreener
    pub async fn get_token_info(&self, token_address: &str, blockchain: &Blockchain) -> Result<TokenInfo, String> {
        // Check cache first
        if let Some(cached_info) = self.get_token_info_from_cache(token_address, blockchain).await {
            println!("Using cached token info for {} on {}", token_address, blockchain.as_str());
            return Ok(cached_info);
        }

        println!("Fetching token info for {} on {}", token_address, blockchain.as_str());

        // Get Dexscreener API URL from environment variable or use default
        let dexscreener_api_url = std::env::var("API_DEXSCREENER_TOKENS")
            .unwrap_or_else(|_| "https://api.dexscreener.com/tokens/v1".to_string());

        // Fetch from Dexscreener using the correct API endpoint
        let chain_id = Self::get_dexscreener_chain_id(blockchain);
        let url = format!("{}/{}/{}", dexscreener_api_url, chain_id, token_address);
        println!("Making request to Dexscreener API: {}", url);

        // Create a client with a 2-minute timeout
        let client = reqwest::Client::builder()
            .timeout(std::time::Duration::from_secs(120))
            .build()
            .unwrap_or_else(|_| reqwest::Client::new());

        println!("Sending request to Dexscreener...");

        // Implement retry logic (up to 4 attempts)
        let mut response = None;
        let mut _error_message = String::new();

        for attempt in 1..=4 {
            println!("API call attempt {} of 4", attempt);

            match tokio::time::timeout(
                std::time::Duration::from_secs(120),
                client.get(&url).send()
            ).await {
                Ok(Ok(resp)) => {
                    println!("Received response from Dexscreener. Status: {}", resp.status());
                    response = Some(resp);
                    break;
                },
                Ok(Err(e)) => {
                    println!("Error fetching from Dexscreener (attempt {}): {}", attempt, e);
                    _error_message = format!("Failed to fetch token info: {}", e);

                    // Wait a longer time before retrying
                    if attempt < 4 {
                        tokio::time::sleep(std::time::Duration::from_secs(5)).await;
                    }
                },
                Err(_) => {
                    println!("Timeout fetching from Dexscreener (attempt {})", attempt);
                    _error_message = "Request timed out".to_string();

                    // Wait a longer time before retrying
                    if attempt < 4 {
                        tokio::time::sleep(std::time::Duration::from_secs(5)).await;
                    }
                }
            }
        }

        let response = match response {
            Some(resp) => resp,
            None => {
                // If all attempts failed, create a minimal fallback response
                println!("All API call attempts failed, using fallback minimal response");

                // Create a minimal token info with just the address
                let mut token_info = TokenInfo::default();
                token_info.name = "Unknown".to_string();
                token_info.symbol = "???".to_string();
                token_info.last_updated = std::time::SystemTime::now()
                    .duration_since(std::time::UNIX_EPOCH)
                    .unwrap_or_default()
                    .as_secs();

                // Cache the fallback result
                self.cache_token_info(token_address, blockchain, token_info.clone()).await;

                return Ok(token_info);
            }
        };

        println!("Parsing JSON response from Dexscreener...");

        // Use a 2-minute timeout for text extraction
        let response_text = match tokio::time::timeout(
            std::time::Duration::from_secs(120),
            response.text()
        ).await {
            Ok(Ok(text)) => {
                println!("Received text response (length: {})", text.len());
                text
            },
            Ok(Err(e)) => {
                println!("Error getting response text: {}", e);

                // Create a minimal fallback response with just the token address
                println!("Using fallback minimal response");
                format!(r#"[{{"chainId":"{}","baseToken":{{"address":"{}","name":"Unknown","symbol":"???"}}}}]"#,
                    chain_id, token_address)
            },
            Err(_) => {
                println!("Timeout getting response text");

                // Create a minimal fallback response with just the token address
                println!("Using fallback minimal response due to timeout");
                format!(r#"[{{"chainId":"{}","baseToken":{{"address":"{}","name":"Unknown","symbol":"???"}}}}]"#,
                    chain_id, token_address)
            }
        };

        let data: serde_json::Value = match serde_json::from_str::<serde_json::Value>(&response_text) {
            Ok(json) => {
                println!("Successfully parsed JSON response");

                // Log a summary of the Dexscreener response
                // The response is now an array at the root level
                if let Some(pairs) = json.as_array() {
                    println!("Dexscreener found {} pairs", pairs.len());

                    for (i, pair) in pairs.iter().enumerate() {
                        let chain_id = pair.get("chainId").and_then(|c| c.as_str()).unwrap_or("unknown");
                        let dex_id = pair.get("dexId").and_then(|d| d.as_str()).unwrap_or("unknown");
                        let price = pair.get("priceUsd").and_then(|p| p.as_str()).unwrap_or("unknown");

                        println!("Pair {}: chainId={}, dexId={}, priceUsd={}", i, chain_id, dex_id, price);

                        // Log base token info
                        if let Some(base_token) = pair.get("baseToken") {
                            let name = base_token.get("name").and_then(|n| n.as_str()).unwrap_or("unknown");
                            let symbol = base_token.get("symbol").and_then(|s| s.as_str()).unwrap_or("unknown");
                            println!("  Base token: name={}, symbol={}", name, symbol);
                        }

                        // Log liquidity info
                        if let Some(liquidity) = pair.get("liquidity") {
                            let usd = liquidity.get("usd").and_then(|u| u.as_f64()).unwrap_or(0.0);
                            println!("  Liquidity: ${:.2}", usd);
                        }

                        // Log volume info
                        if let Some(volume) = pair.get("volume") {
                            let h24 = volume.get("h24").and_then(|v| v.as_f64()).unwrap_or(0.0);
                            println!("  24h Volume: ${:.2}", h24);
                        }

                        // Log price change info
                        if let Some(price_change) = pair.get("priceChange") {
                            let h1 = price_change.get("h1").and_then(|p| p.as_f64()).unwrap_or(0.0);
                            let h24 = price_change.get("h24").and_then(|p| p.as_f64()).unwrap_or(0.0);
                            println!("  Price change: 1h={:.2}%, 24h={:.2}%", h1, h24);
                        }

                        // Log market cap info
                        if let Some(market_cap) = pair.get("marketCap") {
                            println!("  Market Cap: ${:.2}", market_cap.as_f64().unwrap_or(0.0));
                        }
                    }
                } else {
                    println!("No pairs found in Dexscreener response");
                }

                json
            },
            Err(e) => {
                println!("Error parsing JSON: {}", e);
                println!("Response text: {}", response_text);
                return Err(format!("Failed to parse response: {}", e));
            },
        };

        // The response is now an array at the root level
        let pairs = match data.as_array() {
            Some(p) => p,
            None => return Err("Invalid response format, expected an array".to_string()),
        };

        if pairs.is_empty() {
            return Err("No pairs found for token".to_string());
        }

        // Find the pair for the specified blockchain
        let pair = pairs.iter().find(|p| {
            p.get("chainId").and_then(|c| c.as_str()) == Some(chain_id)
        });

        let pair = match pair {
            Some(p) => p,
            None => return Err(format!("No pair found for blockchain {}", blockchain.as_str())),
        };

        // Extract token information
        let mut token_info = TokenInfo::default();

        if let Some(base_token) = pair.get("baseToken") {
            token_info.name = base_token.get("name").and_then(|n| n.as_str()).unwrap_or("Unknown").to_string();
            token_info.symbol = base_token.get("symbol").and_then(|s| s.as_str()).unwrap_or("UNKNOWN").to_string();
        }

        // Price can be a string in the new API format - validate properly
        token_info.price_usd = match pair.get("priceUsd") {
            Some(price) => {
                if let Some(price_str) = price.as_str() {
                    match price_str.parse::<f64>() {
                        Ok(parsed_price) => {
                            if parsed_price >= 0.0 && parsed_price.is_finite() {
                                parsed_price
                            } else {
                                println!("⚠️ Invalid price value for token: {}", parsed_price);
                                0.0
                            }
                        }
                        Err(e) => {
                            println!("⚠️ Failed to parse price string '{}': {}", price_str, e);
                            0.0
                        }
                    }
                } else if let Some(price_num) = price.as_f64() {
                    if price_num >= 0.0 && price_num.is_finite() {
                        price_num
                    } else {
                        println!("⚠️ Invalid price number for token: {}", price_num);
                        0.0
                    }
                } else {
                    println!("⚠️ Price field is neither string nor number");
                    0.0
                }
            },
            None => {
                println!("⚠️ No priceUsd field found in token data");
                0.0
            },
        };

        if let Some(liquidity) = pair.get("liquidity") {
            token_info.liquidity_usd = liquidity.get("usd").and_then(|l| l.as_f64()).unwrap_or(0.0);
        }

        // Enhanced market cap parsing with multiple fallbacks and better error handling
        token_info.market_cap = pair.get("marketCap")
            .and_then(|m| {
                if let Some(mc_str) = m.as_str() {
                    match mc_str.parse::<f64>() {
                        Ok(val) if val > 0.0 => Some(val),
                        _ => None
                    }
                } else {
                    m.as_f64().filter(|&val| val > 0.0)
                }
            })
            .or_else(|| pair.get("fdv").and_then(|m| {
                if let Some(fdv_str) = m.as_str() {
                    match fdv_str.parse::<f64>() {
                        Ok(val) if val > 0.0 => Some(val),
                        _ => None
                    }
                } else {
                    m.as_f64().filter(|&val| val > 0.0)
                }
            }))
            .or_else(|| {
                // Calculate market cap from price and supply if available
                if let (Some(price), Some(supply)) = (
                    pair.get("priceUsd").and_then(|p| {
                        if let Some(p_str) = p.as_str() {
                            p_str.parse::<f64>().ok()
                        } else {
                            p.as_f64()
                        }
                    }),
                    pair.get("info").and_then(|info| info.get("totalSupply")).and_then(|s| {
                        if let Some(s_str) = s.as_str() {
                            s_str.parse::<f64>().ok()
                        } else {
                            s.as_f64()
                        }
                    })
                ) {
                    Some(price * supply)
                } else {
                    None
                }
            })
            .unwrap_or(0.0);

        // Enhanced volume parsing with better validation
        if let Some(volume) = pair.get("volume") {
            token_info.volume_24h = volume.get("h24")
                .and_then(|v| {
                    if let Some(v_str) = v.as_str() {
                        match v_str.parse::<f64>() {
                            Ok(val) if val >= 0.0 => Some(val),
                            _ => None
                        }
                    } else {
                        v.as_f64().filter(|&val| val >= 0.0)
                    }
                })
                .or_else(|| volume.get("24h").and_then(|v| {
                    if let Some(v_str) = v.as_str() {
                        match v_str.parse::<f64>() {
                            Ok(val) if val >= 0.0 => Some(val),
                            _ => None
                        }
                    } else {
                        v.as_f64().filter(|&val| val >= 0.0)
                    }
                }))
                .unwrap_or(0.0);

            // Log volume parsing for debugging
            println!("Parsed volume 24h: ${:.2}", token_info.volume_24h);
        }

        if let Some(price_change) = pair.get("priceChange") {
            token_info.price_change_1h = price_change.get("h1").and_then(|p| p.as_f64()).unwrap_or(0.0);
            token_info.price_change_24h = price_change.get("h24").and_then(|p| p.as_f64()).unwrap_or(0.0);
        }

        token_info.pair_address = pair.get("pairAddress").and_then(|p| p.as_str()).map(|s| s.to_string());
        token_info.dex_id = pair.get("dexId").and_then(|d| d.as_str()).map(|s| s.to_string());
        token_info.chain_id = pair.get("chainId").and_then(|c| c.as_str()).map(|s| s.to_string());

        // Set last updated timestamp
        token_info.last_updated = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs();

        // Cache the result
        self.cache_token_info(token_address, blockchain, token_info.clone()).await;

        Ok(token_info)
    }

    // Environment variable names for honeypot API URLs ()
    const ENV_HONEYPOT_API_EVM: &str = "SEC_ENDPOINT_HP_EVM";
    const ENV_HONEYPOT_API_SOL: &str = "SEC_ENDPOINT_HP_SOL";

    // Default honeypot API URLs if environment variables are not set
    const DEFAULT_HONEYPOT_API_EVM: &str = "https://api.honeypot.is/v2/IsHoneypot";

    // Get honeypot API URL for EVM blockchains
    fn get_evm_honeypot_api_url(blockchain: &Blockchain) -> Result<String, String> {
        match blockchain {
            Blockchain::ETH | Blockchain::BSC | Blockchain::BASE => {
                // Get the API URL from environment variable or use default
                let api_url = std::env::var(Self::ENV_HONEYPOT_API_EVM)
                    .unwrap_or_else(|_| Self::DEFAULT_HONEYPOT_API_EVM.to_string());

                // Return the URL with the address parameter
                Ok(format!("{}?address=", api_url))
            },
            Blockchain::SOL => Err("Solana uses a different API".to_string()),
        }
    }

    // Check if an EVM token is a honeypot
    pub async fn check_evm_honeypot(&self, token_address: &str, blockchain: &Blockchain) -> Result<HoneypotCheckResult, String> {
        // Check cache first
        if let Some(cached_result) = self.get_honeypot_from_cache(token_address, blockchain).await {
            println!("Using cached honeypot check for {} on {}", token_address, blockchain.as_str());
            return Ok(cached_result);
        }

        println!("Checking if {} on {} is a honeypot", token_address, blockchain.as_str());

        // Get API URL
        let api_url = match Self::get_evm_honeypot_api_url(blockchain) {
            Ok(url) => url,
            Err(e) => return Err(e),
        };

        // Build the URL with parameters (address is already included in the API URL)
        let url = format!("{}{}", api_url, token_address);

        // Create a client with a longer timeout (2 minutes)
        let client = reqwest::Client::builder()
            .timeout(std::time::Duration::from_secs(120))
            .build()
            .unwrap_or_else(|_| reqwest::Client::new());

        // Make the request
        let response = match client.get(&url).send().await {
            Ok(resp) => resp,
            Err(e) => {
                // Create a fallback result
                let fallback = HoneypotCheckResult {
                    is_honeypot: false,
                    risk: "unknown".to_string(),
                    buy_tax: "0".to_string(),
                    sell_tax: "0".to_string(),
                    transfer_tax: None,
                    warnings: vec!["Failed to check honeypot status".to_string()],
                    details: format!("Error: {}", e),
                    timestamp: std::time::SystemTime::now()
                        .duration_since(std::time::UNIX_EPOCH)
                        .unwrap_or_default()
                        .as_secs(),
                };

                // Cache the fallback result for a shorter time
                self.cache_honeypot(token_address, blockchain, fallback.clone()).await;

                return Ok(fallback);
            },
        };

        // Get the response text first for logging
        let response_text = match response.text().await {
            Ok(text) => {
                println!("Received honeypot API response (length: {})", text.len());
                text
            },
            Err(e) => {
                println!("Error getting honeypot response text: {}", e);

                // Create a fallback result
                let fallback = HoneypotCheckResult {
                    is_honeypot: false,
                    risk: "unknown".to_string(),
                    buy_tax: "0".to_string(),
                    sell_tax: "0".to_string(),
                    transfer_tax: None,
                    warnings: vec!["Failed to get honeypot check response".to_string()],
                    details: format!("Error: {}", e),
                    timestamp: std::time::SystemTime::now()
                        .duration_since(std::time::UNIX_EPOCH)
                        .unwrap_or_default()
                        .as_secs(),
                };

                // Cache the fallback result for a shorter time
                self.cache_honeypot(token_address, blockchain, fallback.clone()).await;

                return Ok(fallback);
            }
        };

        let data: serde_json::Value = match serde_json::from_str::<serde_json::Value>(&response_text) {
            Ok(json) => {
                println!("Successfully parsed honeypot API response");

                // Log a summary of the honeypot response
                if let Some(summary) = json.get("summary") {
                    let is_honeypot = summary.get("isHoneypot").and_then(|h| h.as_bool()).unwrap_or(false);
                    let risk = summary.get("risk").and_then(|r| r.as_str()).unwrap_or("unknown");

                    println!("Honeypot check result: isHoneypot={}, risk={}", is_honeypot, risk);

                    // Log flags
                    if let Some(flags) = summary.get("flags").and_then(|f| f.as_array()) {
                        println!("Honeypot flags ({}): ", flags.len());
                        for flag in flags {
                            if let Some(description) = flag.get("description").and_then(|d| d.as_str()) {
                                println!("  - {}", description);
                            }
                        }
                    }
                }

                // Log simulation result
                if let Some(simulation) = json.get("simulationResult") {
                    let buy_tax = simulation.get("buyTax").and_then(|t| t.as_str()).unwrap_or("0");
                    let sell_tax = simulation.get("sellTax").and_then(|t| t.as_str()).unwrap_or("0");

                    println!("Simulation result: buyTax={}%, sellTax={}%", buy_tax, sell_tax);

                    if let Some(transfer_tax) = simulation.get("transferTax").and_then(|t| t.as_str()) {
                        println!("  transferTax={}%", transfer_tax);
                    }
                }

                json
            },
            Err(e) => {
                println!("Error parsing honeypot JSON: {}", e);
                println!("Honeypot response text: {}", response_text);

                // Create a fallback result
                let fallback = HoneypotCheckResult {
                    is_honeypot: false,
                    risk: "unknown".to_string(),
                    buy_tax: "0".to_string(),
                    sell_tax: "0".to_string(),
                    transfer_tax: None,
                    warnings: vec!["Failed to parse honeypot check response".to_string()],
                    details: format!("Error: {}", e),
                    timestamp: std::time::SystemTime::now()
                        .duration_since(std::time::UNIX_EPOCH)
                        .unwrap_or_default()
                        .as_secs(),
                };

                // Cache the fallback result for a shorter time
                self.cache_honeypot(token_address, blockchain, fallback.clone()).await;

                return Ok(fallback);
            },
        };

        // Extract data
        let summary = match data.get("summary") {
            Some(s) => s,
            None => {
                return Err("Invalid honeypot check response: missing summary".to_string());
            },
        };

        let simulation_result = data.get("simulationResult");

        // Process flag descriptions
        let mut flag_descriptions = Vec::new();
        let mut warnings = Vec::new();

        if let Some(flags) = summary.get("flags").and_then(|f| f.as_array()) {
            for flag in flags {
                if let Some(description) = flag.get("description").and_then(|d| d.as_str()) {
                    flag_descriptions.push(description.to_string());
                }
            }
        }

        // Add warning messages based on risk level
        let risk = summary.get("risk").and_then(|r| r.as_str()).unwrap_or("unknown");

        if risk == "honeypot" {
            warnings.push("CRITICAL: Honeypot detected - High risk token".to_string());
        } else if risk == "unknown" {
            warnings.push("WARNING: Could not determine if this is a honeypot. Proceed with caution.".to_string());
        }

        // Handle closed source warning
        if flag_descriptions.contains(&"The source code is not available, allowing for hidden functionality.".to_string()) {
            warnings.push("WARNING: Contract's dependencies are closed source, allowing for hidden functionalities.".to_string());
            flag_descriptions.retain(|flag| flag != "The source code is not available, allowing for hidden functionality.");
        }

        // Filter out known false positives
        flag_descriptions.retain(|flag| flag != "All snipers are marked as honeypots (blacklisted). Sniper detection still needs some improvements.");

        // Get tax information
        let buy_tax = simulation_result
            .and_then(|s| s.get("buyTax"))
            .and_then(|t| t.as_str())
            .unwrap_or("0")
            .to_string();

        let sell_tax = simulation_result
            .and_then(|s| s.get("sellTax"))
            .and_then(|t| t.as_str())
            .unwrap_or("0")
            .to_string();

        let transfer_tax = simulation_result
            .and_then(|s| s.get("transferTax"))
            .and_then(|t| t.as_str())
            .map(|s| s.to_string());

        // Create the result
        let result = HoneypotCheckResult {
            is_honeypot: risk == "honeypot",
            risk: risk.to_string(),
            buy_tax,
            sell_tax,
            transfer_tax,
            warnings,
            details: flag_descriptions.join("\n"),
            timestamp: std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap_or_default()
                .as_secs(),
        };

        // Cache the result
        self.cache_honeypot(token_address, blockchain, result.clone()).await;

        Ok(result)
    }

    // Dedicated method for checking tokens for sniping
    pub async fn check_token_for_snipe(&self, token_address: &str, blockchain: &Blockchain) -> Result<Option<TokenInfo>, String> {
        // Check cache first
        if let Some(cached_info) = self.get_token_info_from_cache(token_address, blockchain).await {
            println!("Using cached token info for snipe check: {} on {}", token_address, blockchain.as_str());

            // If token has liquidity, return it to show it's not snipeable
            if cached_info.liquidity_usd > 0.0 || cached_info.pair_address.is_some() {
                return Ok(Some(cached_info));
            }

            // Otherwise, return the token info but indicate it's snipeable
            return Ok(Some(cached_info));
        }

        println!("Checking if token is snipeable: {} on {}", token_address, blockchain.as_str());

        // Get Dexscreener API URL from environment variable or use default
        let dexscreener_api_url = std::env::var("API_DEXSCREENER_TOKENS")
            .unwrap_or_else(|_| "https://api.dexscreener.com/tokens/v1".to_string());

        // Fetch from Dexscreener using the correct API endpoint
        let chain_id = Self::get_dexscreener_chain_id(blockchain);
        let url = format!("{}/{}/{}", dexscreener_api_url, chain_id, token_address);
        println!("Making request to Dexscreener API for snipe check: {}", url);

        // Create a client with a timeout
        let client = reqwest::Client::builder()
            .timeout(std::time::Duration::from_secs(60))
            .build()
            .unwrap_or_else(|_| reqwest::Client::new());

        // Make the request
        let response = match client.get(&url).send().await {
            Ok(resp) => {
                println!("Received response from Dexscreener for snipe check. Status: {}", resp.status());
                resp
            },
            Err(e) => {
                println!("Error fetching from Dexscreener for snipe check: {}", e);
                // Token not found, which is good for sniping
                return Ok(None);
            }
        };

        // Get the response text
        let response_text = match response.text().await {
            Ok(text) => {
                println!("Received text response for snipe check (length: {})", text.len());
                text
            },
            Err(e) => {
                println!("Error getting response text for snipe check: {}", e);
                // Error getting response, assume token not found
                return Ok(None);
            }
        };

        // Parse the response
        let data: serde_json::Value = match serde_json::from_str::<serde_json::Value>(&response_text) {
            Ok(json) => {
                println!("Successfully parsed JSON response for snipe check");
                json
            },
            Err(e) => {
                println!("Error parsing JSON for snipe check: {}", e);
                // Error parsing response, assume token not found
                return Ok(None);
            }
        };

        // Check if any pairs were found
        let pairs = match data.as_array() {
            Some(p) => p,
            None => {
                println!("Invalid response format for snipe check, expected an array");
                return Ok(None);
            }
        };

        if pairs.is_empty() {
            println!("No pairs found for token in snipe check");
            return Ok(None);
        }

        // Find the pair for the specified blockchain
        let pair = pairs.iter().find(|p| {
            p.get("chainId").and_then(|c| c.as_str()) == Some(chain_id)
        });

        let pair = match pair {
            Some(p) => p,
            None => {
                println!("No pair found for blockchain {} in snipe check", blockchain.as_str());
                return Ok(None);
            }
        };

        // Extract token information
        let mut token_info = TokenInfo::default();

        if let Some(base_token) = pair.get("baseToken") {
            token_info.name = base_token.get("name").and_then(|n| n.as_str()).unwrap_or("Unknown").to_string();
            token_info.symbol = base_token.get("symbol").and_then(|s| s.as_str()).unwrap_or("UNKNOWN").to_string();
        }

        // Price can be a string in the new API format - validate properly
        token_info.price_usd = match pair.get("priceUsd") {
            Some(price) => {
                if let Some(price_str) = price.as_str() {
                    match price_str.parse::<f64>() {
                        Ok(parsed_price) => {
                            if parsed_price >= 0.0 && parsed_price.is_finite() {
                                parsed_price
                            } else {
                                println!("⚠️ Invalid price value for token: {}", parsed_price);
                                0.0
                            }
                        }
                        Err(e) => {
                            println!("⚠️ Failed to parse price string '{}': {}", price_str, e);
                            0.0
                        }
                    }
                } else if let Some(price_num) = price.as_f64() {
                    if price_num >= 0.0 && price_num.is_finite() {
                        price_num
                    } else {
                        println!("⚠️ Invalid price number for token: {}", price_num);
                        0.0
                    }
                } else {
                    println!("⚠️ Price field is neither string nor number");
                    0.0
                }
            },
            None => {
                println!("⚠️ No priceUsd field found in token data");
                0.0
            },
        };

        if let Some(liquidity) = pair.get("liquidity") {
            token_info.liquidity_usd = liquidity.get("usd").and_then(|l| l.as_f64()).unwrap_or(0.0);
        }

        token_info.pair_address = pair.get("pairAddress").and_then(|p| p.as_str()).map(|s| s.to_string());
        token_info.dex_id = pair.get("dexId").and_then(|d| d.as_str()).map(|s| s.to_string());
        token_info.chain_id = pair.get("chainId").and_then(|c| c.as_str()).map(|s| s.to_string());

        // Set last updated timestamp
        token_info.last_updated = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs();

        // Cache the result
        self.cache_token_info(token_address, blockchain, token_info.clone()).await;

        Ok(Some(token_info))
    }

    // Check if a Solana token is a honeypot
    pub async fn check_solana_honeypot(&self, token_address: &str) -> Result<HoneypotCheckResult, String> {
        // Check cache first
        if let Some(cached_result) = self.get_honeypot_from_cache(token_address, &Blockchain::SOL).await {
            println!("Using cached honeypot check for {} on SOL", token_address);
            return Ok(cached_result);
        }

        println!("Checking if {} on SOL is a honeypot", token_address);

        // Get the API URL from environment variable or use default
        let api_base_url = std::env::var(Self::ENV_HONEYPOT_API_SOL)
            .unwrap_or_else(|_| "https://api.rugcheck.xyz/v1/tokens".to_string());

        // Make API request to RugCheck
        let url = format!("{}/{}/report/summary", api_base_url, token_address);

        // Create a client with a longer timeout (2 minutes)
        let client = reqwest::Client::builder()
            .timeout(std::time::Duration::from_secs(120))
            .build()
            .unwrap_or_else(|_| reqwest::Client::new());

        let response = match client.get(&url).send().await {
            Ok(resp) => resp,
            Err(e) => {
                // Create a fallback result
                let fallback = HoneypotCheckResult {
                    is_honeypot: false,
                    risk: "unknown".to_string(),
                    buy_tax: "0".to_string(),
                    sell_tax: "0".to_string(),
                    transfer_tax: None,
                    warnings: vec!["Failed to check honeypot status".to_string()],
                    details: format!("Error: {}", e),
                    timestamp: std::time::SystemTime::now()
                        .duration_since(std::time::UNIX_EPOCH)
                        .unwrap_or_default()
                        .as_secs(),
                };

                // Cache the fallback result for a shorter time
                self.cache_honeypot(token_address, &Blockchain::SOL, fallback.clone()).await;

                return Ok(fallback);
            },
        };

        let data: serde_json::Value = match response.json().await {
            Ok(json) => json,
            Err(e) => {
                // Create a fallback result
                let fallback = HoneypotCheckResult {
                    is_honeypot: false,
                    risk: "unknown".to_string(),
                    buy_tax: "0".to_string(),
                    sell_tax: "0".to_string(),
                    transfer_tax: None,
                    warnings: vec!["Failed to parse honeypot check response".to_string()],
                    details: format!("Error: {}", e),
                    timestamp: std::time::SystemTime::now()
                        .duration_since(std::time::UNIX_EPOCH)
                        .unwrap_or_default()
                        .as_secs(),
                };

                // Cache the fallback result for a shorter time
                self.cache_honeypot(token_address, &Blockchain::SOL, fallback.clone()).await;

                return Ok(fallback);
            },
        };

        // Extract risk descriptions and check for "danger" level
        let mut risk_descriptions = Vec::new();
        let mut warnings = Vec::new();
        let mut danger_detected = false;

        if let Some(risks) = data.get("risks").and_then(|r| r.as_array()) {
            for risk in risks {
                let name = risk.get("name").and_then(|n| n.as_str()).unwrap_or("Unknown");
                let description = risk.get("description").and_then(|d| d.as_str()).unwrap_or("");
                let level = risk.get("level").and_then(|l| l.as_str()).unwrap_or("unknown");

                if level == "danger" {
                    danger_detected = true;
                    warnings.push(format!("DANGER: {} - {}", name, description));
                } else if level == "high" {
                    warnings.push(format!("HIGH RISK: {} - {}", name, description));
                }

                risk_descriptions.push(format!("{}: {} ({})", name, description, level));
            }
        }

        // Add special warning if danger level is found
        if danger_detected {
            warnings.insert(0, "CRITICAL: Honeypot detected - High risk token".to_string());
        }

        // Get tax information
        let buy_tax = data.get("simulationResult")
            .and_then(|s| s.get("buyTax"))
            .and_then(|t| t.as_str())
            .unwrap_or("0")
            .to_string();

        let sell_tax = data.get("simulationResult")
            .and_then(|s| s.get("sellTax"))
            .and_then(|t| t.as_str())
            .unwrap_or("0")
            .to_string();

        let transfer_tax = data.get("simulationResult")
            .and_then(|s| s.get("transferTax"))
            .and_then(|t| t.as_str())
            .map(|s| s.to_string());

        // Create the result
        let result = HoneypotCheckResult {
            is_honeypot: danger_detected,
            risk: data.get("score").and_then(|s| s.as_str()).unwrap_or("unknown").to_string(),
            buy_tax,
            sell_tax,
            transfer_tax,
            warnings,
            details: risk_descriptions.join("\n"),
            timestamp: std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap_or_default()
                .as_secs(),
        };

        // Cache the result
        self.cache_honeypot(token_address, &Blockchain::SOL, result.clone()).await;

        Ok(result)
    }

    // Check if a token is a honeypot
    pub async fn check_if_honeypot(&self, token_address: &str, blockchain: &Blockchain) -> Result<HoneypotCheckResult, String> {
        match blockchain {
            Blockchain::SOL => self.check_solana_honeypot(token_address).await,
            _ => self.check_evm_honeypot(token_address, blockchain).await,
        }
    }

    // Get token price in USD (using global cache for instant access)
    pub async fn get_token_price(&self, symbol: &str) -> Result<f64, String> {
        // First check the global cache for instant access (no async)
        let price = Self::get_price_sync(symbol);

        // If we got a valid price from the global cache, return it immediately
        if price > 0.0 {
            return Ok(price);
        }

        // If not in global cache, check the instance cache
        let cache_key = format!("price:{}", symbol.to_lowercase());
        let cache = self.token_cache.read().await;

        if let Some(entry) = cache.get(&cache_key) {
            let now = std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap_or_default()
                .as_secs();

            if now - entry.last_updated < TOKEN_CACHE_TTL {
                println!("Using cached price for {}", symbol);
                return Ok(entry.data.price_usd);
            }
        }
        drop(cache); // Release the read lock

        println!("Determining price for {} - attempting real-time fetch", symbol);

        // Try to fetch real-time price from CoinGecko API
        let coin_id = match symbol.to_lowercase().as_str() {
            "eth" => "ethereum",
            "bnb" => "binancecoin",
            "sol" => "solana",
            _ => {
                println!("Unsupported token for real-time pricing: {}", symbol);
                return Ok(0.0);
            }
        };

        // Attempt real-time price fetch
        match Self::fetch_price_from_api(coin_id).await {
            Ok(real_price) => {
                println!("✅ Got real-time price for {}: ${}", symbol, real_price);

                // Update global cache with fresh price
                if let Ok(mut cache) = GLOBAL_PRICE_CACHE.write() {
                    let now = std::time::SystemTime::now()
                        .duration_since(std::time::UNIX_EPOCH)
                        .unwrap_or_default()
                        .as_secs();
                    cache.insert(symbol.to_lowercase(), (real_price, now));
                }

                return Ok(real_price);
            }
            Err(e) => {
                println!("❌ Failed to get real-time price for {}: {}", symbol, e);

                // Use emergency fallback only as last resort
                let price = match symbol.to_lowercase().as_str() {
                    "eth" => 3400.0,  // Emergency fallback
                    "bnb" => 550.0,   // Emergency fallback
                    "sol" => 140.0,   // Emergency fallback
                    _ => {
                        println!("No emergency fallback available for {}", symbol);
                        0.0
                    }
                };

                println!("Using emergency fallback price for {}: ${}", symbol, price);
                Ok(price)
            }
        }
    }

    /// Get token price in USD using DexScreener API for any blockchain
    pub async fn get_token_price_dexscreener(&self, token_address: &str, blockchain: &Blockchain) -> Result<f64, String> {
        let cache_key = format!("dex_price:{}:{}", blockchain.as_str(), token_address);

        // Check cache first
        let cache = self.token_cache.read().await;
        if let Some(entry) = cache.get(&cache_key) {
            let now = std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap_or_default()
                .as_secs();

            if now - entry.last_updated < TOKEN_CACHE_TTL {
                println!("Using cached DexScreener price for {} on {}: ${:.8}", token_address, blockchain.as_str(), entry.data.price_usd);
                return Ok(entry.data.price_usd);
            }
        }
        drop(cache);

        println!("Fetching token price from DexScreener: {} on {}", token_address, blockchain.as_str());

        // Get DexScreener API URL from environment variable or use default
        let dexscreener_api_url = std::env::var("API_DEXSCREENER_TOKENS")
            .unwrap_or_else(|_| "https://api.dexscreener.com/tokens/v1".to_string());

        // Build the URL for DexScreener API
        let chain_id = Self::get_dexscreener_chain_id(blockchain);
        let url = format!("{}/{}/{}", dexscreener_api_url, chain_id, token_address);
        println!("DexScreener price request URL: {}", url);

        // Create HTTP client with timeout
        let client = reqwest::Client::builder()
            .timeout(std::time::Duration::from_secs(30))
            .build()
            .map_err(|e| format!("Failed to create HTTP client: {}", e))?;

        // Make request with retry logic
        for attempt in 1..=3 {
            println!("DexScreener price API call attempt {} of 3", attempt);

            match client.get(&url).send().await {
                Ok(response) => {
                    if response.status().is_success() {
                        match response.text().await {
                            Ok(response_text) => {
                                // Parse JSON response
                                match serde_json::from_str::<serde_json::Value>(&response_text) {
                                    Ok(data) => {
                                        // The response is an array of pairs
                                        if let Some(pairs) = data.as_array() {
                                            if !pairs.is_empty() {
                                                // Get the first pair (most liquid)
                                                let pair = &pairs[0];

                                                // Extract price from priceUsd field
                                                if let Some(price_value) = pair.get("priceUsd") {
                                                    let price = if let Some(price_str) = price_value.as_str() {
                                                        match price_str.parse::<f64>() {
                                                            Ok(parsed_price) => {
                                                                if parsed_price >= 0.0 && parsed_price.is_finite() {
                                                                    parsed_price
                                                                } else {
                                                                    println!("⚠️ Invalid price value: {}", parsed_price);
                                                                    continue;
                                                                }
                                                            }
                                                            Err(e) => {
                                                                println!("⚠️ Failed to parse price string '{}': {}", price_str, e);
                                                                continue;
                                                            }
                                                        }
                                                    } else if let Some(price_num) = price_value.as_f64() {
                                                        if price_num >= 0.0 && price_num.is_finite() {
                                                            price_num
                                                        } else {
                                                            println!("⚠️ Invalid price number: {}", price_num);
                                                            continue;
                                                        }
                                                    } else {
                                                        println!("⚠️ Price field is neither string nor number");
                                                        continue;
                                                    };

                                                    println!("💰 DexScreener price for {} on {}: ${:.8}", token_address, blockchain.as_str(), price);

                                                    // Cache the result
                                                    let mut cache = self.token_cache.write().await;
                                                    cache.insert(cache_key, CacheEntry {
                                                        data: TokenInfo {
                                                            name: "Unknown".to_string(),
                                                            symbol: "UNKNOWN".to_string(),
                                                            price_usd: price,
                                                            ..Default::default()
                                                        },
                                                        last_updated: std::time::SystemTime::now()
                                                            .duration_since(std::time::UNIX_EPOCH)
                                                            .unwrap_or_default()
                                                            .as_secs(),
                                                    });

                                                    return Ok(price);
                                                } else {
                                                    println!("⚠️ No priceUsd field found in DexScreener response");
                                                }
                                            } else {
                                                println!("⚠️ No pairs found in DexScreener response");
                                            }
                                        } else {
                                            println!("⚠️ DexScreener response is not an array");
                                        }
                                    }
                                    Err(e) => {
                                        println!("Failed to parse DexScreener JSON response (attempt {}): {}", attempt, e);
                                    }
                                }
                            }
                            Err(e) => {
                                println!("Failed to get DexScreener response text (attempt {}): {}", attempt, e);
                            }
                        }
                    } else {
                        println!("DexScreener API returned error status (attempt {}): {}", attempt, response.status());
                    }
                }
                Err(e) => {
                    println!("Failed to make DexScreener request (attempt {}): {}", attempt, e);
                }
            }

            // Wait before retry (except on last attempt)
            if attempt < 3 {
                tokio::time::sleep(std::time::Duration::from_secs(2)).await;
            }
        }

        Err(format!("Failed to fetch token price from DexScreener after 3 attempts"))
    }

    /// Get native token price for blockchain using DexScreener API
    pub async fn get_native_token_price_dexscreener(&self, blockchain: &Blockchain) -> Result<f64, String> {
        // Map blockchain to native token address
        let native_token_address = match blockchain {
            Blockchain::SOL => "So11111111111111111111111111111111111111112", // Wrapped SOL
            Blockchain::ETH => "******************************************", // WETH
            Blockchain::BSC => "******************************************", // WBNB
            Blockchain::BASE => "******************************************", // WETH on Base
        };

        self.get_token_price_dexscreener(native_token_address, blockchain).await
    }

    // Get comprehensive token information with direct API call (no background tasks)
    pub async fn get_comprehensive_token_info(&self, token_address: &str, blockchain: &Blockchain) -> Result<TokenInfo, String> {
        // Check cache first for comprehensive info
        let cache_key = format!("comprehensive:{}:{}", token_address, blockchain.as_str());
        let cache = self.token_cache.read().await;

        if let Some(entry) = cache.get(&cache_key) {
            let now = std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap_or_default()
                .as_secs();

            if now - entry.last_updated < TOKEN_CACHE_TTL {
                println!("Using cached comprehensive token info for {} on {}", token_address, blockchain.as_str());
                return Ok(entry.data.clone());
            }
        }
        drop(cache); // Release the read lock

        println!("Fetching comprehensive token info for {} on {}", token_address, blockchain.as_str());

        // Make the token info API call directly (no timeout, no background task)
        let token_info_result = self.get_token_info(token_address, blockchain).await;

        // Process token info result
        let mut token_info = match token_info_result {
            Ok(info) => {
                println!("Successfully fetched token info from Dexscreener");
                info
            },
            Err(e) => {
                println!("Error fetching token info: {}", e);
                return Err(e);
            },
        };

        // Honeypot check disabled for now
        println!("Honeypot check disabled - using token info only");

        // Add a note that honeypot check is disabled
        token_info.warnings.push("Honeypot check disabled".to_string());

        // Log the final token info
        println!("Final token info: name={}, symbol={}, price=${:.8}, liquidity=${:.2}",
            token_info.name, token_info.symbol, token_info.price_usd, token_info.liquidity_usd);
        println!("Price changes: 1h={:.2}%, 24h={:.2}%",
            token_info.price_change_1h, token_info.price_change_24h);

        // Cache the comprehensive result
        let mut cache = self.token_cache.write().await;
        let now = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs();

        cache.insert(cache_key, CacheEntry {
            data: token_info.clone(),
            last_updated: now,
        });

        Ok(token_info)
    }

    // Format token information as a human-readable message (ultra-simplified version)
    pub fn format_token_info(&self, token_info: &TokenInfo, token_address: &str, blockchain: &Blockchain) -> String {
        // Use a string builder with pre-allocated capacity for better performance
        let mut message = String::with_capacity(500);

        // Simple header with blockchain emoji
        let header_emoji = match blockchain {
            Blockchain::BSC => "🟨",
            Blockchain::SOL => "🟣",
            Blockchain::ETH => "🟦",
            Blockchain::BASE => "🟩",
        };

        // Compact header
        message.push_str(&format!("{} <b>{} TOKEN</b>: {} ({})\n\n",
            header_emoji,
            blockchain.as_str().to_uppercase(),
            token_info.name,
            token_info.symbol));

        // Contract address
        message.push_str(&format!("📍 <code>{}</code>\n\n", token_address));

        // Price and key metrics in a compact format
        if token_info.price_usd > 0.0 {
            message.push_str(&format!("💰 <b>${:.8}</b>\n", token_info.price_usd));
        } else {
            message.push_str("💰 <b>Price:</b> Unknown\n");
        }

        // Price changes with emojis
        let h1_emoji = if token_info.price_change_1h > 0.0 { "🟢" } else if token_info.price_change_1h < 0.0 { "🔴" } else { "⚪" };
        let h24_emoji = if token_info.price_change_24h > 0.0 { "🟢" } else if token_info.price_change_24h < 0.0 { "🔴" } else { "⚪" };

        message.push_str(&format!("{} <b>1h:</b> {:+.2}% | {} <b>24h:</b> {:+.2}%\n",
            h1_emoji, token_info.price_change_1h, h24_emoji, token_info.price_change_24h));

        // Key metrics on one line
        let mut metrics = Vec::new();
        if token_info.market_cap > 0.0 {
            metrics.push(format!("💹 <b>MCap:</b> ${}", Self::format_number(token_info.market_cap)));
        }
        if token_info.liquidity_usd > 0.0 {
            metrics.push(format!("💧 <b>Liq:</b> ${}", Self::format_number(token_info.liquidity_usd)));
        }
        if token_info.volume_24h > 0.0 {
            metrics.push(format!("📈 <b>Vol:</b> ${}", Self::format_number(token_info.volume_24h)));
        }

        if !metrics.is_empty() {
            message.push_str(&format!("{}\n", metrics.join(" | ")));
        }

        // Links section - compact format
        message.push_str("\n🔗 ");

        // Add chart link
        let chart_url = match blockchain {
            Blockchain::ETH => format!("{}/{}", std::env::var("API_DEXSCREENER_CHART_ETH").unwrap_or_else(|_| "https://dexscreener.com/ethereum".to_string()), token_address),
            Blockchain::BSC => format!("{}/{}", std::env::var("API_DEXSCREENER_CHART_BSC").unwrap_or_else(|_| "https://dexscreener.com/bsc".to_string()), token_address),
            Blockchain::SOL => format!("{}/{}", std::env::var("API_DEXSCREENER_CHART_SOL").unwrap_or_else(|_| "https://dexscreener.com/solana".to_string()), token_address),
            Blockchain::BASE => format!("{}/{}", std::env::var("API_DEXSCREENER_CHART_BASE").unwrap_or_else(|_| "https://dexscreener.com/base".to_string()), token_address),
        };
        message.push_str(&format!("<a href=\"{}\">Chart</a> | ", chart_url));

        // Add blockchain explorer link
        let explorer_url = crate::constants::explorer_urls::get_explorer_token_url(blockchain.as_str(), token_address);
        message.push_str(&format!("<a href=\"{}\">Explorer</a>", explorer_url));

        message
    }
}
