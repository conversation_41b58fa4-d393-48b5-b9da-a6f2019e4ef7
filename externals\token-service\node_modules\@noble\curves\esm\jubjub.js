/**
 * @deprecated
 * @module
 */
import { jubjub_findGroupHash, jubjub_groupHash, jubjub as jubjubn } from "./misc.js";
/** @deprecated Use `@noble/curves/misc` module directly. */
export const jubjub = jubjubn;
/** @deprecated Use `@noble/curves/misc` module directly. */
export const findGroupHash = jubjub_findGroupHash;
/** @deprecated Use `@noble/curves/misc` module directly. */
export const groupHash = jubjub_groupHash;
//# sourceMappingURL=jubjub.js.map