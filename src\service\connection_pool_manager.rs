use std::sync::Arc;
use tokio::sync::{RwLock, Semaphore};
use std::collections::HashMap;
use crate::model::{BotError, Blockchain};
use crate::config::defaults;
use std::time::{Duration, Instant};
use futures;

/// High-Performance Connection Pool Manager
/// Optimizes RPC connections and database connections for trading operations
pub struct ConnectionPoolManager {
    // RPC connection pools per blockchain
    rpc_pools: Arc<RwLock<HashMap<Blockchain, Vec<RpcConnection>>>>,
    
    // Connection semaphores for rate limiting
    rpc_semaphores: Arc<RwLock<HashMap<Blockchain, Arc<Semaphore>>>>,
    
    // Health monitoring
    connection_health: Arc<RwLock<HashMap<String, ConnectionHealth>>>,
}

#[derive(Clone)]
pub struct RpcConnection {
    pub url: String,
    pub last_used: Instant,
    pub success_rate: f64,
    pub avg_response_time: Duration,
    pub is_healthy: bool,
}

#[derive(Clone)]
pub struct ConnectionHealth {
    pub total_requests: u64,
    pub successful_requests: u64,
    pub failed_requests: u64,
    pub avg_response_time: Duration,
    pub last_health_check: Instant,
}

impl ConnectionPoolManager {
    /// Initialize connection pool manager with optimized settings
    pub fn new() -> Self {
        println!("Initializing High-Performance Connection Pool Manager");
        println!("   🔗 RPC Pool Size per Chain: {}", defaults::RPC_CONNECTION_POOL_SIZE);
        println!("   💾 Database Pool Size: {}", defaults::DATABASE_CONNECTION_POOL);
        
        Self {
            rpc_pools: Arc::new(RwLock::new(HashMap::new())),
            rpc_semaphores: Arc::new(RwLock::new(HashMap::new())),
            connection_health: Arc::new(RwLock::new(HashMap::new())),
        }
    }

    /// Initialize RPC connection pool for a blockchain
    pub async fn initialize_rpc_pool(&self, blockchain: &Blockchain) -> Result<(), BotError> {
        println!("🔧 Initializing RPC pool for {:?}", blockchain);
        
        let rpc_urls = self.get_rpc_urls_for_blockchain(blockchain);
        let mut connections = Vec::new();
        
        // Create multiple connections for load balancing
        for (i, url) in rpc_urls.iter().enumerate() {
            let connection = RpcConnection {
                url: url.clone(),
                last_used: Instant::now(),
                success_rate: 1.0,
                avg_response_time: Duration::from_millis(100),
                is_healthy: true,
            };
            connections.push(connection);
            
            // Initialize health monitoring
            let health = ConnectionHealth {
                total_requests: 0,
                successful_requests: 0,
                failed_requests: 0,
                avg_response_time: Duration::from_millis(100),
                last_health_check: Instant::now(),
            };
            
            let health_key = format!("{:?}_{}", blockchain, i);
            self.connection_health.write().await.insert(health_key, health);
        }
        
        // Store connections in pool
        self.rpc_pools.write().await.insert(blockchain.clone(), connections);
        
        // Create semaphore for rate limiting
        let semaphore = Arc::new(Semaphore::new(defaults::RPC_CONNECTION_POOL_SIZE));
        self.rpc_semaphores.write().await.insert(blockchain.clone(), semaphore);
        
        println!("✅ RPC pool initialized for {:?} with {} connections", blockchain, rpc_urls.len());
        Ok(())
    }

    /// Get the best available RPC connection for a blockchain
    pub async fn get_best_rpc_connection(&self, blockchain: &Blockchain) -> Result<String, BotError> {
        // Acquire semaphore permit for rate limiting
        let semaphores = self.rpc_semaphores.read().await;
        let semaphore = semaphores.get(blockchain)
            .ok_or_else(|| BotError::network_error(format!("No RPC pool for {:?}", blockchain)))?;
        
        let _permit = semaphore.acquire().await
            .map_err(|e| BotError::network_error(format!("RPC pool exhausted: {}", e)))?;
        
        // Get the best connection based on health metrics
        let pools = self.rpc_pools.read().await;
        let connections = pools.get(blockchain)
            .ok_or_else(|| BotError::network_error(format!("No RPC connections for {:?}", blockchain)))?;
        
        // Find the healthiest connection
        let best_connection = connections.iter()
            .filter(|conn| conn.is_healthy)
            .max_by(|a, b| {
                // Prioritize by success rate, then by response time
                a.success_rate.partial_cmp(&b.success_rate)
                    .unwrap_or(std::cmp::Ordering::Equal)
                    .then_with(|| b.avg_response_time.cmp(&a.avg_response_time))
            })
            .or_else(|| connections.first()) // Fallback to first connection
            .ok_or_else(|| BotError::network_error("No available RPC connections".to_string()))?;
        
        Ok(best_connection.url.clone())
    }

    /// Update connection health metrics
    pub async fn update_connection_health(
        &self,
        blockchain: &Blockchain,
        url: &str,
        success: bool,
        response_time: Duration,
    ) {
        let health_key = format!("{:?}_{}", blockchain, url);
        
        let mut health_map = self.connection_health.write().await;
        if let Some(health) = health_map.get_mut(&health_key) {
            health.total_requests += 1;
            if success {
                health.successful_requests += 1;
            } else {
                health.failed_requests += 1;
            }
            
            // Update average response time (exponential moving average)
            let alpha = 0.1; // Smoothing factor
            health.avg_response_time = Duration::from_nanos(
                (health.avg_response_time.as_nanos() as f64 * (1.0 - alpha) + 
                 response_time.as_nanos() as f64 * alpha) as u64
            );
            
            health.last_health_check = Instant::now();
        }
        
        // Update connection in pool
        let mut pools = self.rpc_pools.write().await;
        if let Some(connections) = pools.get_mut(blockchain) {
            for conn in connections.iter_mut() {
                if conn.url == url {
                    conn.last_used = Instant::now();
                    conn.success_rate = if health_map.get(&health_key).unwrap().total_requests > 0 {
                        health_map.get(&health_key).unwrap().successful_requests as f64 / 
                        health_map.get(&health_key).unwrap().total_requests as f64
                    } else {
                        1.0
                    };
                    conn.avg_response_time = health_map.get(&health_key).unwrap().avg_response_time;
                    conn.is_healthy = conn.success_rate > 0.8; // 80% success rate threshold
                    break;
                }
            }
        }
    }

    /// Get RPC URLs for a specific blockchain
    fn get_rpc_urls_for_blockchain(&self, blockchain: &Blockchain) -> Vec<String> {
        match blockchain {
            Blockchain::SOL => vec![
                "https://api.mainnet-beta.solana.com".to_string(),
                "https://solana-api.projectserum.com".to_string(),
                "https://rpc.ankr.com/solana".to_string(),
                std::env::var("SOLANA_RPC_URL").unwrap_or_else(|_| "https://api.mainnet-beta.solana.com".to_string()),
            ],
            Blockchain::ETH => vec![
                "https://eth-mainnet.g.alchemy.com/v2/demo".to_string(),
                "https://mainnet.infura.io/v3/demo".to_string(),
                "https://rpc.ankr.com/eth".to_string(),
                std::env::var("ETH_RPC_URL").unwrap_or_else(|_| "https://eth-mainnet.g.alchemy.com/v2/demo".to_string()),
            ],
            Blockchain::BSC => vec![
                "https://bsc-dataseed.binance.org".to_string(),
                "https://bsc-dataseed1.defibit.io".to_string(),
                "https://rpc.ankr.com/bsc".to_string(),
                std::env::var("BSC_RPC_URL").unwrap_or_else(|_| "https://bsc-dataseed.binance.org".to_string()),
            ],
            Blockchain::BASE => vec![
                "https://mainnet.base.org".to_string(),
                "https://base-mainnet.g.alchemy.com/v2/demo".to_string(),
                "https://rpc.ankr.com/base".to_string(),
                std::env::var("BASE_RPC_URL").unwrap_or_else(|_| "https://mainnet.base.org".to_string()),
            ],
        }
    }

    /// PRODUCTION: Perform intelligent health checks on all connections
    pub async fn perform_health_checks(&self) {
        println!("🔍 Performing intelligent connection health checks");

        let pools = self.rpc_pools.read().await;
        let mut health_check_futures = Vec::new();

        for (blockchain, connections) in pools.iter() {
            for connection in connections {
                let blockchain_clone = blockchain.clone();
                let url = connection.url.clone();
                let connection_health = self.connection_health.clone();

                // Create health check future for each connection
                let health_check_future = async move {
                    let start_time = Instant::now();
                    let is_healthy = Self::ping_rpc_endpoint(&url, &blockchain_clone).await;
                    let response_time = start_time.elapsed();

                    // Update health metrics
                    let health_key = format!("{:?}_{}", blockchain_clone, url);
                    let mut health_map = connection_health.write().await;
                    if let Some(health) = health_map.get_mut(&health_key) {
                        health.total_requests += 1;
                        if is_healthy {
                            health.successful_requests += 1;
                        } else {
                            health.failed_requests += 1;
                        }
                        health.avg_response_time = response_time;
                        health.last_health_check = Instant::now();
                    }

                    if !is_healthy {
                        println!("⚠️ Unhealthy connection detected: {:?} - {} ({}ms)",
                                blockchain_clone, url, response_time.as_millis());
                    } else {
                        println!("✅ Healthy connection: {:?} - {} ({}ms)",
                                blockchain_clone, url, response_time.as_millis());
                    }
                };

                health_check_futures.push(health_check_future);
            }
        }

        // Execute all health checks in parallel
        futures::future::join_all(health_check_futures).await;
        println!("✅ Health checks completed for all connections");
    }

    /// PRODUCTION: Ping RPC endpoint to check health
    async fn ping_rpc_endpoint(url: &str, blockchain: &Blockchain) -> bool {
        use reqwest;

        let client = match reqwest::Client::builder()
            .timeout(Duration::from_secs(5))
            .build() {
            Ok(client) => client,
            Err(_) => return false,
        };

        // Create appropriate health check request based on blockchain
        let (method, params) = match blockchain {
            Blockchain::SOL => ("getHealth", serde_json::json!([])),
            Blockchain::ETH | Blockchain::BSC | Blockchain::BASE => {
                ("eth_blockNumber", serde_json::json!([]))
            }
        };

        let request_body = serde_json::json!({
            "jsonrpc": "2.0",
            "id": 1,
            "method": method,
            "params": params
        });

        match client
            .post(url)
            .header("Content-Type", "application/json")
            .json(&request_body)
            .send()
            .await {
            Ok(response) => {
                if response.status().is_success() {
                    // Try to parse response to ensure it's valid
                    match response.json::<serde_json::Value>().await {
                        Ok(json) => {
                            // Check if response contains error
                            !json.get("error").is_some()
                        }
                        Err(_) => false,
                    }
                } else {
                    false
                }
            }
            Err(_) => false,
        }
    }

    /// Get connection pool statistics
    pub async fn get_pool_statistics(&self) -> HashMap<Blockchain, PoolStatistics> {
        let mut stats = HashMap::new();
        
        let pools = self.rpc_pools.read().await;
        for (blockchain, connections) in pools.iter() {
            let healthy_connections = connections.iter().filter(|c| c.is_healthy).count();
            let avg_success_rate = connections.iter().map(|c| c.success_rate).sum::<f64>() / connections.len() as f64;
            let avg_response_time = Duration::from_nanos(
                (connections.iter().map(|c| c.avg_response_time.as_nanos()).sum::<u128>() / connections.len() as u128) as u64
            );
            
            stats.insert(blockchain.clone(), PoolStatistics {
                total_connections: connections.len(),
                healthy_connections,
                avg_success_rate,
                avg_response_time,
            });
        }
        
        stats
    }

    /// PRODUCTION: Intelligent load balancing with auto-scaling
    pub async fn auto_scale_connections(&self, blockchain: &Blockchain) -> Result<(), BotError> {
        let pools = self.rpc_pools.read().await;
        let connections = pools.get(blockchain)
            .ok_or_else(|| BotError::network_error(format!("No pool for {:?}", blockchain)))?;

        let healthy_connections = connections.iter().filter(|c| c.is_healthy).count();
        let total_connections = connections.len();

        // Calculate health ratio
        let health_ratio = healthy_connections as f64 / total_connections as f64;

        println!("🔍 Auto-scaling analysis for {:?}: {}/{} healthy ({:.1}%)",
                blockchain, healthy_connections, total_connections, health_ratio * 100.0);

        // Auto-scale based on health ratio
        if health_ratio < 0.5 {
            // Less than 50% healthy - add more connections
            println!("Auto-scaling UP: Adding backup connections for {:?}", blockchain);
            self.add_backup_connections(blockchain).await?;
        } else if health_ratio > 0.9 && total_connections > defaults::RPC_CONNECTION_POOL_SIZE {
            // More than 90% healthy and we have excess - remove some connections
            println!("🔽 Auto-scaling DOWN: Removing excess connections for {:?}", blockchain);
            self.remove_excess_connections(blockchain).await?;
        }

        Ok(())
    }

    /// PRODUCTION: Add backup connections when health is low
    async fn add_backup_connections(&self, blockchain: &Blockchain) -> Result<(), BotError> {
        let backup_urls = self.get_backup_rpc_urls(blockchain);
        let mut pools = self.rpc_pools.write().await;

        if let Some(connections) = pools.get_mut(blockchain) {
            for url in backup_urls {
                // Check if URL already exists
                if !connections.iter().any(|c| c.url == url) {
                    let connection = RpcConnection {
                        url: url.clone(),
                        last_used: Instant::now(),
                        success_rate: 1.0, // Start optimistic
                        avg_response_time: Duration::from_millis(100),
                        is_healthy: true,
                    };
                    connections.push(connection);

                    // Initialize health monitoring
                    let health = ConnectionHealth {
                        total_requests: 0,
                        successful_requests: 0,
                        failed_requests: 0,
                        avg_response_time: Duration::from_millis(100),
                        last_health_check: Instant::now(),
                    };

                    let health_key = format!("{:?}_{}", blockchain, url);
                    self.connection_health.write().await.insert(health_key, health);

                    println!("✅ Added backup connection: {:?} - {}", blockchain, url);
                }
            }
        }

        Ok(())
    }

    /// PRODUCTION: Remove excess connections when health is high
    async fn remove_excess_connections(&self, blockchain: &Blockchain) -> Result<(), BotError> {
        let mut pools = self.rpc_pools.write().await;

        if let Some(connections) = pools.get_mut(blockchain) {
            // Keep only the best performing connections
            connections.sort_by(|a, b| {
                b.success_rate.partial_cmp(&a.success_rate)
                    .unwrap_or(std::cmp::Ordering::Equal)
                    .then_with(|| a.avg_response_time.cmp(&b.avg_response_time))
            });

            // Keep at least the minimum required connections
            let target_size = defaults::RPC_CONNECTION_POOL_SIZE;
            if connections.len() > target_size {
                let removed_count = connections.len() - target_size;
                connections.truncate(target_size);
                println!("🔽 Removed {} excess connections for {:?}", removed_count, blockchain);
            }
        }

        Ok(())
    }

    /// PRODUCTION: Get backup RPC URLs for emergency scaling
    fn get_backup_rpc_urls(&self, blockchain: &Blockchain) -> Vec<String> {
        match blockchain {
            Blockchain::SOL => vec![
                "https://solana-mainnet.rpc.extrnode.com".to_string(),
                "https://rpc.hellomoon.io".to_string(),
                "https://solana.blockpi.network/v1/rpc/public".to_string(),
            ],
            Blockchain::ETH => vec![
                "https://ethereum.publicnode.com".to_string(),
                "https://rpc.flashbots.net".to_string(),
                "https://eth.llamarpc.com".to_string(),
            ],
            Blockchain::BSC => vec![
                "https://bsc.publicnode.com".to_string(),
                "https://bsc-rpc.gateway.pokt.network".to_string(),
                "https://bsc.meowrpc.com".to_string(),
            ],
            Blockchain::BASE => vec![
                "https://base.publicnode.com".to_string(),
                "https://base.meowrpc.com".to_string(),
                "https://base-rpc.publicnode.com".to_string(),
            ],
        }
    }

    /// PRODUCTION: Intelligent connection routing based on request type
    pub async fn get_optimized_connection(&self, blockchain: &Blockchain, request_type: RequestType) -> Result<String, BotError> {
        let pools = self.rpc_pools.read().await;
        let connections = pools.get(blockchain)
            .ok_or_else(|| BotError::network_error(format!("No RPC pool for {:?}", blockchain)))?;

        // Filter connections based on request type requirements
        let suitable_connections: Vec<_> = connections.iter()
            .filter(|conn| conn.is_healthy)
            .filter(|conn| self.is_connection_suitable_for_request(conn, &request_type))
            .collect();

        if suitable_connections.is_empty() {
            // Fallback to any healthy connection
            let fallback = connections.iter()
                .filter(|conn| conn.is_healthy)
                .next()
                .ok_or_else(|| BotError::network_error("No healthy connections available".to_string()))?;
            return Ok(fallback.url.clone());
        }

        // Select best connection based on request type
        let best_connection = match request_type {
            RequestType::HighPriority => {
                // For high priority, prefer lowest latency
                suitable_connections.iter()
                    .min_by_key(|conn| conn.avg_response_time)
                    .unwrap()
            }
            RequestType::HighVolume => {
                // For high volume, prefer highest success rate
                suitable_connections.iter()
                    .max_by(|a, b| a.success_rate.partial_cmp(&b.success_rate).unwrap_or(std::cmp::Ordering::Equal))
                    .unwrap()
            }
            RequestType::Standard => {
                // For standard, balance latency and success rate
                suitable_connections.iter()
                    .max_by(|a, b| {
                        let score_a = a.success_rate - (a.avg_response_time.as_millis() as f64 / 1000.0);
                        let score_b = b.success_rate - (b.avg_response_time.as_millis() as f64 / 1000.0);
                        score_a.partial_cmp(&score_b).unwrap_or(std::cmp::Ordering::Equal)
                    })
                    .unwrap()
            }
        };

        Ok(best_connection.url.clone())
    }

    /// Check if connection is suitable for specific request type
    fn is_connection_suitable_for_request(&self, connection: &RpcConnection, request_type: &RequestType) -> bool {
        match request_type {
            RequestType::HighPriority => {
                connection.success_rate > 0.95 && connection.avg_response_time < Duration::from_millis(500)
            }
            RequestType::HighVolume => {
                connection.success_rate > 0.9
            }
            RequestType::Standard => {
                connection.success_rate > 0.8
            }
        }
    }
}

#[derive(Debug, Clone)]
pub enum RequestType {
    HighPriority,  // Trading operations requiring lowest latency
    HighVolume,    // Bulk operations requiring high reliability
    Standard,      // Regular operations with balanced requirements
}

#[derive(Debug)]
pub struct PoolStatistics {
    pub total_connections: usize,
    pub healthy_connections: usize,
    pub avg_success_rate: f64,
    pub avg_response_time: Duration,
}
