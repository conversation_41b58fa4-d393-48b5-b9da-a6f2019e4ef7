import { useState, useEffect, useMemo, useCallback, useRef } from 'react';
import Card from '../../components/ui/Card';
import Button from '../../components/ui/Button';
import {
  UsersIcon,
  CurrencyDollarIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  CircleStackIcon,
  BoltIcon,
} from '@heroicons/react/24/outline';
import { Line, Bar, Doughnut } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  Tooltip,
  Legend,
  Filler,
} from 'chart.js';
import adminApi, { type DashboardResponse } from '../../services/adminApi';
import DebugAuth from '../../components/DebugAuth';

// Register ChartJS components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  Tooltip,
  Legend,
  Filler
);

// Helper function to format numbers
const formatNumber = (num: number): string => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M';
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K';
  }
  return num.toString();
};

const formatCurrency = (num: number): string => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(num);
};

export default function AdminDashboard() {
  const [loading, setLoading] = useState(true);
  const [timeRange, setTimeRange] = useState('30d');
  const [dashboardData, setDashboardData] = useState<DashboardResponse | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [animatedData, setAnimatedData] = useState<number[][]>([
    [0, 0, 0, 0, 0, 0, 0],
    [0, 0, 0, 0, 0, 0, 0],
  ]);
  const loadingRef = useRef(false);

  // Manual fetch function for testing
  const manualFetch = useCallback(async () => {
    if (loadingRef.current) {
      console.log('⏸️ Skipping manual fetch - already loading');
      return;
    }
    console.log('Manual fetch triggered');
    try {
      loadingRef.current = true;
      setLoading(true);
      setError(null);

      console.log('Starting manual dashboard data fetch...');
      console.log('Auth token from localStorage:', localStorage.getItem('admin_token'));
      console.log('Making API call to:', `${import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api'}/admin/dashboard`);

      const data = await adminApi.getDashboardAnalytics();
      setDashboardData(data);
      console.log('Manual dashboard data received:', data);
    } catch (apiError: any) {
      console.error('Manual fetch error:', apiError);
      console.error('Error details:', {
        message: apiError.message,
        status: apiError.response?.status,
        statusText: apiError.response?.statusText,
        data: apiError.response?.data
      });
      setError(`Failed to load dashboard data: ${apiError.message || 'Unknown error'}`);
    } finally {
      loadingRef.current = false;
      setLoading(false);
    }
  }, []);

  // Real data for the charts - using useMemo to avoid recreation on every render
  const finalData = useMemo(() => {
    if (!dashboardData?.analytics?.hourly_stats) {
      return [
        [0, 0, 0, 0, 0, 0, 0], // Empty data while loading
        [0, 0, 0, 0, 0, 0, 0], // Empty data while loading
      ];
    }

    // Extract real data from API response
    const hourlyStats = dashboardData.analytics.hourly_stats;
    const transactionsData = hourlyStats.map(stat => stat.transactions);
    const volumeData = hourlyStats.map(stat => stat.volume);

    return [transactionsData, volumeData];
  }, [dashboardData?.analytics?.hourly_stats]);

  // Fetch dashboard stats
  const fetchData = useCallback(async () => {
    if (loadingRef.current) {
      console.log('⏸️ Skipping fetch - already loading');
      return;
    }

    try {
      loadingRef.current = true;
      setLoading(true);
      setError(null);

      console.log('Starting dashboard data fetch...');
      console.log('Auth token from localStorage:', localStorage.getItem('admin_token'));
      console.log('Making API call to:', `${import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api'}/admin/dashboard`);

      // Fetch both dashboard analytics and detailed transaction metrics
      const [dashboardData, transactionMetrics] = await Promise.all([
        adminApi.getDashboardAnalytics(),
        adminApi.getTransactionMetrics().catch(() => null) // Don't fail if this endpoint doesn't exist yet
      ]);

      setDashboardData(dashboardData);
      console.log('Dashboard data received:', dashboardData);
      console.log('Transaction metrics received:', transactionMetrics);
    } catch (apiError: any) {
      console.error('Error fetching dashboard data:', apiError);
      console.error('Error details:', {
        message: apiError.message,
        status: apiError.response?.status,
        statusText: apiError.response?.statusText,
        data: apiError.response?.data
      });
      setError(`Failed to load dashboard data: ${apiError.message || 'Unknown error'}`);
    } finally {
      loadingRef.current = false;
      setLoading(false);
    }
  }, [timeRange]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  // Animate the chart data on component mount
  useEffect(() => {
    const animationDuration = 1500; // ms
    const steps = 30;
    const stepDuration = animationDuration / steps;

    let currentStep = 0;

    const interval = setInterval(() => {
      if (currentStep >= steps) {
        clearInterval(interval);
        setAnimatedData(finalData);
        return;
      }

      const progress = (currentStep + 1) / steps;
      const newData = finalData.map((dataset) =>
        dataset.map((value) => Math.round(value * progress))
      );

      setAnimatedData(newData);
      currentStep++;
    }, stepDuration);

    return () => clearInterval(interval);
  }, [finalData]);

  // Create resource stats from API data
  const resourceStats = useMemo(() => {
    if (!dashboardData) return [];

    const { analytics } = dashboardData;

    // Calculate percentage changes based on 24h data vs total
    const userChange = analytics.total_users > 0
      ? ((analytics.active_users_24h / analytics.total_users) * 100).toFixed(1)
      : '0.0';

    const transactionChange = analytics.total_transactions > 0
      ? ((analytics.transactions_24h / analytics.total_transactions) * 100).toFixed(1)
      : '0.0';

    const feeChange = analytics.total_fees_collected > 0
      ? ((analytics.fees_collected_24h / analytics.total_fees_collected) * 100).toFixed(1)
      : '0.0';

    return [
      {
        name: 'Total Users',
        value: formatNumber(analytics.total_users),
        change: `${userChange}% active today`,
        changeType: 'increase' as const,
        icon: UsersIcon,
        color: 'from-purple-500 to-indigo-600'
      },
      {
        name: 'Active Bots',
        value: formatNumber(dashboardData.bots_summary.filter(bot => bot.status === 'Active').length),
        change: `${dashboardData.bots_summary.length} total`,
        changeType: 'increase' as const,
        icon: BoltIcon,
        color: 'from-indigo-500 to-blue-500'
      },
      {
        name: 'Transactions',
        value: formatNumber(analytics.total_transactions),
        change: `${transactionChange}% today`,
        changeType: 'increase' as const,
        icon: CircleStackIcon,
        color: 'from-blue-500 to-cyan-500'
      },
      {
        name: 'Admin Fee Earned',
        value: formatCurrency(analytics.total_fees_collected),
        change: `${feeChange}% today`,
        changeType: 'increase' as const,
        icon: CurrencyDollarIcon,
        color: 'from-green-500 to-emerald-600'
      },
    ];
  }, [dashboardData]);

  // Bot activity chart data
  const resourceUsageChartData = useMemo(() => {
    if (!dashboardData?.analytics?.hourly_stats) {
      return {
        labels: ['0h', '1h', '2h', '3h', '4h', '5h', '6h'],
        datasets: [
          {
            label: 'Transactions',
            data: animatedData[0],
            fill: true,
            backgroundColor: 'rgba(79, 70, 229, 0.3)',
            borderColor: 'rgba(99, 102, 241, 1)',
            borderWidth: 2,
            tension: 0.4,
            pointBackgroundColor: 'rgba(99, 102, 241, 1)',
            pointBorderColor: '#fff',
            pointHoverBackgroundColor: '#fff',
            pointHoverBorderColor: 'rgba(99, 102, 241, 1)',
            pointRadius: 4,
          },
          {
            label: 'Volume',
            data: animatedData[1],
            fill: true,
            backgroundColor: 'rgba(124, 58, 237, 0.3)',
            borderColor: 'rgba(139, 92, 246, 1)',
            borderWidth: 2,
            tension: 0.4,
            pointBackgroundColor: 'rgba(139, 92, 246, 1)',
            pointBorderColor: '#fff',
            pointHoverBackgroundColor: '#fff',
            pointHoverBorderColor: 'rgba(139, 92, 246, 1)',
            pointRadius: 4,
          },
        ],
      };
    }

    // Generate labels from hourly stats
    const hourlyStats = dashboardData.analytics.hourly_stats;
    const labels = hourlyStats.map(stat => `${stat.hour}h`);

    return {
      labels,
      datasets: [
        {
          label: 'Transactions',
          data: animatedData[0],
          fill: true,
          backgroundColor: 'rgba(79, 70, 229, 0.3)',
          borderColor: 'rgba(99, 102, 241, 1)',
          borderWidth: 2,
          tension: 0.4,
          pointBackgroundColor: 'rgba(99, 102, 241, 1)',
          pointBorderColor: '#fff',
          pointHoverBackgroundColor: '#fff',
          pointHoverBorderColor: 'rgba(99, 102, 241, 1)',
          pointRadius: 4,
        },
        {
          label: 'Volume',
          data: animatedData[1],
          fill: true,
          backgroundColor: 'rgba(124, 58, 237, 0.3)',
          borderColor: 'rgba(139, 92, 246, 1)',
          borderWidth: 2,
          tension: 0.4,
          pointBackgroundColor: 'rgba(139, 92, 246, 1)',
          pointBorderColor: '#fff',
          pointHoverBackgroundColor: '#fff',
          pointHoverBorderColor: 'rgba(139, 92, 246, 1)',
          pointRadius: 4,
        },
      ],
    };
  }, [dashboardData?.analytics?.hourly_stats, animatedData]);

  // Weekly transactions chart data
  const weeklyRequestsChartData = useMemo(() => {
    if (!dashboardData?.analytics?.hourly_stats) {
      return {
        labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
        datasets: [{
          label: 'Bot Transactions',
          data: [0, 0, 0, 0, 0, 0, 0],
          backgroundColor: 'rgba(6, 182, 212, 0.7)',
          borderColor: 'rgba(6, 182, 212, 1)',
          borderWidth: 1,
          borderRadius: 4,
        }],
      };
    }

    // Group hourly stats into daily data (assuming we have 24 hours of data)
    const hourlyStats = dashboardData.analytics.hourly_stats;
    const dailyData = [];

    // Group by days (every 24 hours = 1 day)
    for (let day = 0; day < 7; day++) {
      const dayStart = day * 24;
      const dayEnd = Math.min((day + 1) * 24, hourlyStats.length);
      const dayTransactions = hourlyStats
        .slice(dayStart, dayEnd)
        .reduce((sum, stat) => sum + stat.transactions, 0);
      dailyData.push(dayTransactions);
    }

    return {
      labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
      datasets: [{
        label: 'Bot Transactions',
        data: dailyData,
        backgroundColor: 'rgba(6, 182, 212, 0.7)',
        borderColor: 'rgba(6, 182, 212, 1)',
        borderWidth: 1,
        borderRadius: 4,
      }],
    };
  }, [dashboardData?.analytics?.hourly_stats]);

  // Blockchain distribution chart data
  const blockchainDistributionData = useMemo(() => {
    if (!dashboardData) {
      return {
        labels: [],
        datasets: [{
          data: [],
          backgroundColor: [],
          borderColor: [],
          borderWidth: 1,
        }],
      };
    }

    const { blockchain_distribution } = dashboardData.analytics;
    const labels = Object.keys(blockchain_distribution);
    const data = Object.values(blockchain_distribution);

    const colors = [
      'rgba(99, 102, 241, 0.7)',
      'rgba(139, 92, 246, 0.7)',
      'rgba(6, 182, 212, 0.7)',
      'rgba(16, 185, 129, 0.7)',
    ];

    const borderColors = [
      'rgba(99, 102, 241, 1)',
      'rgba(139, 92, 246, 1)',
      'rgba(6, 182, 212, 1)',
      'rgba(16, 185, 129, 1)',
    ];

    return {
      labels,
      datasets: [{
        data,
        backgroundColor: colors.slice(0, labels.length),
        borderColor: borderColors.slice(0, labels.length),
        borderWidth: 1,
      }],
    };
  }, [dashboardData]);

  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top' as const,
        labels: {
          color: 'rgba(255, 255, 255, 0.7)',
          font: {
            family: 'Inter',
            size: 12,
          },
          boxWidth: 12,
          padding: 15,
        },
      },
      tooltip: {
        backgroundColor: 'rgba(17, 24, 39, 0.8)',
        titleFont: {
          family: 'Inter',
          size: 13,
        },
        bodyFont: {
          family: 'Inter',
          size: 12,
        },
        padding: 12,
        borderColor: 'rgba(255, 255, 255, 0.1)',
        borderWidth: 1,
      },
    },
    scales: {
      y: {
        beginAtZero: true,
        grid: {
          color: 'rgba(255, 255, 255, 0.05)',
        },
        ticks: {
          color: 'rgba(255, 255, 255, 0.6)',
          font: {
            family: 'Inter',
            size: 11,
          },
        },
      },
      x: {
        grid: {
          color: 'rgba(255, 255, 255, 0.05)',
        },
        ticks: {
          color: 'rgba(255, 255, 255, 0.6)',
          font: {
            family: 'Inter',
            size: 11,
          },
        },
      },
    },
  };

  const pieChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'right' as const,
        labels: {
          color: 'rgba(255, 255, 255, 0.7)',
          font: {
            family: 'Inter',
            size: 12,
          },
          padding: 15,
        },
      },
      tooltip: {
        backgroundColor: 'rgba(17, 24, 39, 0.8)',
        titleFont: {
          family: 'Inter',
          size: 13,
        },
        bodyFont: {
          family: 'Inter',
          size: 12,
        },
        padding: 12,
        borderColor: 'rgba(255, 255, 255, 0.1)',
        borderWidth: 1,
      },
    },
  };

  function classNames(...classes: string[]) {
    return classes.filter(Boolean).join(' ');
  }

  return (
    <div className="space-y-8">
      <DebugAuth />
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
        <div>
          <h1 className="text-2xl font-bold text-white">Dashboard</h1>
          <p className="mt-1 text-sm text-gray-400">Last {timeRange}</p>
        </div>
        <div className="flex space-x-2">
          <Button
            variant="secondary"
            size="sm"
            onClick={manualFetch}
            disabled={loading}
          >
            {loading ? 'Loading...' : 'Fetch Analytics'}
          </Button>
          <Button
            variant={timeRange === '24h' ? 'primary' : 'outline'}
            size="sm"
            onClick={() => setTimeRange('24h')}
          >
            24h
          </Button>
          <Button
            variant={timeRange === '7d' ? 'primary' : 'outline'}
            size="sm"
            onClick={() => setTimeRange('7d')}
          >
            7d
          </Button>
          <Button
            variant={timeRange === '30d' ? 'primary' : 'outline'}
            size="sm"
            onClick={() => setTimeRange('30d')}
          >
            30d
          </Button>
        </div>
      </div>

      {loading ? (
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"></div>
        </div>
      ) : error ? (
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <p className="text-red-400 mb-4">{error}</p>
            <Button onClick={() => window.location.reload()}>Retry</Button>
          </div>
        </div>
      ) : !dashboardData ? (
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <p className="text-gray-400 mb-2">No dashboard data available</p>
            <p className="text-sm text-gray-500">Please check your API connection</p>
          </div>
        </div>
      ) : (
        <>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {resourceStats.map((stat) => (
              <Card
                key={stat.name}
                className={`p-6 relative overflow-hidden animate-fade-in`}
              >
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className={`h-12 w-12 rounded-full bg-gradient-to-br ${stat.color} bg-opacity-20 flex items-center justify-center`}>
                      <stat.icon className="h-6 w-6 text-white" aria-hidden="true" />
                    </div>
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-300 truncate">{stat.name}</dt>
                      <dd>
                        <div className="text-lg font-semibold text-white">{stat.value}</div>
                      </dd>
                    </dl>
                  </div>
                </div>
                <div className="absolute bottom-0 inset-x-0 bg-gradient-to-r from-transparent via-indigo-500/20 to-transparent h-0.5"></div>

                <div className="absolute top-6 right-6">
                  <div
                    className={classNames(
                      stat.changeType === 'increase' ? 'bg-green-900/30 text-green-300 border border-green-500/30' : 'bg-red-900/30 text-red-300 border border-red-500/30',
                      'inline-flex items-baseline rounded-full px-2.5 py-0.5 text-sm font-medium backdrop-blur-sm'
                    )}
                  >
                    {stat.changeType === 'increase' ? (
                      <ArrowUpIcon
                        className="-ml-1 mr-0.5 h-4 w-4 flex-shrink-0 self-center text-green-400"
                        aria-hidden="true"
                      />
                    ) : (
                      <ArrowDownIcon
                        className="-ml-1 mr-0.5 h-4 w-4 flex-shrink-0 self-center text-red-400"
                        aria-hidden="true"
                      />
                    )}
                    {stat.change}
                  </div>
                </div>
              </Card>
            ))}
          </div>

          <div className="grid grid-cols-1 gap-8">
            <Card className="p-6 relative overflow-hidden">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-base font-semibold leading-6 text-white">Bot Activity</h3>
                <div className="flex items-center space-x-2">
                  <Button variant="outline" size="sm">Export</Button>
                </div>
              </div>
              <div className="h-80 relative">
                <Line data={resourceUsageChartData} options={chartOptions} />
              </div>
            </Card>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <div className="lg:col-span-2">
              <Card className="p-6 relative overflow-hidden">
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-base font-semibold leading-6 text-white">Weekly Transactions</h3>
                  <Button variant="outline" size="sm">Export</Button>
                </div>
                <div className="h-80 relative">
                  <Bar data={weeklyRequestsChartData} options={chartOptions} />
                </div>
              </Card>
            </div>

            <div className="lg:col-span-1">
              <Card className="p-6 relative overflow-hidden h-full">
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-base font-semibold leading-6 text-white">Bot Chain Distribution</h3>
                </div>
                <div className="h-80 relative">
                  <Doughnut data={blockchainDistributionData} options={pieChartOptions} />
                </div>
              </Card>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <div className="lg:col-span-1">
              <Card className="p-6 relative overflow-hidden">
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-base font-semibold leading-6 text-white">Active Bots</h3>
                  <Button variant="primary" size="sm">New Bot</Button>
                </div>
                <div className="space-y-4">
                  {dashboardData.bots_summary && dashboardData.bots_summary.length > 0 ? (
                    dashboardData.bots_summary.slice(0, 3).map((bot) => (
                    <div key={bot.id} className="p-4 rounded-lg border border-white/10 bg-white/5">
                      <div className="flex justify-between items-start">
                        <div>
                          <h4 className="text-sm font-medium text-white">{bot.name}</h4>
                          <p className="mt-1 text-xs text-gray-400">
                            {bot.bot_type} • {bot.active_users} users
                          </p>
                        </div>
                        <span className={`inline-flex items-center rounded-full px-2 py-0.5 text-xs font-medium ${
                          bot.status === 'Active'
                            ? 'bg-green-900/30 border border-green-500/30 text-green-300'
                            : 'bg-red-900/30 border border-red-500/30 text-red-300'
                        }`}>
                          {bot.status}
                        </span>
                      </div>
                      <div className="mt-3">
                        <div className="flex justify-between text-xs text-gray-400 mb-1">
                          <span>Success Rate</span>
                          <span>{bot.success_rate.toFixed(1)}%</span>
                        </div>
                        <div className="w-full bg-gray-700 rounded-full h-1.5">
                          <div
                            className="bg-indigo-500 h-1.5 rounded-full"
                            style={{ width: `${bot.success_rate}%` }}
                          ></div>
                        </div>
                      </div>
                    </div>
                    ))
                  ) : (
                    <div className="text-center py-8">
                      <p className="text-gray-400">No active bots found</p>
                    </div>
                  )}
                </div>
              </Card>
            </div>

            <div className="lg:col-span-2">
              <Card className="p-6 relative overflow-hidden">
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-base font-semibold leading-6 text-white">Bot Activity Log</h3>
                  <Button variant="outline" size="sm">View All</Button>
                </div>

                <div className="space-y-6">
                  {dashboardData.recent_transactions && dashboardData.recent_transactions.length > 0 ? (
                    dashboardData.recent_transactions.slice(0, 4).map((transaction) => (
                    <div key={transaction.id} className="flex">
                      <div className={`flex-shrink-0 h-8 w-8 rounded-full ${
                        transaction.status === 'completed' ? 'bg-green-500' : 'bg-red-500'
                      } flex items-center justify-center mr-4`}>
                        <CurrencyDollarIcon className="h-4 w-4 text-white" aria-hidden="true" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-white">
                          {transaction.bot_type} Transaction
                        </p>
                        <p className="text-sm text-gray-400">
                          {formatCurrency(transaction.amount)} {transaction.token_symbol} on {transaction.blockchain}
                        </p>
                        <p className="text-xs text-gray-500 mt-1">
                          {new Date(transaction.timestamp * 1000).toLocaleString()}
                        </p>
                      </div>
                      <div className="flex-shrink-0">
                        <span className={`inline-flex items-center rounded-full px-2 py-0.5 text-xs font-medium ${
                          transaction.status === 'completed'
                            ? 'bg-green-900/30 border border-green-500/30 text-green-300'
                            : 'bg-red-900/30 border border-red-500/30 text-red-300'
                        }`}>
                          {transaction.status}
                        </span>
                      </div>
                    </div>
                    ))
                  ) : (
                    <div className="text-center py-8">
                      <p className="text-gray-400">No recent transactions found</p>
                    </div>
                  )}
                </div>
              </Card>
            </div>
          </div>
        </>
      )}
    </div>
  );
}
