use teloxide::types::{Inline<PERSON><PERSON>boardButton, InlineKeyboardMarkup};
use teloxide::prelude::Requester;
use teloxide::payloads::SendMessageSetters;
use std::sync::Arc;
use tokio::sync::RwLock;
use std::collections::HashMap;
use std::time::Instant;
use crate::model::{BotError, Blockchain, UserData};
use crate::service::BotService;
use crate::service::token_info_service::TokenInfoService;

struct ActiveScan {
    token_address: String,
    blockchain: Blockchain,
    message_id: i32,
    chat_id: i64,
    last_updated: Instant,
    is_active: bool,
}

lazy_static::lazy_static! {
    static ref ACTIVE_SCANS: Arc<RwLock<HashMap<String, ActiveScan>>> = Arc::new(RwLock::new(HashMap::new()));
    static ref USER_STATES: Arc<RwLock<HashMap<i64, String>>> = Arc::new(RwLock::new(HashMap::new()));
}

const POLLING_INTERVAL: u64 = 30;

pub async fn show_scan_prompt(
    bot_service: &BotService,
    user_data: &UserData,
    message_id: i32,
) -> Result<(), BotError> {
    let blockchain = user_data.user.current_blockchain.clone();
    show_scan_prompt_with_blockchain(bot_service, user_data, message_id, &blockchain).await
}

pub async fn show_scan_prompt_with_blockchain(
    bot_service: &BotService,
    user_data: &UserData,
    message_id: i32,
    blockchain: &Blockchain,
) -> Result<(), BotError> {
    let chat_id = user_data.chat_id();

    bot_service.delete_message(chat_id, message_id).await?;

    let force_reply = teloxide::types::ForceReply::new().selective(true);
    let prompt_text = format!(
        "📝 <b>{} Contract Scanner</b>\n\nEnter address:",
        blockchain.as_str().to_uppercase()
    );

    let sent_msg = bot_service.bot().send_message(
        teloxide::types::ChatId(chat_id),
        prompt_text
    )
    .parse_mode(teloxide::types::ParseMode::Html)
    .reply_markup(force_reply)
    .await?;

    let _bot_service_clone = bot_service.clone();
    let _blockchain_clone = blockchain.clone();
    let prompt_msg_id = sent_msg.id.0;

    let context = format!("scan_contract:{}:{}", blockchain.as_str(), prompt_msg_id);
    {
        let mut user_states = USER_STATES.write().await;
        user_states.insert(chat_id, context);
    }

    println!("Waiting for user to enter contract address...");
    println!("Prompt message ID {} stored in user state", prompt_msg_id);
    return Ok(());
}

pub async fn scan_contract(
    bot_service: &BotService,
    user_data: &UserData,
    message_id: i32,
    token_address: &str,
    blockchain: &Blockchain,
) -> Result<(), BotError> {
    let chat_id = user_data.chat_id();

    let token_service = TokenInfoService::new();
    let honeypot_service = bot_service.honeypot_service();

    let token_address_owned = token_address.to_string();
    let blockchain_owned = blockchain.clone();
    let token_info_future = tokio::spawn(async move {
        if let Some(cached_info) = token_service.get_token_info_from_cache(&token_address_owned, &blockchain_owned).await {
            println!("Using cached token info for {} on {}", token_address_owned, blockchain_owned.as_str());
            return Ok(cached_info);
        }

        token_service.get_comprehensive_token_info(&token_address_owned, &blockchain_owned).await
    });
    let honeypot_address = token_address.to_string();
    let honeypot_blockchain = blockchain.clone();
    let honeypot_service_clone = honeypot_service.clone();
    let honeypot_future = tokio::spawn(async move {
        honeypot_service_clone.check_honeypot(&honeypot_address, &honeypot_blockchain).await
    });
    let token_info_result = match tokio::time::timeout(std::time::Duration::from_secs(120), token_info_future).await {
        Ok(Ok(result)) => result,
        Ok(Err(e)) => {
            let error_text = format!(
                "❌ <b>Error Scanning Contract</b>\n\n\
                Contract: <code>{}</code>\n\n\
                Error: Failed to fetch token information: {}",
                token_address, e
            );

            let keyboard = create_scan_keyboard(blockchain, token_address, false);

            bot_service.edit_message_with_keyboard(chat_id, message_id, &error_text, keyboard).await?;

            return Ok(());
        },
        Err(_) => {
            let error_text = format!(
                "❌ <b>Request Timed Out</b>\n\n\
                Contract: <code>{}</code>\n\n\
                Error: Request timed out. Please try again.",
                token_address
            );

            let keyboard = create_scan_keyboard(blockchain, token_address, false);

            bot_service.edit_message_with_keyboard(chat_id, message_id, &error_text, keyboard).await?;

            return Ok(());
        }
    };
    let honeypot_result = match tokio::time::timeout(std::time::Duration::from_secs(30), honeypot_future).await {
        Ok(Ok(result)) => result,
        Ok(Err(e)) => {
            println!("Error checking if token is honeypot: {}", e);
            Err(format!("Failed to check honeypot status: {}", e))
        },
        Err(_) => {
            println!("Honeypot check timed out");
            Err("Honeypot check timed out".to_string())
        }
    };
    if let Ok(ref token_info) = token_info_result {
        if let Some(chain_id) = &token_info.chain_id {
            let detected_blockchain = match chain_id.as_str() {
                "ethereum" => Some(Blockchain::ETH),
                "bsc" => Some(Blockchain::BSC),
                "solana" => Some(Blockchain::SOL),
                "base" => Some(Blockchain::BASE),
                _ => None,
            };

            if let Some(detected_blockchain) = detected_blockchain {
                if detected_blockchain != *blockchain {
                    println!("Token is on a different blockchain: {:?}", detected_blockchain);

                    let switch_text = format!(
                        "⚠️ <b>Token Found on Different Blockchain</b>\n\n\
                        The token <code>{}</code> was found on {} blockchain, but you are currently on {} blockchain.\n\n\
                        Would you like to switch to {} blockchain to view this token?",
                        token_address,
                        detected_blockchain.as_str().to_uppercase(),
                        blockchain.as_str().to_uppercase(),
                        detected_blockchain.as_str().to_uppercase()
                    );
                    let keyboard = teloxide::types::InlineKeyboardMarkup::new(vec![
                        vec![
                            teloxide::types::InlineKeyboardButton::callback(
                                format!("✅ Switch to {}", detected_blockchain.as_str().to_uppercase()),
                                format!("view_{}", detected_blockchain.as_str()),
                            ),
                        ],
                        vec![
                            teloxide::types::InlineKeyboardButton::callback(
                                "❌ Cancel".to_string(),
                                "dismiss_message",
                            ),
                        ],
                    ]);

                    bot_service.edit_message_with_keyboard(chat_id, message_id, &switch_text, keyboard).await?;

                    let _tg_user = bot_service.bot().get_chat_member(
                        teloxide::types::ChatId(chat_id),
                        teloxide::types::UserId(chat_id as u64)
                    ).await?.user;
                    let mut user_data_mut = user_data.clone();
                    match detected_blockchain {
                        Blockchain::BSC => user_data_mut.config.bsc_current_contract_address = Some(token_address.to_string()),
                        Blockchain::SOL => user_data_mut.config.sol_current_contract_address = Some(token_address.to_string()),
                        Blockchain::ETH => user_data_mut.config.eth_current_contract_address = Some(token_address.to_string()),
                        Blockchain::BASE => user_data_mut.config.base_current_contract_address = Some(token_address.to_string()),
                    }

                    crate::service::DbService::save_user_config(&user_data_mut.config).await?;

                    bot_service.user_service().cache_user_data(user_data_mut).await;

                    return Ok(());
                }
            }
        }
    }

    let token_info = match token_info_result {
        Ok(info) => info,
        Err(e) => {
            let error_text = format!(
                "❌ <b>Error Scanning Contract</b>\n\n\
                Contract: <code>{}</code>\n\n\
                Error: {}",
                token_address, e
            );

            let keyboard = create_scan_keyboard(blockchain, token_address, false);

            bot_service.edit_message_with_keyboard(chat_id, message_id, &error_text, keyboard).await?;

            return Ok(());
        }
    };
    let token_service = TokenInfoService::new();
    let mut token_text = token_service.format_token_info(&token_info, token_address, blockchain);

    // Add honeypot information if available
    if let Ok(honeypot_data) = honeypot_result {
        // Add a separator
        token_text.push_str("\n\n<b>🔍 Honeypot Analysis:</b>\n");

        // Add honeypot status
        if honeypot_data.is_honeypot {
            token_text.push_str("⚠️ <b>DANGER: THIS TOKEN IS LIKELY A HONEYPOT!</b>\n");
        } else {
            token_text.push_str("✅ <b>No honeypot detected</b>\n");
        }

        // Add tax information
        token_text.push_str(&format!("💸 Buy Tax: {}%\n", honeypot_data.buy_tax));
        token_text.push_str(&format!("💸 Sell Tax: {}%\n", honeypot_data.sell_tax));

        // Add warnings if any
        if !honeypot_data.warnings.is_empty() {
            token_text.push_str("\n<b>⚠️ Warnings:</b>\n");
            for warning in honeypot_data.warnings.iter().take(3) { // Limit to 3 warnings to avoid message too long
                token_text.push_str(&format!("• {}\n", warning));
            }

            if honeypot_data.warnings.len() > 3 {
                token_text.push_str(&format!("• ... and {} more warnings\n", honeypot_data.warnings.len() - 3));
            }
        }

        // Add risk level
        token_text.push_str(&format!("\n<b>Risk Level:</b> {}\n", honeypot_data.risk));
    } else if let Err(_) = &honeypot_result {
        // Add a note that honeypot check failed
        token_text.push_str("\n\n<b>🔍 Honeypot Analysis:</b>\n");
        token_text.push_str("⚠️ <b>Could not check if this token is a honeypot.</b>\n");
        token_text.push_str("⚠️ <b>PROCEED WITH CAUTION!</b>\n");
    }

    println!("Displaying token info for {} on {}", token_address, blockchain.as_str());

    // Create the keyboard
    let keyboard = create_scan_keyboard(blockchain, token_address, true);

    // Edit the message to show the token information
    match bot_service.edit_message_with_keyboard(chat_id, message_id, &token_text, keyboard).await {
        Ok(_) => println!("Successfully displayed token info for {}", token_address),
        Err(e) => {
            println!("Error displaying token info: {}", e);
            return Err(BotError::GeneralError(format!("Failed to display token info: {}", e)));
        }
    };

    // No longer registering for background polling since refresh button is available
    println!("Token info displayed - refresh button available for updates");

    Ok(())
}

// Create the scan keyboard
pub fn create_scan_keyboard(blockchain: &Blockchain, token_address: &str, show_actions: bool) -> InlineKeyboardMarkup {
    let mut keyboard = Vec::new();

    if show_actions {
        // Add refresh, sell buttons
        keyboard.push(vec![
            InlineKeyboardButton::callback(
                "🔄 Refresh".to_string(),
                format!("refresh_token:{}:{}", blockchain.as_str(), token_address),
            ),
            InlineKeyboardButton::callback(
                "💸 Sell".to_string(),
                format!("sell_token:{}:{}", blockchain.as_str(), token_address),
            )
        ]);

        // Add chart, spend buttons
        keyboard.push(vec![
            InlineKeyboardButton::callback(
                "📈 Chart".to_string(),
                format!("chart_token:{}:{}", blockchain.as_str(), token_address),
            ),
            InlineKeyboardButton::callback(
                format!("💰 Buy with {}", blockchain.as_str().to_uppercase()),
                format!("spend_token:{}:{}", blockchain.as_str(), token_address),
            )
        ]);
    }

    // Add navigation buttons row
    keyboard.push(vec![
        InlineKeyboardButton::callback(
            "◀️ Back to Dashboard".to_string(),
            format!("view_{}", blockchain.as_str()),
        ),
        InlineKeyboardButton::callback(
            "❌ Dismiss".to_string(),
            "dismiss_message",
        )
    ]);

    InlineKeyboardMarkup::new(keyboard)
}

// Register an active scan
async fn register_active_scan(chat_id: i64, message_id: i32, token_address: &str, blockchain: &Blockchain) {
    let key = format!("{}:{}:{}", chat_id, message_id, token_address);

    let mut scans = ACTIVE_SCANS.write().await;
    scans.insert(key, ActiveScan {
        token_address: token_address.to_string(),
        blockchain: blockchain.clone(),
        message_id,
        chat_id,
        last_updated: Instant::now(),
        is_active: true,
    });
}

// Deregister an active scan
async fn deregister_active_scan(chat_id: i64, message_id: i32, token_address: &str) {
    let key = format!("{}:{}:{}", chat_id, message_id, token_address);

    let mut scans = ACTIVE_SCANS.write().await;
    if let Some(scan) = scans.get_mut(&key) {
        scan.is_active = false;
    }
}

// Get user states for message handler
pub fn get_user_states() -> &'static Arc<RwLock<HashMap<i64, String>>> {
    &USER_STATES
}

// Handle buy token amount from user input
pub async fn handle_buy_token_amount(
    bot_service: &BotService,
    chat_id: i64,
    prompt_msg_id: i32,
    user_msg_id: i32,
    amount_text: &str,
    token_address: &str,
    blockchain: &Blockchain,
) -> Result<(), BotError> {
    // Delete the user's message to keep the chat clean
    let _ = bot_service.delete_message(chat_id, user_msg_id).await;

    // Try to parse the amount
    let amount = match amount_text.trim().parse::<f64>() {
        Ok(val) if val > 0.0 => val,
        _ => {
            // Invalid amount, show error
            let error_text = format!(
                "❌ <b>Invalid Amount</b>\n\n\
                Please enter a valid positive number."
            );

            bot_service.edit_message(chat_id, prompt_msg_id, &error_text).await?;

            // Wait a moment and then delete the message
            tokio::time::sleep(tokio::time::Duration::from_secs(3)).await;
            let _ = bot_service.delete_message(chat_id, prompt_msg_id).await;

            return Ok(());
        }
    };

    // Get the user data
    let tg_user = bot_service.bot().get_chat_member(
        teloxide::types::ChatId(chat_id),
        teloxide::types::UserId(chat_id as u64)
    ).await?.user;
    let user_data = bot_service.user_service().get_or_create_user_data(chat_id, &tg_user).await?;

    // For Solana, use the SolanaTraderService
    if *blockchain == Blockchain::SOL {
        // Execute the buy order using the Solana trader service
        return crate::screens::sol_trading::execute_buy_order(
            bot_service,
            &user_data,
            chat_id,
            prompt_msg_id,
            token_address,
            amount
        ).await;
    }

    // For EVM chains, use the chain-specific implementation
    match blockchain {
        Blockchain::BSC => {
            // Execute buy token using the BSC implementation
            return crate::screens::bsc_screen::execute_buy_token(
                bot_service,
                &user_data,
                prompt_msg_id,
                token_address,
                amount
            ).await;
        },
        Blockchain::ETH => {
            // Execute buy token using the ETH implementation
            return crate::screens::eth_screen::execute_buy_token(
                bot_service,
                &user_data,
                prompt_msg_id,
                token_address,
                amount
            ).await;
        },
        Blockchain::BASE => {
            // Execute buy token using the BASE implementation
            return crate::screens::base_screen::execute_buy_token(
                bot_service,
                &user_data,
                prompt_msg_id,
                token_address,
                amount
            ).await;
        },
        _ => {
            // For other blockchains, use the existing implementation
        }
    }
    // Get token info from cache if available
    let token_service = TokenInfoService::new();
    let token_info_opt = token_service.get_token_info_from_cache(token_address, blockchain).await;

    // Build the confirmation message
    let mut confirm_text = format!(
        "✅ <b>Buy Order Submitted</b>\n\n"
    );

    // Add token info if available
    let token_name = if let Some(ref token_info) = token_info_opt {
        confirm_text.push_str(&format!("<b>{}</b> ({})\n", token_info.name, token_info.symbol));

        if token_info.price_usd > 0.0 {
            confirm_text.push_str(&format!("💰 Price: ${:.8}\n", token_info.price_usd));

            // Calculate estimated tokens
            let estimated_tokens = amount / token_info.price_usd;
            confirm_text.push_str(&format!("🔢 Est. Tokens: {:.4}\n", estimated_tokens));
        }

        token_info.name.clone()
    } else {
        // If we don't have token info in cache, try to fetch it again
        match token_service.get_comprehensive_token_info(token_address, blockchain).await {
            Ok(fresh_token_info) => {
                confirm_text.push_str(&format!("<b>{}</b> ({})\n", fresh_token_info.name, fresh_token_info.symbol));

                if fresh_token_info.price_usd > 0.0 {
                    confirm_text.push_str(&format!("💰 Price: ${:.8}\n", fresh_token_info.price_usd));

                    // Calculate estimated tokens
                    let estimated_tokens = amount / fresh_token_info.price_usd;
                    confirm_text.push_str(&format!("🔢 Est. Tokens: {:.4}\n", estimated_tokens));
                }

                fresh_token_info.name.clone()
            },
            Err(_) => {
                // If we still can't get the token info, use the address as the name
                let short_address = if token_address.len() > 10 {
                    format!("{}...{}", &token_address[0..6], &token_address[token_address.len()-4..])
                } else {
                    token_address.to_string()
                };
                format!("Token {}", short_address)
            }
        }
    };

    // Add amount and contract address
    confirm_text.push_str(&format!("\n💵 Amount: {} {} to buy <b>{}</b>\n", amount, blockchain.as_str().to_uppercase(), token_name));
    confirm_text.push_str(&format!("📍 Contract: <code>{}</code>\n\n", token_address));
    confirm_text.push_str("This feature is not fully implemented yet. Your order has been recorded.");

    // Create the keyboard with back buttons
    let keyboard = InlineKeyboardMarkup::new(vec![
        vec![
            InlineKeyboardButton::callback(
                "◀️ Back to Token".to_string(),
                format!("refresh_token:{}:{}", blockchain.as_str(), token_address),
            )
        ],
        vec![
            InlineKeyboardButton::callback(
                "🏠 Dashboard".to_string(),
                format!("view_{}", blockchain.as_str()),
            ),
            InlineKeyboardButton::callback(
                "❌ Dismiss".to_string(),
                "dismiss_message",
            )
        ]
    ]);

    // Edit the message to show the confirmation
    bot_service.edit_message_with_keyboard(chat_id, prompt_msg_id, &confirm_text, keyboard).await?;

    Ok(())
}

// Handle sell token amount from user input (percentage)
pub async fn handle_sell_token_amount(
    bot_service: &BotService,
    chat_id: i64,
    prompt_msg_id: i32,
    user_msg_id: i32,
    percent_text: &str,
    token_address: &str,
    blockchain: &Blockchain,
) -> Result<(), BotError> {
    // Delete the user's message to keep the chat clean
    let _ = bot_service.delete_message(chat_id, user_msg_id).await;

    // Try to parse the percentage
    let percent = match percent_text.trim().parse::<f64>() {
        Ok(val) if val > 0.0 && val <= 100.0 => val,
        _ => {
            // Invalid percentage, show error
            let error_text = format!(
                "❌ <b>Invalid Percentage</b>\n\n\
                Please enter a valid percentage between 1 and 100."
            );

            bot_service.edit_message(chat_id, prompt_msg_id, &error_text).await?;

            // Wait a moment and then delete the message
            tokio::time::sleep(tokio::time::Duration::from_secs(3)).await;
            let _ = bot_service.delete_message(chat_id, prompt_msg_id).await;

            return Ok(());
        }
    };

    // Get the user data
    let tg_user = bot_service.bot().get_chat_member(
        teloxide::types::ChatId(chat_id),
        teloxide::types::UserId(chat_id as u64)
    ).await?.user;
    let user_data = bot_service.user_service().get_or_create_user_data(chat_id, &tg_user).await?;

    // For Solana, use the SolanaTraderService
    if *blockchain == Blockchain::SOL {
        // Execute the sell order using the Solana trader service
        return crate::screens::sol_trading::execute_sell_order(
            bot_service,
            &user_data,
            chat_id,
            prompt_msg_id,
            token_address,
            percent
        ).await;
    }

    // For EVM chains, use the chain-specific implementation
    match blockchain {
        Blockchain::BSC => {
            // Convert percentage to decimal (0.0-1.0)
            let percentage_decimal = percent / 100.0;

            // Execute sell token using the BSC implementation
            return crate::screens::bsc_screen::execute_sell_token(
                bot_service,
                &user_data,
                prompt_msg_id,
                token_address,
                percentage_decimal
            ).await;
        },
        Blockchain::ETH => {
            // Convert percentage to decimal (0.0-1.0)
            let percentage_decimal = percent / 100.0;

            // Execute sell token using the ETH implementation
            return crate::screens::eth_screen::execute_sell_token(
                bot_service,
                &user_data,
                prompt_msg_id,
                token_address,
                percentage_decimal
            ).await;
        },
        Blockchain::BASE => {
            // Convert percentage to decimal (0.0-1.0)
            let percentage_decimal = percent / 100.0;

            // Execute sell token using the BASE implementation
            return crate::screens::base_screen::execute_sell_token(
                bot_service,
                &user_data,
                prompt_msg_id,
                token_address,
                percentage_decimal
            ).await;
        },
        _ => {
            // For other blockchains, use the existing implementation
        }
    }

    // Get token info from cache if available
    let token_service = TokenInfoService::new();
    let token_info_opt = token_service.get_token_info_from_cache(token_address, blockchain).await;

    // Build the confirmation message
    let mut confirm_text = format!(
        "✅ <b>Sell Order Submitted</b>\n\n"
    );

    // Add token info if available
    let token_name = if let Some(ref token_info) = token_info_opt {
        confirm_text.push_str(&format!("<b>{}</b> ({})\n", token_info.name, token_info.symbol));

        if token_info.price_usd > 0.0 {
            confirm_text.push_str(&format!("💰 Price: ${:.8}\n", token_info.price_usd));

            // We don't know the actual token amount without balance info
            // Just show the percentage for now
        }

        token_info.name.clone()
    } else {
        // If we don't have token info in cache, try to fetch it again
        match token_service.get_comprehensive_token_info(token_address, blockchain).await {
            Ok(fresh_token_info) => {
                confirm_text.push_str(&format!("<b>{}</b> ({})\n", fresh_token_info.name, fresh_token_info.symbol));

                if fresh_token_info.price_usd > 0.0 {
                    confirm_text.push_str(&format!("💰 Price: ${:.8}\n", fresh_token_info.price_usd));
                }

                fresh_token_info.name.clone()
            },
            Err(_) => {
                // If we still can't get the token info, use the address as the name
                let short_address = if token_address.len() > 10 {
                    format!("{}...{}", &token_address[0..6], &token_address[token_address.len()-4..])
                } else {
                    token_address.to_string()
                };
                format!("Token {}", short_address)
            }
        }
    };

    // Add percentage and contract address
    if percent == 100.0 {
        confirm_text.push_str(&format!("\n🔢 Amount: All <b>{}</b> tokens (100%)\n", token_name));
    } else {
        confirm_text.push_str(&format!("\n🔢 Amount: {:.1}% of <b>{}</b> tokens\n", percent, token_name));
    }

    confirm_text.push_str(&format!("📍 Contract: <code>{}</code>\n\n", token_address));
    confirm_text.push_str("This feature is not fully implemented yet. Your order has been recorded.");

    // Create the keyboard with back buttons
    let keyboard = InlineKeyboardMarkup::new(vec![
        vec![
            InlineKeyboardButton::callback(
                "◀️ Back to Token".to_string(),
                format!("refresh_token:{}:{}", blockchain.as_str(), token_address),
            )
        ],
        vec![
            InlineKeyboardButton::callback(
                "🏠 Dashboard".to_string(),
                format!("view_{}", blockchain.as_str()),
            ),
            InlineKeyboardButton::callback(
                "❌ Dismiss".to_string(),
                "dismiss_message",
            )
        ]
    ]);

    // Edit the message to show the confirmation
    bot_service.edit_message_with_keyboard(chat_id, prompt_msg_id, &confirm_text, keyboard).await?;

    Ok(())
}

// Handle the refresh token callback
pub async fn handle_refresh_token(
    bot_service: &BotService,
    callback_id: &str,
    chat_id: i64,
    message_id: i32,
    token_address: &str,
    blockchain: &Blockchain,
) -> Result<(), BotError> {
    // Create token info service
    let token_service = TokenInfoService::new();

    // Answer the callback query immediately to show loading
    bot_service.answer_callback_query(callback_id, Some("Refreshing token information..."), false).await?;

    // Use tokio::spawn directly instead of creating a new runtime

    // Clone the references to avoid lifetime issues
    let token_address_owned = token_address.to_string();
    let blockchain_owned = blockchain.clone();

    // Spawn a task to fetch token info with higher priority
    let token_info_future = tokio::spawn(async move {
        // First check if we have it in cache
        if let Some(cached_info) = token_service.get_token_info_from_cache(&token_address_owned, &blockchain_owned).await {
            println!("Using cached token info for {} on {}", token_address_owned, blockchain_owned.as_str());
            return Ok(cached_info);
        }

        // If not in cache, fetch comprehensive info
        token_service.get_comprehensive_token_info(&token_address_owned, &blockchain_owned).await
    });

    // Wait for the token info with a timeout (2 minutes)
    let token_info = match tokio::time::timeout(std::time::Duration::from_secs(120), token_info_future).await {
        Ok(Ok(Ok(info))) => info,
        Ok(Ok(Err(_))) | Ok(Err(_)) | Err(_) => {
            // Try to get token info from cache for display
            let token_service = TokenInfoService::new();
            let token_info_opt = token_service.get_token_info_from_cache(token_address, blockchain).await;

            // Get token name for display
            let token_name = if let Some(ref token_info) = token_info_opt {
                format!("{} ({})", token_info.name, token_info.symbol)
            } else {
                // If we still can't get the token info, use a shortened version of the address
                let short_address = if token_address.len() > 10 {
                    format!("{}...{}", &token_address[0..6], &token_address[token_address.len()-4..])
                } else {
                    token_address.to_string()
                };
                format!("Token {}", short_address)
            };

            // Show error message
            let error_text = format!(
                "❌ <b>Error Refreshing Token</b>\n\n\
                Token: <b>{}</b>\n\
                Contract: <code>{}</code>\n\n\
                Error: Failed to refresh token information. Please try again.",
                token_name,
                token_address
            );

            // Create the keyboard with back button
            let keyboard = create_scan_keyboard(blockchain, token_address, false);

            bot_service.edit_message_with_keyboard(chat_id, message_id, &error_text, keyboard).await?;

            return Ok(());
        }
    };

    // Format token information
    let token_service = TokenInfoService::new();
    let token_text = token_service.format_token_info(&token_info, token_address, blockchain);

    println!("Refreshing token info for {} on {}", token_address, blockchain.as_str());

    // Create the keyboard
    let keyboard = create_scan_keyboard(blockchain, token_address, true);

    // Edit the message to show the token information
    match bot_service.edit_message_with_keyboard(chat_id, message_id, &token_text, keyboard).await {
        Ok(_) => println!("Successfully refreshed token info for {}", token_address),
        Err(e) => {
            println!("Error refreshing token info: {}", e);
            return Err(BotError::GeneralError(format!("Failed to refresh token info: {}", e)));
        }
    };

    // No longer updating active scans since background polling is removed

    Ok(())
}

/// 🚀 NEW: Handle refresh token with new message (don't edit existing receipts)
pub async fn handle_refresh_token_new_message(
    bot_service: &BotService,
    callback_id: &str,
    chat_id: i64,
    token_address: &str,
    blockchain: &Blockchain,
) -> Result<(), BotError> {
    // Create token info service
    let token_service = TokenInfoService::new();

    // Answer the callback query immediately to show loading
    bot_service.answer_callback_query(callback_id, Some("Loading token information..."), false).await?;

    // Send a new message instead of editing existing one
    let loading_text = format!(
        "⏳ <b>Loading Token Information...</b>\n\n\
        Contract: <code>{}</code>\n\
        Blockchain: <b>{}</b>\n\n\
        Please wait...",
        token_address,
        blockchain.to_string().to_uppercase()
    );

    let sent_msg = bot_service.send_message(chat_id, &loading_text).await?;
    let new_message_id = sent_msg.id.0;

    // Clone the references to avoid lifetime issues
    let token_address_owned = token_address.to_string();
    let blockchain_owned = blockchain.clone();
    let bot_service_clone = bot_service.clone();

    // Spawn background task to fetch token info
    tokio::spawn(async move {
        // Fetch token information
        let token_info = match token_service.get_token_info(&token_address_owned, &blockchain_owned).await {
            Ok(info) => info,
            Err(e) => {
                println!("Error fetching token info: {}", e);
                let error_text = format!(
                    "❌ <b>Error Loading Token</b>\n\n\
                    Contract: <code>{}</code>\n\
                    Error: {}\n\n\
                    Please check the contract address and try again.",
                    token_address_owned,
                    e
                );

                let _ = bot_service_clone.edit_message(chat_id, new_message_id, &error_text).await;
                return;
            }
        };

        // Format token information
        let token_text = token_service.format_token_info(&token_info, &token_address_owned, &blockchain_owned);

        println!("Displaying new token info for {} on {}", token_address_owned, blockchain_owned.as_str());

        // Create the keyboard
        let keyboard = create_scan_keyboard(&blockchain_owned, &token_address_owned, true);

        // Edit the message to show the token information
        match bot_service_clone.edit_message_with_keyboard(chat_id, new_message_id, &token_text, keyboard).await {
            Ok(_) => println!("Successfully displayed new token info for {}", token_address_owned),
            Err(e) => {
                println!("Error displaying token info: {}", e);
            }
        };
    });

    Ok(())
}

// Handle the sell token callback - use the EVM swap screen for EVM chains
pub async fn handle_sell_token(
    bot_service: &BotService,
    callback_id: &str,
    chat_id: i64,
    message_id: i32,
    token_address: &str,
    blockchain: &Blockchain,
) -> Result<(), BotError> {
    // Answer the callback query first
    bot_service.answer_callback_query(callback_id, Some("Preparing sell screen..."), false).await?;

    // For Solana, use the existing implementation
    if *blockchain == Blockchain::SOL {
        // Create a loading message
        let loading_text = format!(
            "🔄 <b>Preparing to sell token on {}</b>\n\n\
            Token: <code>{}</code>\n\n\
            Loading token information...",
            blockchain.as_str().to_uppercase(),
            token_address
        );

        // Edit the message to show loading
        let edited_msg = bot_service.edit_message(chat_id, message_id, &loading_text).await?;

        // Clone what we need for the background task
        let bot_service_clone = bot_service.clone();
        let token_address_clone = token_address.to_string();
        let blockchain_clone = blockchain.clone();
        let chat_id_clone = chat_id;
        let message_id_clone = edited_msg.id.0;

        // Spawn a background task to fetch token info and update the message
        tokio::spawn(async move {
            // Get token info from cache if available
            let token_service = TokenInfoService::new();
            let token_info_opt = token_service.get_token_info_from_cache(&token_address_clone, &blockchain_clone).await;

            // Build the sell text with token info if available
            let mut sell_text = format!(
                "💸 <b>Sell {} Token</b>\n\n",
                blockchain_clone.as_str().to_uppercase()
            );

            // Add token info if available
            let token_name = if let Some(ref token_info) = token_info_opt {
                sell_text.push_str(&format!("<b>{}</b> ({})\n", token_info.name, token_info.symbol));

                if token_info.price_usd > 0.0 {
                    sell_text.push_str(&format!("💰 Price: ${:.8}\n", token_info.price_usd));
                }

                token_info.name.clone()
            } else {
                // If we don't have token info in cache, use a shortened version of the address
                let short_address = if token_address_clone.len() > 10 {
                    format!("{}...{}", &token_address_clone[0..6], &token_address_clone[token_address_clone.len()-4..])
                } else {
                    token_address_clone.clone()
                };
                format!("Token {}", short_address)
            };

            // Add contract address and instructions
            sell_text.push_str(&format!("\nContract: <code>{}</code>\n\n", token_address_clone));
            sell_text.push_str(&format!("Enter the percentage of <b>{}</b> tokens you want to sell (1-100):", token_name));

            // Edit the message with the prompt
            if let Ok(edited_msg) = bot_service_clone.edit_message(chat_id_clone, message_id_clone, &sell_text).await {
                // Store the context for handling the reply
                let context = format!("sell_token_custom:{}:{}:{}", blockchain_clone.as_str(), token_address_clone, edited_msg.id.0);
                let mut user_states = USER_STATES.write().await;
                user_states.insert(chat_id_clone, context);
            }
        });

        return Ok(());
    }

    // For BSC, use the same force reply pattern as Solana
    if *blockchain == Blockchain::BSC {
        // Create a loading message
        let loading_text = format!(
            "🔄 <b>Preparing to sell token on BSC</b>\n\n\
            Token: <code>{}</code>\n\n\
            Loading token information...",
            token_address
        );

        // Edit the message to show loading
        let edited_msg = bot_service.edit_message(chat_id, message_id, &loading_text).await?;

        // Clone what we need for the background task
        let bot_service_clone = bot_service.clone();
        let token_address_clone = token_address.to_string();
        let chat_id_clone = chat_id;
        let message_id_clone = edited_msg.id.0;

        // Spawn a background task to fetch token info and update the message
        tokio::spawn(async move {
            // Get token balance and info
            let blockchain_service = bot_service_clone.blockchain_service();

            // Get user data
            let tg_user = bot_service_clone.bot().get_chat_member(
                teloxide::types::ChatId(chat_id_clone),
                teloxide::types::UserId(chat_id_clone as u64)
            ).await.unwrap().user;
            let user_data = bot_service_clone.user_service().get_or_create_user_data(chat_id_clone, &tg_user).await.unwrap();
            let wallet = user_data.get_wallet(&Blockchain::BSC);

            // Get token balance
            let balance_result = blockchain_service
                .get_token_balance(&wallet.address, &token_address_clone, &Blockchain::BSC)
                .await;

            match balance_result {
                Ok((balance, _decimals)) => {
                    // If balance is 0, show error message
                    if balance <= 0.0 {
                        // Use a scoped block for error message
                        {
                            // Use a pre-allocated buffer for the error message
                            let mut error_text = String::with_capacity(256);
                            error_text.push_str("❌ <b>No tokens to sell</b>\n\n");
                            error_text.push_str(&format!("Token: <code>{}</code>\n\n", token_address_clone));
                            error_text.push_str("You don't have any tokens to sell.");

                            let keyboard = InlineKeyboardMarkup::new(vec![vec![
                                InlineKeyboardButton::callback("🔙 Back", "back"),
                            ]]);

                            let _ = bot_service_clone.edit_message_with_keyboard(
                                chat_id_clone,
                                message_id_clone,
                                &error_text,
                                keyboard,
                            ).await;
                        }
                        return;
                    }

                    // Get token info if available
                    let token_service = TokenInfoService::new();
                    let token_info_result = token_service.get_comprehensive_token_info(&token_address_clone, &Blockchain::BSC).await;

                    // Get token name with minimal allocations
                    let token_name = match token_info_result {
                        Ok(info) => {
                            let mut name = String::with_capacity(info.name.len() + info.symbol.len() + 3);
                            name.push_str(&info.name);
                            name.push_str(" (");
                            name.push_str(&info.symbol);
                            name.push(')');
                            name
                        },
                        Err(_) => {
                            // If we can't get token info, use a shortened version of the address
                            if token_address_clone.len() > 10 {
                                let mut short = String::with_capacity(14);
                                short.push_str(&token_address_clone[0..6]);
                                short.push_str("...");
                                short.push_str(&token_address_clone[token_address_clone.len()-4..]);
                                format!("Token {}", short)
                            } else {
                                format!("Token {}", token_address_clone)
                            }
                        }
                    };

                    // Build prompt message with minimal allocations
                    let mut prompt_text = String::with_capacity(512);
                    prompt_text.push_str("💰 <b>Sell Token on BSC</b>\n\n");

                    prompt_text.push_str(&format!("Token: <b>{}</b>\n", token_name));
                    prompt_text.push_str(&format!("Address: <code>{}</code>\n\n", token_address_clone));
                    prompt_text.push_str(&format!("Your Balance: {} tokens\n\n", balance));
                    prompt_text.push_str("Enter the percentage of tokens you want to sell (1-100):");

                    // Edit the message with the prompt
                    if let Ok(edited_msg) = bot_service_clone.edit_message(chat_id_clone, message_id_clone, &prompt_text).await {
                        // Store the context for handling the reply
                        let context = format!("sell_token_custom:bsc:{}:{}", token_address_clone, edited_msg.id.0);
                        let mut user_states = USER_STATES.write().await;
                        user_states.insert(chat_id_clone, context);
                    }
                },
                Err(e) => {
                    // Use a scoped block for error message
                    {
                        // Use a pre-allocated buffer for the error message
                        let mut error_text = String::with_capacity(256);
                        error_text.push_str("❌ <b>Error getting token balance</b>\n\n");
                        error_text.push_str(&format!("Token: <code>{}</code>\n\n", token_address_clone));
                        error_text.push_str(&format!("Error: {}", e));

                        let keyboard = InlineKeyboardMarkup::new(vec![vec![
                            InlineKeyboardButton::callback("🔙 Back", "back"),
                        ]]);

                        let _ = bot_service_clone.edit_message_with_keyboard(
                            chat_id_clone,
                            message_id_clone,
                            &error_text,
                            keyboard,
                        ).await;
                    }
                }
            }
        });
    }
    // For ETH and BASE, use the same force reply pattern as BSC and Solana
    else if *blockchain == Blockchain::ETH || *blockchain == Blockchain::BASE {
        // Create a loading message
        let loading_text = format!(
            "🔄 <b>Preparing to sell token on {}</b>\n\n\
            Token: <code>{}</code>\n\n\
            Loading token information...",
            blockchain.as_str().to_uppercase(),
            token_address
        );

        // Edit the message to show loading
        let edited_msg = bot_service.edit_message(chat_id, message_id, &loading_text).await?;

        // Clone what we need for the background task
        let bot_service_clone = bot_service.clone();
        let token_address_clone = token_address.to_string();
        let blockchain_clone = blockchain.clone();
        let chat_id_clone = chat_id;
        let message_id_clone = edited_msg.id.0;

        // Spawn a background task to fetch token info and update the message
        tokio::spawn(async move {
            // Get token balance and info
            let blockchain_service = bot_service_clone.blockchain_service();

            // Get user data
            let tg_user = bot_service_clone.bot().get_chat_member(
                teloxide::types::ChatId(chat_id_clone),
                teloxide::types::UserId(chat_id_clone as u64)
            ).await.unwrap().user;
            let user_data = bot_service_clone.user_service().get_or_create_user_data(chat_id_clone, &tg_user).await.unwrap();
            let wallet = user_data.get_wallet(&blockchain_clone);

            // Get token balance
            let balance_result = blockchain_service
                .get_token_balance(&wallet.address, &token_address_clone, &blockchain_clone)
                .await;

            match balance_result {
                Ok((balance, _decimals)) => {
                    // If balance is 0, show error message
                    if balance <= 0.0 {
                        // Use a scoped block for error message
                        {
                            // Use a pre-allocated buffer for the error message
                            let mut error_text = String::with_capacity(256);
                            error_text.push_str("❌ <b>No tokens to sell</b>\n\n");
                            error_text.push_str(&format!("Token: <code>{}</code>\n\n", token_address_clone));
                            error_text.push_str("You don't have any tokens to sell.");

                            let keyboard = InlineKeyboardMarkup::new(vec![vec![
                                InlineKeyboardButton::callback("🔙 Back", "back"),
                            ]]);

                            let _ = bot_service_clone.edit_message_with_keyboard(
                                chat_id_clone,
                                message_id_clone,
                                &error_text,
                                keyboard,
                            ).await;
                        }
                        return;
                    }

                    // Get token info if available
                    let token_service = TokenInfoService::new();
                    let token_info_result = token_service.get_comprehensive_token_info(&token_address_clone, &blockchain_clone).await;

                    // Get token name with minimal allocations
                    let token_name = match token_info_result {
                        Ok(info) => {
                            let mut name = String::with_capacity(info.name.len() + info.symbol.len() + 3);
                            name.push_str(&info.name);
                            name.push_str(" (");
                            name.push_str(&info.symbol);
                            name.push(')');
                            name
                        },
                        Err(_) => {
                            // If we can't get token info, use a shortened version of the address
                            if token_address_clone.len() > 10 {
                                let mut short = String::with_capacity(14);
                                short.push_str(&token_address_clone[0..6]);
                                short.push_str("...");
                                short.push_str(&token_address_clone[token_address_clone.len()-4..]);
                                format!("Token {}", short)
                            } else {
                                format!("Token {}", token_address_clone)
                            }
                        }
                    };

                    // Get native token name
                    let _native_token = match blockchain_clone {
                        Blockchain::ETH => "ETH",
                        Blockchain::BASE => "ETH", // BASE uses ETH as its native token
                        _ => "ETH", // Default to ETH
                    };

                    // Build prompt message with minimal allocations
                    let mut prompt_text = String::with_capacity(512);
                    prompt_text.push_str(&format!("� <b>Sell Token on {}</b>\n\n", blockchain_clone.as_str().to_uppercase()));

                    prompt_text.push_str(&format!("Token: <b>{}</b>\n", token_name));
                    prompt_text.push_str(&format!("Address: <code>{}</code>\n\n", token_address_clone));
                    prompt_text.push_str(&format!("Your Balance: {} tokens\n\n", balance));
                    prompt_text.push_str("Enter the percentage of tokens you want to sell (1-100):");

                    // Edit the message with the prompt
                    if let Ok(edited_msg) = bot_service_clone.edit_message(chat_id_clone, message_id_clone, &prompt_text).await {
                        // Store the context for handling the reply
                        let context = format!("sell_token_custom:{}:{}:{}", blockchain_clone.as_str(), token_address_clone, edited_msg.id.0);
                        let mut user_states = USER_STATES.write().await;
                        user_states.insert(chat_id_clone, context);
                    }
                },
                Err(e) => {
                    // Use a scoped block for error message
                    {
                        // Use a pre-allocated buffer for the error message
                        let mut error_text = String::with_capacity(256);
                        error_text.push_str("❌ <b>Error getting token balance</b>\n\n");
                        error_text.push_str(&format!("Token: <code>{}</code>\n\n", token_address_clone));
                        error_text.push_str(&format!("Error: {}", e));

                        let keyboard = InlineKeyboardMarkup::new(vec![vec![
                            InlineKeyboardButton::callback("🔙 Back", "back"),
                        ]]);

                        let _ = bot_service_clone.edit_message_with_keyboard(
                            chat_id_clone,
                            message_id_clone,
                            &error_text,
                            keyboard,
                        ).await;
                    }
                }
            }
        });
    }

    Ok(())
}

// Environment variable names for chart URLs ()
const ENV_CHART_URL_ETH: &str = "API_DEXSCREENER_CHART_ETH";
const ENV_CHART_URL_BSC: &str = "API_DEXSCREENER_CHART_BSC";
const ENV_CHART_URL_SOL: &str = "API_DEXSCREENER_CHART_SOL";
const ENV_CHART_URL_BASE: &str = "API_DEXSCREENER_CHART_BASE";

// Default chart URLs if environment variables are not set
const DEFAULT_CHART_URL_ETH: &str = "https://dexscreener.com/ethereum";
const DEFAULT_CHART_URL_BSC: &str = "https://dexscreener.com/bsc";
const DEFAULT_CHART_URL_SOL: &str = "https://dexscreener.com/solana";
const DEFAULT_CHART_URL_BASE: &str = "https://dexscreener.com/base";

// Handle the chart token callback
pub async fn handle_chart_token(
    bot_service: &BotService,
    callback_id: &str,
    token_address: &str,
    blockchain: &Blockchain,
) -> Result<(), BotError> {
    // Get the chart URL from environment variables or use defaults
    let base_url = match blockchain {
        Blockchain::ETH => std::env::var(ENV_CHART_URL_ETH).unwrap_or_else(|_| DEFAULT_CHART_URL_ETH.to_string()),
        Blockchain::BSC => std::env::var(ENV_CHART_URL_BSC).unwrap_or_else(|_| DEFAULT_CHART_URL_BSC.to_string()),
        Blockchain::SOL => std::env::var(ENV_CHART_URL_SOL).unwrap_or_else(|_| DEFAULT_CHART_URL_SOL.to_string()),
        Blockchain::BASE => std::env::var(ENV_CHART_URL_BASE).unwrap_or_else(|_| DEFAULT_CHART_URL_BASE.to_string()),
    };

    // Construct the full URL
    let chart_url = format!("{}/{}", base_url, token_address);

    // Answer the callback query with the chart URL
    bot_service.answer_callback_query(callback_id, Some(&format!("Opening chart: {}", chart_url)), true).await?;

    Ok(())
}

// Handle the spend token callback - use the EVM swap screen for EVM chains
pub async fn handle_spend_token(
    bot_service: &BotService,
    callback_id: &str,
    chat_id: i64,
    message_id: i32,
    token_address: &str,
    blockchain: &Blockchain,
) -> Result<(), BotError> {
    // Answer the callback query first
    bot_service.answer_callback_query(callback_id, Some("Preparing buy screen..."), false).await?;

    // For Solana, use the existing implementation
    if *blockchain == Blockchain::SOL {
        // Create a loading message
        let loading_text = format!(
            "🔄 <b>Preparing to buy token on {}</b>\n\n\
            Token: <code>{}</code>\n\n\
            Loading token information...",
            blockchain.as_str().to_uppercase(),
            token_address
        );

        // Edit the message to show loading
        let edited_msg = bot_service.edit_message(chat_id, message_id, &loading_text).await?;

        // Clone what we need for the background task
        let bot_service_clone = bot_service.clone();
        let token_address_clone = token_address.to_string();
        let blockchain_clone = blockchain.clone();
        let chat_id_clone = chat_id;
        let message_id_clone = edited_msg.id.0;

        // Spawn a background task to fetch token info and update the message
        tokio::spawn(async move {
            // Use the existing implementation for Solana
            let token_service = TokenInfoService::new();
            let token_info_opt = token_service.get_token_info_from_cache(&token_address_clone, &blockchain_clone).await;

            // Build the prompt message with token info if available
            let mut prompt_text = format!(
                "💰 <b>Buy {} Token</b>\n\n",
                blockchain_clone.as_str().to_uppercase()
            );

            // Add token info if available
            let token_name = if let Some(ref token_info) = token_info_opt {
                prompt_text.push_str(&format!("<b>{}</b> ({})\n", token_info.name, token_info.symbol));

                if token_info.price_usd > 0.0 {
                    prompt_text.push_str(&format!("💰 Price: ${:.8}\n", token_info.price_usd));
                }

                token_info.name.clone()
            } else {
                // If we don't have token info in cache, use a shortened version of the address
                let short_address = if token_address_clone.len() > 10 {
                    format!("{}...{}", &token_address_clone[0..6], &token_address_clone[token_address_clone.len()-4..])
                } else {
                    token_address_clone.clone()
                };
                format!("Token {}", short_address)
            };

            // Add contract address and instructions
            prompt_text.push_str(&format!("\nContract: <code>{}</code>\n\n", token_address_clone));
            prompt_text.push_str(&format!("How much {} do you want to spend to buy <b>{}</b>?", blockchain_clone.as_str().to_uppercase(), token_name));

            // Edit the message with the prompt
            if let Ok(edited_msg) = bot_service_clone.edit_message(chat_id_clone, message_id_clone, &prompt_text).await {
                // Store the context for handling the reply
                let context = format!("buy_token:{}:{}:{}", blockchain_clone.as_str(), token_address_clone, edited_msg.id.0);
                println!("🔍 DEBUG: Setting buy_token state for chat_id {}: {}", chat_id_clone, context);
                let mut user_states = USER_STATES.write().await;
                user_states.insert(chat_id_clone, context);
                println!("🔍 DEBUG: State set successfully. Current states: {:?}", *user_states);
            }
        });

        return Ok(());
    }

    // For BSC, use the same force reply pattern as Solana
    if *blockchain == Blockchain::BSC {
        // Create a loading message
        let loading_text = format!(
            "🔄 <b>Preparing to buy token on BSC</b>\n\n\
            Token: <code>{}</code>\n\n\
            Loading token information...",
            token_address
        );

        // Edit the message to show loading
        let edited_msg = bot_service.edit_message(chat_id, message_id, &loading_text).await?;

        // Clone what we need for the background task
        let bot_service_clone = bot_service.clone();
        let token_address_clone = token_address.to_string();
        let chat_id_clone = chat_id;
        let message_id_clone = edited_msg.id.0;

        // Spawn a background task to update the message
        tokio::spawn(async move {
            // For BSC, we don't need to call Dexscreener or CoinGecko
            // Just use a shortened version of the address for display
            let token_name = if token_address_clone.len() > 10 {
                let mut short = String::with_capacity(14);
                short.push_str(&token_address_clone[0..6]);
                short.push_str("...");
                short.push_str(&token_address_clone[token_address_clone.len()-4..]);
                format!("Token {}", short)
            } else {
                format!("Token {}", token_address_clone)
            };

            // Build prompt message with minimal allocations
            let mut prompt_text = String::with_capacity(512);
            prompt_text.push_str("🛒 <b>Buy Token on BSC</b>\n\n");

            prompt_text.push_str(&format!("Token: <b>{}</b>\n", token_name));
            prompt_text.push_str(&format!("Address: <code>{}</code>\n\n", token_address_clone));
            prompt_text.push_str("How much BNB do you want to spend?");

            // Edit the message with the prompt
            if let Ok(edited_msg) = bot_service_clone.edit_message(chat_id_clone, message_id_clone, &prompt_text).await {
                // Store the context for handling the reply
                let context = format!("buy_token:bsc:{}:{}", token_address_clone, edited_msg.id.0);
                let mut user_states = USER_STATES.write().await;
                user_states.insert(chat_id_clone, context);
            }
        });
    }
    // For ETH and BASE, use the same force reply pattern as BSC and Solana
    else if *blockchain == Blockchain::ETH || *blockchain == Blockchain::BASE {
        // Create a loading message
        let loading_text = format!(
            "🔄 <b>Preparing to buy token on {}</b>\n\n\
            Token: <code>{}</code>\n\n\
            Loading token information...",
            blockchain.as_str().to_uppercase(),
            token_address
        );

        // Edit the message to show loading
        let edited_msg = bot_service.edit_message(chat_id, message_id, &loading_text).await?;

        // Clone what we need for the background task
        let bot_service_clone = bot_service.clone();
        let token_address_clone = token_address.to_string();
        let blockchain_clone = blockchain.clone();
        let chat_id_clone = chat_id;
        let message_id_clone = edited_msg.id.0;

        // Spawn a background task to update the message
        tokio::spawn(async move {
            // For ETH and BASE, we don't need to call Dexscreener or CoinGecko
            // Just use a shortened version of the address for display
            let token_name = if token_address_clone.len() > 10 {
                let mut short = String::with_capacity(14);
                short.push_str(&token_address_clone[0..6]);
                short.push_str("...");
                short.push_str(&token_address_clone[token_address_clone.len()-4..]);
                format!("Token {}", short)
            } else {
                format!("Token {}", token_address_clone)
            };

            // Build prompt message with minimal allocations
            let mut prompt_text = String::with_capacity(512);
            prompt_text.push_str(&format!("🛒 <b>Buy Token on {}</b>\n\n", blockchain_clone.as_str().to_uppercase()));

            prompt_text.push_str(&format!("Token: <b>{}</b>\n", token_name));
            prompt_text.push_str(&format!("Address: <code>{}</code>\n\n", token_address_clone));
            prompt_text.push_str(&format!("How much {} do you want to spend?", blockchain_clone.as_str().to_uppercase()));

            // Edit the message with the prompt
            if let Ok(edited_msg) = bot_service_clone.edit_message(chat_id_clone, message_id_clone, &prompt_text).await {
                // Store the context for handling the reply
                let context = format!("buy_token:{}:{}:{}", blockchain_clone.as_str(), token_address_clone, edited_msg.id.0);
                let mut user_states = USER_STATES.write().await;
                user_states.insert(chat_id_clone, context);
            }
        });
    }

    Ok(())
}
