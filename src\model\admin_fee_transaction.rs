use serde::{Deserialize, Serialize};
use mongodb::bson::oid::ObjectId;
use std::time::{SystemTime, UNIX_EPOCH};
use crate::model::blockchain::Blockchain;

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub enum FeeTransactionType {
    #[serde(rename = "buy_fee")]
    BuyFee,
    #[serde(rename = "sell_fee")]
    SellFee,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub enum FeeTransactionStatus {
    #[serde(rename = "pending")]
    Pending,
    #[serde(rename = "completed")]
    Completed,
    #[serde(rename = "failed")]
    Failed,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AdminFeeTransaction {
    #[serde(rename = "_id", skip_serializing_if = "Option::is_none")]
    pub id: Option<ObjectId>,
    pub user_id: ObjectId,
    pub trade_id: Option<ObjectId>,
    pub blockchain: Blockchain,
    pub transaction_type: FeeTransactionType,
    pub status: FeeTransactionStatus,

    // Fee details
    pub fee_percentage: f64,
    pub original_amount: f64,
    pub fee_amount: f64,
    pub fee_token_symbol: String,
    pub fee_token_address: String,

    // Transaction details
    pub transaction_hash: Option<String>,
    pub admin_address: String,
    pub user_wallet_address: String,

    // Metadata
    pub gas_fee: Option<f64>,
    pub block_number: Option<u64>,
    pub error_message: Option<String>,
    pub retry_count: i32,

    // Timestamps
    pub created_at: u64,
    pub updated_at: u64,
    pub completed_at: Option<u64>,
}

fn default_timestamp() -> u64 {
    SystemTime::now()
        .duration_since(UNIX_EPOCH)
        .unwrap_or_default()
        .as_secs()
}

impl AdminFeeTransaction {
    pub fn new(
        user_id: ObjectId,
        trade_id: Option<ObjectId>,
        blockchain: Blockchain,
        transaction_type: FeeTransactionType,
        fee_percentage: f64,
        original_amount: f64,
        fee_amount: f64,
        fee_token_symbol: String,
        fee_token_address: String,
        admin_address: String,
        user_wallet_address: String,
    ) -> Self {
        let now = default_timestamp();

        Self {
            id: None,
            user_id,
            trade_id,
            blockchain,
            transaction_type,
            status: FeeTransactionStatus::Pending,
            fee_percentage,
            original_amount,
            fee_amount,
            fee_token_symbol,
            fee_token_address,
            transaction_hash: None,
            admin_address,
            user_wallet_address,
            gas_fee: None,
            block_number: None,
            error_message: None,
            retry_count: 0,
            created_at: now,
            updated_at: now,
            completed_at: None,
        }
    }

    pub fn mark_completed(&mut self, transaction_hash: String, block_number: Option<u64>, gas_fee: Option<f64>) {
        self.status = FeeTransactionStatus::Completed;
        self.transaction_hash = Some(transaction_hash);
        self.block_number = block_number;
        self.gas_fee = gas_fee;
        self.completed_at = Some(default_timestamp());
        self.updated_at = default_timestamp();
    }

    pub fn mark_failed(&mut self, error_message: String) {
        self.status = FeeTransactionStatus::Failed;
        self.error_message = Some(error_message);
        self.retry_count += 1;
        self.updated_at = default_timestamp();
    }

    pub fn can_retry(&self) -> bool {
        self.retry_count < 3 && matches!(self.status, FeeTransactionStatus::Failed)
    }
}

impl Default for AdminFeeTransaction {
    fn default() -> Self {
        let now = default_timestamp();

        Self {
            id: None,
            user_id: ObjectId::new(),
            trade_id: None,
            blockchain: Blockchain::SOL,
            transaction_type: FeeTransactionType::BuyFee,
            status: FeeTransactionStatus::Pending,
            fee_percentage: 0.5,
            original_amount: 0.0,
            fee_amount: 0.0,
            fee_token_symbol: "SOL".to_string(),
            fee_token_address: "So11111111111111111111111111111111111111112".to_string(),
            transaction_hash: None,
            admin_address: String::new(),
            user_wallet_address: String::new(),
            gas_fee: None,
            block_number: None,
            error_message: None,
            retry_count: 0,
            created_at: now,
            updated_at: now,
            completed_at: None,
        }
    }
}
