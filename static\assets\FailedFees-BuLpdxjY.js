import{r,j as e,B as u,c as l,a as j}from"./index-dCUkEeO4.js";import{C as i}from"./Card-CLlE15Sf.js";import{adminApi as N}from"./adminApi-BFZ8qr13.js";import{F as w}from"./ArrowPathIcon-CSc3-Uxt.js";import{F as g}from"./ClockIcon-BpyFwEBR.js";function k(){const[d,c]=r.useState(!0),[x,m]=r.useState(null),[t,h]=r.useState(null);r.useEffect(()=>{n()},[]);const n=async()=>{try{c(!0),m(null);const s=await N.getFailedFeeTransactions();h(s)}catch(s){console.error("Error fetching failed fees:",s),m(s.message||"Failed to fetch failed fee transactions")}finally{c(!1)}},o=s=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD",minimumFractionDigits:2,maximumFractionDigits:6}).format(s),f=s=>new Date(s*1e3).toLocaleString(),p=s=>{if(s.user_info&&s.user_info.length>0){const a=s.user_info[0];if(a.first_name)return a.first_name;if(a.username)return`@${a.username}`;if(a.chat_id)return`User ${a.chat_id}`}return`User ${s.user_id}`};return d?e.jsx("div",{className:"flex items-center justify-center py-12",children:e.jsxs("div",{className:"animate-pulse flex space-x-4 items-center",children:[e.jsx("div",{className:"h-12 w-12 rounded-full bg-red-500/20"}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("div",{className:"h-4 w-36 bg-red-500/20 rounded"}),e.jsx("div",{className:"h-4 w-24 bg-red-500/20 rounded"})]})]})}):e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold text-white",children:"Failed Fee Collections"}),e.jsx("p",{className:"mt-1 text-sm text-gray-400",children:"Monitor and manage failed admin fee transactions"})]}),e.jsxs(u,{variant:"outline",size:"sm",onClick:n,disabled:d,children:[e.jsx(w,{className:"h-4 w-4 mr-2"}),"Refresh"]})]}),x&&e.jsx("div",{className:"rounded-md bg-red-900/20 border border-red-500/30 p-4",children:e.jsxs("div",{className:"flex",children:[e.jsx(l,{className:"h-5 w-5 text-red-400"}),e.jsxs("div",{className:"ml-3",children:[e.jsx("h3",{className:"text-sm font-medium text-red-400",children:"Error"}),e.jsx("p",{className:"mt-1 text-sm text-red-300",children:x})]})]})}),t&&e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4",children:[e.jsx(i,{className:"p-4 md:p-6",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"flex-shrink-0",children:e.jsx("div",{className:"h-10 w-10 md:h-12 md:w-12 rounded-full bg-red-800/30 flex items-center justify-center",children:e.jsx(l,{className:"h-5 w-5 md:h-6 md:w-6 text-red-400"})})}),e.jsx("div",{className:"ml-3 md:ml-5 w-0 flex-1",children:e.jsxs("dl",{children:[e.jsx("dt",{className:"text-xs md:text-sm font-medium text-gray-300 truncate",children:"Failed Transactions"}),e.jsx("dd",{children:e.jsx("div",{className:"text-base md:text-lg font-semibold text-white",children:t.summary.total_failed.toLocaleString()})})]})})]})}),e.jsx(i,{className:"p-4 md:p-6",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"flex-shrink-0",children:e.jsx("div",{className:"h-10 w-10 md:h-12 md:w-12 rounded-full bg-yellow-800/30 flex items-center justify-center",children:e.jsx(j,{className:"h-5 w-5 md:h-6 md:w-6 text-yellow-400"})})}),e.jsx("div",{className:"ml-3 md:ml-5 w-0 flex-1",children:e.jsxs("dl",{children:[e.jsx("dt",{className:"text-xs md:text-sm font-medium text-gray-300 truncate",children:"Lost Fees"}),e.jsx("dd",{children:e.jsx("div",{className:"text-base md:text-lg font-semibold text-white",children:o(t.summary.total_failed_amount)})})]})})]})}),e.jsx(i,{className:"p-4 md:p-6",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"flex-shrink-0",children:e.jsx("div",{className:"h-10 w-10 md:h-12 md:w-12 rounded-full bg-blue-800/30 flex items-center justify-center",children:e.jsx(g,{className:"h-5 w-5 md:h-6 md:w-6 text-blue-400"})})}),e.jsx("div",{className:"ml-3 md:ml-5 w-0 flex-1",children:e.jsxs("dl",{children:[e.jsx("dt",{className:"text-xs md:text-sm font-medium text-gray-300 truncate",children:"Recent Failures"}),e.jsx("dd",{children:e.jsx("div",{className:"text-base md:text-lg font-semibold text-white",children:t.total_count.toLocaleString()})})]})})]})})]}),e.jsxs(i,{className:"p-6",children:[e.jsxs("div",{className:"flex items-center mb-6",children:[e.jsx(l,{className:"h-6 w-6 text-red-400 mr-3"}),e.jsx("h3",{className:"text-lg font-semibold text-white",children:"Failed Fee Transactions"})]}),t.failed_transactions.length===0?e.jsxs("div",{className:"text-center py-8",children:[e.jsx(l,{className:"h-12 w-12 mx-auto mb-4 text-gray-500"}),e.jsx("h3",{className:"text-lg font-medium text-white mb-2",children:"No failed transactions"}),e.jsx("p",{className:"text-gray-400",children:"All fee collections are working properly."})]}):e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:"min-w-full divide-y divide-white/10",children:[e.jsx("thead",{children:e.jsxs("tr",{children:[e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider",children:"User"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider",children:"Blockchain"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider",children:"Type"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider",children:"Fee Amount"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider",children:"Error"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider",children:"Retries"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider",children:"Date"})]})}),e.jsx("tbody",{className:"divide-y divide-white/10",children:t.failed_transactions.map(s=>e.jsxs("tr",{className:"hover:bg-white/5",children:[e.jsxs("td",{className:"px-6 py-4 whitespace-nowrap",children:[e.jsx("div",{className:"text-sm text-white",children:p(s)}),e.jsxs("div",{className:"text-xs text-gray-400 font-mono",children:[s.user_wallet_address.slice(0,8),"...",s.user_wallet_address.slice(-6)]})]}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsx("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-800 text-gray-300",children:s.blockchain.toUpperCase()})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsx("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${s.transaction_type==="buy_fee"?"bg-green-900/30 text-green-400":"bg-red-900/30 text-red-400"}`,children:s.transaction_type==="buy_fee"?"Buy Fee":"Sell Fee"})}),e.jsxs("td",{className:"px-6 py-4 whitespace-nowrap",children:[e.jsxs("div",{className:"text-sm text-white",children:[s.fee_amount.toFixed(6)," ",s.fee_token_symbol]}),e.jsxs("div",{className:"text-xs text-gray-400",children:[s.fee_percentage,"% fee"]})]}),e.jsx("td",{className:"px-6 py-4",children:e.jsx("div",{className:"text-sm text-red-400 max-w-xs truncate",title:s.error_message,children:s.error_message})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsxs("div",{className:"text-sm text-white",children:[s.retry_count,"/3"]})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsx("div",{className:"text-sm text-white",children:f(s.created_at)})})]},s._id))})]})})]})]})]})}export{k as default};
