"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getWalletBalance = getWalletBalance;
const ethers_1 = require("ethers");
const web3_js_1 = require("@solana/web3.js");
// RPC endpoints
const RPC_ENDPOINTS = {
    ETH: process.env.ETH_RPC_URL || 'https://eth-mainnet.g.alchemy.com/v2/demo',
    BSC: process.env.BSC_RPC_URL || 'https://bsc-dataseed.binance.org',
    SOL: process.env.SOL_RPC_URL || 'https://api.mainnet-beta.solana.com',
    BASE: process.env.BASE_RPC_URL || 'https://mainnet.base.org'
};
/**
 * Get the native token balance for a wallet
 * @param blockchain The blockchain to query (ETH, BSC, SOL, BASE)
 * @param walletAddress The wallet address to check
 * @returns The wallet balance as a string
 */
async function getWalletBalance(blockchain, walletAddress) {
    try {
        switch (blockchain) {
            case 'ETH':
            case 'BSC':
            case 'BASE': {
                // Create provider based on blockchain
                const provider = new ethers_1.ethers.JsonRpcProvider(RPC_ENDPOINTS[blockchain]);
                // Get balance in wei
                const balanceWei = await provider.getBalance(walletAddress);
                // Convert to ether units and return
                return ethers_1.ethers.formatEther(balanceWei);
            }
            case 'SOL': {
                // Create Solana connection
                const connection = new web3_js_1.Connection(RPC_ENDPOINTS.SOL);
                // Get balance in lamports
                const balanceLamports = await connection.getBalance(new web3_js_1.PublicKey(walletAddress));
                // Convert to SOL (1 SOL = 10^9 lamports)
                const balanceSol = balanceLamports / **********;
                return balanceSol.toString();
            }
            default:
                throw new Error(`Unsupported blockchain: ${blockchain}`);
        }
    }
    catch (error) {
        console.error(`Error getting wallet balance for ${blockchain}:`, error);
        throw error;
    }
}
