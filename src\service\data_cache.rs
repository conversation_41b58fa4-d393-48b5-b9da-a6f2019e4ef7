use std::collections::HashMap;
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::sync::RwLock;
use tokio::time::sleep;
use tracing::{warn, error};

use crate::model::{BotError, Blockchain, User, Trade, Snipe};
use crate::service::blockchain_service::{BlockchainService, TokenBalance, Transaction};
use crate::service::DbService;

/// Cache entry with expiration time
struct CacheEntry<T> {
    data: T,
    last_updated: Instant,
}

/// Data cache for prefetching and storing frequently accessed data
pub struct DataCache {
    /// User balances cache: (chat_id, blockchain) -> balance
    balances: RwLock<HashMap<(i64, Blockchain), CacheEntry<f64>>>,

    /// User token holdings cache: (chat_id, blockchain) -> token holdings
    token_holdings: RwLock<HashMap<(i64, Blockchain), CacheEntry<Vec<TokenBalance>>>>,

    /// User trades cache: (chat_id, blockchain) -> trades
    trades: RwLock<HashMap<(i64, Blockchain), CacheEntry<Vec<Transaction>>>>,

    /// User pending transactions cache: (chat_id, blockchain) -> pending transactions
    pending_txs: RwLock<HashMap<(i64, Blockchain), CacheEntry<Vec<Transaction>>>>,

    /// Cache expiration time
    cache_expiration: Duration,

    /// Blockchain service
    blockchain_service: Arc<BlockchainService>,
}

impl DataCache {
    /// Creates a new data cache with the specified expiration time (in seconds)
    pub fn new(cache_expiration_seconds: u64) -> Arc<Self> {
        let blockchain_service = BlockchainService::new();

        let cache = Arc::new(Self {
            balances: RwLock::new(HashMap::new()),
            token_holdings: RwLock::new(HashMap::new()),
            trades: RwLock::new(HashMap::new()),
            pending_txs: RwLock::new(HashMap::new()),
            cache_expiration: Duration::from_secs(cache_expiration_seconds),
            blockchain_service,
        });

        // Start background cleanup task
        Self::start_cleanup_task(Arc::clone(&cache));

        cache
    }

    /// Starts a background task to clean up expired cache entries
    fn start_cleanup_task(cache: Arc<Self>) {
        tokio::spawn(async move {
            loop {
                // Sleep for 5 minutes
                sleep(Duration::from_secs(300)).await;

                // Clean up expired cache entries
                cache.cleanup_expired().await;
            }
        });
    }

    /// Cleans up expired cache entries
    async fn cleanup_expired(&self) {
        let now = Instant::now();

        // Clean up balances cache
        {
            let mut balances = self.balances.write().await;
            balances.retain(|_, entry| now.duration_since(entry.last_updated) < self.cache_expiration);
        }

        // Clean up token holdings cache
        {
            let mut token_holdings = self.token_holdings.write().await;
            token_holdings.retain(|_, entry| now.duration_since(entry.last_updated) < self.cache_expiration);
        }

        // Clean up trades cache
        {
            let mut trades = self.trades.write().await;
            trades.retain(|_, entry| now.duration_since(entry.last_updated) < self.cache_expiration);
        }

        // Clean up pending transactions cache
        {
            let mut pending_txs = self.pending_txs.write().await;
            pending_txs.retain(|_, entry| now.duration_since(entry.last_updated) < self.cache_expiration);
        }

        log::debug!("Cache cleanup completed");
    }

    /// Prefetches data for a user on a specific blockchain
    pub async fn prefetch_data(&self, user: &User, blockchain: Blockchain) -> Result<(), BotError> {
        let chat_id = user.chat_id;

        // Prefetch balance
        self.prefetch_balance(chat_id, blockchain).await?;

        // Prefetch token holdings
        self.prefetch_token_holdings(chat_id, blockchain).await?;

        // Prefetch trades
        self.prefetch_trades(chat_id, blockchain).await?;

        // Prefetch pending transactions
        self.prefetch_pending_txs(chat_id, blockchain).await?;

        Ok(())
    }

    /// Prefetches balance for a user on a specific blockchain
    async fn prefetch_balance(&self, chat_id: i64, blockchain: Blockchain) -> Result<(), BotError> {
        // Get the user from the database
        let user = match DbService::find_user_by_chat_id(chat_id).await? {
            Some(user) => user,
            None => return Err(BotError::user_error("User not found")),
        };

        // Get the wallet for the blockchain
        let wallet = match blockchain {
            Blockchain::BSC => &user.bsc_wallet,
            Blockchain::SOL => &user.sol_wallet,
            Blockchain::ETH => &user.eth_wallet,
            Blockchain::BASE => &user.base_wallet,
        };

        // Get the balance from the blockchain
        let balance = self.blockchain_service.get_balance(wallet, blockchain).await?;

        // Update the cache
        let mut balances = self.balances.write().await;
        balances.insert((chat_id, blockchain), CacheEntry {
            data: balance,
            last_updated: Instant::now(),
        });

        Ok(())
    }

    /// Gets the balance for a user on a specific blockchain
    pub async fn get_balance(&self, chat_id: i64, blockchain: Blockchain) -> Result<f64, BotError> {
        // Check if the balance is in the cache
        {
            let balances = self.balances.read().await;
            if let Some(entry) = balances.get(&(chat_id, blockchain)) {
                if Instant::now().duration_since(entry.last_updated) < self.cache_expiration {
                    return Ok(entry.data);
                }
            }
        }

        // If not in cache or expired, prefetch it
        self.prefetch_balance(chat_id, blockchain).await?;

        // Get the updated balance from the cache
        let balances = self.balances.read().await;
        if let Some(entry) = balances.get(&(chat_id, blockchain)) {
            Ok(entry.data)
        } else {
            // Cache miss after prefetch - retry once then return 0.0
            warn!("Cache miss for balance after prefetch for chat_id: {}, blockchain: {:?}", chat_id, blockchain);
            drop(balances); // Release read lock
            self.prefetch_balance(chat_id, blockchain).await?;

            let balances = self.balances.read().await;
            if let Some(entry) = balances.get(&(chat_id, blockchain)) {
                Ok(entry.data)
            } else {
                error!("Failed to cache balance after retry for chat_id: {}, blockchain: {:?}", chat_id, blockchain);
                Ok(0.0)
            }
        }
    }

    /// Prefetches token holdings for a user on a specific blockchain
    async fn prefetch_token_holdings(&self, chat_id: i64, blockchain: Blockchain) -> Result<(), BotError> {
        // Get the user from the database
        let user = match DbService::find_user_by_chat_id(chat_id).await? {
            Some(user) => user,
            None => return Err(BotError::user_error("User not found")),
        };

        // Get the wallet for the blockchain
        let wallet = match blockchain {
            Blockchain::BSC => &user.bsc_wallet,
            Blockchain::SOL => &user.sol_wallet,
            Blockchain::ETH => &user.eth_wallet,
            Blockchain::BASE => &user.base_wallet,
        };

        // Get the token holdings from the blockchain
        let token_holdings = self.blockchain_service.get_token_balances(wallet, blockchain).await?;

        // Update the cache
        let mut holdings = self.token_holdings.write().await;
        holdings.insert((chat_id, blockchain), CacheEntry {
            data: token_holdings,
            last_updated: Instant::now(),
        });

        Ok(())
    }

    /// Gets the token holdings for a user on a specific blockchain
    pub async fn get_token_holdings(&self, chat_id: i64, blockchain: Blockchain) -> Result<Vec<TokenBalance>, BotError> {
        // Check if the token holdings are in the cache
        {
            let holdings = self.token_holdings.read().await;
            if let Some(entry) = holdings.get(&(chat_id, blockchain)) {
                if Instant::now().duration_since(entry.last_updated) < self.cache_expiration {
                    return Ok(entry.data.clone());
                }
            }
        }

        // If not in cache or expired, prefetch them
        self.prefetch_token_holdings(chat_id, blockchain).await?;

        // Get the updated token holdings from the cache
        let holdings = self.token_holdings.read().await;
        if let Some(entry) = holdings.get(&(chat_id, blockchain)) {
            Ok(entry.data.clone())
        } else {
            // Cache miss after prefetch - retry once then return empty vec
            warn!("Cache miss for token holdings after prefetch for chat_id: {}, blockchain: {:?}", chat_id, blockchain);
            drop(holdings); // Release read lock
            self.prefetch_token_holdings(chat_id, blockchain).await?;

            let holdings = self.token_holdings.read().await;
            if let Some(entry) = holdings.get(&(chat_id, blockchain)) {
                Ok(entry.data.clone())
            } else {
                error!("Failed to cache token holdings after retry for chat_id: {}, blockchain: {:?}", chat_id, blockchain);
                Ok(Vec::new())
            }
        }
    }

    /// Prefetches trades for a user on a specific blockchain
    async fn prefetch_trades(&self, chat_id: i64, blockchain: Blockchain) -> Result<(), BotError> {
        // Get the user from the database
        let user = match DbService::find_user_by_chat_id(chat_id).await? {
            Some(user) => user,
            None => return Err(BotError::user_error("User not found")),
        };

        // Get the wallet for the blockchain
        let wallet = match blockchain {
            Blockchain::BSC => &user.bsc_wallet,
            Blockchain::SOL => &user.sol_wallet,
            Blockchain::ETH => &user.eth_wallet,
            Blockchain::BASE => &user.base_wallet,
        };

        // Get the recent transactions from the blockchain
        let transactions = self.blockchain_service.get_recent_transactions(wallet, blockchain).await?;

        // Update the cache
        let mut trades_cache = self.trades.write().await;
        trades_cache.insert((chat_id, blockchain), CacheEntry {
            data: transactions,
            last_updated: Instant::now(),
        });

        Ok(())
    }

    /// Gets the trades for a user on a specific blockchain
    pub async fn get_trades(&self, chat_id: i64, blockchain: Blockchain) -> Result<Vec<Transaction>, BotError> {
        // Check if the trades are in the cache
        {
            let trades = self.trades.read().await;
            if let Some(entry) = trades.get(&(chat_id, blockchain)) {
                if Instant::now().duration_since(entry.last_updated) < self.cache_expiration {
                    return Ok(entry.data.clone());
                }
            }
        }

        // If not in cache or expired, prefetch them
        self.prefetch_trades(chat_id, blockchain).await?;

        // Get the updated trades from the cache
        let trades = self.trades.read().await;
        if let Some(entry) = trades.get(&(chat_id, blockchain)) {
            Ok(entry.data.clone())
        } else {
            // Cache miss after prefetch - retry once then return empty vec
            warn!("Cache miss for trades after prefetch for chat_id: {}, blockchain: {:?}", chat_id, blockchain);
            drop(trades); // Release read lock
            self.prefetch_trades(chat_id, blockchain).await?;

            let trades = self.trades.read().await;
            if let Some(entry) = trades.get(&(chat_id, blockchain)) {
                Ok(entry.data.clone())
            } else {
                error!("Failed to cache trades after retry for chat_id: {}, blockchain: {:?}", chat_id, blockchain);
                Ok(Vec::new())
            }
        }
    }

    /// Prefetches pending transactions for a user on a specific blockchain
    async fn prefetch_pending_txs(&self, chat_id: i64, blockchain: Blockchain) -> Result<(), BotError> {
        // Get the user from the database
        let user = match DbService::find_user_by_chat_id(chat_id).await? {
            Some(user) => user,
            None => return Err(BotError::user_error("User not found")),
        };

        // Get the wallet for the blockchain
        let wallet = match blockchain {
            Blockchain::BSC => &user.bsc_wallet,
            Blockchain::SOL => &user.sol_wallet,
            Blockchain::ETH => &user.eth_wallet,
            Blockchain::BASE => &user.base_wallet,
        };

        // Get the pending transactions from the blockchain
        let pending_txs = self.blockchain_service.get_pending_transactions(wallet, blockchain).await?;

        // Update the cache
        let mut txs = self.pending_txs.write().await;
        txs.insert((chat_id, blockchain), CacheEntry {
            data: pending_txs,
            last_updated: Instant::now(),
        });

        Ok(())
    }

    /// Gets the pending transactions for a user on a specific blockchain
    pub async fn get_pending_txs(&self, chat_id: i64, blockchain: Blockchain) -> Result<Vec<Transaction>, BotError> {
        // Check if the pending transactions are in the cache
        {
            let txs = self.pending_txs.read().await;
            if let Some(entry) = txs.get(&(chat_id, blockchain)) {
                if Instant::now().duration_since(entry.last_updated) < self.cache_expiration {
                    return Ok(entry.data.clone());
                }
            }
        }

        // If not in cache or expired, prefetch them
        self.prefetch_pending_txs(chat_id, blockchain).await?;

        // Get the updated pending transactions from the cache
        let txs = self.pending_txs.read().await;
        if let Some(entry) = txs.get(&(chat_id, blockchain)) {
            Ok(entry.data.clone())
        } else {
            // Cache miss after prefetch - retry once then return empty vec
            warn!("Cache miss for pending transactions after prefetch for chat_id: {}, blockchain: {:?}", chat_id, blockchain);
            drop(txs); // Release read lock
            self.prefetch_pending_txs(chat_id, blockchain).await?;

            let txs = self.pending_txs.read().await;
            if let Some(entry) = txs.get(&(chat_id, blockchain)) {
                Ok(entry.data.clone())
            } else {
                error!("Failed to cache pending transactions after retry for chat_id: {}, blockchain: {:?}", chat_id, blockchain);
                Ok(Vec::new())
            }
        }
    }

    /// Formats token holdings for display
    pub fn format_token_holdings(&self, holdings: &[TokenBalance]) -> String {
        if holdings.is_empty() {
            return "• <code>None</code>".to_string();
        }

        holdings.iter()
            .map(|tb| format!("• <code>{:.4} {}</code> (${:.2})", tb.amount, tb.token, tb.value_usd))
            .collect::<Vec<String>>()
            .join("\n")
    }

    /// Formats trades for display
    pub fn format_trades(&self, transactions: &[Transaction]) -> String {
        if transactions.is_empty() {
            return "• <code>No recent trades</code>".to_string();
        }

        transactions.iter()
            .take(3) // Limit to 3 most recent transactions
            .map(|tx| {
                let token_str = tx.token.as_ref().map_or("".to_string(), |t| format!(" {}", t));
                format!("• <code>{:.4}{}</code> ({})", tx.value, token_str,
                    chrono::DateTime::from_timestamp(tx.timestamp, 0)
                        .map_or("unknown time".to_string(), |_dt|
                            format!("{} ago", Self::humanize_duration(chrono::Utc::now().timestamp() - tx.timestamp))
                        )
                )
            })
            .collect::<Vec<String>>()
            .join("\n")
    }

    /// Formats pending transactions for display
    pub fn format_pending_txs(&self, transactions: &[Transaction]) -> String {
        let pending_txs: Vec<&Transaction> = transactions.iter()
            .filter(|tx| tx.status == crate::service::blockchain_service::TransactionStatus::Pending)
            .collect();

        if pending_txs.is_empty() {
            return "• <code>None</code>".to_string();
        }

        pending_txs.iter()
            .take(3) // Limit to 3 most recent pending transactions
            .map(|tx| {
                let token_str = tx.token.as_ref().map_or("".to_string(), |t| format!(" {}", t));
                format!("• <code>{:.4}{}</code> (pending for {})",
                    tx.value,
                    token_str,
                    Self::humanize_duration(chrono::Utc::now().timestamp() - tx.timestamp)
                )
            })
            .collect::<Vec<String>>()
            .join("\n")
    }

    /// Humanizes a duration in seconds
    fn humanize_duration(seconds: i64) -> String {
        if seconds < 60 {
            return format!("{} seconds", seconds);
        } else if seconds < 3600 {
            return format!("{} minutes", seconds / 60);
        } else if seconds < 86400 {
            return format!("{} hours", seconds / 3600);
        } else {
            return format!("{} days", seconds / 86400);
        }
    }
}
