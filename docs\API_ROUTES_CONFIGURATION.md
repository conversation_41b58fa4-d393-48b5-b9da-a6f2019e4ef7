# API Routes Configuration

This document explains how to configure API routes using environment variables in the EasyBot system.

## Overview

All API routes in the EasyBot system are now configurable through environment variables. This provides flexibility for different deployment environments and allows for easy customization without code changes.

## Environment Variables

### Authentication Routes

| Environment Variable | Default Value | Description |
|---------------------|---------------|-------------|
| `API_AUTH_BASE` | `/api/auth` | Base path for authentication endpoints |
| `API_AUTH_LOGIN` | `/api/auth/login` | Admin login endpoint |
| `API_AUTH_LOGOUT` | `/api/auth/logout` | Admin logout endpoint |
| `API_AUTH_REFRESH` | `/api/auth/refresh` | Token refresh endpoint |
| `API_AUTH_ME` | `/api/auth/me` | Current user info endpoint |
| `API_AUTH_CHANGE_PASSWORD` | `/api/auth/change-password` | Password change endpoint |
| `API_AUTH_CREATE_ADMIN` | `/api/auth/create-admin` | Create admin endpoint (SuperAdmin only) |

### Bot Management Routes

| Environment Variable | Default Value | Description |
|---------------------|---------------|-------------|
| `API_BOTS_BASE` | `/api/bots` | Base path for bot management |
| `API_BOTS_ANALYTICS` | `/api/bots/analytics` | Bot analytics endpoint |
| `API_BOT_START` | `/start` | Bot start action suffix |
| `API_BOT_STOP` | `/stop` | Bot stop action suffix |
| `API_BOT_RESTART` | `/restart` | Bot restart action suffix |
| `API_BOT_STATS` | `/stats` | Bot statistics update suffix |

### Admin Panel Routes

| Environment Variable | Default Value | Description |
|---------------------|---------------|-------------|
| `API_ADMIN_BASE` | `/api/admin` | Base path for admin endpoints |
| `API_ADMIN_DASHBOARD` | `/api/admin/dashboard` | Main dashboard endpoint |
| `API_ADMIN_ANALYTICS` | `/api/admin/analytics` | Analytics endpoint |
| `API_ADMIN_SYSTEM_HEALTH` | `/api/admin/system-health` | System health endpoint |
| `API_ADMIN_ALERTS` | `/api/admin/alerts` | System alerts endpoint |
| `API_ADMIN_TRANSACTIONS` | `/api/admin/transactions` | Transactions management |
| `API_ADMIN_USERS` | `/api/admin/users` | Users management |
| `API_ADMIN_ADMINS` | `/api/admin/admins` | Admin users management |
| `API_ADMIN_SETTINGS` | `/api/admin/settings` | Settings management |

## Configuration Examples

### Development Environment

For development, you can use the default routes by not setting any environment variables, or create a `.env` file:

```bash
# Development API Routes
API_AUTH_LOGIN=/api/auth/login
API_BOTS_BASE=/api/bots
API_ADMIN_DASHBOARD=/api/admin/dashboard
```

### Production Environment

For production, you might want to use versioned or custom routes:

```bash
# Production API Routes with versioning
API_AUTH_BASE=/api/v1/auth
API_AUTH_LOGIN=/api/v1/auth/authenticate
API_BOTS_BASE=/api/v1/trading-bots
API_ADMIN_DASHBOARD=/api/v1/admin/overview
```

### Custom Deployment

For custom deployments with specific requirements:

```bash
# Custom API Routes
API_AUTH_BASE=/secure/auth
API_BOTS_BASE=/trading/bots
API_ADMIN_BASE=/management
API_ADMIN_DASHBOARD=/management/dashboard
```

## Route Construction

The system constructs full routes by combining base paths with suffixes. For example:

- Bot start: `{API_BOTS_BASE}/:id{API_BOT_START}` → `/api/bots/:id/start`
- User details: `{API_ADMIN_USERS}/:id` → `/api/admin/users/:id`
- Settings by key: `{API_ADMIN_SETTINGS}/:key` → `/api/admin/settings/:key`

## Frontend Configuration

When changing API routes, make sure to update the frontend API client accordingly. The frontend should use the same base URLs for API calls.

## Security Considerations

1. **Path Traversal**: Ensure custom routes don't introduce path traversal vulnerabilities
2. **Consistency**: Keep route patterns consistent across environments
3. **Documentation**: Update API documentation when routes change
4. **CORS**: Update CORS configuration if routes change

## Testing

To test with custom routes:

1. Set environment variables in your `.env` file
2. Restart the application
3. Verify routes are working with tools like curl or Postman:

```bash
# Test login with custom route
curl -X POST http://localhost:3000/api/v1/auth/authenticate \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"password"}'
```

## Deployment

### Docker

When deploying with Docker, pass environment variables:

```bash
docker run -e API_AUTH_BASE=/api/v1/auth -e API_BOTS_BASE=/api/v1/bots easybot
```

### Kubernetes

Use ConfigMaps or Secrets for environment variables:

```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: easybot-config
data:
  API_AUTH_BASE: "/api/v1/auth"
  API_BOTS_BASE: "/api/v1/bots"
  API_ADMIN_DASHBOARD: "/api/v1/admin/dashboard"
```

### Systemd

Set environment variables in the service file:

```ini
[Service]
Environment=API_AUTH_BASE=/api/v1/auth
Environment=API_BOTS_BASE=/api/v1/bots
ExecStart=/usr/local/bin/easybot
```

## Troubleshooting

### Common Issues

1. **404 Errors**: Check if environment variables are set correctly
2. **CORS Issues**: Update CORS configuration for new routes
3. **Frontend Errors**: Ensure frontend API client uses correct base URLs

### Debugging

Enable debug logging to see which routes are being registered:

```bash
LOG_LEVEL=DEBUG ./easybot
```

This will show route registration in the logs:

```
[INFO] Registering route: POST /api/auth/login
[INFO] Registering route: GET /api/bots
[INFO] Registering route: GET /api/admin/dashboard
```

## Best Practices

1. **Use consistent naming**: Keep route names logical and consistent
2. **Version your APIs**: Use versioning for production deployments
3. **Document changes**: Update documentation when routes change
4. **Test thoroughly**: Test all endpoints after route changes
5. **Backup configurations**: Keep backup of working configurations

## Migration Guide

When migrating from hardcoded routes to environment-based routes:

1. Identify current routes in use
2. Set corresponding environment variables
3. Test all endpoints
4. Update frontend configuration
5. Update documentation
6. Deploy and monitor

For questions or issues, refer to the main documentation or create an issue in the repository.
