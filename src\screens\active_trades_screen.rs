use teloxide::types::{InlineKeyboardButton, InlineKeyboardMarkup};
use crate::model::{UserData, BotError, Blockchain, Trade};
use crate::service::{BotService, DbService};
use crate::service::token_info_service::{TokenInfoService, TokenInfo};
use mongodb::bson::oid::ObjectId;

pub async fn show_active_trades_screen(
    bot_service: &BotService,
    user_data: &UserData,
    message_id: i32,
    blockchain: Blockchain,
) -> Result<(), BotError> {
    println!("Showing active trades for {:?}", blockchain);

    let chat_id = user_data.chat_id();

    // Get active trades from the database
    let trades = DbService::find_trades_by_user_and_blockchain(chat_id, blockchain.clone()).await?;

    // Show the first trade or a message if no trades
    show_trade_at_index(bot_service, user_data, message_id, &blockchain, &trades, 0).await
}

pub async fn show_trade_at_index(
    bot_service: &BotService,
    user_data: &UserData,
    message_id: i32,
    blockchain: &Blockchain,
    trades: &[Trade],
    index: usize,
) -> Result<(), BotError> {
    let chat_id = user_data.chat_id();

    if trades.is_empty() {
        // No trades found
        let no_trades_text = format!(
            "🔄 <b>{} Active Trades</b>\n\n\
            👤 <b>User:</b> {}\n\n\
            <b>No active trades found.</b>\n\n\
            You can start trading by using the /scan command to scan a contract.",
            blockchain.to_string().to_uppercase(),
            user_data.display_name()
        );

        let keyboard = create_empty_trades_keyboard(blockchain);
        bot_service.edit_message_with_keyboard(
            chat_id,
            message_id,
            &no_trades_text,
            keyboard,
        ).await?;

        return Ok(());
    }

    // Ensure index is valid
    let current_index = if index >= trades.len() { 0 } else { index };
    let trade = &trades[current_index];

    // Format the trade details
    let amount_str = match blockchain {
        Blockchain::BSC => format!("{} BNB", trade.price.to_string()),
        Blockchain::ETH => format!("{} ETH", trade.price.to_string()),
        Blockchain::BASE => format!("{} ETH", trade.price.to_string()),
        Blockchain::SOL => format!("{} SOL", trade.price.to_string()),
    };

    let token_amount_str = match blockchain {
        Blockchain::BSC => format!("{} tokens", trade.amount.to_string()),
        Blockchain::ETH => format!("{} tokens", trade.amount.to_string()),
        Blockchain::BASE => format!("{} tokens", trade.amount.to_string()),
        Blockchain::SOL => format!("{} tokens", trade.amount.to_string()),
    };

    let token_name = trade.token_name.clone().unwrap_or_else(|| "Unknown Token".to_string());

    // Create explorer link
    let explorer_url = match blockchain {
        Blockchain::BSC => format!("https://bscscan.com/token/{}", trade.contract_address),
        Blockchain::ETH => format!("https://etherscan.io/token/{}", trade.contract_address),
        Blockchain::BASE => format!("https://basescan.org/token/{}", trade.contract_address),
        Blockchain::SOL => format!("https://solscan.io/token/{}", trade.contract_address),
    };

    // Get current token price and calculate P&L if possible
    let (current_price, pnl_info) = match get_current_token_info(&trade.contract_address, blockchain).await {
        Ok(info) => {
            let current_value = trade.amount_out.unwrap_or(0.0) * info.price_usd;
            let spent_amount = trade.amount_in.unwrap_or(0.0);
            let pnl = current_value - spent_amount;
            let pnl_percentage = if spent_amount > 0.0 { (pnl / spent_amount) * 100.0 } else { 0.0 };

            let pnl_emoji = if pnl > 0.0 { "📈" } else if pnl < 0.0 { "📉" } else { "➖" };
            let pnl_color = if pnl > 0.0 { "🟢" } else if pnl < 0.0 { "🔴" } else { "⚪" };

            (info.price_usd, format!("\n<b>Current Price:</b> ${:.8}\n<b>Current Value:</b> ${:.2}\n{} <b>P&L:</b> ${:.2} ({:.1}%) {}",
                info.price_usd, current_value, pnl_color, pnl, pnl_percentage, pnl_emoji))
        },
        Err(_) => (0.0, "\n<i>Unable to fetch current price</i>".to_string())
    };

    // Create the trade text with enhanced information
    let trade_text = format!(
        "🔄 <b>{} Active Trades</b>\n\n\
        👤 <b>User:</b> {}\n\n\
        <b>Trade #{}/{}:</b>\n\n\
        <b>Token:</b> {}\n\
        <b>Contract:</b> <code>{}</code>\n\
        <b>Amount Spent:</b> {}\n\
        <b>Tokens Received:</b> {}\n\
        <b>Date:</b> {}{}\n\n\
        <a href=\"{}\">View on Explorer</a>\n\n\
        💡 <i>Use the buttons below to manage this trade</i>",
        blockchain.to_string().to_uppercase(),
        user_data.display_name(),
        current_index + 1,
        trades.len(),
        token_name,
        trade.contract_address,
        amount_str,
        token_amount_str,
        trade.created_at.format("%Y-%m-%d %H:%M:%S"),
        pnl_info,
        explorer_url
    );

    // Create keyboard with navigation buttons
    let keyboard = create_trades_keyboard(blockchain, trades, current_index, trade.id.unwrap());

    bot_service.edit_message_with_keyboard(
        chat_id,
        message_id,
        &trade_text,
        keyboard,
    ).await?;

    Ok(())
}

fn create_empty_trades_keyboard(blockchain: &Blockchain) -> InlineKeyboardMarkup {
    let keyboard = vec![
        vec![
            InlineKeyboardButton::callback(
                "🔄 Refresh".to_string(),
                format!("refresh_active_trades_{}", blockchain.as_str()),
            )
        ],
        vec![
            InlineKeyboardButton::callback(
                "◀️ Back to Dashboard".to_string(),
                format!("view_{}", blockchain.as_str()),
            )
        ]
    ];

    InlineKeyboardMarkup::new(keyboard)
}

fn create_trades_keyboard(
    blockchain: &Blockchain,
    trades: &[Trade],
    current_index: usize,
    _trade_id: ObjectId,
) -> InlineKeyboardMarkup {
    let mut keyboard = Vec::new();

    // Add navigation buttons if there are multiple trades
    if trades.len() > 1 {
        let prev_index = if current_index == 0 { trades.len() - 1 } else { current_index - 1 };
        let next_index = if current_index == trades.len() - 1 { 0 } else { current_index + 1 };

        keyboard.push(vec![
            InlineKeyboardButton::callback(
                "⬅️ Previous".to_string(),
                format!("active_trade_prev_{}_{}", blockchain.as_str(), prev_index),
            ),
            InlineKeyboardButton::callback(
                "➡️ Next".to_string(),
                format!("active_trade_next_{}_{}", blockchain.as_str(), next_index),
            ),
        ]);
    }

    // Add quick sell buttons for faster trading
    keyboard.push(vec![
        InlineKeyboardButton::callback(
            "🔥 Quick Sell 100%".to_string(),
            format!("quick_sell:{}:{}:100", blockchain.as_str(), trades[current_index].contract_address),
        ),
    ]);

    keyboard.push(vec![
        InlineKeyboardButton::callback(
            "💎 Sell 50%".to_string(),
            format!("quick_sell:{}:{}:50", blockchain.as_str(), trades[current_index].contract_address),
        ),
        InlineKeyboardButton::callback(
            "💰 Sell 25%".to_string(),
            format!("quick_sell:{}:{}:25", blockchain.as_str(), trades[current_index].contract_address),
        ),
    ]);

    // Add refresh button
    keyboard.push(vec![
        InlineKeyboardButton::callback(
            "🔄 Refresh".to_string(),
            format!("refresh_active_trades_{}", blockchain.as_str()),
        ),
    ]);

    // Add back button
    keyboard.push(vec![
        InlineKeyboardButton::callback(
            "◀️ Back to Dashboard".to_string(),
            format!("view_{}", blockchain.as_str()),
        ),
    ]);

    InlineKeyboardMarkup::new(keyboard)
}

// Helper function to get current token information
async fn get_current_token_info(token_address: &str, blockchain: &Blockchain) -> Result<TokenInfo, BotError> {
    let token_service = TokenInfoService::new();
    token_service.get_token_info(token_address, blockchain).await
        .map_err(|e| BotError::general_error(format!("Failed to get token info: {}", e)))
}
