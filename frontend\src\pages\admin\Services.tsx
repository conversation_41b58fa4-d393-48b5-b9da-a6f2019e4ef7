import { useState, useEffect } from 'react';
import Card from '../../components/ui/Card';
import Button from '../../components/ui/Button';
import {
  WrenchScrewdriverIcon,
  CheckCircleIcon,
  XCircleIcon,
  ArrowPathIcon,
  PlusIcon,
} from '@heroicons/react/24/outline';

// This would be fetched from API in production
const services = [];

export default function AdminServices() {
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Simulate loading
    const timer = setTimeout(() => {
      setLoading(false);
    }, 800);

    return () => clearTimeout(timer);
  }, []);

  function classNames(...classes: string[]) {
    return classes.filter(Boolean).join(' ');
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"></div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-2xl font-bold text-white">Services</h1>
          <p className="mt-1 text-sm text-gray-400">Manage your bot services and APIs</p>
        </div>
        <Button variant="glass" glow={true} size="md">
          <PlusIcon className="h-5 w-5 mr-2" />
          New Service
        </Button>
      </div>

      <div className="grid grid-cols-1 gap-6">
        <Card className="p-6 relative overflow-hidden">
          <div className="overflow-hidden rounded-xl border border-white/10 bg-white/5 backdrop-blur-sm">
            <table className="min-w-full divide-y divide-white/10">
              <thead>
                <tr>
                  <th scope="col" className="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-white sm:pl-6">
                    Service
                  </th>
                  <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-white">
                    Status
                  </th>
                  <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-white">
                    Version
                  </th>
                  <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-white">
                    Last Updated
                  </th>
                  <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-white">
                    Uptime
                  </th>
                  <th scope="col" className="relative py-3.5 pl-3 pr-4 sm:pr-6">
                    <span className="sr-only">Actions</span>
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-white/10">
                {services.map((service) => (
                  <tr key={service.id}>
                    <td className="whitespace-nowrap py-4 pl-4 pr-3 text-sm sm:pl-6">
                      <div className="flex items-center">
                        <div className="h-10 w-10 flex-shrink-0 rounded-full bg-indigo-800/30 flex items-center justify-center">
                          <WrenchScrewdriverIcon className="h-5 w-5 text-indigo-400" aria-hidden="true" />
                        </div>
                        <div className="ml-4">
                          <div className="font-medium text-white">{service.name}</div>
                          <div className="text-gray-400">{service.endpoints} endpoints</div>
                        </div>
                      </div>
                    </td>
                    <td className="whitespace-nowrap px-3 py-4 text-sm">
                      <span
                        className={classNames(
                          service.status === 'active'
                            ? 'bg-green-900/30 text-green-400 border border-green-500/30'
                            : 'bg-yellow-900/30 text-yellow-400 border border-yellow-500/30',
                          'inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium'
                        )}
                      >
                        {service.status === 'active' ? (
                          <CheckCircleIcon className="-ml-0.5 mr-1.5 h-3 w-3 text-green-400" />
                        ) : (
                          <ArrowPathIcon className="-ml-0.5 mr-1.5 h-3 w-3 text-yellow-400" />
                        )}
                        {service.status === 'active' ? 'Active' : 'Maintenance'}
                      </span>
                    </td>
                    <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-300">
                      {service.version}
                    </td>
                    <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-300">
                      {service.lastUpdated}
                    </td>
                    <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-300">
                      {service.uptime}
                    </td>
                    <td className="relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-6">
                      <div className="flex justify-end space-x-2">
                        <Button variant="outline" size="sm">Manage</Button>
                        <Button variant="outline" size="sm">Logs</Button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </Card>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card className="p-6 relative overflow-hidden">
          <h3 className="text-base font-semibold leading-6 text-white mb-4">Service Health</h3>
          <div className="space-y-4">
            <div>
              <div className="flex justify-between mb-1">
                <span className="text-sm text-gray-300">API Response Time</span>
                <span className="text-sm text-gray-300">45ms</span>
              </div>
              <div className="w-full bg-gray-700 rounded-full h-2">
                <div
                  className="bg-green-500 h-2 rounded-full"
                  style={{ width: '15%' }}
                ></div>
              </div>
            </div>

            <div>
              <div className="flex justify-between mb-1">
                <span className="text-sm text-gray-300">Error Rate</span>
                <span className="text-sm text-gray-300">0.02%</span>
              </div>
              <div className="w-full bg-gray-700 rounded-full h-2">
                <div
                  className="bg-green-500 h-2 rounded-full"
                  style={{ width: '2%' }}
                ></div>
              </div>
            </div>

            <div>
              <div className="flex justify-between mb-1">
                <span className="text-sm text-gray-300">CPU Usage</span>
                <span className="text-sm text-gray-300">32%</span>
              </div>
              <div className="w-full bg-gray-700 rounded-full h-2">
                <div
                  className="bg-indigo-500 h-2 rounded-full"
                  style={{ width: '32%' }}
                ></div>
              </div>
            </div>

            <div>
              <div className="flex justify-between mb-1">
                <span className="text-sm text-gray-300">Memory Usage</span>
                <span className="text-sm text-gray-300">45%</span>
              </div>
              <div className="w-full bg-gray-700 rounded-full h-2">
                <div
                  className="bg-indigo-500 h-2 rounded-full"
                  style={{ width: '45%' }}
                ></div>
              </div>
            </div>
          </div>
        </Card>

        <Card className="p-6 relative overflow-hidden">
          <h3 className="text-base font-semibold leading-6 text-white mb-4">Recent Deployments</h3>
          <div className="space-y-4">
            <div className="flex items-start">
              <div className="flex-shrink-0">
                <CheckCircleIcon className="h-5 w-5 text-green-400" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-white">Trading Bot API v1.2.3</p>
                <p className="mt-1 text-xs text-gray-400">Deployed 2 hours ago by admin</p>
              </div>
            </div>

            <div className="flex items-start">
              <div className="flex-shrink-0">
                <CheckCircleIcon className="h-5 w-5 text-green-400" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-white">Notification Service v1.3.2</p>
                <p className="mt-1 text-xs text-gray-400">Deployed 1 week ago by admin</p>
              </div>
            </div>

            <div className="flex items-start">
              <div className="flex-shrink-0">
                <XCircleIcon className="h-5 w-5 text-red-400" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-white">Analytics Engine v1.1.0</p>
                <p className="mt-1 text-xs text-gray-400">Failed deployment 6 hours ago</p>
              </div>
            </div>

            <div className="flex items-start">
              <div className="flex-shrink-0">
                <CheckCircleIcon className="h-5 w-5 text-green-400" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-white">User Authentication v2.0.1</p>
                <p className="mt-1 text-xs text-gray-400">Deployed 1 day ago by admin</p>
              </div>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
}
