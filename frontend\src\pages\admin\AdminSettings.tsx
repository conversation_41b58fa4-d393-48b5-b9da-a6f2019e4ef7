import { useState, useEffect, useCallback, useRef } from 'react';
import Card from '../../components/ui/Card';
import Button from '../../components/ui/Button';
import {
  CurrencyDollarIcon,
  ShieldCheckIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
} from '@heroicons/react/24/outline';
import { adminApi } from '../../services/adminApi';

// Local interface to match backend SettingsResponse
interface AdminSettings {
  id: string;
  admin_fee_percentage: number;
  max_users_per_admin: number;
  maintenance_mode: boolean;
  updated_at: number;
  updated_by: string;
  custom_settings: Record<string, any>;
}

interface UpdateSettingsRequest {
  admin_fee_percentage?: number;
  maintenance_mode?: boolean;
}

export default function AdminSettingsPage() {
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [settings, setSettings] = useState<AdminSettings | null>(null);
  const [formData, setFormData] = useState<UpdateSettingsRequest>({
    admin_fee_percentage: 0.5,
    maintenance_mode: false,
  });
  const [saveSuccess, setSaveSuccess] = useState(false);
  const [saveError, setSaveError] = useState('');
  const loadingRef = useRef(false);

  const fetchSettings = useCallback(async () => {
    if (loadingRef.current) {
      console.log('⏸️ Skipping fetch - already loading');
      return;
    }
      try {
        loadingRef.current = true;
        setLoading(true);
        console.log('🔄 Fetching admin settings...');

        const data = await adminApi.getSettings();
        console.log('📥 Settings response:', data);

        setSettings(data);
        setFormData({
          admin_fee_percentage: data.admin_fee_percentage,
          maintenance_mode: data.maintenance_mode,
        });
      } catch (error: any) {
        console.error('❌ Failed to fetch settings:', error);
        setSaveError(error.response?.data?.error || 'Failed to load settings');
      } finally {
        loadingRef.current = false;
        setLoading(false);
      }
    }, []);

  useEffect(() => {
    fetchSettings();
  }, [fetchSettings]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;

    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : type === 'number' ? parseFloat(value) : value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      setSaving(true);
      setSaveSuccess(false);
      setSaveError('');
      console.log('💾 Saving settings:', formData);

      const updatedSettings = await adminApi.updateSettings(formData);
      console.log('✅ Settings saved:', updatedSettings);

      setSettings(updatedSettings);
      setSaveSuccess(true);

      // Reset success message after 3 seconds
      setTimeout(() => {
        setSaveSuccess(false);
      }, 3000);
    } catch (error: any) {
      console.error('❌ Error saving settings:', error);
      setSaveError(error.response?.data?.error || 'Failed to save settings. Please try again.');

      // Reset error message after 3 seconds
      setTimeout(() => {
        setSaveError('');
      }, 3000);
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"></div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-2xl font-bold text-white">Admin Settings</h1>
          <p className="mt-1 text-sm text-gray-400">Configure global system settings</p>
        </div>
      </div>

      <form onSubmit={handleSubmit}>
        <div className="grid grid-cols-1 gap-8">
          <Card className="p-6 relative overflow-hidden">
            <div className="flex items-center mb-6">
              <CurrencyDollarIcon className="h-6 w-6 text-indigo-400 mr-2" />
              <h3 className="text-lg font-semibold text-white">Admin Fee Settings</h3>
            </div>

            <div className="space-y-4">
              <div>
                <label htmlFor="admin_fee_percentage" className="block text-sm font-medium text-gray-300 mb-1">
                  Admin Fee Percentage
                </label>
                <div className="relative mt-1 rounded-md shadow-sm">
                  <input
                    type="number"
                    name="admin_fee_percentage"
                    id="admin_fee_percentage"
                    className="block w-full rounded-md border-gray-700 bg-gray-800 text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                    placeholder="0.5"
                    step="0.001"
                    min="0.001"
                    max="100"
                    value={formData.admin_fee_percentage || ''}
                    onChange={handleInputChange}
                    required
                  />
                  <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                    <span className="text-gray-400 sm:text-sm">%</span>
                  </div>
                </div>
                <p className="mt-1 text-sm text-gray-400">
                  Percentage fee charged on all bot transactions. Supports decimal values (e.g., 0.5%, 1.25%, 2.5%). This fee is automatically calculated and deducted from each transaction.
                </p>
              </div>
            </div>
          </Card>

          <Card className="p-6 relative overflow-hidden">
            <div className="flex items-center mb-6">
              <ShieldCheckIcon className="h-6 w-6 text-indigo-400 mr-2" />
              <h3 className="text-lg font-semibold text-white">System Settings</h3>
            </div>

            <div className="space-y-4">
              <div className="flex items-center">
                <input
                  id="maintenance_mode"
                  name="maintenance_mode"
                  type="checkbox"
                  className="h-4 w-4 rounded border-gray-700 bg-gray-800 text-indigo-600 focus:ring-indigo-500"
                  checked={formData.maintenance_mode}
                  onChange={handleInputChange}
                />
                <label htmlFor="maintenance_mode" className="ml-2 block text-sm text-gray-300">
                  Maintenance Mode
                </label>
              </div>
              <p className="text-sm text-gray-400">
                When enabled, the system will be in maintenance mode and bots will not execute trades.
              </p>

              {formData.maintenance_mode && (
                <div className="mt-4 p-3 bg-yellow-900/30 border border-yellow-700/30 rounded-md">
                  <div className="flex items-start">
                    <ExclamationTriangleIcon className="h-5 w-5 text-yellow-500 mr-2 mt-0.5" />
                    <p className="text-sm text-yellow-200">
                      Warning: Enabling maintenance mode will pause all bot operations and trading activities.
                      Only enable this during scheduled maintenance periods.
                    </p>
                  </div>
                </div>
              )}
            </div>
          </Card>

          <div className="flex justify-end">
            <Button
              type="submit"
              variant="primary"
              disabled={saving}
            >
              {saving ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2"></div>
                  Saving...
                </>
              ) : 'Save Settings'}
            </Button>
          </div>

          {saveSuccess && (
            <div className="p-3 bg-green-900/30 border border-green-700/30 rounded-md">
              <div className="flex items-start">
                <CheckCircleIcon className="h-5 w-5 text-green-500 mr-2 mt-0.5" />
                <p className="text-sm text-green-200">
                  Settings saved successfully!
                </p>
              </div>
            </div>
          )}

          {saveError && (
            <div className="p-3 bg-red-900/30 border border-red-700/30 rounded-md">
              <div className="flex items-start">
                <ExclamationTriangleIcon className="h-5 w-5 text-red-500 mr-2 mt-0.5" />
                <p className="text-sm text-red-200">
                  {saveError}
                </p>
              </div>
            </div>
          )}
        </div>
      </form>
    </div>
  );
}
