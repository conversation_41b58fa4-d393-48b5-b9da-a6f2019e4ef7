use std::time::{SystemTime, UNIX_EPOCH};
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::RwLock;
use serde::{Deserialize, Serialize};
use reqwest::Client;
use crate::model::Blockchain;
use crate::config::AppConfig;

// Cache TTL in seconds (30 minutes)
const CACHE_TTL: u64 = 1800;

// Environment variable names for API endpoints ()
const ENV_HONEYPOT_API_EVM: &str = "SEC_ENDPOINT_HP_EVM";
const ENV_HONEYPOT_API_SOL: &str = "SEC_ENDPOINT_HP_SOL";

// Default API URLs if environment variables are not set
const DEFAULT_HONEYPOT_API_EVM: &str = "https://api.honeypot.is/v2/IsHoneypot";
const DEFAULT_HONEYPOT_API_SOL: &str = "https://api.rugcheck.xyz/v1/tokens";

// Honeypot check result structure
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct HoneypotCheckResult {
    pub is_honeypot: bool,
    pub risk: String,
    pub buy_tax: String,
    pub sell_tax: String,
    pub transfer_tax: Option<String>,
    pub buy_gas: Option<String>,
    pub sell_gas: Option<String>,
    pub warnings: Vec<String>,
    pub details: String,
    pub timestamp: u64,
}

// Cache entry structure
struct CacheEntry {
    data: HoneypotCheckResult,
    last_updated: u64,
}

// Honeypot service
#[derive(Clone)]
pub struct HoneypotService {
    client: Client,
    cache: Arc<RwLock<HashMap<String, CacheEntry>>>,
}

impl HoneypotService {
    // Create a new honeypot service
    pub fn new() -> Self {
        // Create HTTP client with timeout (30 seconds)
        let client = Client::builder()
            .timeout(std::time::Duration::from_secs(30))
            .build()
            .unwrap_or_default();

        Self {
            client,
            cache: Arc::new(RwLock::new(HashMap::new())),
        }
    }

    // Get honeypot API URL for EVM blockchains
    fn get_evm_honeypot_api_url(blockchain: &Blockchain) -> Result<String, String> {
        match blockchain {
            Blockchain::ETH | Blockchain::BSC | Blockchain::BASE => {
                // Get the config (we'll use this in the future for additional settings)
                let _config = AppConfig::get();

                // Get the API URL from environment variable or use default
                let api_url = std::env::var(ENV_HONEYPOT_API_EVM)
                    .unwrap_or_else(|_| DEFAULT_HONEYPOT_API_EVM.to_string());

                // Return the URL with the address parameter
                Ok(format!("{}?address=", api_url))
            },
            Blockchain::SOL => Err("Solana uses a different API".to_string()),
        }
    }

    // Check if a token is a honeypot
    pub async fn check_honeypot(
        &self,
        token_address: &str,
        blockchain: &Blockchain,
    ) -> Result<HoneypotCheckResult, String> {
        // Check cache first
        if let Some(cached_result) = self.get_from_cache(token_address, blockchain).await {
            println!("Using cached honeypot check for {} on {}", token_address, blockchain.as_str());
            return Ok(cached_result);
        }

        // Call the appropriate API based on blockchain
        let result = match blockchain {
            Blockchain::ETH | Blockchain::BSC | Blockchain::BASE => {
                self.check_evm_honeypot(token_address, blockchain).await
            }
            Blockchain::SOL => self.check_solana_honeypot(token_address).await,
        };

        // Cache the result if successful
        if let Ok(ref check_result) = result {
            self.cache_result(token_address, blockchain, check_result.clone()).await;
        }

        result
    }

    // Check if an EVM token is a honeypot
    async fn check_evm_honeypot(
        &self,
        token_address: &str,
        blockchain: &Blockchain,
    ) -> Result<HoneypotCheckResult, String> {
        println!("Checking if {} on {} is a honeypot", token_address, blockchain.as_str());

        // Get API URL
        let api_url = match Self::get_evm_honeypot_api_url(blockchain) {
            Ok(url) => url,
            Err(e) => return Err(e),
        };

        // Get the chain ID for the blockchain
        let chain_id = match blockchain {
            Blockchain::ETH => "1",
            Blockchain::BSC => "56",
            Blockchain::BASE => "8453",
            _ => return Err("Unsupported blockchain for EVM honeypot check".to_string()),
        };

        // Build the URL with parameters including chain ID
        let url = format!("{}{}&chainID={}", api_url, token_address, chain_id);

        // Make the request
        let response = match self.client.get(&url).send().await {
            Ok(resp) => resp,
            Err(e) => {
                // Create a fallback result
                let fallback = HoneypotCheckResult {
                    is_honeypot: false,
                    risk: "unknown".to_string(),
                    buy_tax: "0".to_string(),
                    sell_tax: "0".to_string(),
                    transfer_tax: Some("0".to_string()),
                    buy_gas: None,
                    sell_gas: None,
                    warnings: vec!["Failed to check honeypot status".to_string()],
                    details: format!("Error: {}", e),
                    timestamp: SystemTime::now()
                        .duration_since(UNIX_EPOCH)
                        .unwrap_or_default()
                        .as_secs(),
                };

                // Cache the fallback result for a shorter time
                self.cache_result(token_address, blockchain, fallback.clone()).await;

                return Ok(fallback);
            }
        };

        // Parse the response
        let data: serde_json::Value = match response.json().await {
            Ok(json) => json,
            Err(e) => return Err(format!("Failed to parse honeypot response: {}", e)),
        };

        // Extract the honeypot information
        let summary = data.get("summary").ok_or("Missing summary in response")?;
        let simulation_result = data.get("simulationResult").ok_or("Missing simulationResult in response")?;

        // Process flag descriptions
        let mut warnings = Vec::new();
        let mut flag_descriptions = Vec::new();

        if let Some(flags) = summary.get("flags").and_then(|f| f.as_array()) {
            for flag in flags {
                if let Some(desc) = flag.get("description").and_then(|d| d.as_str()) {
                    // Skip known false positives
                    if desc == "All snipers are marked as honeypots (blacklisted). Sniper detection still needs some improvements." {
                        continue;
                    }
                    flag_descriptions.push(desc.to_string());
                }
            }
        }

        // Add warning messages based on risk level
        let risk = summary.get("risk").and_then(|r| r.as_str()).unwrap_or("unknown");
        if risk == "honeypot" {
            warnings.push("CRITICAL: Honeypot detected - High risk token".to_string());
        } else if risk == "unknown" {
            warnings.push("WARNING: Could not determine if this is a honeypot. Proceed with caution.".to_string());
        }

        // Handle closed source warning
        if flag_descriptions.contains(&"The source code is not available, allowing for hidden functionality.".to_string()) {
            warnings.push("WARNING: Contract's dependencies are closed source, allowing for hidden functionalities.".to_string());
            flag_descriptions.retain(|flag| flag != "The source code is not available, allowing for hidden functionality.");
        }

        // Get tax information
        let buy_tax = simulation_result.get("buyTax").and_then(|t| t.as_f64()).unwrap_or(0.0).to_string();
        let sell_tax = simulation_result.get("sellTax").and_then(|t| t.as_f64()).unwrap_or(0.0).to_string();
        let transfer_tax = simulation_result.get("transferTax").and_then(|t| t.as_f64()).map(|t| t.to_string());
        let buy_gas = simulation_result.get("buyGas").and_then(|g| g.as_str()).map(|g| g.to_string());
        let sell_gas = simulation_result.get("sellGas").and_then(|g| g.as_str()).map(|g| g.to_string());

        // Create the result
        let result = HoneypotCheckResult {
            is_honeypot: data.get("honeypotResult").and_then(|h| h.get("isHoneypot")).and_then(|h| h.as_bool()).unwrap_or(false),
            risk: risk.to_string(),
            buy_tax,
            sell_tax,
            transfer_tax,
            buy_gas,
            sell_gas,
            warnings,
            details: flag_descriptions.join("\n"),
            timestamp: SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .unwrap_or_default()
                .as_secs(),
        };

        Ok(result)
    }

    // Check if a Solana token is a honeypot
    async fn check_solana_honeypot(&self, token_address: &str) -> Result<HoneypotCheckResult, String> {
        println!("Checking if {} on SOL is a honeypot", token_address);

        // Get the config (we'll use this in the future for additional settings)
        let _config = AppConfig::get();

        // Get the API URL from environment variable or use default
        let api_base_url = std::env::var(ENV_HONEYPOT_API_SOL)
            .unwrap_or_else(|_| DEFAULT_HONEYPOT_API_SOL.to_string());

        // Make API request to RugCheck
        let url = format!("{}/{}/report/summary", api_base_url, token_address);

        // Make the request
        let response = match self.client.get(&url).send().await {
            Ok(resp) => resp,
            Err(e) => {
                // Create a fallback result
                let fallback = HoneypotCheckResult {
                    is_honeypot: false,
                    risk: "unknown".to_string(),
                    buy_tax: "0".to_string(),
                    sell_tax: "0".to_string(),
                    transfer_tax: None,
                    buy_gas: None,
                    sell_gas: None,
                    warnings: vec!["Failed to check honeypot status".to_string()],
                    details: format!("Error: {}", e),
                    timestamp: SystemTime::now()
                        .duration_since(UNIX_EPOCH)
                        .unwrap_or_default()
                        .as_secs(),
                };

                // Cache the fallback result for a shorter time
                self.cache_result(token_address, &Blockchain::SOL, fallback.clone()).await;

                return Ok(fallback);
            }
        };

        // Parse the response
        let data: serde_json::Value = match response.json().await {
            Ok(json) => json,
            Err(e) => return Err(format!("Failed to parse honeypot response: {}", e)),
        };

        // Extract risk descriptions and check for "danger" level
        let mut risk_descriptions = Vec::new();
        let mut warnings = Vec::new();
        let mut danger_detected = false;

        if let Some(risks) = data.get("risks").and_then(|r| r.as_array()) {
            for risk in risks {
                let name = risk.get("name").and_then(|n| n.as_str()).unwrap_or("Unknown");
                let description = risk.get("description").and_then(|d| d.as_str()).unwrap_or("No description");
                let level = risk.get("level").and_then(|l| l.as_str()).unwrap_or("unknown");

                let risk_text = format!("{}: {} ({})", name, description, level);
                risk_descriptions.push(risk_text);

                if level == "danger" {
                    danger_detected = true;
                    warnings.push(format!("DANGER: {} - {}", name, description));
                } else if level == "high" {
                    warnings.push(format!("HIGH RISK: {} - {}", name, description));
                }
            }
        }

        // Add special warning if danger level is found
        if danger_detected {
            warnings.insert(0, "CRITICAL: Honeypot detected - High risk token".to_string());
        }

        // Create the result
        let result = HoneypotCheckResult {
            is_honeypot: danger_detected,
            risk: data.get("score").and_then(|s| s.as_f64()).map(|s| s.to_string()).unwrap_or_else(|| "unknown".to_string()),
            buy_tax: "0".to_string(),  // Solana doesn't have explicit taxes like EVM chains
            sell_tax: "0".to_string(),
            transfer_tax: None,
            buy_gas: None,
            sell_gas: None,
            warnings,
            details: risk_descriptions.join("\n"),
            timestamp: SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .unwrap_or_default()
                .as_secs(),
        };

        Ok(result)
    }

    // Get a result from the cache
    async fn get_from_cache(&self, token_address: &str, blockchain: &Blockchain) -> Option<HoneypotCheckResult> {
        let cache_key = format!("honeypot:{}:{}", blockchain.as_str(), token_address);
        let cache = self.cache.read().await;

        if let Some(entry) = cache.get(&cache_key) {
            let now = SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .unwrap_or_default()
                .as_secs();

            if now - entry.last_updated < CACHE_TTL {
                return Some(entry.data.clone());
            }
        }

        None
    }

    // Cache a result
    async fn cache_result(&self, token_address: &str, blockchain: &Blockchain, result: HoneypotCheckResult) {
        let cache_key = format!("honeypot:{}:{}", blockchain.as_str(), token_address);
        let now = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs();

        let mut cache = self.cache.write().await;
        cache.insert(cache_key, CacheEntry {
            data: result,
            last_updated: now,
        });
    }
}
