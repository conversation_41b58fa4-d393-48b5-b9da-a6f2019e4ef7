use teloxide::types::{Inline<PERSON>eyboardButton, InlineKeyboardMarkup};
use teloxide::prelude::Requester;
use teloxide::payloads::EditMessageTextSetters;
use std::str::FromStr;
use solana_sdk::pubkey::Pubkey;
use solana_sdk::signature::Signer;
use crate::model::{BotError, Blockchain, UserData};
use crate::service::BotService;
use crate::service::token_info_service::TokenInfoService;
// Get the Solana explorer URL from environment variables
fn get_solana_explorer_tx_url<T: AsRef<str>>(tx_hash: T) -> String {
    crate::constants::explorer_urls::get_explorer_tx_url("sol", tx_hash.as_ref())
}

pub async fn execute_buy_order(
    bot_service: &BotService,
    user_data: &UserData,
    chat_id: i64,
    message_id: i32,
    token_address: &str,
    amount: f64,
) -> Result<(), BotError> {
    let token_service = TokenInfoService::new();
    let token_info_opt = token_service.get_token_info_from_cache(token_address, &Blockchain::SOL).await;

    let token_name = if let Some(ref token_info) = token_info_opt {
        format!("{} ({})", token_info.name, token_info.symbol)
    } else {
        format!("Token {}", token_address)
    };
    let initial_loading_text = format!(
        "⏳ <b>Processing Buy Order...</b>\n\n\
        Token: <b>{}</b>\n\
        Amount: <b>{} SOL</b>\n\
        Contract: <code>{}</code>\n\n\
        Please wait while your transaction is being processed...",
        token_name,
        amount,
        token_address
    );

    bot_service.edit_message(chat_id, message_id, &initial_loading_text).await?;

    let wallet = user_data.get_wallet(&Blockchain::SOL);

    let slippage_bps = user_data.get_config(&Blockchain::SOL)
        .map(|config| {
            let slippage_f64 = config.slippage.to_string().parse::<f64>().unwrap_or(0.5);
            (slippage_f64 * 100.0) as u32
        })
        .unwrap_or(50);

    let sol_amount = (amount * 1_000_000_000.0) as u64;
    let token_pubkey = match Pubkey::from_str(token_address) {
        Ok(pubkey) => pubkey,
        Err(e) => {
            let error_text = format!(
                "❌ <b>Invalid Token Address</b>\n\n\
                Error: {}\n\n\
                Please try again with a valid Solana token address.",
                e
            );

            bot_service.edit_message(chat_id, message_id, &error_text).await?;
            return Err(BotError::user_error(format!("Invalid token address: {}", e)));
        }
    };

    let keypair = {
        let decoded = match bs58::decode(&wallet.private_key).into_vec() {
            Ok(bytes) => bytes,
            Err(e) => {
                let error_text = format!(
                    "❌ <b>Wallet Error</b>\n\n\
                    Error: Failed to decode private key: {}\n\n\
                    Please check your wallet configuration.",
                    e
                );

                // Create keyboard with back buttons
                let keyboard = InlineKeyboardMarkup::new(vec![
                    vec![
                        InlineKeyboardButton::callback(
                            "◀️ Back to Token".to_string(),
                            "back_to_token".to_string(),
                        )
                    ],
                    vec![
                        InlineKeyboardButton::callback(
                            "🏠 Dashboard".to_string(),
                            "view_sol".to_string(),
                        ),
                        InlineKeyboardButton::callback(
                            "❌ Dismiss".to_string(),
                            "dismiss_message".to_string(),
                        )
                    ]
                ]);

                bot_service.edit_message_with_keyboard(chat_id, message_id, &error_text, keyboard).await?;
                return Err(BotError::user_error(format!("Wallet error: {}", e)));
            }
        };

        match solana_sdk::signature::Keypair::from_bytes(&decoded) {
            Ok(kp) => kp,
            Err(e) => {
                let error_text = format!(
                    "❌ <b>Wallet Error</b>\n\n\
                    Error: Invalid private key: {}\n\n\
                    Please check your wallet configuration.",
                    e
                );

                // Create keyboard with back buttons
                let keyboard = InlineKeyboardMarkup::new(vec![
                    vec![
                        InlineKeyboardButton::callback(
                            "◀️ Back to Token".to_string(),
                            "back_to_token".to_string(),
                        )
                    ],
                    vec![
                        InlineKeyboardButton::callback(
                            "🏠 Dashboard".to_string(),
                            "view_sol".to_string(),
                        ),
                        InlineKeyboardButton::callback(
                            "❌ Dismiss".to_string(),
                            "dismiss_message".to_string(),
                        )
                    ]
                ]);

                bot_service.edit_message_with_keyboard(chat_id, message_id, &error_text, keyboard).await?;
                return Err(BotError::user_error(format!("Wallet error: {}", e)));
            }
        }
    };

    // 🚀 AUTO-TRIGGER: Use high-performance trader for optimized execution
    let result = if crate::config::defaults::USE_HIGH_PERFORMANCE_TRADER {
        // Convert Keypair to Wallet for high-performance trader
        let wallet = crate::model::Wallet {
            address: keypair.pubkey().to_string(),
            private_key: bs58::encode(keypair.to_bytes()).into_string(),
            mnemonic: None,
        };

        // Execute high-performance buy - now returns SwapResult directly
        match bot_service.execute_high_performance_solana_buy(
            &wallet,
            token_address,
            sol_amount,
            slippage_bps as u16,
            Some(chat_id),
        ).await {
            Ok(result) => result,
            Err(e) => {
                let error_text = format!(
                    "❌ <b>Transaction Failed</b>\n\n\
                    Token: <b>{}</b>\n\
                    Amount: <b>{} SOL</b>\n\
                    Error: {}\n\n\
                    Please try again later.",
                    token_name,
                    amount,
                    e
                );

                let keyboard = InlineKeyboardMarkup::new(vec![
                    vec![
                        InlineKeyboardButton::callback(
                            "◀️ Back to Token".to_string(),
                            "back_to_token".to_string(),
                        )
                    ],
                    vec![
                        InlineKeyboardButton::callback(
                            "🏠 Dashboard".to_string(),
                            "view_sol".to_string(),
                        ),
                        InlineKeyboardButton::callback(
                            "❌ Dismiss".to_string(),
                            "dismiss_message".to_string(),
                        )
                    ]
                ]);

                bot_service.edit_message_with_keyboard(chat_id, message_id, &error_text, keyboard).await?;
                return Err(BotError::user_error(format!("High-performance buy failed: {}", e)));
            }
        }
    } else {
        // Fallback to regular trader
        match bot_service.solana_trader_service().buy_token_with_sol(
            &keypair,
            &token_pubkey,
            sol_amount,
            slippage_bps,
            Some(chat_id) // 🚀 FIX: Pass actual chat_id
        ).await {
            Ok(result) => result,
            Err(e) => {
                let token_service = TokenInfoService::new();
                let fresh_token_info_opt = token_service.get_token_info_from_cache(token_address, &Blockchain::SOL).await;

                let display_token_name = if let Some(ref token_info) = fresh_token_info_opt {
                    format!("{} ({})", token_info.name, token_info.symbol)
                } else {
                    token_name.clone()
                };
                let error_text = format!(
                    "❌ <b>Transaction Failed</b>\n\n\
                    Token: <b>{}</b>\n\
                    Amount: <b>{} SOL</b>\n\
                    Error: {}\n\n\
                    Please try again later.",
                    display_token_name,
                    amount,
                    e
                );

                let keyboard = InlineKeyboardMarkup::new(vec![
                    vec![
                        InlineKeyboardButton::callback(
                            "◀️ Back to Token".to_string(),
                            "back_to_token".to_string(),
                        )
                    ],
                    vec![
                        InlineKeyboardButton::callback(
                            "🏠 Dashboard".to_string(),
                            "view_sol".to_string(),
                        ),
                        InlineKeyboardButton::callback(
                            "❌ Dismiss".to_string(),
                            "dismiss_message".to_string(),
                        )
                    ]
                ]);

                bot_service.edit_message_with_keyboard(chat_id, message_id, &error_text, keyboard).await?;
                return Err(BotError::user_error(format!("Transaction failed: {}", e)));
            }
        }
    };

    // Debug logging to identify the issue
    println!("🔍 DEBUG: Preparing success message");
    println!("   Result signature: {}", result.signature);
    println!("   Result output_amount: {}", result.output_amount);
    println!("   Result output_token decimals: {}", result.output_token.decimals);
    println!("   Result price_impact: {}", result.price_impact);

    let formatted_tokens = format_token_amount(result.output_amount, result.output_token.decimals);
    println!("   Formatted tokens: {}", formatted_tokens);

    let success_text = format!(
        "✅ <b>Buy Order Successful!</b>\n\n\
        Amount Spent: <b>{} SOL</b>\n\
        Tokens Received: <b>{}</b>\n\
        Price Impact: <b>{:.2}%</b>\n\
        Contract: <code>{}</code>\n\n\
        <a href=\"{}\">View Transaction on Solana Explorer</a>",
        amount,
        formatted_tokens,
        result.price_impact,
        token_address,
        get_solana_explorer_tx_url(result.signature.to_string())
    );

    println!("🔍 DEBUG: Success text prepared: {}", success_text);

    // Store token address in user state for shorter callback data
    let mut user_states = crate::screens::scan_screen::get_user_states().write().await;
    user_states.insert(chat_id, format!("last_token:{}:{}", Blockchain::SOL.as_str(), token_address));
    drop(user_states);

    let keyboard = InlineKeyboardMarkup::new(vec![
        vec![
            InlineKeyboardButton::callback(
                "◀️ Back to Token".to_string(),
                "back_to_last_token".to_string(),
            )
        ],
        vec![
            InlineKeyboardButton::callback(
                "🏠 Dashboard".to_string(),
                "new_msg_view_sol".to_string(),
            ),
            InlineKeyboardButton::callback(
                "❌ Dismiss".to_string(),
                "dismiss_message".to_string(),
            )
        ]
    ]);

    println!("🔍 DEBUG: About to edit message with success text");
    match bot_service.edit_message_with_keyboard(chat_id, message_id, &success_text, keyboard).await {
        Ok(_) => {
            println!("✅ DEBUG: Successfully updated message with success text");
        }
        Err(e) => {
            println!("❌ DEBUG: Failed to update message: {}", e);
            return Err(e);
        }
    }

    Ok(())
}

pub async fn execute_sell_order(
    bot_service: &BotService,
    user_data: &UserData,
    chat_id: i64,
    message_id: i32,
    token_address: &str,
    percent: f64,
) -> Result<(), BotError> {
    let token_service = TokenInfoService::new();
    let token_info_opt = token_service.get_token_info_from_cache(token_address, &Blockchain::SOL).await;

    let token_name = if let Some(ref token_info) = token_info_opt {
        format!("{} ({})", token_info.name, token_info.symbol)
    } else {
        format!("Token {}", token_address)
    };
    let initial_loading_text = format!(
        "⏳ <b>Processing Sell Order...</b>\n\n\
        Token: <b>{}</b>\n\
        Amount: <b>{:.1}%</b>\n\
        Contract: <code>{}</code>\n\n\
        Please wait while your transaction is being processed...",
        token_name,
        percent,
        token_address
    );

    bot_service.edit_message(chat_id, message_id, &initial_loading_text).await?;

    let wallet = user_data.get_wallet(&Blockchain::SOL);

    let slippage_bps = user_data.get_config(&Blockchain::SOL)
        .map(|config| {
            let slippage_f64 = config.slippage.to_string().parse::<f64>().unwrap_or(0.5);
            (slippage_f64 * 100.0) as u32
        })
        .unwrap_or(50);
    let token_pubkey = match Pubkey::from_str(token_address) {
        Ok(pubkey) => pubkey,
        Err(e) => {
            let error_text = format!(
                "❌ <b>Invalid Token Address</b>\n\n\
                Error: {}\n\n\
                Please try again with a valid Solana token address.",
                e
            );

            bot_service.edit_message(chat_id, message_id, &error_text).await?;
            return Err(BotError::user_error(format!("Invalid token address: {}", e)));
        }
    };

    let keypair = {
        let decoded = match bs58::decode(&wallet.private_key).into_vec() {
            Ok(bytes) => bytes,
            Err(e) => {
                let error_text = format!(
                    "❌ <b>Wallet Error</b>\n\n\
                    Error: Failed to decode private key: {}\n\n\
                    Please check your wallet configuration.",
                    e
                );

                bot_service.edit_message(chat_id, message_id, &error_text).await?;
                return Err(BotError::user_error(format!("Wallet error: {}", e)));
            }
        };

        match solana_sdk::signature::Keypair::from_bytes(&decoded) {
            Ok(kp) => kp,
            Err(e) => {
                let error_text = format!(
                    "❌ <b>Wallet Error</b>\n\n\
                    Error: Invalid private key: {}\n\n\
                    Please check your wallet configuration.",
                    e
                );

                bot_service.edit_message(chat_id, message_id, &error_text).await?;
                return Err(BotError::user_error(format!("Wallet error: {}", e)));
            }
        }
    };

    let percentage = percent.clamp(1.0, 100.0) as u8;

    // 🚀 AUTO-TRIGGER: Use high-performance trader with fallback for sell
    let wallet = crate::model::Wallet {
        address: keypair.pubkey().to_string(),
        private_key: bs58::encode(keypair.to_bytes()).into_string(),
        mnemonic: None,
    };

    // Calculate token amount to sell based on percentage
    let token_balance = match bot_service.solana_trader_service().get_token_balance(&keypair.pubkey(), &token_pubkey).await {
        Ok(balance) => balance,
        Err(e) => {
            let error_text = format!(
                "❌ <b>Failed to get token balance</b>\n\n\
                Error: {}\n\n\
                Please try again later.",
                e
            );
            bot_service.edit_message(chat_id, message_id, &error_text).await?;
            return Err(BotError::user_error(format!("Failed to get token balance: {}", e)));
        }
    };

    let token_amount_to_sell = (token_balance as f64 * (percentage as f64 / 100.0)) as u64;

    // Execute high-performance sell - now returns SwapResult directly
    let result = match bot_service.execute_high_performance_solana_sell(
        &wallet,
        token_address,
        token_amount_to_sell,
        slippage_bps as u16,
        Some(chat_id),
    ).await {
        Ok(result) => result,
        Err(e) => {
            // High-performance sell failed, try fallback to normal trader
            println!("⚠️ High-Performance Solana Sell FAILED: {}", e);
            println!("🔄 FALLBACK: Using normal Solana trader");

            match bot_service.solana_trader_service().sell_token_for_sol_with_percentage(
                &keypair,
                &token_pubkey,
                percentage,
                slippage_bps,
                Some(chat_id) // 🚀 FIX: Pass actual chat_id
            ).await {
                Ok(result) => result,
                Err(e) => {
                    let token_service = TokenInfoService::new();
                    let fresh_token_info_opt = token_service.get_token_info_from_cache(token_address, &Blockchain::SOL).await;

                    let display_token_name = if let Some(ref token_info) = fresh_token_info_opt {
                        format!("{} ({})", token_info.name, token_info.symbol)
                    } else {
                        token_name.clone()
                    };
                    let error_text = format!(
                        "❌ <b>Transaction Failed</b>\n\n\
                        Token: <b>{}</b>\n\
                        Amount: <b>{:.1}%</b>\n\
                        Error: {}\n\n\
                        Please try again later.",
                        display_token_name,
                        percent,
                        e
                    );

                    let keyboard = InlineKeyboardMarkup::new(vec![
                        vec![
                            InlineKeyboardButton::callback(
                                "◀️ Back to Token".to_string(),
                                "back_to_token".to_string(),
                            )
                        ],
                        vec![
                            InlineKeyboardButton::callback(
                                "🏠 Dashboard".to_string(),
                                "view_sol".to_string(),
                            ),
                            InlineKeyboardButton::callback(
                                "❌ Dismiss".to_string(),
                                "dismiss_message".to_string(),
                            )
                        ]
                    ]);

                    bot_service.edit_message_with_keyboard(chat_id, message_id, &error_text, keyboard).await?;
                    return Err(BotError::user_error(format!("Transaction failed: {}", e)));
                }
            }
        }
    };

    // Debug logging to identify the issue
    println!("🔍 DEBUG SELL: Preparing success message");
    println!("   Result signature: {}", result.signature);
    println!("   Result input_amount: {}", result.input_amount);
    println!("   Result input_token decimals: {}", result.input_token.decimals);
    println!("   Result output_amount: {}", result.output_amount);
    println!("   Result price_impact: {}", result.price_impact);

    let formatted_tokens_sold = format_token_amount(result.input_amount, result.input_token.decimals);
    let formatted_sol_received = format_token_amount(result.output_amount, 9); // SOL has 9 decimals
    println!("   Formatted tokens sold: {}", formatted_tokens_sold);
    println!("   Formatted SOL received: {}", formatted_sol_received);

    let success_text = format!(
        "✅ <b>Sell Order Successful!</b>\n\n\
        Amount Sold: <b>{}</b>\n\
        SOL Received: <b>{} SOL</b>\n\
        Price Impact: <b>{:.2}%</b>\n\
        Contract: <code>{}</code>\n\n\
        <a href=\"{}\">View Transaction on Solana Explorer</a>",
        formatted_tokens_sold,
        formatted_sol_received,
        result.price_impact,
        token_address,
        get_solana_explorer_tx_url(result.signature.to_string())
    );

    println!("🔍 DEBUG SELL: Success text prepared: {}", success_text);

    // Store token address in user state for shorter callback data
    let mut user_states = crate::screens::scan_screen::get_user_states().write().await;
    user_states.insert(chat_id, format!("last_token:{}:{}", Blockchain::SOL.as_str(), token_address));
    drop(user_states);

    let keyboard = InlineKeyboardMarkup::new(vec![
        vec![
            InlineKeyboardButton::callback(
                "◀️ Back to Token".to_string(),
                "back_to_last_token".to_string(),
            )
        ],
        vec![
            InlineKeyboardButton::callback(
                "🏠 Dashboard".to_string(),
                "new_msg_view_sol".to_string(),
            ),
            InlineKeyboardButton::callback(
                "❌ Dismiss".to_string(),
                "dismiss_message".to_string(),
            )
        ]
    ]);

    println!("🔍 DEBUG SELL: About to edit message with success text");
    match bot_service.edit_message_with_keyboard(chat_id, message_id, &success_text, keyboard).await {
        Ok(_) => {
            println!("✅ DEBUG SELL: Successfully updated message with success text");
        }
        Err(e) => {
            println!("❌ DEBUG SELL: Failed to update message: {}", e);
            return Err(e);
        }
    }

    Ok(())
}

fn format_token_amount(amount: u64, decimals: u8) -> String {
    let decimal_factor = 10u64.pow(decimals as u32) as f64;
    let formatted_amount = amount as f64 / decimal_factor;

    if formatted_amount < 0.000001 {
        format!("{:.8} ({})", formatted_amount, amount)
    } else if formatted_amount < 0.001 {
        format!("{:.6}", formatted_amount)
    } else if formatted_amount < 1.0 {
        format!("{:.6}", formatted_amount)
    } else if formatted_amount < 1000.0 {
        format!("{:.6}", formatted_amount)
    } else {
        format!("{:.6}", formatted_amount)
    }
}
