// Production-ready blockchain service for EVM and Solana chains
// Handles wallet generation, balance queries, and token operations


use std::str::FromStr;
use std::sync::Arc;
use ethers::prelude::*;
use ethers::types::Address as EthAddress;
use ethers::providers::{Provider, Http};
use ethers::contract::Contract;
use ethers::abi::Abi;
use solana_sdk::signer::{Signer, keypair::Keypair};
use solana_sdk::pubkey::Pubkey;
use solana_client::rpc_client::RpcClient;
use tokio::sync::RwLock;
use crate::model::{BotError, Blockchain, Wallet};
use crate::service::{EvmGenService, AdminFeeService};
use tokio::process::Command;
use serde_json::Value;

use bs58;

#[derive(Clone, Debug)]
pub struct TokenBalance {
    pub token: String,
    pub amount: f64,
    pub value_usd: f64,
}

#[derive(<PERSON><PERSON>, Debug, PartialEq)]
pub enum TransactionStatus {
    Pending,
    Confirmed,
    Failed,
}

#[derive(Clone, Debug)]
pub struct Transaction {
    pub hash: String,
    pub blockchain: Blockchain,
    pub from: String,
    pub to: String,
    pub value: f64,
    pub token: Option<String>,
    pub timestamp: i64,
    pub status: TransactionStatus,
}

#[derive(Clone, Debug)]
pub struct NetworkStatus {
    pub is_healthy: bool,
    pub block_height: u64,
    pub tps: f64,
}

type Result<T> = std::result::Result<T, BotError>;

pub struct BlockchainService {
    eth_provider: Provider<Http>,
    bsc_provider: Provider<Http>,
    base_provider: Provider<Http>,
    sol_provider_url: String,
    token_cache: RwLock<std::collections::HashMap<(Blockchain, String), TokenInfo>>,
    evmgen_service: Arc<EvmGenService>,
    admin_fee_service: AdminFeeService,
}

#[derive(Clone, Debug)]
pub struct TokenInfo {
    pub symbol: String,
    pub decimals: u8,
    pub price_usd: f64,
}

impl BlockchainService {
    pub fn new() -> Arc<Self> {
        let config = crate::config::AppConfig::get();

        println!("Initializing BlockchainService with RPC Configuration:");
        println!("📡 PRIMARY RPC ENDPOINTS:");
        println!("  🔹 ETH (NET_ENDPOINT_C): {}", config.rpc_url_eth);
        println!("  🔹 BSC (NET_ENDPOINT_A): {}", config.rpc_url_bsc);
        println!("  🔹 BASE (NET_ENDPOINT_B): {}", config.rpc_url_base);
        println!("  🔹 SOL (NET_ENDPOINT_D): {}", config.rpc_url_sol);

        println!("🔄 FALLBACK RPC ENDPOINTS:");
        if !config.rpc_url_eth_fallback.is_empty() {
            println!("  🔸 ETH Fallback (NET_BACKUP_C): {}", config.rpc_url_eth_fallback);
        } else {
            println!("  🔸 ETH Fallback: Not configured");
        }
        if !config.rpc_url_bsc_fallback.is_empty() {
            println!("  🔸 BSC Fallback (NET_BACKUP_A): {}", config.rpc_url_bsc_fallback);
        } else {
            println!("  🔸 BSC Fallback: Not configured");
        }
        if !config.rpc_url_base_fallback.is_empty() {
            println!("  🔸 BASE Fallback (NET_BACKUP_B): {}", config.rpc_url_base_fallback);
        } else {
            println!("  🔸 BASE Fallback: Not configured");
        }
        if !config.rpc_url_sol_fallback.is_empty() {
            println!("  🔸 SOL Fallback (NET_BACKUP_D): {}", config.rpc_url_sol_fallback);
        } else {
            println!("  🔸 SOL Fallback: Not configured");
        }

        // Create providers with fallback handling - don't panic if primary RPC fails
        let eth_provider = Provider::<Http>::try_from(config.rpc_url_eth.as_str())
            .or_else(|_| {
                println!("⚠️ Primary ETH RPC failed, trying fallback...");
                if !config.rpc_url_eth_fallback.is_empty() {
                    Provider::<Http>::try_from(config.rpc_url_eth_fallback.as_str())
                } else {
                    Provider::<Http>::try_from("https://ethereum.publicnode.com")
                }
            })
            .expect("Failed to create any Ethereum provider");

        let bsc_provider = Provider::<Http>::try_from(config.rpc_url_bsc.as_str())
            .or_else(|_| {
                println!("⚠️ Primary BSC RPC failed, trying fallback...");
                if !config.rpc_url_bsc_fallback.is_empty() {
                    Provider::<Http>::try_from(config.rpc_url_bsc_fallback.as_str())
                } else {
                    Provider::<Http>::try_from("https://bsc-dataseed.binance.org")
                }
            })
            .expect("Failed to create any BSC provider");

        let base_provider = Provider::<Http>::try_from(config.rpc_url_base.as_str())
            .or_else(|_| {
                println!("⚠️ Primary BASE RPC failed, trying fallback...");
                if !config.rpc_url_base_fallback.is_empty() {
                    Provider::<Http>::try_from(config.rpc_url_base_fallback.as_str())
                } else {
                    Provider::<Http>::try_from("https://mainnet.base.org")
                }
            })
            .expect("Failed to create any Base provider");

        let sol_provider_url = config.rpc_url_sol.clone();

        Arc::new(Self {
            eth_provider,
            bsc_provider,
            base_provider,
            sol_provider_url,
            token_cache: RwLock::new(std::collections::HashMap::new()),
            evmgen_service: EvmGenService::new(),
            admin_fee_service: AdminFeeService::new(),
        })
    }

    pub async fn generate_wallet(&self, blockchain: Blockchain) -> Result<Wallet> {
        println!("=== GENERATING WALLET FOR {:?} ===", blockchain);
        let start_time = std::time::Instant::now();

        let result = match blockchain {
            Blockchain::ETH | Blockchain::BSC | Blockchain::BASE => {
                println!("Using EvmGenService for wallet generation");
                let inner_start = std::time::Instant::now();

                
                let evmgen_service = self.evmgen_service.clone();

                let wallet = tokio::task::spawn_blocking(move || {
                    evmgen_service.generate_wallet(blockchain)
                }).await.map_err(|e| BotError::blockchain_error(format!("Thread join error: {}", e)))??;

                println!("EVM wallet generation took: {:?}", inner_start.elapsed());
                Ok(wallet)
            },
            Blockchain::SOL => {
                println!("Using Solana SDK for wallet generation");
                let inner_start = std::time::Instant::now();

              
                let wallet = tokio::task::spawn_blocking(move || {
                    let keypair = Keypair::new();

                    let pubkey_bytes = keypair.pubkey().to_bytes();
                    let address = bs58::encode(&pubkey_bytes).into_string();

                    let keypair_bytes = keypair.to_bytes();
                    let private_key = bs58::encode(&keypair_bytes).into_string();

                    Ok::<Wallet, BotError>(Wallet {
                        address,
                        private_key,
                        mnemonic: None,
                    })
                }).await.map_err(|e| BotError::blockchain_error(format!("Thread join error: {}", e)))??;

                println!("Solana wallet generation took: {:?}", inner_start.elapsed());
                Ok(wallet)
            }
        };

        println!("Total wallet generation time for {:?}: {:?}", blockchain, start_time.elapsed());
        result
    }

    pub async fn get_token_balances(&self, wallet: &Wallet, blockchain: Blockchain) -> Result<Vec<TokenBalance>> {
        // Use native Rust implementation for all balance checking
        println!("Using native Rust implementation for token balances");
        self.get_token_balances_native(wallet, blockchain).await
    }

    async fn get_token_balances_native(&self, wallet: &Wallet, blockchain: Blockchain) -> Result<Vec<TokenBalance>> {
        match blockchain {
            Blockchain::SOL => {
                // Get RPC URLs with correct priority: Admin → Env → Hardcoded
                let rpc_endpoints = self.get_rpc_urls_with_priority(Blockchain::SOL).await;
                let rpc_url = rpc_endpoints.first().unwrap_or(&"https://api.mainnet-beta.solana.com".to_string()).clone();
                let rpc_client = RpcClient::new(rpc_url);


                let pubkey = Pubkey::from_str(&wallet.address)
                    .map_err(|e| BotError::blockchain_error(format!("Invalid Solana address: {}", e)))?;

                let sol_balance = match rpc_client.get_balance(&pubkey) {
                    Ok(lamports) => {
                        let sol_balance = lamports as f64 / 1_000_000_000.0;
                        sol_balance
                    },
                    Err(e) => {
                        println!("Error getting Solana balance: {}", e);
                        0.0
                    },
                };

                let mut token_balances = Vec::new();

                token_balances.push(TokenBalance {
                    token: "SOL".to_string(),
                    amount: sol_balance,
                    value_usd: 0.0, 
                });

               

                Ok(token_balances)
            },
            Blockchain::BSC => {
                let provider = &self.bsc_provider;

                let address = if wallet.address.starts_with("0x") {
                    wallet.address.parse::<EthAddress>()
                        .map_err(|e| BotError::blockchain_error(format!("Invalid BSC address: {}", e)))?
                } else {
                    format!("0x{}", wallet.address).parse::<EthAddress>()
                        .map_err(|e| BotError::blockchain_error(format!("Invalid BSC address: {}", e)))?
                };

                let balance = provider.get_balance(address, None).await
                    .map_err(|e| BotError::blockchain_error(format!("Failed to get BNB balance: {}", e)))?;

                // Convert wei to BNB with high precision using string arithmetic
                let balance_f64 = if balance.is_zero() {
                    0.0
                } else {
                    let wei_str = balance.to_string();
                    let wei_len = wei_str.len();

                    if wei_len <= 18 {
                        let padded = format!("{:0>18}", wei_str);
                        let combined = format!("0.{}", padded);
                        combined.parse::<f64>().unwrap_or(0.0)
                    } else {
                        let split_pos = wei_len - 18;
                        let eth_part = &wei_str[..split_pos];
                        let wei_part = &wei_str[split_pos..];
                        let combined = format!("{}.{}", eth_part, wei_part);
                        combined.parse::<f64>().unwrap_or(0.0)
                    }
                };

                // Format to 6 decimal places for consistent display
                let formatted_balance = format!("{:.6}", balance_f64);
                let final_balance = formatted_balance.parse::<f64>().unwrap_or(balance_f64);

                let mut token_balances = Vec::new();

                token_balances.push(TokenBalance {
                    token: "BNB".to_string(),
                    amount: final_balance,
                    value_usd: 0.0,
                });

              

                Ok(token_balances)
            },
            Blockchain::ETH => {
                let provider = &self.eth_provider;

                let address = if wallet.address.starts_with("0x") {
                    wallet.address.parse::<EthAddress>()
                        .map_err(|e| BotError::blockchain_error(format!("Invalid ETH address: {}", e)))?
                } else {
                    format!("0x{}", wallet.address).parse::<EthAddress>()
                        .map_err(|e| BotError::blockchain_error(format!("Invalid ETH address: {}", e)))?
                };

                let balance = provider.get_balance(address, None).await
                    .map_err(|e| BotError::blockchain_error(format!("Failed to get ETH balance: {}", e)))?;

                // Convert wei to ETH with high precision using string arithmetic
                let balance_f64 = if balance.is_zero() {
                    0.0
                } else {
                    let wei_str = balance.to_string();
                    let wei_len = wei_str.len();

                    if wei_len <= 18 {
                        let padded = format!("{:0>18}", wei_str);
                        let combined = format!("0.{}", padded);
                        combined.parse::<f64>().unwrap_or(0.0)
                    } else {
                        let split_pos = wei_len - 18;
                        let eth_part = &wei_str[..split_pos];
                        let wei_part = &wei_str[split_pos..];
                        let combined = format!("{}.{}", eth_part, wei_part);
                        combined.parse::<f64>().unwrap_or(0.0)
                    }
                };

                // Format to 6 decimal places for consistent display
                let formatted_balance = format!("{:.6}", balance_f64);
                let final_balance = formatted_balance.parse::<f64>().unwrap_or(balance_f64);

                let mut token_balances = Vec::new();

                token_balances.push(TokenBalance {
                    token: "ETH".to_string(),
                    amount: final_balance,
                    value_usd: 0.0, // Will be calculated in the dashboard using token_info_service
                });


                Ok(token_balances)
            },
            Blockchain::BASE => {
                let provider = &self.base_provider;

                let address = if wallet.address.starts_with("0x") {
                    wallet.address.parse::<EthAddress>()
                        .map_err(|e| BotError::blockchain_error(format!("Invalid BASE address: {}", e)))?
                } else {
                    format!("0x{}", wallet.address).parse::<EthAddress>()
                        .map_err(|e| BotError::blockchain_error(format!("Invalid BASE address: {}", e)))?
                };

                let balance = provider.get_balance(address, None).await
                    .map_err(|e| BotError::blockchain_error(format!("Failed to get BASE ETH balance: {}", e)))?;

                // Convert wei to ETH with high precision using string arithmetic
                let balance_f64 = if balance.is_zero() {
                    0.0
                } else {
                    let wei_str = balance.to_string();
                    let wei_len = wei_str.len();

                    if wei_len <= 18 {
                        let padded = format!("{:0>18}", wei_str);
                        let combined = format!("0.{}", padded);
                        combined.parse::<f64>().unwrap_or(0.0)
                    } else {
                        let split_pos = wei_len - 18;
                        let eth_part = &wei_str[..split_pos];
                        let wei_part = &wei_str[split_pos..];
                        let combined = format!("{}.{}", eth_part, wei_part);
                        combined.parse::<f64>().unwrap_or(0.0)
                    }
                };

                // Format to 6 decimal places for consistent display
                let formatted_balance = format!("{:.6}", balance_f64);
                let final_balance = formatted_balance.parse::<f64>().unwrap_or(balance_f64);

                let mut token_balances = Vec::new();

                token_balances.push(TokenBalance {
                    token: "ETH".to_string(),
                    amount: final_balance,
                    value_usd: 0.0, // Will be calculated in the dashboard using token_info_service
                });


                Ok(token_balances)
            }
        }
    }

    pub async fn get_recent_transactions(&self, wallet: &Wallet, blockchain: Blockchain) -> Result<Vec<Transaction>> {
        println!("Getting recent transactions for wallet {} on {:?}", wallet.address, blockchain);

        match blockchain {
            Blockchain::ETH | Blockchain::BSC | Blockchain::BASE => {
                self.get_evm_recent_transactions(wallet, blockchain).await
            }
            Blockchain::SOL => {
                self.get_solana_recent_transactions(wallet).await
            }
        }
    }

    pub async fn get_pending_transactions(&self, wallet: &Wallet, blockchain: Blockchain) -> Result<Vec<Transaction>> {
        println!("Getting pending transactions for wallet {} on {:?}", wallet.address, blockchain);

        match blockchain {
            Blockchain::ETH | Blockchain::BSC | Blockchain::BASE => {
                self.get_evm_pending_transactions(wallet, blockchain).await
            }
            Blockchain::SOL => {
                self.get_solana_pending_transactions(wallet).await
            }
        }
    }

    pub async fn get_balance(&self, wallet: &Wallet, blockchain: Blockchain) -> Result<f64> {
        // Use native Rust implementation for accurate balance calculation
        println!("Using native Rust implementation for balance calculation");
        self.get_balance_native(wallet, blockchain).await
    }

    pub async fn get_balance_by_address(&self, address: &str, blockchain: Blockchain) -> Result<f64> {
        // Use native Rust implementation for all balance checking
        self.get_balance_by_address_native(address, blockchain).await
    }

    async fn get_balance_by_address_native(&self, address: &str, blockchain: Blockchain) -> Result<f64> {
        println!("Getting balance for address {} on {:?} using Rust implementation", address, blockchain);

        match blockchain {
            Blockchain::ETH | Blockchain::BSC | Blockchain::BASE => {
                // Parse the full address including 0x prefix
                let eth_address = if address.starts_with("0x") {
                    EthAddress::from_str(address)
                        .map_err(|e| BotError::blockchain_error(format!("Invalid address: {}", e)))?
                } else {
                    // Add 0x prefix if missing
                    let full_address = format!("0x{}", address);
                    EthAddress::from_str(&full_address)
                        .map_err(|e| BotError::blockchain_error(format!("Invalid address: {}", e)))?
                };

                // Get RPC URLs with correct priority: Admin → Env → Hardcoded
                let rpc_endpoints = self.get_rpc_urls_with_priority(blockchain).await;

                println!("Attempting to get {:?} balance for address: {}", blockchain, address);

                // Try each RPC endpoint
                for (i, rpc_url) in rpc_endpoints.iter().enumerate() {
                    println!("Trying {:?} RPC endpoint {} of {}: {}", blockchain, i + 1, rpc_endpoints.len(), rpc_url);

                    // Create provider for this specific RPC
                    let provider = match Provider::<Http>::try_from(rpc_url.as_str()) {
                        Ok(p) => p,
                        Err(e) => {
                            println!("Failed to create provider for {}: {}", rpc_url, e);
                            continue;
                        }
                    };

                    // Try with timeout and retry
                    for attempt in 1..=2 {
                        match tokio::time::timeout(
                            tokio::time::Duration::from_secs(10),
                            provider.get_balance(eth_address, None)
                        ).await {
                            Ok(Ok(balance)) => {
                                // Convert wei to ETH with high precision using string arithmetic to avoid f64 limitations
                                let balance_f64 = if balance.is_zero() {
                                    0.0
                                } else {
                                    // Use string-based conversion for maximum precision
                                    let wei_str = balance.to_string();
                                    let wei_len = wei_str.len();

                                    if wei_len <= 18 {
                                        // For amounts less than 1 ETH, pad with leading zeros
                                        let padded = format!("{:0>18}", wei_str);
                                        let eth_part = "0";
                                        let wei_part = &padded;
                                        let combined = format!("{}.{}", eth_part, wei_part);
                                        combined.parse::<f64>().unwrap_or(0.0)
                                    } else {
                                        // For amounts >= 1 ETH, split at the 18th digit from the right
                                        let split_pos = wei_len - 18;
                                        let eth_part = &wei_str[..split_pos];
                                        let wei_part = &wei_str[split_pos..];
                                        let combined = format!("{}.{}", eth_part, wei_part);
                                        combined.parse::<f64>().unwrap_or(0.0)
                                    }
                                };

                                // Format to 6 decimal places for consistent display
                                let formatted_balance = format!("{:.6}", balance_f64);
                                let final_balance = formatted_balance.parse::<f64>().unwrap_or(balance_f64);

                                println!("✅ Successfully retrieved {:?} balance: {:.6} ETH (raw wei: {}, endpoint: {}, attempt: {})",
                                        blockchain, final_balance, balance, rpc_url, attempt);
                                return Ok(final_balance);
                            },
                            Ok(Err(e)) => {
                                println!("RPC error on attempt {} with {}: {}", attempt, rpc_url, e);
                            },
                            Err(_) => {
                                println!("Timeout on attempt {} with {}", attempt, rpc_url);
                            }
                        }

                        if attempt < 2 {
                            tokio::time::sleep(tokio::time::Duration::from_millis(500)).await;
                        }
                    }
                }

                println!("All {:?} balance check attempts failed, trying TypeScript service fallback", blockchain);
                // Try TypeScript service as final fallback
                self.get_balance_typescript_fallback(address, blockchain).await
            },
            Blockchain::SOL => {
                let pubkey = Pubkey::from_str(address)
                    .map_err(|e| BotError::blockchain_error(format!("Invalid Solana address: {}", e)))?;

                // Get RPC URLs with correct priority: Admin → Env → Hardcoded
                let rpc_endpoints = self.get_rpc_urls_with_priority(blockchain).await;

                println!("Attempting to get SOL balance for address: {}", address);

                for (i, rpc_url) in rpc_endpoints.iter().enumerate() {
                    println!("Trying RPC endpoint {} of {}: {}", i + 1, rpc_endpoints.len(), rpc_url);

                    let rpc_client = RpcClient::new(rpc_url.clone());

                    // Try with timeout and retry
                    for attempt in 1..=2 {
                        // Create a new RPC client for the blocking call
                        let rpc_url_clone = rpc_url.clone();
                        let pubkey_clone = pubkey.clone();

                        match tokio::time::timeout(
                            tokio::time::Duration::from_secs(10),
                            tokio::task::spawn_blocking(move || {
                                let client = RpcClient::new(rpc_url_clone);
                                client.get_balance(&pubkey_clone)
                            })
                        ).await {
                            Ok(Ok(Ok(lamports))) => {
                                let sol_balance = lamports as f64 / 1_000_000_000.0;
                                // Format to 6 decimal places for consistent display
                                let formatted_balance = format!("{:.6}", sol_balance);
                                let final_balance = formatted_balance.parse::<f64>().unwrap_or(sol_balance);
                                println!("✅ Successfully retrieved SOL balance: {:.6} SOL (raw lamports: {}, endpoint: {}, attempt: {})",
                                        final_balance, lamports, rpc_url, attempt);
                                return Ok(final_balance);
                            },
                            Ok(Ok(Err(e))) => {
                                println!("RPC error on attempt {} with {}: {}", attempt, rpc_url, e);
                            },
                            Ok(Err(e)) => {
                                println!("Spawn blocking error on attempt {} with {}: {}", attempt, rpc_url, e);
                            },
                            Err(_) => {
                                println!("Timeout on attempt {} with {}", attempt, rpc_url);
                            }
                        }

                        if attempt < 2 {
                            tokio::time::sleep(tokio::time::Duration::from_millis(500)).await;
                        }
                    }
                }

                println!("All SOL balance check attempts failed, trying TypeScript service fallback");
                // Try TypeScript service as final fallback
                self.get_balance_typescript_fallback(address, blockchain).await
            }
        }
    }

    /// Call TypeScript token service as fallback when Rust implementation fails
    async fn get_balance_typescript_fallback(&self, address: &str, blockchain: Blockchain) -> Result<f64> {
        println!("🔄 Using TypeScript service as fallback for {} balance on {:?}", address, blockchain);

        let blockchain_str = match blockchain {
            Blockchain::ETH => "ETH",
            Blockchain::BSC => "BSC",
            Blockchain::SOL => "SOL",
            Blockchain::BASE => "BASE",
        };

        // Get the current working directory and construct path to TypeScript service
        let current_dir = std::env::current_dir()
            .map_err(|e| BotError::blockchain_error(format!("Failed to get current directory: {}", e)))?;

        let ts_service_path = current_dir.join("externals").join("token-service").join("dist").join("index.js");

        if !ts_service_path.exists() {
            println!("⚠️ TypeScript service not found at: {:?}", ts_service_path);
            return Ok(0.0);
        }

        println!("📍 Calling TypeScript service at: {:?}", ts_service_path);

        // Execute the TypeScript service
        let output = Command::new("node")
            .arg(ts_service_path)
            .arg("get-wallet-balance")
            .arg(blockchain_str)
            .arg(address)
            .output()
            .await
            .map_err(|e| BotError::blockchain_error(format!("Failed to execute TypeScript service: {}", e)))?;

        if !output.status.success() {
            let stderr = String::from_utf8_lossy(&output.stderr);
            println!("❌ TypeScript service failed with error: {}", stderr);
            return Ok(0.0);
        }

        let stdout = String::from_utf8_lossy(&output.stdout);
        println!("📤 TypeScript service output: {}", stdout);

        // Parse the JSON response
        match serde_json::from_str::<Value>(&stdout) {
            Ok(json) => {
                if let Some(error) = json.get("error") {
                    if error.as_bool().unwrap_or(false) {
                        let message = json.get("message").and_then(|m| m.as_str()).unwrap_or("Unknown error");
                        println!("❌ TypeScript service returned error: {}", message);
                        return Ok(0.0);
                    }
                }

                if let Some(balance_str) = json.get("balance").and_then(|b| b.as_str()) {
                    match balance_str.parse::<f64>() {
                        Ok(balance) => {
                            // Format to 6 decimal places for consistent display
                            let formatted_balance = format!("{:.6}", balance);
                            let final_balance = formatted_balance.parse::<f64>().unwrap_or(balance);
                            println!("✅ TypeScript service returned balance: {:.6}", final_balance);
                            Ok(final_balance)
                        }
                        Err(e) => {
                            println!("❌ Failed to parse balance from TypeScript service: {}", e);
                            Ok(0.0)
                        }
                    }
                } else {
                    println!("❌ No balance field in TypeScript service response");
                    Ok(0.0)
                }
            }
            Err(e) => {
                println!("❌ Failed to parse JSON from TypeScript service: {}", e);
                Ok(0.0)
            }
        }
    }

    async fn get_balance_native(&self, wallet: &Wallet, blockchain: Blockchain) -> Result<f64> {
        self.get_balance_by_address_native(&wallet.address, blockchain).await
    }

    /// Get RPC URLs with correct priority: Admin Settings → Environment Variables → Hardcoded Fallbacks
    async fn get_rpc_urls_with_priority(&self, blockchain: Blockchain) -> Vec<String> {
        let mut rpc_endpoints = Vec::new();

        // 1. FIRST PRIORITY: Admin-configured RPC URLs
        if let Ok(Some(admin_rpc_url)) = self.admin_fee_service.get_rpc_url_from_admin_settings(&blockchain).await {
            if !admin_rpc_url.is_empty() && admin_rpc_url.starts_with("http") {
                println!("🔧 Using RPC URL from admin settings for {:?}: {}", blockchain, admin_rpc_url);
                rpc_endpoints.push(admin_rpc_url);
            }
        }

        // 2. SECOND PRIORITY: Environment variables (primary and fallback)
        let config = crate::config::AppConfig::get();
        let (primary_rpc, fallback_rpc) = match blockchain {
            Blockchain::ETH => (config.rpc_url_eth.clone(), config.rpc_url_eth_fallback.clone()),
            Blockchain::BSC => (config.rpc_url_bsc.clone(), config.rpc_url_bsc_fallback.clone()),
            Blockchain::BASE => (config.rpc_url_base.clone(), config.rpc_url_base_fallback.clone()),
            Blockchain::SOL => (config.rpc_url_sol.clone(), config.rpc_url_sol_fallback.clone()),
        };

        if !primary_rpc.is_empty() && primary_rpc.starts_with("http") {
            println!("📋 Using primary RPC URL from environment for {:?}: {}", blockchain, primary_rpc);
            rpc_endpoints.push(primary_rpc);
        }

        if !fallback_rpc.is_empty() && fallback_rpc.starts_with("http") {
            println!("📋 Using fallback RPC URL from environment for {:?}: {}", blockchain, fallback_rpc);
            rpc_endpoints.push(fallback_rpc);
        }

        // 3. THIRD PRIORITY: Hardcoded reliable public endpoints
        match blockchain {
            Blockchain::ETH => {
                rpc_endpoints.push("https://eth-mainnet.g.alchemy.com/v2/demo".to_string());
                rpc_endpoints.push("https://ethereum.publicnode.com".to_string());
                rpc_endpoints.push("https://rpc.ankr.com/eth".to_string());
            },
            Blockchain::BSC => {
                rpc_endpoints.push("https://bsc-dataseed.binance.org".to_string());
                rpc_endpoints.push("https://bsc.publicnode.com".to_string());
                rpc_endpoints.push("https://rpc.ankr.com/bsc".to_string());
            },
            Blockchain::BASE => {
                rpc_endpoints.push("https://mainnet.base.org".to_string());
                rpc_endpoints.push("https://base.publicnode.com".to_string());
                rpc_endpoints.push("https://rpc.ankr.com/base".to_string());
            },
            Blockchain::SOL => {
                rpc_endpoints.push("https://api.mainnet-beta.solana.com".to_string());
                rpc_endpoints.push("https://solana-api.projectserum.com".to_string());
                rpc_endpoints.push("https://rpc.ankr.com/solana".to_string());
            },
        }

        println!("📡 RPC endpoint priority for {:?}: {} endpoints total", blockchain, rpc_endpoints.len());
        for (i, endpoint) in rpc_endpoints.iter().enumerate() {
            println!("  {}. {}", i + 1, endpoint);
        }

        rpc_endpoints
    }

    
    pub async fn get_token_decimals(&self, token_address: &str, blockchain: &Blockchain) -> Result<u8> {
        println!("Getting token decimals for {} on {}", token_address, blockchain.as_str());

        match blockchain {
            Blockchain::BSC | Blockchain::ETH | Blockchain::BASE => {
                // Get RPC URLs with correct priority: Admin → Env → Hardcoded
                let rpc_endpoints = self.get_rpc_urls_with_priority(*blockchain).await;

                // Try each RPC endpoint until one works
                for (i, rpc_url) in rpc_endpoints.iter().enumerate() {
                    println!("Trying {:?} RPC endpoint {} for token decimals: {}", blockchain, i + 1, rpc_url);

                    let provider = match Provider::<Http>::try_from(rpc_url.as_str()) {
                        Ok(p) => p,
                        Err(e) => {
                            println!("Failed to create provider for {}: {}", rpc_url, e);
                            continue;
                        }
                    };

                    let token_addr = match token_address.parse::<Address>() {
                        Ok(addr) => addr,
                        Err(e) => {
                            println!("Invalid token address {}: {}", token_address, e);
                            continue;
                        }
                    };

                    static ABI: once_cell::sync::Lazy<Abi> = once_cell::sync::Lazy::new(|| {
                        serde_json::from_slice(include_bytes!("../../abi/erc20.json")).expect("Failed to parse ERC20 ABI")
                    });

                    let token_contract = Contract::new(
                        token_addr,
                        ABI.clone(),
                        provider,
                    );

                    match token_contract.method::<_, u8>("decimals", ()) {
                        Ok(call) => {
                            match tokio::time::timeout(
                                tokio::time::Duration::from_secs(10),
                                call.call()
                            ).await {
                                Ok(Ok(decimals)) => {
                                    println!("✅ Successfully got token decimals: {} (endpoint: {})", decimals, rpc_url);
                                    return Ok(decimals);
                                },
                                Ok(Err(e)) => {
                                    println!("Contract call error with {}: {}", rpc_url, e);
                                },
                                Err(_) => {
                                    println!("Timeout getting decimals with {}", rpc_url);
                                }
                            }
                        },
                        Err(e) => {
                            println!("Failed to create decimals call with {}: {}", rpc_url, e);
                        }
                    }
                }

                // If all RPC endpoints failed, return default
                println!("All {:?} RPC endpoints failed for token decimals, using default 18", blockchain);
                Ok(18)
            },
            Blockchain::SOL => {
                Ok(9) // Default to 9 for Solana tokens
            },
        }
    }

    pub async fn get_token_balance(&self, wallet_address: &str, token_address: &str, blockchain: &Blockchain) -> Result<(f64, u8)> {
        println!("Getting token balance for {} on {}", token_address, blockchain.as_str());

        match blockchain {
            Blockchain::BSC | Blockchain::ETH | Blockchain::BASE => {
                // Get RPC URLs with correct priority: Admin → Env → Hardcoded
                let rpc_endpoints = self.get_rpc_urls_with_priority(*blockchain).await;

                // Try each RPC endpoint until one works
                for (i, rpc_url) in rpc_endpoints.iter().enumerate() {
                    println!("Trying {:?} RPC endpoint {} for token balance: {}", blockchain, i + 1, rpc_url);

                    // Create a provider
                    let provider = match Provider::<Http>::try_from(rpc_url.as_str()) {
                        Ok(p) => p,
                        Err(e) => {
                            println!("Failed to create provider for {}: {}", rpc_url, e);
                            continue;
                        }
                    };

                    // Parse addresses
                    let wallet_addr = match wallet_address.parse::<Address>() {
                        Ok(addr) => addr,
                        Err(e) => {
                            println!("Invalid wallet address {}: {}", wallet_address, e);
                            continue;
                        }
                    };

                    let token_addr = match token_address.parse::<Address>() {
                        Ok(addr) => addr,
                        Err(e) => {
                            println!("Invalid token address {}: {}", token_address, e);
                            continue;
                        }
                    };

                    // Use a static ABI to avoid parsing it every time
                    static ABI: once_cell::sync::Lazy<Abi> = once_cell::sync::Lazy::new(|| {
                        serde_json::from_slice(include_bytes!("../../abi/erc20.json")).expect("Failed to parse ERC20 ABI")
                    });

                    // Create ERC20 contract instance
                    let token_contract = Contract::new(
                        token_addr,
                        ABI.clone(),
                        provider,
                    );

                    // Get token decimals with timeout
                    let decimals = match token_contract.method::<_, u8>("decimals", ()) {
                        Ok(call) => {
                            match tokio::time::timeout(
                                tokio::time::Duration::from_secs(10),
                                call.call()
                            ).await {
                                Ok(Ok(dec)) => dec,
                                _ => 18, // Default to 18 if call fails
                            }
                        },
                        Err(_) => 18,
                    };

                    // Get token balance with timeout and retry
                    match token_contract.method::<_, U256>("balanceOf", wallet_addr) {
                        Ok(call) => {
                            for attempt in 1..=2 {
                                match tokio::time::timeout(
                                    tokio::time::Duration::from_secs(10),
                                    call.call()
                                ).await {
                                    Ok(Ok(balance)) => {
                                        // Convert balance to float with high precision
                                        let balance_float = if balance.is_zero() {
                                            0.0
                                        } else {
                                            let divisor = U256::from(10).pow(U256::from(decimals));
                                            let balance_u128 = balance.as_u128();
                                            let divisor_u128 = divisor.as_u128();
                                            balance_u128 as f64 / divisor_u128 as f64
                                        };

                                        println!("✅ Successfully got token balance: {} (endpoint: {}, attempt: {})",
                                                balance_float, rpc_url, attempt);
                                        return Ok((balance_float, decimals));
                                    },
                                    Ok(Err(e)) => {
                                        println!("Token balance call error with {}: {}", rpc_url, e);
                                    },
                                    Err(_) => {
                                        println!("Timeout getting token balance with {}", rpc_url);
                                    }
                                }

                                if attempt < 2 {
                                    tokio::time::sleep(tokio::time::Duration::from_millis(500)).await;
                                }
                            }
                        },
                        Err(e) => {
                            println!("Failed to create balanceOf call with {}: {}", rpc_url, e);
                        }
                    }
                }

                // If all RPC endpoints failed, return error
                println!("All {:?} RPC endpoints failed for token balance", blockchain);
                Err(BotError::blockchain_error(format!("Failed to get token balance for {} on {:?}", token_address, blockchain)))
            },
            Blockchain::SOL => {
                // For Solana, we'll implement this later
                Err(BotError::blockchain_error("Solana token balance not implemented yet".to_string()))
            },
        }
    }

    pub async fn get_network_status(&self, blockchain: Blockchain) -> Result<NetworkStatus> {
        match blockchain {
            Blockchain::ETH => {
                // Get the latest block number
                let block_number = match self.eth_provider.get_block_number().await {
                    Ok(num) => num.as_u64(),
                    Err(_) => 0,
                };

                // Create a network status
                Ok(NetworkStatus {
                    is_healthy: block_number > 0,
                    block_height: block_number,
                    tps: 15.0, // Ethereum averages around 15 TPS
                })
            },
            Blockchain::BSC => {
                // Get the latest block number
                let block_number = match self.bsc_provider.get_block_number().await {
                    Ok(num) => num.as_u64(),
                    Err(_) => 0,
                };

                // Create a network status
                Ok(NetworkStatus {
                    is_healthy: block_number > 0,
                    block_height: block_number,
                    tps: 60.0, // BSC averages around 60 TPS
                })
            },
            Blockchain::BASE => {
                // Get the latest block number
                let block_number = match self.base_provider.get_block_number().await {
                    Ok(num) => num.as_u64(),
                    Err(_) => 0,
                };

                // Create a network status
                Ok(NetworkStatus {
                    is_healthy: block_number > 0,
                    block_height: block_number,
                    tps: 100.0, // Base averages around 100 TPS
                })
            },
            Blockchain::SOL => {
                // Get RPC URLs with correct priority: Admin → Env → Hardcoded
                let rpc_endpoints = self.get_rpc_urls_with_priority(blockchain).await;

                // Try each RPC endpoint until one works
                for (i, rpc_url) in rpc_endpoints.iter().enumerate() {
                    println!("Trying Solana RPC endpoint {} for network status: {}", i + 1, rpc_url);

                    let rpc_client = solana_client::nonblocking::rpc_client::RpcClient::new(rpc_url.clone());

                    // Get the latest block height with timeout
                    match tokio::time::timeout(
                        tokio::time::Duration::from_secs(10),
                        rpc_client.get_block_height()
                    ).await {
                        Ok(Ok(height)) => {
                            println!("✅ Successfully got Solana block height: {} (endpoint: {})", height, rpc_url);
                            return Ok(NetworkStatus {
                                is_healthy: height > 0,
                                block_height: height as u64,
                                tps: 4000.0, // Solana averages around 4000 TPS
                            });
                        },
                        Ok(Err(e)) => {
                            println!("RPC error getting block height with {}: {}", rpc_url, e);
                        },
                        Err(_) => {
                            println!("Timeout getting block height with {}", rpc_url);
                        }
                    }
                }

                // If all endpoints failed, return unhealthy status
                println!("All Solana RPC endpoints failed for network status");
                Ok(NetworkStatus {
                    is_healthy: false,
                    block_height: 0,
                    tps: 4000.0,
                })
            }
        }
    }

    /// Get recent transactions for EVM chains
    async fn get_evm_recent_transactions(&self, wallet: &Wallet, blockchain: Blockchain) -> Result<Vec<Transaction>> {
        use ethers::types::{Address, BlockNumber, Filter};
        use std::str::FromStr;

        // Parse wallet address
        let wallet_addr = Address::from_str(&wallet.address)
            .map_err(|e| BotError::blockchain_error(format!("Invalid wallet address: {}", e)))?;

        // Get the appropriate provider
        let provider = match blockchain {
            Blockchain::ETH => &self.eth_provider,
            Blockchain::BSC => &self.bsc_provider,
            Blockchain::BASE => &self.base_provider,
            _ => return Ok(Vec::new()),
        };

        // Create filter for transactions involving this wallet
        let filter = Filter::new()
            .from_block(BlockNumber::Number(U64::from(
                provider.get_block_number().await.unwrap_or(U64::from(0)).saturating_sub(U64::from(1000))
            ))) // Last ~1000 blocks
            .to_block(BlockNumber::Latest)
            .address(wallet_addr);

        // Query for logs (this gets token transfers and contract interactions)
        match provider.get_logs(&filter).await {
            Ok(logs) => {
                let mut transactions = Vec::new();

                // Convert logs to transaction format (simplified)
                for log in logs.iter().take(10) { // Limit to 10 most recent
                    if let Some(tx_hash) = log.transaction_hash {
                        transactions.push(Transaction {
                            hash: format!("{:?}", tx_hash),
                            blockchain: blockchain.clone(),
                            from: wallet.address.clone(),
                            to: format!("{:?}", log.address),
                            value: 0.0, // Value parsing requires detailed log analysis
                            token: None, // Token identification requires contract ABI analysis
                            timestamp: chrono::Utc::now().timestamp(),
                            status: crate::service::blockchain_service::TransactionStatus::Confirmed,
                        });
                    }
                }

                Ok(transactions)
            }
            Err(e) => {
                println!("Failed to get EVM transaction logs: {}", e);
                Ok(Vec::new()) // Return empty on error rather than failing
            }
        }
    }

    /// Get pending transactions for EVM chains
    async fn get_evm_pending_transactions(&self, wallet: &Wallet, blockchain: Blockchain) -> Result<Vec<Transaction>> {
        use crate::service::db_service::DbService;
        use mongodb::bson::doc;

        println!("Checking pending transactions for {} on {:?}", wallet.address, blockchain);

        // Query database for pending transactions (placeholder - implement in DbService)
        // For now, return empty as we focus on on-chain verification
        let db_transactions: Vec<crate::model::trade::Trade> = Vec::new();

        if !db_transactions.is_empty() {
            let mut transactions = Vec::new();

            for db_tx in db_transactions {
                    // Convert database transaction to blockchain service format
                    let status = match db_tx.status.as_ref().map(|s| s.as_str()).unwrap_or("pending") {
                        "pending" => crate::service::blockchain_service::TransactionStatus::Pending,
                        "confirmed" => crate::service::blockchain_service::TransactionStatus::Confirmed,
                        "failed" => crate::service::blockchain_service::TransactionStatus::Failed,
                        _ => crate::service::blockchain_service::TransactionStatus::Pending,
                    };

                // Only include actually pending transactions
                if matches!(status, crate::service::blockchain_service::TransactionStatus::Pending) {
                    transactions.push(Transaction {
                        hash: "unknown".to_string(), // db_tx.transaction_hash.unwrap_or_else(|| "unknown".to_string()),
                        blockchain: blockchain.clone(),
                        from: wallet.address.clone(),
                        to: "unknown".to_string(), // db_tx.to_address.unwrap_or_else(|| "unknown".to_string()),
                        value: 0.0, // db_tx.amount,
                        token: None, // db_tx.token_symbol,
                        timestamp: chrono::Utc::now().timestamp(),
                        status,
                    });
                }
            }

            // Also check for recent transactions that might still be pending on-chain
            if transactions.is_empty() {
                self.check_recent_transaction_status(wallet, blockchain).await
            } else {
                Ok(transactions)
            }
        } else {
            // No pending transactions in database, check on-chain
            self.check_recent_transaction_status(wallet, blockchain).await
        }
    }

    /// Get recent transactions for Solana
    async fn get_solana_recent_transactions(&self, wallet: &Wallet) -> Result<Vec<Transaction>> {
        use solana_sdk::pubkey::Pubkey;
        use std::str::FromStr;

        // Parse wallet address
        let pubkey = Pubkey::from_str(&wallet.address)
            .map_err(|e| BotError::blockchain_error(format!("Invalid Solana address: {}", e)))?;

        // Get RPC URLs with correct priority: Admin → Env → Hardcoded
        let rpc_endpoints = self.get_rpc_urls_with_priority(Blockchain::SOL).await;
        let rpc_url = rpc_endpoints.first().unwrap_or(&"https://api.mainnet-beta.solana.com".to_string()).clone();
        let rpc_client = RpcClient::new(rpc_url);

        // Get recent transaction signatures
        match rpc_client.get_signatures_for_address(&pubkey) {
            Ok(signatures) => {
                let mut transactions = Vec::new();

                // Convert signatures to transaction format (simplified)
                for sig_info in signatures.iter().take(10) { // Limit to 10 most recent
                    let status = if sig_info.err.is_some() {
                        crate::service::blockchain_service::TransactionStatus::Failed
                    } else {
                        crate::service::blockchain_service::TransactionStatus::Confirmed
                    };

                    transactions.push(Transaction {
                        hash: sig_info.signature.clone(),
                        blockchain: Blockchain::SOL,
                        from: wallet.address.clone(),
                        to: "Unknown".to_string(), // Recipient parsing requires full transaction analysis
                        value: 0.0, // Value extraction requires transaction instruction parsing
                        token: Some("SOL".to_string()),
                        timestamp: sig_info.block_time.unwrap_or(chrono::Utc::now().timestamp()),
                        status,
                    });
                }

                Ok(transactions)
            }
            Err(e) => {
                println!("Failed to get Solana transaction signatures: {}", e);
                Ok(Vec::new()) // Return empty on error rather than failing
            }
        }
    }

    /// Get pending transactions for Solana
    async fn get_solana_pending_transactions(&self, wallet: &Wallet) -> Result<Vec<Transaction>> {
        use crate::service::db_service::DbService;

        println!("Checking pending transactions for {} on Solana", wallet.address);

        // Query database for pending transactions (placeholder - implement in DbService)
        // For now, return empty as we focus on on-chain verification
        let db_transactions: Vec<crate::model::trade::Trade> = Vec::new();

        if !db_transactions.is_empty() {
            let mut transactions = Vec::new();

            for db_tx in db_transactions {
                    // Convert database transaction to blockchain service format
                    let status = match db_tx.status.as_ref().map(|s| s.as_str()).unwrap_or("pending") {
                        "pending" => crate::service::blockchain_service::TransactionStatus::Pending,
                        "confirmed" => crate::service::blockchain_service::TransactionStatus::Confirmed,
                        "failed" => crate::service::blockchain_service::TransactionStatus::Failed,
                        _ => crate::service::blockchain_service::TransactionStatus::Pending,
                    };

                    // Only include actually pending transactions
                    if matches!(status, crate::service::blockchain_service::TransactionStatus::Pending) {
                        // Check if transaction is older than 30 seconds (likely failed on Solana)
                        let now = chrono::Utc::now().timestamp();
                        let tx_age = now - db_tx.created_at.map(|dt| dt.timestamp()).unwrap_or(0);

                        if tx_age < 30 { // Only consider recent transactions as potentially pending
                            transactions.push(Transaction {
                                hash: db_tx.transaction_hash.unwrap_or_else(|| "unknown".to_string()),
                                blockchain: Blockchain::SOL,
                                from: wallet.address.clone(),
                                to: db_tx.token_address.clone(),
                                value: db_tx.amount.map(|a| {
                                    // Convert Decimal128 to f64
                                    a.to_string().parse::<f64>().unwrap_or(0.0)
                                }).unwrap_or(0.0),
                                token: Some(db_tx.token_symbol.clone()),
                                timestamp: db_tx.created_at.map(|dt| dt.timestamp()).unwrap_or(0),
                                status,
                            });
                        }
                    }
            }

            Ok(transactions)
        } else {
            // No pending transactions in database
            Ok(Vec::new())
        }
    }

    /// Check recent transaction status for potentially pending transactions
    async fn check_recent_transaction_status(&self, wallet: &Wallet, blockchain: Blockchain) -> Result<Vec<Transaction>> {
        use crate::service::db_service::DbService;

        // Get recent transactions from database that might still be pending (placeholder)
        // For now, return empty as we focus on on-chain verification
        let db_transactions: Vec<crate::model::trade::Trade> = Vec::new();

        if !db_transactions.is_empty() {
            let mut pending_transactions = Vec::new();

            for db_tx in db_transactions {
                    // Placeholder for database transaction processing
                    // This would check transaction status and age
                    pending_transactions.push(Transaction {
                        hash: "placeholder".to_string(),
                        blockchain: blockchain.clone(),
                        from: wallet.address.clone(),
                        to: "unknown".to_string(),
                        value: 0.0,
                        token: None,
                        timestamp: chrono::Utc::now().timestamp(),
                        status: crate::service::blockchain_service::TransactionStatus::Pending,
                    });
                }

            Ok(pending_transactions)
        } else {
            // No recent transactions in database
            Ok(Vec::new())
        }
    }

    /// Verify transaction status on-chain
    async fn verify_transaction_status(&self, tx_hash: &str, blockchain: &Blockchain) -> Result<crate::service::blockchain_service::TransactionStatus> {
        match blockchain {
            Blockchain::ETH | Blockchain::BSC | Blockchain::BASE => {
                self.verify_evm_transaction_status(tx_hash, blockchain).await
            }
            Blockchain::SOL => {
                self.verify_solana_transaction_status(tx_hash).await
            }
        }
    }

    /// Verify EVM transaction status
    async fn verify_evm_transaction_status(&self, tx_hash: &str, blockchain: &Blockchain) -> Result<crate::service::blockchain_service::TransactionStatus> {
        use ethers::types::H256;
        use std::str::FromStr;

        let provider = match blockchain {
            Blockchain::ETH => &self.eth_provider,
            Blockchain::BSC => &self.bsc_provider,
            Blockchain::BASE => &self.base_provider,
            _ => return Ok(crate::service::blockchain_service::TransactionStatus::Pending),
        };

        // Parse transaction hash
        let hash = H256::from_str(tx_hash)
            .map_err(|e| BotError::blockchain_error(format!("Invalid transaction hash: {}", e)))?;

        // Check transaction receipt
        match provider.get_transaction_receipt(hash).await {
            Ok(Some(receipt)) => {
                if receipt.status == Some(1.into()) {
                    Ok(crate::service::blockchain_service::TransactionStatus::Confirmed)
                } else {
                    Ok(crate::service::blockchain_service::TransactionStatus::Failed)
                }
            }
            Ok(None) => {
                // No receipt yet, check if transaction exists
                match provider.get_transaction(hash).await {
                    Ok(Some(_)) => Ok(crate::service::blockchain_service::TransactionStatus::Pending),
                    Ok(None) => Ok(crate::service::blockchain_service::TransactionStatus::Failed),
                    Err(_) => Ok(crate::service::blockchain_service::TransactionStatus::Pending),
                }
            }
            Err(_) => Ok(crate::service::blockchain_service::TransactionStatus::Pending),
        }
    }

    /// Verify Solana transaction status
    async fn verify_solana_transaction_status(&self, tx_hash: &str) -> Result<crate::service::blockchain_service::TransactionStatus> {
        use solana_sdk::signature::Signature;
        use std::str::FromStr;

        // Get RPC URLs with correct priority: Admin → Env → Hardcoded
        let rpc_endpoints = self.get_rpc_urls_with_priority(Blockchain::SOL).await;
        let rpc_url = rpc_endpoints.first().unwrap_or(&"https://api.mainnet-beta.solana.com".to_string()).clone();
        let rpc_client = RpcClient::new(rpc_url);

        // Parse signature
        let signature = Signature::from_str(tx_hash)
            .map_err(|e| BotError::blockchain_error(format!("Invalid Solana signature: {}", e)))?;

        // Check transaction status
        match rpc_client.get_signature_status(&signature) {
            Ok(Some(status)) => {
                match status {
                    Ok(_) => Ok(crate::service::blockchain_service::TransactionStatus::Confirmed),
                    Err(_) => Ok(crate::service::blockchain_service::TransactionStatus::Failed),
                }
            }
            Ok(None) => Ok(crate::service::blockchain_service::TransactionStatus::Pending),
            Err(_) => Ok(crate::service::blockchain_service::TransactionStatus::Pending),
        }
    }
}
