use crate::model::{
    User, UserWallets, UserConfig, UserSession, UserDashboard, UserTrades,
    Blockchain, Wallet
};

/// A struct that holds all user-related data
#[derive(Debug, Clone)]
pub struct UserData {
    pub user: User,
    pub wallets: UserWallets,
    pub config: UserConfig,
    pub session: UserSession,
    pub dashboard: UserDashboard,
    pub trades: UserTrades,
}

impl UserData {
    pub fn new(
        user: User,
        wallets: UserWallets,
        config: UserConfig,
        session: UserSession,
        dashboard: UserDashboard,
        trades: UserTrades,
    ) -> Self {
        Self {
            user,
            wallets,
            config,
            session,
            dashboard,
            trades,
        }
    }

    pub fn display_name(&self) -> String {
        self.user.display_name()
    }

    pub fn chat_id(&self) -> i64 {
        self.user.chat_id
    }

    pub fn current_blockchain(&self) -> Blockchain {
        self.user.current_blockchain
    }

    pub fn set_current_blockchain(&mut self, blockchain: Blockchain) {
        self.user.current_blockchain = blockchain;
    }

    pub fn get_wallet(&self, blockchain: &Blockchain) -> &Wallet {
        match blockchain {
            Blockchain::BSC => &self.wallets.bsc_wallet,
            Blockchain::SOL => &self.wallets.sol_wallet,
            Blockchain::ETH => &self.wallets.eth_wallet,
            Blockchain::BASE => &self.wallets.base_wallet,
        }
    }

    pub fn wallets_generated(&self) -> bool {
        self.wallets.wallets_generated
    }

    pub fn set_wallets_generated(&mut self, generated: bool) {
        self.wallets.wallets_generated = generated;
    }

    pub fn track_conversation_message(&mut self, message_id: i32) {
        self.session.track_conversation_message(message_id);
    }

    pub fn take_conversation_messages(&mut self) -> Vec<i32> {
        self.session.take_conversation_messages()
    }

    pub fn clear_conversation(&mut self) {
        self.session.clear_conversation();
    }

    pub fn get_current_contract_address(&self) -> Option<&String> {
        self.config.get_current_contract_address()
    }

    pub fn get_current_token_name(&self) -> Option<&String> {
        self.config.get_current_token_name()
    }

    pub fn get_config(&self, blockchain: &Blockchain) -> Option<&crate::model::wallet_config::WalletConfig> {
        self.config.get_config(blockchain)
    }

    pub fn update_last_seen(&mut self) {
        self.user.update_last_seen();
    }
}
