services:
  - type: web
    name: easybot
    env: docker
    buildCommand: chmod +x build-render.sh && ./build-render.sh
    startCommand: ./target/release/Easybot
    envVars:
      # OpenSSL configuration for Linux
      - key: OPENSSL_NO_VENDOR
        value: "0"
      - key: OPENSSL_STATIC
        value: "1"
      - key: RUSTLS_NATIVE_CERTS
        value: "1"

      # Explicitly unset Windows paths
      - key: OPENSSL_DIR
        value: ""
      - key: OPENSSL_LIB_DIR
        value: ""
      - key: OPENSSL_INCLUDE_DIR
        value: ""

      # Port configuration - Render will provide PORT automatically
      # SOCKET_PORT will be PORT + 1
      - key: SOCKET_PORT
        value: "10001"
