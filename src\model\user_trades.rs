use serde::{Deserialize, Serialize};
use mongodb::bson::oid::ObjectId;

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct UserTrades {
    #[serde(rename = "_id", skip_serializing_if = "Option::is_none")]
    pub id: Option<ObjectId>,
    pub user_id: ObjectId,
    pub trades: TradeReferences,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct TradeReferences {
    #[serde(default)]
    pub bsc: Vec<ObjectId>,
    #[serde(default)]
    pub sol: Vec<ObjectId>,
    #[serde(default)]
    pub eth: Vec<ObjectId>,
    #[serde(default)]
    pub base: Vec<ObjectId>,
}

impl Default for TradeReferences {
    fn default() -> Self {
        Self {
            bsc: Vec::new(),
            sol: Vec::new(),
            eth: Vec::new(),
            base: Vec::new(),
        }
    }
}

impl UserTrades {
    pub fn new(user_id: ObjectId) -> Self {
        Self {
            id: None,
            user_id,
            trades: TradeReferences::default(),
        }
    }
}
