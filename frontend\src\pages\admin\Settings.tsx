import { useState, useEffect } from 'react';
import { Card } from '../../components/ui/Card';
import Button from '../../components/ui/Button';
import {
  CogIcon,
  CurrencyDollarIcon,
  WalletIcon,
  ShieldCheckIcon,
  ExclamationTriangleIcon,
} from '@heroicons/react/24/outline';
import adminApi from '../../services/adminApi';

interface AdminSettings {
  id?: string;
  admin_fee_percentage: number;
  admin_fee_percentage_eth?: number;
  admin_fee_percentage_bsc?: number;
  admin_fee_percentage_base?: number;
  admin_fee_percentage_sol?: number;
  admin_wallet_eth?: string;
  admin_wallet_bsc?: string;
  admin_wallet_base?: string;
  admin_wallet_sol?: string;
  max_users_per_admin: number;
  maintenance_mode: boolean;
  auto_fee_collection: boolean;
  fee_collection_threshold: number;
  updated_at: number;
  updated_by: string;
  custom_settings: Record<string, any>;
}

export default function AdminSettings() {
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [settings, setSettings] = useState<AdminSettings | null>(null);
  const [formData, setFormData] = useState<Partial<AdminSettings>>({});

  useEffect(() => {
    fetchSettings();
  }, []);

  const fetchSettings = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await adminApi.getSettings();
      setSettings(data);
      setFormData(data);
    } catch (err: any) {
      console.error('Error fetching settings:', err);
      setError(err.message || 'Failed to fetch settings');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: keyof AdminSettings, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSave = async () => {
    try {
      setSaving(true);
      setError(null);
      setSuccess(null);

      const updatedSettings = await adminApi.updateSettings(formData);
      setSettings(updatedSettings);
      setFormData(updatedSettings);
      setSuccess('Settings updated successfully!');
      
      // Clear success message after 3 seconds
      setTimeout(() => setSuccess(null), 3000);
    } catch (err: any) {
      console.error('Error updating settings:', err);
      setError(err.message || 'Failed to update settings');
    } finally {
      setSaving(false);
    }
  };

  const handleReset = async () => {
    if (window.confirm('Are you sure you want to reset all settings to defaults? This action cannot be undone.')) {
      try {
        setSaving(true);
        setError(null);
        // This would call a reset endpoint
        await fetchSettings(); // For now, just refetch
        setSuccess('Settings reset to defaults!');
        setTimeout(() => setSuccess(null), 3000);
      } catch (err: any) {
        console.error('Error resetting settings:', err);
        setError(err.message || 'Failed to reset settings');
      } finally {
        setSaving(false);
      }
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-pulse flex space-x-4 items-center">
          <div className="h-12 w-12 rounded-full bg-indigo-500/20"></div>
          <div className="space-y-2">
            <div className="h-4 w-36 bg-indigo-500/20 rounded"></div>
            <div className="h-4 w-24 bg-indigo-500/20 rounded"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-2xl font-bold text-white">Admin Settings</h1>
          <p className="mt-1 text-sm text-gray-400">
            Configure admin fees, wallets, and system settings
          </p>
        </div>
        <div className="flex space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleReset}
            disabled={saving}
          >
            Reset to Defaults
          </Button>
          <Button
            variant="primary"
            size="sm"
            onClick={handleSave}
            disabled={saving || !formData}
          >
            {saving ? 'Saving...' : 'Save Changes'}
          </Button>
        </div>
      </div>

      {error && (
        <div className="rounded-md bg-red-900/20 border border-red-500/30 p-4">
          <div className="flex">
            <ExclamationTriangleIcon className="h-5 w-5 text-red-400" />
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-400">Error</h3>
              <p className="mt-1 text-sm text-red-300">{error}</p>
            </div>
          </div>
        </div>
      )}

      {success && (
        <div className="rounded-md bg-green-900/20 border border-green-500/30 p-4">
          <div className="flex">
            <ShieldCheckIcon className="h-5 w-5 text-green-400" />
            <div className="ml-3">
              <h3 className="text-sm font-medium text-green-400">Success</h3>
              <p className="mt-1 text-sm text-green-300">{success}</p>
            </div>
          </div>
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Fee Configuration */}
        <Card className="p-6">
          <div className="flex items-center mb-6">
            <CurrencyDollarIcon className="h-6 w-6 text-indigo-400 mr-3" />
            <h3 className="text-lg font-semibold text-white">Fee Configuration</h3>
          </div>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Default Admin Fee (%)
              </label>
              <input
                type="number"
                step="0.1"
                min="0"
                max="10"
                value={formData.admin_fee_percentage || 0}
                onChange={(e) => handleInputChange('admin_fee_percentage', parseFloat(e.target.value))}
                className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-md text-white focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
              />
              <p className="mt-1 text-xs text-gray-400">
                Default fee percentage applied to all networks
              </p>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Ethereum Fee (%)
                </label>
                <input
                  type="number"
                  step="0.1"
                  min="0"
                  max="10"
                  value={formData.admin_fee_percentage_eth || ''}
                  onChange={(e) => handleInputChange('admin_fee_percentage_eth', e.target.value ? parseFloat(e.target.value) : undefined)}
                  className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-md text-white focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                  placeholder="Use default"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  BSC Fee (%)
                </label>
                <input
                  type="number"
                  step="0.1"
                  min="0"
                  max="10"
                  value={formData.admin_fee_percentage_bsc || ''}
                  onChange={(e) => handleInputChange('admin_fee_percentage_bsc', e.target.value ? parseFloat(e.target.value) : undefined)}
                  className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-md text-white focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                  placeholder="Use default"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Base Fee (%)
                </label>
                <input
                  type="number"
                  step="0.1"
                  min="0"
                  max="10"
                  value={formData.admin_fee_percentage_base || ''}
                  onChange={(e) => handleInputChange('admin_fee_percentage_base', e.target.value ? parseFloat(e.target.value) : undefined)}
                  className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-md text-white focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                  placeholder="Use default"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Solana Fee (%)
                </label>
                <input
                  type="number"
                  step="0.1"
                  min="0"
                  max="10"
                  value={formData.admin_fee_percentage_sol || ''}
                  onChange={(e) => handleInputChange('admin_fee_percentage_sol', e.target.value ? parseFloat(e.target.value) : undefined)}
                  className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-md text-white focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                  placeholder="Use default"
                />
              </div>
            </div>

            <div className="flex items-center">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={formData.auto_fee_collection || false}
                  onChange={(e) => handleInputChange('auto_fee_collection', e.target.checked)}
                  className="rounded border-gray-600 bg-gray-800 text-indigo-600 focus:ring-indigo-500"
                />
                <span className="ml-2 text-sm text-gray-300">Auto Fee Collection</span>
              </label>
            </div>
          </div>
        </Card>

        {/* Wallet Configuration */}
        <Card className="p-6">
          <div className="flex items-center mb-6">
            <WalletIcon className="h-6 w-6 text-indigo-400 mr-3" />
            <h3 className="text-lg font-semibold text-white">Admin Wallets</h3>
          </div>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Ethereum Wallet Address
              </label>
              <input
                type="text"
                value={formData.admin_wallet_eth || ''}
                onChange={(e) => handleInputChange('admin_wallet_eth', e.target.value)}
                className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-md text-white focus:ring-2 focus:ring-indigo-500 focus:border-transparent font-mono text-sm"
                placeholder="0x..."
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                BSC Wallet Address
              </label>
              <input
                type="text"
                value={formData.admin_wallet_bsc || ''}
                onChange={(e) => handleInputChange('admin_wallet_bsc', e.target.value)}
                className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-md text-white focus:ring-2 focus:ring-indigo-500 focus:border-transparent font-mono text-sm"
                placeholder="0x..."
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Base Wallet Address
              </label>
              <input
                type="text"
                value={formData.admin_wallet_base || ''}
                onChange={(e) => handleInputChange('admin_wallet_base', e.target.value)}
                className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-md text-white focus:ring-2 focus:ring-indigo-500 focus:border-transparent font-mono text-sm"
                placeholder="0x..."
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Solana Wallet Address
              </label>
              <input
                type="text"
                value={formData.admin_wallet_sol || ''}
                onChange={(e) => handleInputChange('admin_wallet_sol', e.target.value)}
                className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-md text-white focus:ring-2 focus:ring-indigo-500 focus:border-transparent font-mono text-sm"
                placeholder="HAodjxQ3345hj8dchuAgwXscEmH2wgBw7uUTCeZKUjBj"
              />
              <p className="mt-1 text-xs text-gray-400">
                Enter Solana wallet address in base58 format (32-44 characters)
              </p>
            </div>
          </div>
        </Card>

        {/* System Configuration */}
        <Card className="p-6 lg:col-span-2">
          <div className="flex items-center mb-6">
            <CogIcon className="h-6 w-6 text-indigo-400 mr-3" />
            <h3 className="text-lg font-semibold text-white">System Configuration</h3>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="flex items-center">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={formData.maintenance_mode || false}
                  onChange={(e) => handleInputChange('maintenance_mode', e.target.checked)}
                  className="rounded border-gray-600 bg-gray-800 text-indigo-600 focus:ring-indigo-500"
                />
                <span className="ml-2 text-sm text-gray-300">Maintenance Mode</span>
              </label>
              <p className="ml-2 text-xs text-gray-400">
                Disables bot for all users
              </p>
            </div>

            <div className="text-sm text-gray-400">
              <p><strong>Last Updated:</strong> {settings?.updated_at ? new Date(settings.updated_at * 1000).toLocaleString() : 'Never'}</p>
              <p><strong>Updated By:</strong> {settings?.updated_by || 'Unknown'}</p>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
}
