use std::str::FromStr;
use std::sync::Arc;
use std::time::Duration;
use std::collections::HashMap;

use solana_client::nonblocking::rpc_client::RpcClient;
use solana_sdk::commitment_config::CommitmentConfig;
use solana_sdk::instruction::{AccountMeta, Instruction};
use solana_sdk::pubkey::Pubkey;
use solana_sdk::signature::{Keypair, Signature};
use solana_sdk::signer::Signer;
use solana_sdk::transaction::{Transaction, VersionedTransaction};
use solana_sdk::message::Message;
use solana_sdk::compute_budget::ComputeBudgetInstruction;
use solana_sdk::system_instruction;
use spl_token::instruction as token_instruction;
use spl_associated_token_account::instruction as ata_instruction;
use tokio::sync::{<PERSON><PERSON><PERSON>, RwLock};
use reqwest::Client;
use serde::{Deserialize, Serialize};
use serde_json::json;
use thiserror::Error;

use crate::model::BotError;
use crate::service::raydium_swap;

// Constants for Raydium AMM
const RAYDIUM_SWAP_PROGRAM_ID: &str = "675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8";
const RAYDIUM_AMM_PROGRAM_ID: &str = "675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8";

// SOL mint address
const SOL_MINT: &str = "So11111111111111111111111111111111111111112";
const WSOL_MINT: &str = "So11111111111111111111111111111111111111112";

// Constants for Jito API
const JITO_API_BASE_URL: &str = "https://mainnet.block-engine.jito.wtf/api/v1";
const JITO_BUNDLE_ENDPOINT: &str = "/bundles";

// Error type for direct swap operations
#[derive(Error, Debug)]
pub enum DirectSwapError {
    #[error("Raydium API error: {0}")]
    RaydiumApiError(String),

    #[error("Jito API error: {0}")]
    JitoApiError(String),

    #[error("Solana RPC error: {0}")]
    SolanaRpcError(String),

    #[error("Transaction error: {0}")]
    TransactionError(String),

    #[error("Serialization error: {0}")]
    SerializationError(String),

    #[error("Timeout error")]
    TimeoutError,

    #[error("Pool not found: {0}")]
    PoolNotFound(String),
}

// Convert DirectSwapError to BotError
impl From<DirectSwapError> for BotError {
    fn from(error: DirectSwapError) -> Self {
        BotError::GeneralError(error.to_string())
    }
}

// Raydium Pool information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RaydiumPool {
    pub id: String,
    pub base_mint: String,
    pub quote_mint: String,
    pub lp_mint: String,
    pub base_decimals: u8,
    pub quote_decimals: u8,
    pub lp_decimals: u8,
    pub version: u8,
    pub program_id: String,
    pub authority: String,
    pub open_orders: String,
    pub target_orders: String,
    pub base_vault: String,
    pub quote_vault: String,
    pub withdraw_queue: String,
    pub lp_vault: String,
    pub market_id: String,
    pub market_program_id: String,
    pub market_authority: String,
    pub market_base_vault: String,
    pub market_quote_vault: String,
    pub market_bids: String,
    pub market_asks: String,
    pub market_event_queue: String,
}

// Jito API response types
#[derive(Debug, Deserialize)]
pub struct JitoBundleResponse {
    pub uuid: String,
}

// Swap result information
#[derive(Debug, Clone)]
pub struct DirectSwapResult {
    pub signature: Signature,
    pub input_mint: String,
    pub output_mint: String,
    pub input_amount: u64,
    pub output_amount: u64,
    pub success: bool,
    pub timestamp: chrono::DateTime<chrono::Utc>,
}

// Direct swap service for Solana
pub struct DirectSwapService {
    // HTTP client for API requests
    http_client: Client,

    // Solana RPC client for blockchain interactions
    rpc_client: Arc<RpcClient>,

    // Jito API key (optional)
    jito_api_key: Option<String>,

    // Cache for Raydium pools
    pool_cache: Arc<RwLock<HashMap<String, RaydiumPool>>>,

    // Transaction processing queue
    tx_queue: Arc<Mutex<Vec<VersionedTransaction>>>,

    // Number of worker threads
    worker_threads: usize,
}

impl DirectSwapService {
    // Create a new DirectSwapService
    pub fn new(rpc_url: &str, jito_api_key: Option<String>, worker_threads: usize) -> Self {
        // Create HTTP client with timeout
        let http_client = Client::builder()
            .timeout(Duration::from_secs(30))
            .build()
            .unwrap_or_default();

        // Create Solana RPC client
        let rpc_client = Arc::new(RpcClient::new_with_commitment(
            rpc_url.to_string(),
            CommitmentConfig::confirmed(),
        ));

        // Create pool cache
        let pool_cache = Arc::new(RwLock::new(HashMap::new()));

        // Create transaction queue
        let tx_queue = Arc::new(Mutex::new(Vec::new()));

        Self {
            http_client,
            rpc_client,
            jito_api_key,
            pool_cache,
            tx_queue,
            worker_threads,
        }
    }

    // Initialize the service and start worker threads
    pub async fn initialize(&self) {
        // Start worker threads for processing transactions
        for i in 0..self.worker_threads {
            let tx_queue = self.tx_queue.clone();
            let rpc_client = self.rpc_client.clone();
            let http_client = self.http_client.clone();
            let jito_api_key = self.jito_api_key.clone();

            tokio::spawn(async move {
                println!("Starting worker thread {}", i);
                Self::transaction_worker(tx_queue, rpc_client, http_client, jito_api_key).await;
            });
        }

        // Fetch and cache Raydium pools
        self.fetch_raydium_pools().await;
    }

    // Fetch Raydium pools from the API
    async fn fetch_raydium_pools(&self) {
        // Raydium API endpoint for pools
        let raydium_api_url = "https://api.raydium.io/v2/sdk/liquidity/mainnet.json";

        match self.http_client.get(raydium_api_url).send().await {
            Ok(response) => {
                if let Ok(pools_data) = response.json::<serde_json::Value>().await {
                    if let Some(pools) = pools_data.get("official").and_then(|v| v.as_array()) {
                        let mut cache = self.pool_cache.write().await;

                        for pool_data in pools {
                            if let Ok(pool) = serde_json::from_value::<RaydiumPool>(pool_data.clone()) {
                                // Create cache keys for both directions of the pool
                                let key1 = format!("{}:{}", pool.base_mint, pool.quote_mint);
                                let key2 = format!("{}:{}", pool.quote_mint, pool.base_mint);

                                cache.insert(key1, pool.clone());
                                cache.insert(key2, pool);
                            }
                        }

                        println!("Cached {} Raydium pools", cache.len() / 2);
                    }
                }
            },
            Err(e) => {
                println!("Error fetching Raydium pools: {}", e);
            }
        }
    }

    // Worker thread for processing transactions - optimized for stack usage
    async fn transaction_worker(
        tx_queue: Arc<Mutex<Vec<VersionedTransaction>>>,
        rpc_client: Arc<RpcClient>,
        http_client: Client,
        jito_api_key: Option<String>,
    ) {
        loop {
            // Get transactions from queue - use a separate function to reduce stack usage
            let txs = match Self::get_transactions_from_queue(&tx_queue).await {
                Some(txs) => txs,
                None => continue, // Queue was empty, continue the loop
            };

            // Process transactions based on count - use separate functions to reduce stack usage
            if txs.len() == 1 {
                // Process single transaction in a separate task to isolate stack usage
                let tx = txs[0].clone();
                let rpc_client_clone = rpc_client.clone();

                tokio::spawn(async move {
                    Self::process_single_transaction(&tx, &rpc_client_clone).await;
                });
            } else if txs.len() > 1 {
                // Process bundle in a separate task to isolate stack usage
                let txs_clone = txs.clone();
                let http_client_clone = http_client.clone();
                let jito_api_key_clone = jito_api_key.clone();

                tokio::spawn(async move {
                    let _ = Self::send_jito_bundle(&http_client_clone, &txs_clone, &jito_api_key_clone).await;
                });
            }

            // Sleep briefly to prevent CPU hogging
            tokio::time::sleep(Duration::from_millis(10)).await;
        }
    }

    // Helper function to get transactions from queue - reduces stack usage in the main loop
    async fn get_transactions_from_queue(
        tx_queue: &Arc<Mutex<Vec<VersionedTransaction>>>,
    ) -> Option<Vec<VersionedTransaction>> {
        let mut queue = tx_queue.lock().await;
        if queue.is_empty() {
            tokio::time::sleep(Duration::from_millis(100)).await;
            return None;
        }

        // Take up to 5 transactions (Jito bundle limit)
        let mut txs = Vec::with_capacity(5); // Pre-allocate with exact capacity
        for _ in 0..5 {
            if let Some(tx) = queue.pop() {
                txs.push(tx);
            } else {
                break;
            }
        }
        Some(txs)
    }

    // Helper function to process a single transaction - reduces stack usage and cleans up memory
    async fn process_single_transaction(
        tx: &VersionedTransaction,
        rpc_client: &Arc<RpcClient>,
    ) {
        // Use a scoped block for serialization to ensure memory is freed after use
        let tx_bytes = {
            // Serialize transaction with error handling
            match bincode::serialize(tx) {
                Ok(bytes) => bytes,
                Err(e) => {
                    println!("Failed to serialize transaction: {}", e);
                    return;
                }
            }
        }; // Serialization temporaries are dropped here

        // Create config in a separate scope to reduce stack usage
        let config = {
            solana_client::rpc_config::RpcSendTransactionConfig {
                skip_preflight: true,
                preflight_commitment: Some(CommitmentConfig::processed()),
                encoding: None,
                max_retries: Some(3),
                min_context_slot: None,
            }
        }; // Any temporaries used in config creation are dropped here

        // Send transaction
        {
            let result = rpc_client.send_transaction_with_config(&tx_bytes, config).await;
            if let Err(e) = result {
                println!("Failed to send transaction: {}", e);
            }
        } // Result is dropped here

        // Explicitly drop tx_bytes to free memory
        drop(tx_bytes);
        drop(config);
    }

    // Send a bundle of transactions via Jito with optimized memory usage
    async fn send_jito_bundle(
        http_client: &Client,
        txs: &[VersionedTransaction],
        jito_api_key: &Option<String>,
    ) -> Result<String, DirectSwapError> {
        // Serialize transactions in a separate scope to free memory after use
        let tx_bytes: Vec<String> = {
            // Process each transaction in a way that frees memory after each one
            let mut results = Vec::with_capacity(txs.len());

            for tx in txs.iter() {
                // Serialize in a separate scope
                let encoded = {
                    let tx_bytes = bincode::serialize(tx)
                        .map_err(|e| DirectSwapError::SerializationError(e.to_string()))?;
                    let encoded = base64::encode(&tx_bytes);
                    // tx_bytes is dropped here
                    encoded
                };
                results.push(encoded);
            }

            results
        }; // All intermediate serialization results are dropped here

        // Create bundle request in a separate scope
        let bundle_request = {
            json!({
                "transactions": tx_bytes,
                "header": {
                    "timeout_slots": 3,
                    "protection": "jitodontfront"
                }
            })
        }; // Any temporaries used in JSON creation are dropped here

        // We're done with tx_bytes after creating the request
        drop(tx_bytes);

        // Build request with API key if available
        let mut request_builder = http_client.post(format!("{}{}", JITO_API_BASE_URL, JITO_BUNDLE_ENDPOINT))
            .json(&bundle_request);

        if let Some(api_key) = jito_api_key {
            request_builder = request_builder.header("Authorization", format!("Bearer {}", api_key));
        }

        // Send request
        let response = request_builder.send().await
            .map_err(|e| DirectSwapError::JitoApiError(e.to_string()))?;

        // Parse response
        if response.status().is_success() {
            let bundle_response: JitoBundleResponse = response.json().await
                .map_err(|e| DirectSwapError::SerializationError(e.to_string()))?;
            Ok(bundle_response.uuid)
        } else {
            let error_text = response.text().await
                .unwrap_or_else(|_| "Unknown error".to_string());
            Err(DirectSwapError::JitoApiError(error_text))
        }
    }

    // Find a Raydium pool for the given token pair
    async fn find_pool(&self, input_mint: &Pubkey, output_mint: &Pubkey) -> Result<RaydiumPool, DirectSwapError> {
        // Create cache key
        let cache_key = format!("{}:{}", input_mint.to_string(), output_mint.to_string());

        // Check cache
        {
            let cache = self.pool_cache.read().await;
            if let Some(pool) = cache.get(&cache_key) {
                return Ok(pool.clone());
            }
        }

        // If not in cache, refresh pools and try again
        self.fetch_raydium_pools().await;

        // Check cache again
        {
            let cache = self.pool_cache.read().await;
            if let Some(pool) = cache.get(&cache_key) {
                return Ok(pool.clone());
            }
        }

        // If still not found, return error
        Err(DirectSwapError::PoolNotFound(format!("No pool found for {}:{}", input_mint, output_mint)))
    }

    // Execute a direct swap using Raydium
    pub async fn execute_direct_swap(
        &self,
        wallet: &Keypair,
        input_mint: &Pubkey,
        output_mint: &Pubkey,
        amount_in: u64,
        slippage_bps: u32,
    ) -> Result<DirectSwapResult, DirectSwapError> {
        // Find pool for the token pair
        let pool = self.find_pool(input_mint, output_mint).await?;

        // Calculate minimum output amount based on slippage
        let min_amount_out = if input_mint.to_string() == pool.base_mint {
            // Base to quote swap
            let base_decimals = pool.base_decimals;
            let quote_decimals = pool.quote_decimals;

            // Simple calculation for minimum output
            // In a production environment, you would use a price oracle or pool data
            let slippage_factor = 1.0 - (slippage_bps as f64 / 10000.0);
            let estimated_out = (amount_in as f64 * slippage_factor) as u64;

            // Adjust for decimal differences
            if base_decimals > quote_decimals {
                estimated_out / 10u64.pow((base_decimals - quote_decimals) as u32)
            } else if quote_decimals > base_decimals {
                estimated_out * 10u64.pow((quote_decimals - base_decimals) as u32)
            } else {
                estimated_out
            }
        } else {
            // Quote to base swap
            let base_decimals = pool.base_decimals;
            let quote_decimals = pool.quote_decimals;

            // Simple calculation for minimum output
            let slippage_factor = 1.0 - (slippage_bps as f64 / 10000.0);
            let estimated_out = (amount_in as f64 * slippage_factor) as u64;

            // Adjust for decimal differences
            if quote_decimals > base_decimals {
                estimated_out / 10u64.pow((quote_decimals - base_decimals) as u32)
            } else if base_decimals > quote_decimals {
                estimated_out * 10u64.pow((base_decimals - quote_decimals) as u32)
            } else {
                estimated_out
            }
        };

        // Get recent blockhash
        let recent_blockhash = self.rpc_client.get_latest_blockhash().await
            .map_err(|e| DirectSwapError::SolanaRpcError(e.to_string()))?;

        // Create swap transaction
        let transaction = raydium_swap::create_swap_transaction(
            &pool,
            wallet,
            input_mint,
            output_mint,
            amount_in,
            min_amount_out,
            recent_blockhash,
        ).map_err(|e| DirectSwapError::TransactionError(e.to_string()))?;

        // Convert to versioned transaction
        let versioned_transaction = VersionedTransaction::from(transaction);

        // Add to transaction queue for processing
        {
            let mut queue = self.tx_queue.lock().await;
            queue.push(versioned_transaction.clone());
        }

        // Create result
        let result = DirectSwapResult {
            signature: versioned_transaction.signatures[0],
            input_mint: input_mint.to_string(),
            output_mint: output_mint.to_string(),
            input_amount: amount_in,
            output_amount: min_amount_out, // This is just the minimum, actual amount will be higher
            success: true,
            timestamp: chrono::Utc::now(),
        };

        Ok(result)
    }

    // Simplified method to buy a token with SOL using direct swap
    pub async fn buy_token_with_sol_direct(
        &self,
        wallet: &Keypair,
        token_mint: &Pubkey,
        sol_amount: u64,
        slippage_bps: u32,
    ) -> Result<DirectSwapResult, DirectSwapError> {
        // SOL mint address
        let sol_mint = Pubkey::from_str(SOL_MINT)
            .map_err(|e| DirectSwapError::TransactionError(e.to_string()))?;

        // Execute direct swap
        self.execute_direct_swap(wallet, &sol_mint, token_mint, sol_amount, slippage_bps).await
    }

    // Simplified method to sell a token for SOL using direct swap
    pub async fn sell_token_for_sol_direct(
        &self,
        wallet: &Keypair,
        token_mint: &Pubkey,
        token_amount: u64,
        slippage_bps: u32,
    ) -> Result<DirectSwapResult, DirectSwapError> {
        // SOL mint address
        let sol_mint = Pubkey::from_str(SOL_MINT)
            .map_err(|e| DirectSwapError::TransactionError(e.to_string()))?;

        // Execute direct swap
        self.execute_direct_swap(wallet, token_mint, &sol_mint, token_amount, slippage_bps).await
    }
}
