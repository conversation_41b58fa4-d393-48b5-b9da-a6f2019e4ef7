import{r as t,j as e,B as b,c as _,b as S,a as k}from"./index-dCUkEeO4.js";import{C as g}from"./Card-CLlE15Sf.js";import{adminApi as j}from"./adminApi-BFZ8qr13.js";function C({title:i,titleId:n,...d},l){return t.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:l,"aria-labelledby":n},d),i?t.createElement("title",{id:n},i):null,t.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M4.5 12a7.5 7.5 0 0 0 15 0m-15 0a7.5 7.5 0 1 1 15 0m-15 0H3m16.5 0H21m-1.5 0H12m-8.457 3.077 1.41-.513m14.095-5.13 1.41-.513M5.106 17.785l1.15-.964m11.49-9.642 1.149-.964M7.501 19.795l.75-1.3m7.5-12.99.75-1.3m-6.063 16.658.26-1.477m2.605-14.772.26-1.477m0 17.726-.26-1.477M10.698 4.614l-.26-1.477M16.5 19.794l-.75-1.299M7.5 4.205 12 12m6.894 5.785-1.149-.964M6.256 7.178l-1.15-.964m15.352 8.864-1.41-.513M4.954 9.435l-1.41-.514M12.002 12l-3.75 6.495"}))}const F=t.forwardRef(C);function E({title:i,titleId:n,...d},l){return t.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:l,"aria-labelledby":n},d),i?t.createElement("title",{id:n},i):null,t.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M21 12a2.25 2.25 0 0 0-2.25-2.25H15a3 3 0 1 1-6 0H5.25A2.25 2.25 0 0 0 3 12m18 0v6a2.25 2.25 0 0 1-2.25 2.25H5.25A2.25 2.25 0 0 1 3 18v-6m18 0V9M3 12V9m18 0a2.25 2.25 0 0 0-2.25-2.25H5.25A2.25 2.25 0 0 0 3 9m18 0V6a2.25 2.25 0 0 0-2.25-2.25H5.25A2.25 2.25 0 0 0 3 6v3"}))}const A=t.forwardRef(E);function R(){const[i,n]=t.useState(!0),[d,l]=t.useState(!1),[u,c]=t.useState(null),[h,m]=t.useState(null),[o,f]=t.useState(null),[a,x]=t.useState({});t.useEffect(()=>{p()},[]);const p=async()=>{try{n(!0),c(null);const s=await j.getSettings();f(s),x(s)}catch(s){console.error("Error fetching settings:",s),c(s.message||"Failed to fetch settings")}finally{n(!1)}},r=(s,w)=>{x(N=>({...N,[s]:w}))},y=async()=>{try{l(!0),c(null),m(null);const s=await j.updateSettings(a);f(s),x(s),m("Settings updated successfully!"),setTimeout(()=>m(null),3e3)}catch(s){console.error("Error updating settings:",s),c(s.message||"Failed to update settings")}finally{l(!1)}},v=async()=>{if(window.confirm("Are you sure you want to reset all settings to defaults? This action cannot be undone."))try{l(!0),c(null),await p(),m("Settings reset to defaults!"),setTimeout(()=>m(null),3e3)}catch(s){console.error("Error resetting settings:",s),c(s.message||"Failed to reset settings")}finally{l(!1)}};return i?e.jsx("div",{className:"flex items-center justify-center py-12",children:e.jsxs("div",{className:"animate-pulse flex space-x-4 items-center",children:[e.jsx("div",{className:"h-12 w-12 rounded-full bg-indigo-500/20"}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("div",{className:"h-4 w-36 bg-indigo-500/20 rounded"}),e.jsx("div",{className:"h-4 w-24 bg-indigo-500/20 rounded"})]})]})}):e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold text-white",children:"Admin Settings"}),e.jsx("p",{className:"mt-1 text-sm text-gray-400",children:"Configure admin fees, wallets, and system settings"})]}),e.jsxs("div",{className:"flex space-x-2",children:[e.jsx(b,{variant:"outline",size:"sm",onClick:v,disabled:d,children:"Reset to Defaults"}),e.jsx(b,{variant:"primary",size:"sm",onClick:y,disabled:d||!a,children:d?"Saving...":"Save Changes"})]})]}),u&&e.jsx("div",{className:"rounded-md bg-red-900/20 border border-red-500/30 p-4",children:e.jsxs("div",{className:"flex",children:[e.jsx(_,{className:"h-5 w-5 text-red-400"}),e.jsxs("div",{className:"ml-3",children:[e.jsx("h3",{className:"text-sm font-medium text-red-400",children:"Error"}),e.jsx("p",{className:"mt-1 text-sm text-red-300",children:u})]})]})}),h&&e.jsx("div",{className:"rounded-md bg-green-900/20 border border-green-500/30 p-4",children:e.jsxs("div",{className:"flex",children:[e.jsx(S,{className:"h-5 w-5 text-green-400"}),e.jsxs("div",{className:"ml-3",children:[e.jsx("h3",{className:"text-sm font-medium text-green-400",children:"Success"}),e.jsx("p",{className:"mt-1 text-sm text-green-300",children:h})]})]})}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[e.jsxs(g,{className:"p-6",children:[e.jsxs("div",{className:"flex items-center mb-6",children:[e.jsx(k,{className:"h-6 w-6 text-indigo-400 mr-3"}),e.jsx("h3",{className:"text-lg font-semibold text-white",children:"Fee Configuration"})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Default Admin Fee (%)"}),e.jsx("input",{type:"number",step:"0.1",min:"0",max:"10",value:a.admin_fee_percentage||0,onChange:s=>r("admin_fee_percentage",parseFloat(s.target.value)),className:"w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-md text-white focus:ring-2 focus:ring-indigo-500 focus:border-transparent"}),e.jsx("p",{className:"mt-1 text-xs text-gray-400",children:"Default fee percentage applied to all networks"})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Ethereum Fee (%)"}),e.jsx("input",{type:"number",step:"0.1",min:"0",max:"10",value:a.admin_fee_percentage_eth||"",onChange:s=>r("admin_fee_percentage_eth",s.target.value?parseFloat(s.target.value):void 0),className:"w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-md text-white focus:ring-2 focus:ring-indigo-500 focus:border-transparent",placeholder:"Use default"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"BSC Fee (%)"}),e.jsx("input",{type:"number",step:"0.1",min:"0",max:"10",value:a.admin_fee_percentage_bsc||"",onChange:s=>r("admin_fee_percentage_bsc",s.target.value?parseFloat(s.target.value):void 0),className:"w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-md text-white focus:ring-2 focus:ring-indigo-500 focus:border-transparent",placeholder:"Use default"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Base Fee (%)"}),e.jsx("input",{type:"number",step:"0.1",min:"0",max:"10",value:a.admin_fee_percentage_base||"",onChange:s=>r("admin_fee_percentage_base",s.target.value?parseFloat(s.target.value):void 0),className:"w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-md text-white focus:ring-2 focus:ring-indigo-500 focus:border-transparent",placeholder:"Use default"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Solana Fee (%)"}),e.jsx("input",{type:"number",step:"0.1",min:"0",max:"10",value:a.admin_fee_percentage_sol||"",onChange:s=>r("admin_fee_percentage_sol",s.target.value?parseFloat(s.target.value):void 0),className:"w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-md text-white focus:ring-2 focus:ring-indigo-500 focus:border-transparent",placeholder:"Use default"})]})]}),e.jsx("div",{className:"flex items-center",children:e.jsxs("label",{className:"flex items-center",children:[e.jsx("input",{type:"checkbox",checked:a.auto_fee_collection||!1,onChange:s=>r("auto_fee_collection",s.target.checked),className:"rounded border-gray-600 bg-gray-800 text-indigo-600 focus:ring-indigo-500"}),e.jsx("span",{className:"ml-2 text-sm text-gray-300",children:"Auto Fee Collection"})]})})]})]}),e.jsxs(g,{className:"p-6",children:[e.jsxs("div",{className:"flex items-center mb-6",children:[e.jsx(A,{className:"h-6 w-6 text-indigo-400 mr-3"}),e.jsx("h3",{className:"text-lg font-semibold text-white",children:"Admin Wallets"})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Ethereum Wallet Address"}),e.jsx("input",{type:"text",value:a.admin_wallet_eth||"",onChange:s=>r("admin_wallet_eth",s.target.value),className:"w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-md text-white focus:ring-2 focus:ring-indigo-500 focus:border-transparent font-mono text-sm",placeholder:"0x..."})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"BSC Wallet Address"}),e.jsx("input",{type:"text",value:a.admin_wallet_bsc||"",onChange:s=>r("admin_wallet_bsc",s.target.value),className:"w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-md text-white focus:ring-2 focus:ring-indigo-500 focus:border-transparent font-mono text-sm",placeholder:"0x..."})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Base Wallet Address"}),e.jsx("input",{type:"text",value:a.admin_wallet_base||"",onChange:s=>r("admin_wallet_base",s.target.value),className:"w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-md text-white focus:ring-2 focus:ring-indigo-500 focus:border-transparent font-mono text-sm",placeholder:"0x..."})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Solana Wallet Address"}),e.jsx("input",{type:"text",value:a.admin_wallet_sol||"",onChange:s=>r("admin_wallet_sol",s.target.value),className:"w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-md text-white focus:ring-2 focus:ring-indigo-500 focus:border-transparent font-mono text-sm",placeholder:"HAodjxQ3345hj8dchuAgwXscEmH2wgBw7uUTCeZKUjBj"}),e.jsx("p",{className:"mt-1 text-xs text-gray-400",children:"Enter Solana wallet address in base58 format (32-44 characters)"})]})]})]}),e.jsxs(g,{className:"p-6 lg:col-span-2",children:[e.jsxs("div",{className:"flex items-center mb-6",children:[e.jsx(F,{className:"h-6 w-6 text-indigo-400 mr-3"}),e.jsx("h3",{className:"text-lg font-semibold text-white",children:"System Configuration"})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsxs("label",{className:"flex items-center",children:[e.jsx("input",{type:"checkbox",checked:a.maintenance_mode||!1,onChange:s=>r("maintenance_mode",s.target.checked),className:"rounded border-gray-600 bg-gray-800 text-indigo-600 focus:ring-indigo-500"}),e.jsx("span",{className:"ml-2 text-sm text-gray-300",children:"Maintenance Mode"})]}),e.jsx("p",{className:"ml-2 text-xs text-gray-400",children:"Disables bot for all users"})]}),e.jsxs("div",{className:"text-sm text-gray-400",children:[e.jsxs("p",{children:[e.jsx("strong",{children:"Last Updated:"})," ",o!=null&&o.updated_at?new Date(o.updated_at*1e3).toLocaleString():"Never"]}),e.jsxs("p",{children:[e.jsx("strong",{children:"Updated By:"})," ",(o==null?void 0:o.updated_by)||"Unknown"]})]})]})]})]})]})}export{R as default};
