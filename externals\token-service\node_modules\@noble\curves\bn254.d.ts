import { type CurveFn as BLSCurveFn, type PostPrecomputeFn } from './abstract/bls.ts';
import { type CurveFn } from './abstract/weierstrass.ts';
export declare const _postPrecompute: PostPrecomputeFn;
/**
 * bn254 (a.k.a. alt_bn128) pairing-friendly curve.
 * Contains G1 / G2 operations and pairings.
 */
export declare const bn254: BLSCurveFn;
/**
 * bn254 weierstrass curve with ECDSA.
 * This is very rare and probably not used anywhere.
 * Instead, you should use G1 / G2, defined above.
 * @deprecated
 */
export declare const bn254_weierstrass: CurveFn;
//# sourceMappingURL=bn254.d.ts.map