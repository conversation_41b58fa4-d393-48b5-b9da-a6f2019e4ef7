use serde::{Deserialize, Serialize};
use std::collections::HashMap;

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct AdminDashboard {
    pub analytics: DashboardAnalytics,
    pub bots: Vec<BotSummary>,
    pub recent_transactions: Vec<TransactionSummary>,
    pub system_health: SystemHealth,
    pub alerts: Vec<SystemAlert>,
}

#[derive(<PERSON>bu<PERSON>, <PERSON>lone, Serialize, Deserialize)]
pub struct DashboardAnalytics {
    pub total_users: i64,
    pub active_users_24h: i64,
    pub total_transactions: i64,
    pub transactions_24h: i64,
    pub total_volume: f64,
    pub volume_24h: f64,
    pub total_fees_collected: f64,
    pub fees_collected_24h: f64,
    pub success_rate: f64,
    pub average_response_time: f64,
    pub blockchain_distribution: HashMap<String, i64>,
    pub hourly_stats: Vec<HourlyStats>,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct HourlyStats {
    pub hour: u64,
    pub transactions: i64,
    pub volume: f64,
    pub users: i64,
    pub success_rate: f64,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct BotSummary {
    pub id: String,
    pub name: String,
    pub bot_type: String,
    pub status: String,
    pub active_users: i64,
    pub transactions_24h: i64,
    pub volume_24h: f64,
    pub success_rate: f64,
    pub uptime_percentage: f64,
    pub last_error: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TransactionSummary {
    pub id: String,
    pub user_id: String,
    pub username: Option<String>,
    pub bot_type: String,
    pub transaction_type: String,
    pub amount: f64,
    pub token_symbol: String,
    pub status: String,
    pub timestamp: u64,
    pub blockchain: String,
    pub gas_fee: Option<f64>,
    pub success: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SystemHealth {
    pub uptime_seconds: u64,
    pub cpu_usage: f64,
    pub memory_usage: f64,
    pub disk_usage: f64,
    pub active_connections: i64,
    pub api_requests_per_minute: i64,
    pub database_connections: i64,
    pub blockchain_connections: HashMap<String, bool>,
    pub last_backup: Option<u64>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SystemAlert {
    pub id: String,
    pub severity: AlertSeverity,
    pub title: String,
    pub message: String,
    pub timestamp: u64,
    pub resolved: bool,
    pub component: String,
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum AlertSeverity {
    Info,
    Warning,
    Error,
    Critical,
}

impl AdminDashboard {
    pub fn new() -> Self {
        Self {
            analytics: DashboardAnalytics::default(),
            bots: Vec::new(),
            recent_transactions: Vec::new(),
            system_health: SystemHealth::default(),
            alerts: Vec::new(),
        }
    }
}

impl Default for DashboardAnalytics {
    fn default() -> Self {
        Self {
            total_users: 0,
            active_users_24h: 0,
            total_transactions: 0,
            transactions_24h: 0,
            total_volume: 0.0,
            volume_24h: 0.0,
            total_fees_collected: 0.0,
            fees_collected_24h: 0.0,
            success_rate: 0.0,
            average_response_time: 0.0,
            blockchain_distribution: HashMap::new(),
            hourly_stats: Vec::new(),
        }
    }
}

impl Default for SystemHealth {
    fn default() -> Self {
        Self {
            uptime_seconds: 0,
            cpu_usage: 0.0,
            memory_usage: 0.0,
            disk_usage: 0.0,
            active_connections: 0,
            api_requests_per_minute: 0,
            database_connections: 0,
            blockchain_connections: HashMap::new(),
            last_backup: None,
        }
    }
}
