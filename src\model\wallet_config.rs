use serde::{Deserialize, Serialize};
use bson::Decimal128;
use std::str::FromStr;
use crate::config::defaults;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WalletConfig {
    pub slippage: Decimal128,
    pub antirug: bool,
    pub anti_mev: bool,
    pub max_gas_price: Decimal128,
    pub max_gas_limit: i32,
    pub auto_buy: bool,
    pub min_liquidity: Decimal128,
    pub max_market_cap: Decimal128,
    pub max_liquidity: Decimal128,
    pub auto_sell: bool,
    pub sell_high: Decimal128,
    pub sell_low: Decimal128,
    pub auto_approve: bool,
}

impl Default for WalletConfig {
    fn default() -> Self {
        Self {
            slippage: Decimal128::from_str("20.0").unwrap(), // Default slippage 20%
            antirug: true, // Enable anti-rug by default
            anti_mev: true, // Enable anti-mev by default
            max_gas_price: Decimal128::from_str("10.0").unwrap(),
            max_gas_limit: 1000000,
            auto_buy: false,
            min_liquidity: Decimal128::from_str("0.0").unwrap(),
            max_market_cap: Decimal128::from_str("0.0").unwrap(),
            max_liquidity: Decimal128::from_str("0.0").unwrap(),
            auto_sell: false,
            sell_high: Decimal128::from_str("20.0").unwrap(), // Default sell high 20%
            sell_low: Decimal128::from_str("10.0").unwrap(), // Default sell low 10%
            auto_approve: true, // Set auto_approve to true by default
        }
    }
}

impl WalletConfig {
    pub fn bsc_default() -> Self {
        Self {
            slippage: Decimal128::from_str("20.0").unwrap(),
            ..Default::default()
        }
    }

    pub fn eth_default() -> Self {
        Self {
            slippage: Decimal128::from_str("20.0").unwrap(),
            ..Default::default()
        }
    }

    pub fn sol_default() -> Self {
        Self {
            slippage: Decimal128::from_str("20.0").unwrap(),
            ..Default::default()
        }
    }

    pub fn base_default() -> Self {
        Self {
            slippage: Decimal128::from_str("20.0").unwrap(),
            ..Default::default()
        }
    }
}
