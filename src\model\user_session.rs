use serde::{Deserialize, Serialize};
use mongodb::bson::oid::ObjectId;
use std::time::{SystemTime, UNIX_EPOCH};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UserSession {
    #[serde(rename = "_id", skip_serializing_if = "Option::is_none")]
    pub id: Option<ObjectId>,
    pub user_id: ObjectId,
    #[serde(default)]
    pub recent_messages: Vec<i32>,
    #[serde(default)]
    pub active_conversation: Vec<i32>,
    #[serde(default = "default_timestamp")]
    pub last_activity: u64,
    #[serde(default)]
    pub dashboard_message_id: Option<i32>,
}

fn default_timestamp() -> u64 {
    SystemTime::now()
        .duration_since(UNIX_EPOCH)
        .unwrap_or_default()
        .as_secs()
}

impl UserSession {
    pub fn new(user_id: ObjectId) -> Self {
        Self {
            id: None,
            user_id,
            recent_messages: Vec::new(),
            active_conversation: Vec::new(),
            last_activity: default_timestamp(),
            dashboard_message_id: None,
        }
    }

    // Track a message ID for later cleanup
    pub fn track_message(&mut self, message_id: i32) {
        // Keep only the last 20 messages to avoid excessive storage
        if self.recent_messages.len() >= 20 {
            self.recent_messages.remove(0);
        }
        self.recent_messages.push(message_id);
        self.update_activity();
    }

    // Get all tracked message IDs and clear the list
    pub fn take_recent_messages(&mut self) -> Vec<i32> {
        self.update_activity();
        std::mem::take(&mut self.recent_messages)
    }

    // Clear all tracked messages
    pub fn clear_recent_messages(&mut self) {
        self.recent_messages.clear();
        self.update_activity();
    }

    // Track a message in the active conversation
    pub fn track_conversation_message(&mut self, message_id: i32) {
        // Keep track of the message in the active conversation
        self.active_conversation.push(message_id);

        // Limit the size to prevent excessive memory usage (keep last 50 messages)
        if self.active_conversation.len() > 50 {
            self.active_conversation.remove(0);
        }

        self.update_activity();
    }

    // Get all messages in the active conversation and clear the list
    pub fn take_conversation_messages(&mut self) -> Vec<i32> {
        self.update_activity();
        std::mem::take(&mut self.active_conversation)
    }

    // Clear all messages in the active conversation
    pub fn clear_conversation(&mut self) {
        self.active_conversation.clear();
        self.update_activity();
    }

    // Update the last activity timestamp
    fn update_activity(&mut self) {
        self.last_activity = default_timestamp();
    }

    // Check if the session is expired (inactive for more than 24 hours)
    pub fn is_expired(&self) -> bool {
        let now = default_timestamp();
        now - self.last_activity > 24 * 60 * 60 // 24 hours in seconds
    }

    // Set the dashboard message ID
    pub fn set_dashboard_message_id(&mut self, message_id: i32) {
        self.dashboard_message_id = Some(message_id);
        self.update_activity();
    }

    // Clear the dashboard message ID
    pub fn clear_dashboard_message_id(&mut self) {
        self.dashboard_message_id = None;
        self.update_activity();
    }
}
