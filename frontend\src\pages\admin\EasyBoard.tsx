import { useState, useEffect } from 'react';
import { <PERSON> } from 'react-router-dom';
import {
  ArrowUpIcon,
  ArrowDownIcon,
  UserIcon,
  CurrencyDollarIcon,
  BoltIcon,
  ClockIcon,
  ChartBarIcon,
  ShieldCheckIcon,
  WrenchScrewdriverIcon,
} from '@heroicons/react/24/outline';
import { Card } from '../../components/ui/Card';
import { Line } from 'react-chartjs-2';
import { adminApi } from '../../services/adminApi';
import 'chart.js/auto';

// Chart.js options
const chartOptions = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      position: 'top' as const,
      labels: {
        color: '#e5e7eb',
      },
    },
    tooltip: {
      mode: 'index' as const,
      intersect: false,
    },
  },
  scales: {
    y: {
      grid: {
        color: 'rgba(255, 255, 255, 0.1)',
      },
      ticks: {
        color: '#e5e7eb',
      },
    },
    x: {
      grid: {
        color: 'rgba(255, 255, 255, 0.1)',
      },
      ticks: {
        color: '#e5e7eb',
      },
    },
  },
};

export default function EasyBoard() {
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState({
    totalUsers: 0,
    activeUsers: 0,
    userGrowth: 0,
    totalBots: 0,
    activeBots: 0,
    botGrowth: 0,
    totalTransactions: 0,
    transactionVolume: 0,
    transactionGrowth: 0,
    adminFeeEarned: 0,
    adminFeeGrowth: 0,
    systemStatus: 'operational',
    alerts: [],
  });

  const [transactionData, setTransactionData] = useState({
    labels: [],
    datasets: [
      {
        label: 'Transaction Volume',
        data: [],
        borderColor: 'rgb(99, 102, 241)',
        backgroundColor: 'rgba(99, 102, 241, 0.5)',
      },
      {
        label: 'Admin Fee Earned',
        data: [],
        borderColor: 'rgb(34, 197, 94)',
        backgroundColor: 'rgba(34, 197, 94, 0.5)',
      },
    ],
  });



  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        // Fetch real data from API
        const dashboardData = await adminApi.getDashboardAnalytics();

        setStats({
          totalUsers: dashboardData.analytics.total_users,
          activeUsers: dashboardData.analytics.active_users_24h,
          userGrowth: dashboardData.analytics.total_users > 0
            ? ((dashboardData.analytics.active_users_24h / dashboardData.analytics.total_users) * 100)
            : 0,
          totalBots: dashboardData.bots_summary.length,
          activeBots: dashboardData.bots_summary.filter(bot => bot.status === 'Active').length,
          botGrowth: dashboardData.bots_summary.length > 0
            ? ((dashboardData.bots_summary.filter(bot => bot.status === 'Active').length / dashboardData.bots_summary.length) * 100)
            : 0,
          totalTransactions: dashboardData.analytics.total_transactions,
          transactionVolume: dashboardData.analytics.total_volume,
          transactionGrowth: dashboardData.analytics.total_transactions > 0
            ? ((dashboardData.analytics.transactions_24h / dashboardData.analytics.total_transactions) * 100)
            : 0,
          adminFeeEarned: dashboardData.analytics.total_fees_collected,
          adminFeeGrowth: dashboardData.analytics.total_fees_collected > 0
            ? ((dashboardData.analytics.fees_collected_24h / dashboardData.analytics.total_fees_collected) * 100)
            : 0,
          systemStatus: dashboardData.system_health.status === 'healthy' ? 'operational' : 'degraded',
          alerts: dashboardData.alerts,
        });

        // Generate chart data from hourly stats
        const hourlyStats = dashboardData.analytics.hourly_stats;
        if (hourlyStats && hourlyStats.length > 0) {
          // Group hourly data into daily data for the chart
          const dailyData = [];
          const dailyFees = [];
          const labels = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];

          for (let day = 0; day < 7; day++) {
            const dayStart = day * 24;
            const dayEnd = Math.min((day + 1) * 24, hourlyStats.length);
            const dayVolume = hourlyStats
              .slice(dayStart, dayEnd)
              .reduce((sum, stat) => sum + stat.volume, 0);
            const dayFees = dayVolume * (dashboardData.analytics.total_fees_collected / dashboardData.analytics.total_volume || 0.025);

            dailyData.push(dayVolume);
            dailyFees.push(dayFees);
          }

          setTransactionData({
            labels,
            datasets: [
              {
                label: 'Transaction Volume',
                data: dailyData,
                borderColor: 'rgb(99, 102, 241)',
                backgroundColor: 'rgba(99, 102, 241, 0.5)',
              },
              {
                label: 'Admin Fee Earned',
                data: dailyFees,
                borderColor: 'rgb(34, 197, 94)',
                backgroundColor: 'rgba(34, 197, 94, 0.5)',
              },
            ],
          });
        }



        setLoading(false);
      } catch (error) {
        console.error('Error fetching dashboard stats:', error);
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(value);
  };

  const formatNumber = (value: number) => {
    return new Intl.NumberFormat('en-US').format(value);
  };



  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-2xl font-bold text-white">EasyBoard Dashboard</h1>
          <p className="mt-1 text-sm text-gray-400">Overview of your EasyBot system performance</p>
        </div>
        <div className="flex items-center space-x-2">

        </div>
      </div>

      {loading ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"></div>
        </div>
      ) : (
        <>
          {/* Stats Cards */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card className="p-4">
              <div className="flex items-center">
                <div className="flex-shrink-0 p-3 rounded-md bg-indigo-500/20">
                  <UserIcon className="h-6 w-6 text-indigo-400" />
                </div>
                <div className="ml-4">
                  <h3 className="text-sm font-medium text-gray-400">Total Users</h3>
                  <div className="flex items-center">
                    <p className="text-2xl font-semibold text-white">{formatNumber(stats.totalUsers)}</p>
                    <span className="ml-2 text-xs font-medium text-green-400 flex items-center">
                      <ArrowUpIcon className="h-3 w-3 mr-0.5" />
                      {stats.userGrowth}%
                    </span>
                  </div>
                </div>
              </div>
            </Card>

            <Card className="p-4">
              <div className="flex items-center">
                <div className="flex-shrink-0 p-3 rounded-md bg-purple-500/20">
                  <WrenchScrewdriverIcon className="h-6 w-6 text-purple-400" />
                </div>
                <div className="ml-4">
                  <h3 className="text-sm font-medium text-gray-400">Active Bots</h3>
                  <div className="flex items-center">
                    <p className="text-2xl font-semibold text-white">{formatNumber(stats.activeBots)}</p>
                    <span className="ml-2 text-xs font-medium text-green-400 flex items-center">
                      <ArrowUpIcon className="h-3 w-3 mr-0.5" />
                      {stats.botGrowth}%
                    </span>
                  </div>
                </div>
              </div>
            </Card>

            <Card className="p-4">
              <div className="flex items-center">
                <div className="flex-shrink-0 p-3 rounded-md bg-blue-500/20">
                  <BoltIcon className="h-6 w-6 text-blue-400" />
                </div>
                <div className="ml-4">
                  <h3 className="text-sm font-medium text-gray-400">Transactions</h3>
                  <div className="flex items-center">
                    <p className="text-2xl font-semibold text-white">{formatNumber(stats.totalTransactions)}</p>
                    <span className="ml-2 text-xs font-medium text-green-400 flex items-center">
                      <ArrowUpIcon className="h-3 w-3 mr-0.5" />
                      {stats.transactionGrowth}%
                    </span>
                  </div>
                </div>
              </div>
            </Card>

            <Card className="p-4">
              <div className="flex items-center">
                <div className="flex-shrink-0 p-3 rounded-md bg-green-500/20">
                  <CurrencyDollarIcon className="h-6 w-6 text-green-400" />
                </div>
                <div className="ml-4">
                  <h3 className="text-sm font-medium text-gray-400">Admin Fee Earned</h3>
                  <div className="flex items-center">
                    <p className="text-2xl font-semibold text-white">{formatCurrency(stats.adminFeeEarned)}</p>
                    <span className="ml-2 text-xs font-medium text-green-400 flex items-center">
                      <ArrowUpIcon className="h-3 w-3 mr-0.5" />
                      {stats.adminFeeGrowth}%
                    </span>
                  </div>
                </div>
              </div>
            </Card>
          </div>

          {/* Main Content */}
          <div className="grid grid-cols-1 gap-6">
            <Card className="p-4">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-medium text-white">Transaction Overview</h3>
                <div className="text-sm text-gray-400">Last 7 days</div>
              </div>
              <div className="h-80">
                <Line options={chartOptions} data={transactionData} />
              </div>
            </Card>
          </div>

          {/* Quick Links */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
            <Link to="/dashboard/users">
              <Card className="p-4 hover:bg-gray-800/50 transition-colors duration-200">
                <div className="flex items-center">
                  <div className="flex-shrink-0 p-3 rounded-md bg-indigo-500/20">
                    <UserIcon className="h-6 w-6 text-indigo-400" />
                  </div>
                  <div className="ml-4">
                    <h3 className="text-sm font-medium text-white">User Analytics</h3>
                    <p className="text-xs text-gray-400 mt-1">View detailed user statistics</p>
                  </div>
                </div>
              </Card>
            </Link>

            <Link to="/dashboard/bots">
              <Card className="p-4 hover:bg-gray-800/50 transition-colors duration-200">
                <div className="flex items-center">
                  <div className="flex-shrink-0 p-3 rounded-md bg-purple-500/20">
                    <WrenchScrewdriverIcon className="h-6 w-6 text-purple-400" />
                  </div>
                  <div className="ml-4">
                    <h3 className="text-sm font-medium text-white">Bot Management</h3>
                    <p className="text-xs text-gray-400 mt-1">Configure and monitor bots</p>
                  </div>
                </div>
              </Card>
            </Link>

            <Link to="/dashboard/transactions">
              <Card className="p-4 hover:bg-gray-800/50 transition-colors duration-200">
                <div className="flex items-center">
                  <div className="flex-shrink-0 p-3 rounded-md bg-blue-500/20">
                    <BoltIcon className="h-6 w-6 text-blue-400" />
                  </div>
                  <div className="ml-4">
                    <h3 className="text-sm font-medium text-white">Transactions</h3>
                    <p className="text-xs text-gray-400 mt-1">View transaction history</p>
                  </div>
                </div>
              </Card>
            </Link>

            <Link to="/dashboard/settings">
              <Card className="p-4 hover:bg-gray-800/50 transition-colors duration-200">
                <div className="flex items-center">
                  <div className="flex-shrink-0 p-3 rounded-md bg-green-500/20">
                    <ShieldCheckIcon className="h-6 w-6 text-green-400" />
                  </div>
                  <div className="ml-4">
                    <h3 className="text-sm font-medium text-white">Admin Settings</h3>
                    <p className="text-xs text-gray-400 mt-1">Configure system settings</p>
                  </div>
                </div>
              </Card>
            </Link>
          </div>
        </>
      )}
    </div>
  );
}
